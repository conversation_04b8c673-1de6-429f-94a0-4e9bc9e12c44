import tap from "tap";
import { fastify } from "../index";
import { MedplumClient, MedplumClientOptions, EmailPasswordLoginRequest, ProfileResource } from '@medplum/core';
import { JSDOM } from 'jsdom';
import axios from 'axios';
import puppeteer from 'puppeteer';
import { spawn, spawnSync } from 'child_process';
import path from 'path';
import os from 'os';
const { window } = new JSDOM();
global.window = window as unknown as Window & typeof globalThis;
global.document = window.document;
global.Element = window.Element;
const sessionStorageMock: Storage = {
    store: {} as Record<string, string>,
    getItem: function (key: string) {
        return this.store[key] || null;
    },
    setItem: function (key: string, value: string) {
        this.store[key] = value.toString();
    },
    removeItem: function (key: string) {
        delete this.store[key];
    },
    clear: function () {
        this.store = {};
    },
    get length() {
        return Object.keys(this.store).length;
    },
    key: function (index: number) {
        return Object.keys(this.store)[index] || null;
    },
};

global.sessionStorage = sessionStorageMock;

// Test Data for PCC Patient Testing.
const bearerToken: string = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************.4m-83vKFSyJT0A4FGL3zmEwvabJf3DbpiVY8AMG4L3o";
const xOrganizationId: string = "15ACD770-F4FB-4901-A83C-14E63B67F9C1";
let pccPatientName: string = "";
const xOrganizationIdIncorrect: string = "15ACD770-F4FB-4901-A83C-14E63B6";
const pccPatientIdIncorrect: string = "1111111";
let pccPatientId: string = '';
let activeLogin: any = {
    accessToken: ''
};
let serverProcess: any = null;
let browser: any = null;
const getPCCAccessToken = async () => {
    try {
        browser = await puppeteer.launch({ headless: true }); //False to observe actual puppeteer actions, set to true before PR review
        const page = await browser.newPage();
        await page.goto('http://localhost:5173/login', { waitUntil: 'domcontentloaded' });
        await new Promise(resolve => setTimeout(resolve, 600));
        await page.click('[id="pcc-login-btn"]');

        await page.waitForSelector('[name="un"]');
        await page.waitForSelector('[name="pw"]');
        await page.type('[name="un"]', 'relfac5911.registerednurse');
        await page.type('[name="pw"]', '2x5KUBvKj4OQtD5');

        await page.waitForSelector('[id="signin"]', { visible: true });
        console.log("Clicked the signin button...");
        await page.click('[id="signin"]');


        await page.waitForNavigation({ waitUntil: 'networkidle2' });
        await new Promise(resolve => setTimeout(resolve, 900));
        const localStorageData = await page.evaluate(() => {
            let json = {};
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key) { json[key] = localStorage.getItem(key); }
            }
            return json;
        });

        if (localStorageData['activeLoginEHR']) {
            activeLogin = JSON.parse(localStorageData['activeLoginEHR'] as string);
            console.log('activeLogin:', activeLogin);
        }

        await page.waitForNavigation({ waitUntil: 'networkidle2' });
    } catch (err) {
        console.log('err: ', err);
    }
    finally {
        await browser.close();
    }
};

const waitForServer = async (url: string, timeout: number = 100000) => {
    const start = Date.now();
    while (Date.now() - start < timeout) {
        try {
            const response = await axios.get(url);
            if (response.status === 200) {
                return;
            }
        } catch (error) {
            // If the server is not ready yet, continue waiting
        }
        await new Promise(resolve => setTimeout(resolve, 1000));
    }
    throw new Error('Server did not start within the timeout period');
};

const startFrontendServer = () => {
    return new Promise((resolve, reject) => {
        const frontendServerPath = path.join(__dirname, '../../json-ui');
        const serverProcess = spawn('pnpm', ['run', 'dev', '--filter', 'json-ui'], {
            cwd: frontendServerPath,
            shell: true
        });

        serverProcess.stdout.on('data', (data) => {
            console.log(`Frontend server stdout: ${data}`);
            if (data.includes('ready in')) {
                resolve(serverProcess);
            }
        });

        serverProcess.on('close', (code) => {
            console.log(`Frontend server process exited with code ${code}`);
        });

        serverProcess.on('error', (err) => {
            reject(`Failed to start the frontend server: ${err}`);
        });
    });
};

tap.teardown(() => {
    fastify.close();
    if (os.platform() === 'win32') {
        if (serverProcess) {
            console.log('Killing the frontend server process...');
            spawnSync("taskkill", ["/pid", serverProcess.pid, '/f', '/t']);
        }
    } else {
        if (serverProcess) {
            console.log('Killing the frontend server process ...');
            serverProcess.kill();
        }
    }
    process.exit(0);
});
tap.before(async () => {
    console.log('Starting frontend server...');
    serverProcess = await startFrontendServer();
    console.log('Frontend server is ready...');

    console.log('Waiting for the backend server to be ready...');
    await waitForServer('http://localhost:3000/');
    console.log('Backend server is ready...');

    console.log('Starting getPCCAccessToken...');
    await getPCCAccessToken();
    console.log('Completed getPCCAccessToken...');
});

tap.test("requests the health check up", async (t) => {
    const response = await fastify.inject({
        method: "GET",
        url: `/`,
        headers: ({
            'Authorization': `Bearer ${bearerToken}`,
            'x-access-token': activeLogin.accessToken as string
        }),
    });

    t.equal(response.statusCode, 200);
    t.equal(response.body, 'Node Services');
});

tap.test("get all PCC patient list with GET `/api/pcc/patients/`, should return success", async (t) => {
    const response = await fastify.inject({
        method: "GET",
        url: `/api/pcc/patients/?_sort=name&_order=asc&_start=0&_end=25&patientStatus=current`,
        headers: ({
            'Content-Type': 'application/json',
            'x-organization-id': xOrganizationId,
            'Authorization': `Bearer ${bearerToken}`,
            'x-access-token': activeLogin.accessToken,
            'x-id-token': activeLogin.idToken,
            'origin': 'localhost'
        }),
    });

    t.equal(response.statusCode, 200);
    const responseBody = JSON.parse(response.body);
    if (responseBody[0] && responseBody[0].id) {
        pccPatientId = responseBody[0].id;
        pccPatientName = responseBody[0].name;
    }
    t.equal(responseBody[0].resourceType, 'Patient');
    t.equal(responseBody[0].patientStatus, 'Current');
    t.ok(Array.from(responseBody).length >= 1);
});

tap.test("get all PCC patient list whose patient status is new with GET `/api/pcc/patients/`, should return success", async (t) => {
    const response = await fastify.inject({
        method: "GET",
        url: `/api/pcc/patients/?_sort=name&_order=asc&_start=0&_end=25&patientStatus=new`,
        headers: ({
            'Content-Type': 'application/json',
            'x-organization-id': xOrganizationId,
            'Authorization': `Bearer ${bearerToken}`,
            'x-access-token': activeLogin.accessToken,
            'x-id-token': activeLogin.idToken,
            'origin': 'localhost'
        }),
    });

    t.equal(response.statusCode, 200);
    const responseBody = JSON.parse(response.body);
    if (responseBody[0] && responseBody[0].id) {
        t.equal(responseBody[0].resourceType, 'Patient');
        t.equal(responseBody[0].patientStatus, 'New');
        t.equal(responseBody[0].active, true);
        t.ok(Array.from(responseBody).length >= 1);
    }
});

tap.test("get all PCC patient list whose patient status is discharged with GET `/api/pcc/patients/`, should return success", async (t) => {
    const response = await fastify.inject({
        method: "GET",
        url: `/api/pcc/patients/?_sort=name&_order=asc&_start=0&_end=25&patientStatus=discharged`,
        headers: ({
            'Content-Type': 'application/json',
            'x-organization-id': xOrganizationId,
            'Authorization': `Bearer ${bearerToken}`,
            'x-access-token': activeLogin.accessToken,
            'x-id-token': activeLogin.idToken,
            'origin': 'localhost'
        }),
    });

    t.equal(response.statusCode, 200);
    const responseBody = JSON.parse(response.body);
    if (responseBody[0] && responseBody[0].id) {
        t.equal(responseBody[0].resourceType, 'Patient');
        t.equal(responseBody[0].patientStatus, 'discharged');
        t.ok(Array.from(responseBody).length >= 1);
    }
});

tap.test("get a PCC patient details with GET `/api/pcc/patients/`, should return success", async (t) => {
    const firstName = pccPatientName.split(' ')[0];
    const response = await fastify.inject({
        method: "GET",
        url: `/api/pcc/patients/?_sort=name&_order=asc&_start=0&_end=25&patientStatus=current&_search=${firstName}`,
        headers: ({
            'Content-Type': 'application/json',
            'x-organization-id': xOrganizationId,
            'Authorization': `Bearer ${bearerToken}`,
            'x-access-token': activeLogin.accessToken,
            'x-id-token': activeLogin.idToken,
            'origin': 'localhost'
        }),
    });

    t.equal(response.statusCode, 200);
    const responseBody = JSON.parse(response.body);
    t.equal(responseBody[0].resourceType, 'Patient');
    t.equal(responseBody[0].active, true);
    t.equal(responseBody[0].name, pccPatientName);
});

tap.test("get a PCC patient details with incorrect patient name with GET `/api/pcc/patients/`, should return failure", async (t) => {
    const response = await fastify.inject({
        method: "GET",
        url: `/api/pcc/patients/?_sort=name&_order=asc&_start=0&_end=25&patientStatus=current&_search=asdfghh`,
        headers: ({
            'Content-Type': 'application/json',
            'x-organization-id': xOrganizationId,
            'Authorization': `Bearer ${bearerToken}`,
            'x-access-token': activeLogin.accessToken,
            'x-id-token': activeLogin.idToken,
            'origin': 'localhost'
        }),
    });

    t.equal(response.statusCode, 200);
    t.equal(response.statusMessage, 'OK');
    const responseBody = JSON.parse(response.body);
    t.ok(Array.isArray(responseBody), 'Response body should be an array');
    t.equal(responseBody.length, 0);
});

tap.test("get a specific PCC patient details with GET `/api/pcc/patients/{id}`, should return success", async (t) => {
    const response = await fastify.inject({
        method: "GET",
        url: `/api/pcc/patients/${pccPatientId}`,
        headers: ({
            'Content-Type': 'application/json',
            'x-organization-id': xOrganizationId,
            'Authorization': `Bearer ${bearerToken}`,
            'x-access-token': activeLogin.accessToken,
            'x-id-token': activeLogin.idToken,
            'origin': 'localhost'
        }),
    });

    t.equal(response.statusCode, 200);
    const responseBody = JSON.parse(response.body);
    t.equal(responseBody.id, pccPatientId);
    t.equal(responseBody.resourceType, 'Patient');
    t.equal(responseBody.active, true);
    t.ok(responseBody.name);
    t.ok(responseBody.mobilePhone);
    t.ok(responseBody.primaryLanguage);
});

tap.test("get PCC patient's condition with GET `/api/pcc/patients/{id}/conditions`, should return success", async (t) => {
    const response = await fastify.inject({
        method: "GET",
        url: `/api/pcc/patients/${pccPatientId}/conditions?_start=0&_end=25`,
        headers: ({
            'Content-Type': 'application/json',
            'x-organization-id': xOrganizationId,
            'Authorization': `Bearer ${bearerToken}`,
            'x-access-token': activeLogin.accessToken,
            'x-id-token': activeLogin.idToken,
            'origin': 'localhost'
        }),
    });

    t.equal(response.statusCode, 200);
    t.equal(response.statusMessage, 'OK');
    const responseBody = JSON.parse(response.body);
    t.ok(Array.isArray(responseBody), 'Response body should be an array');
    t.ok(responseBody.length >= 1, "At least one document should be returned");
    t.ok(responseBody[0].id, "First patient should have an id");
});

tap.test("get PCC patient details with incorrect x-organization-id with GET `/api/pcc/patients/{id}`, should return failure", async (t) => {
    const response = await fastify.inject({
        method: "GET",
        url: `/api/pcc/patients/${pccPatientId}`,
        headers: ({
            'Content-Type': 'application/json',
            'x-organization-id': xOrganizationIdIncorrect,
            'Authorization': `Bearer ${bearerToken}`,
            'x-access-token': activeLogin.accessToken,
            'x-id-token': activeLogin.idToken,
            'origin': 'localhost'
        }),
    });

    t.equal(response.statusCode, 401);
    t.equal(response.statusMessage, 'Unauthorized');
    const responseBody = JSON.parse(response.body);
    t.equal(responseBody.statusCode, 401);
    t.equal(responseBody.error, 'Unauthorized');
    t.equal(responseBody.message, `Your organization (id: ${xOrganizationIdIncorrect})is not registered. Please contact your administrator.`);
});

tap.test("get PCC patient details with incorrect patient id with GET `/api/pcc/patients/{id}`, should return failure", async (t) => {
    const response = await fastify.inject({
        method: "GET",
        url: `/api/pcc/patients/${pccPatientIdIncorrect}`,
        headers: ({
            'Content-Type': 'application/json',
            'x-organization-id': xOrganizationId,
            'Authorization': `Bearer ${bearerToken}`,
            'x-access-token': activeLogin.accessToken,
            'x-id-token': activeLogin.idToken,
            'origin': 'localhost'
        }),
    });

    t.equal(response.statusCode, 404);
    t.equal(response.statusMessage, 'Not Found');
    const responseBody = JSON.parse(response.body);
    t.equal(responseBody.statusCode, 404);
    t.equal(responseBody.error, 'Not Found');
    t.equal(responseBody.message, 'PointClickCare API error: Patient Not Found.');
});

tap.test("get PCC patient details without x-access-token with GET `/api/pcc/patients/{id}`, should return failure", async (t) => {
    const response = await fastify.inject({
        method: "GET",
        url: `/api/pcc/patients/${pccPatientId}`,
        headers: ({
            'Content-Type': 'application/json',
            'x-organization-id': xOrganizationId,
            'Authorization': `Bearer ${bearerToken}`,
            'x-id-token': activeLogin.idToken,
            'origin': 'localhost'
        }),
    });

    t.equal(response.statusCode, 401);
    t.equal(response.statusMessage, 'Unauthorized');
    const responseBody = JSON.parse(response.body);
    t.equal(responseBody.statusCode, 401);
    t.equal(responseBody.error, 'Unauthorized');
    t.equal(responseBody.message, "Request header 'x-access-token' is required.");
});

tap.test("get PCC patient details without x-id-token with GET `/api/pcc/patients/{id}`, should return failure", async (t) => {
    const response = await fastify.inject({
        method: "GET",
        url: `/api/pcc/patients/${pccPatientId}`,
        headers: ({
            'Content-Type': 'application/json',
            'x-organization-id': xOrganizationId,
            'Authorization': `Bearer ${bearerToken}`,
            'x-access-token': activeLogin.accessToken,
            'origin': 'localhost'
        }),
    });

    t.equal(response.statusCode, 401);
    const responseBody = JSON.parse(response.body);
    t.equal(responseBody.statusCode, 401);
    t.equal(responseBody.error, 'Unauthorized');
    t.equal(responseBody.message, 'FastifyError [FST_JWT_AUTHORIZATION_TOKEN_INVALID]: Authorization token is invalid: The token is malformed.');
});

tap.test("get PCC patient details without origin with GET `/api/pcc/patients/{id}`, should return failure", async (t) => {
    const response = await fastify.inject({
        method: "GET",
        url: `/api/pcc/patients/${pccPatientId}`,
        headers: ({
            'Content-Type': 'application/json',
            'x-organization-id': xOrganizationId,
            'Authorization': `Bearer ${bearerToken}`,
            'x-access-token': activeLogin.accessToken,
            'x-id-token': activeLogin.idToken,
        }),
    });

    t.equal(response.statusCode, 401);
    t.equal(response.statusMessage, 'Unauthorized');
    const responseBody = JSON.parse(response.body);
    t.equal(responseBody.statusCode, 401);
    t.equal(responseBody.error, 'Unauthorized');
    t.equal(responseBody.message, 'JsonWebTokenError: invalid signature');
});