import fastifyAutoload from '@fastify/autoload';
import fastifyJwt, { FastifyJWTOptions, SignPayloadType, TokenOrHeader } from '@fastify/jwt';
import { FastifyInstance, FastifyPluginAsync, FastifyPluginOptions, FastifyReply, FastifyRequest } from 'fastify';
import path from 'path';
import { IUser } from 'relic-ui';
import buildGetJwks from 'get-jwks';
import JwksClient from 'jwks-rsa';
import { IdentityServiceInterface } from '../services/identity/identity.interface';
import { EntraExternalService } from '../services/identity/identity.entra-external';
import { requestContext } from '@fastify/request-context';

const getJwks = buildGetJwks();
declare module 'fastify' {
  export interface FastifyInstance {
    identityService: IdentityServiceInterface;
  }
}

const entraExternalProvider: FastifyPluginAsync = async (fastify: FastifyInstance, opts: FastifyPluginOptions) => {
  const entraService = new EntraExternalService(fastify, opts);
  if (!fastify.hasDecorator('identityService')) {
    fastify.decorate('identityService', entraService as unknown as IdentityServiceInterface);
  }
  const entraExternalJwtConfig: FastifyJWTOptions = {
    namespace: 'entra-external',
    jwtVerify: 'jwtVerify',
    secret: async function (req: FastifyRequest, token: TokenOrHeader): Promise<string> {
      const {
        payload: { iss, tid },
        header: { kid },
      } = token;
      const policy = 'B2C_1A_SIGNIN_RELIC';
      const issUri = new URL(iss);
      const entraExternalIss = path.join(issUri.origin, tid, policy, issUri.pathname.includes('v2.0') ? 'v2.0/' : '/'); //Note: getJwksUri bug, requires trailing '/'
      const jwksUri = await getJwks.getJwksUri(entraExternalIss);
      //TODO: To be fixed. Cache values
      const jwkClient = JwksClient({
        jwksUri,
        cache: true,
        rateLimit: true,
      });
      const key = await jwkClient.getSigningKey(kid);
      const publicKey = key.getPublicKey();
      return publicKey;
    },
    sign: {
      algorithm: 'RS256',
    },
    decode: {
      complete: true,
    },
    verify: {
      complete: true,
      algorithms: ['RS256'],
      extractToken: function (req: FastifyRequest): string {
        return req.headers['x-access-token'] as string;
      },
    },
    // Refer https://github.com/fastify/fastify-jwt#formatuser for more details
    formatUser: async function (tokenPayload: SignPayloadType): Promise<IUser> {
      const relicAiUser = await fastify.identityService.whoAmI(tokenPayload);
      return relicAiUser;
    },
  };

  fastify.register(fastifyJwt, entraExternalJwtConfig);

  async function verifyJwt(req: FastifyRequest, rep: FastifyReply) {
    requestContext.set('accessToken', req.headers['x-access-token'] as string);
    const relicAiUser = (await req.jwtVerify()) as IUser;
    requestContext.set('user', relicAiUser);
    if (relicAiUser.userIdentity) {
      requestContext.set('whoami', relicAiUser.userIdentity);
    }
    if (relicAiUser.organizationId) {
      requestContext.set('organizationId', relicAiUser.organizationId);
      await fastify.setMyOrganization(relicAiUser.organizationId);
    }
    if (relicAiUser.decodedJwtToken) {
      requestContext.set('decodedJwtToken', relicAiUser.decodedJwtToken); //TODO: Legacy needs these values available in requestcontext. To be fixed by using req.user value
    }
  }

  async function verifyAccess(req: FastifyRequest, rep: FastifyReply) {
    return true; // Placeholder for access verification logic. TBD: To be fixed later.
  }

  fastify.addHook('onRequest', fastify.auth([verifyJwt, verifyAccess], { relation: 'and' }));

  fastify.decorate('createExternalEntraPractitioner');

  fastify.log.info('Entra-External Routes loading...');

  //Publishing entra-external Routes
  await fastify.register(fastifyAutoload, {
    dir: path.join(__dirname, './api'),
    options: {
      ...opts,
      prefix: '/api/entra-external',
    },
    autoHooks: true,
    cascadeHooks: true,
    routeParams: true,
  });
  await fastify.register(fastifyAutoload, {
    dir: path.join(__dirname, './entraExternal'),
    options: {
      ...opts,
      prefix: '/api/entra-external',
    },
    autoHooks: true,
    cascadeHooks: true,
    routeParams: true,
  });
  fastify.log.info('Entra-External Routes loaded successfully.');
};

export default entraExternalProvider;
