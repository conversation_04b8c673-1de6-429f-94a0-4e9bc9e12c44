import {
  LoginPageProps,
  useActiveAuthProvider,
  useLink,
  useLogin,
  useRouterContext,
  useRouterType,
  useTranslate,
  type BaseRecord,
  type HttpError,
} from '@refinedev/core';
import { ThemedTitleV2 } from '@refinedev/mui';
import { useForm } from '@refinedev/react-hook-form';
import * as React from 'react';
import { Controller, FormProvider } from 'react-hook-form';

import LoadingButton from '@mui/lab/LoadingButton';
import { FormControl, FormHelperText, TextField } from '@mui/material';
import Box, { BoxProps } from '@mui/material/Box';
import Button from '@mui/material/Button';
import Card from '@mui/material/Card';
import CardContent, { CardContentProps } from '@mui/material/CardContent';
import Checkbox from '@mui/material/Checkbox';
import Container from '@mui/material/Container';
import Divider from '@mui/material/Divider';
import FormControlLabel from '@mui/material/FormControlLabel';
import MuiLink from '@mui/material/Link';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';

import { FormPropsType, LoginFormTypes } from 'src/types';
import { layoutStyles, titleStyles } from './styles';

type B2CLoginProps = LoginPageProps<BoxProps, CardContentProps, FormPropsType>;
/**
 * login will be used as the default type of the <AuthPage> component. The login page will be used to log in to the system.
 * @see {@link https://refine.dev/docs/api-reference/mui/components/mui-auth-page/#login} for more details.
 */
export const B2CLoginPage: React.FC<B2CLoginProps> = ({
  providers,
  registerLink,
  forgotPasswordLink,
  rememberMe,
  contentProps,
  wrapperProps,
  renderContent,
  formProps,
  title,
  hideForm,
}) => {
  const { onSubmit, ...useFormProps } = formProps || {};
  const methods = useForm<BaseRecord, HttpError, LoginFormTypes>({
    ...useFormProps,
  });

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = methods;

  const values = watch();

  const authProvider = useActiveAuthProvider();
  const { mutate: login, isLoading } = useLogin<LoginFormTypes>({
    v3LegacyAuthProviderCompatible: Boolean(authProvider?.isLegacy),
  });
  const translate = useTranslate();
  const routerType = useRouterType();
  const Link = useLink();
  const { Link: LegacyLink } = useRouterContext();

  const ActiveLink = routerType === 'legacy' ? LegacyLink : Link;

  const PageTitle =
    title === false ? null : (
      <div
        style={{
          display: 'flex',
          justifyContent: 'center',
          marginBottom: '32px',
          fontSize: '20px',
        }}
      >
        {title ?? (
          <ThemedTitleV2
            collapsed={false}
            wrapperStyles={{
              gap: '8px',
            }}
          />
        )}
      </div>
    );

  const renderProviders = () => {
    if (providers && providers.length > 0) {
      return (
        <>
          <Stack spacing={1}>
            {providers.map((provider: any) => {
              return (
                <Button
                  key={provider.name}
                  variant="outlined"
                  fullWidth
                  sx={{
                    color: 'primary.light',
                    borderColor: 'primary.light',
                    textTransform: 'none',
                  }}
                  onClick={() => login({ providerName: provider.name })}
                  startIcon={provider.icon}
                >
                  {provider.label}
                </Button>
              );
            })}
          </Stack>
          {!hideForm && (
            <Divider
              sx={{
                fontSize: '12px',
                marginY: '16px',
              }}
            >
              {translate('pages.login.divider', 'or')}
            </Divider>
          )}
        </>
      );
    }
    return null;
  };

  const Content = (
    <Card {...(contentProps ?? {})}>
      <CardContent sx={{ p: '32px', '&:last-child': { pb: '32px' } }}>
        <Typography
          component="h1"
          variant="h5"
          align="center"
          style={titleStyles}
          color="primary"
          fontWeight={700}
        >
          {translate('pages.login.b2ctitle', 'Sign in to your account')}
        </Typography>
        {renderProviders()}
        {!hideForm && (
          <Box
            component="form"
            onSubmit={handleSubmit(data => {
              if (onSubmit) {
                return onSubmit(data);
              }

              return login(data);
            })}
          >
            <FormControl fullWidth>
              <Controller
                name="id"
                defaultValue={values.id || ''}
                rules={{
                  required: 'This field is required',
                  validate: (value?: string) => {
                    if (!value) {
                      return 'This field is required';
                    }
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    const phoneRegex = /^\+\d{1,2}\d{10}$/;
                    if (emailRegex.test(value)) {
                      return true;
                    } else if (phoneRegex.test(value)) {
                      return true;
                    } else {
                      return 'Invalid email or phone number. Please ensure the phone number includes the country code.';
                    }
                  },
                }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    sx={{
                      minWidth: '350px',
                    }}
                    label="Email Id or Phone Number"
                    placeholder="Enter your email or phone number"
                    error={!!errors.id}
                  />
                )}
              />
            </FormControl>
            {errors?.id && <FormHelperText>{errors.id.message}</FormHelperText>}
            <Box
              component="div"
              sx={{
                mt: '24px',
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}
            >
              {rememberMe ?? (
                <FormControlLabel
                  sx={{
                    span: {
                      fontSize: '14px',
                      color: 'text.secondary',
                    },
                  }}
                  color="secondary"
                  control={
                    <Checkbox
                      size="small"
                      id="remember"
                      {...register('remember')}
                    />
                  }
                  label={translate(
                    'pages.login.buttons.rememberMe',
                    'Remember me',
                  )}
                />
              )}
              {forgotPasswordLink ?? (
                <MuiLink
                  variant="body2"
                  color="primary"
                  fontSize="12px"
                  component={ActiveLink}
                  underline="none"
                  to="/forgot-password"
                >
                  {translate(
                    'pages.login.buttons.forgotPassword',
                    'Forgot password?',
                  )}
                </MuiLink>
              )}
            </Box>
            <LoadingButton
              type="submit"
              fullWidth
              variant="contained"
              loading={isLoading || formProps?.disabled}
              disabled={formProps?.disabled || isLoading}
              sx={{ mt: '24px' }}
            >
              {translate('pages.login.continue', 'Continue')}
            </LoadingButton>
          </Box>
        )}
        {registerLink ?? (
          <Box
            sx={{
              mt: '24px',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            <Typography
              textAlign="center"
              variant="body2"
              component="span"
              fontSize="12px"
            >
              {translate(
                'pages.login.buttons.noAccount',
                'Don’t have an account?',
              )}
            </Typography>
            <MuiLink
              ml="4px"
              fontSize="12px"
              variant="body2"
              color="primary"
              component={ActiveLink}
              underline="none"
              to="/register"
              fontWeight="bold"
            >
              {translate('pages.login.signup', 'Sign up')}
            </MuiLink>
          </Box>
        )}
      </CardContent>
    </Card>
  );

  return (
    <FormProvider {...methods}>
      <Box component="div" style={layoutStyles} {...(wrapperProps ?? {})}>
        <Container
          component="main"
          sx={{
            display: 'flex',
            flexDirection: 'column',
            justifyContent: hideForm ? 'flex-start' : 'center',
            alignItems: 'center',
            minHeight: '100dvh',
            padding: '16px',
            width: '100%',
            maxWidth: '400px',
          }}
        >
          <Box
            sx={{
              width: '100%',
              maxWidth: '400px',
              display: 'flex',
              flexDirection: 'column',
              paddingTop: hideForm ? '15dvh' : 0,
            }}
          >
            {renderContent ? (
              renderContent(Content, PageTitle)
            ) : (
              <>
                {PageTitle}
                {Content}
              </>
            )}
          </Box>
        </Container>
      </Box>
    </FormProvider>
  );
};
