import React from 'react';
import { useDataGrid } from '@refinedev/mui';

import { GridColDef } from '@mui/x-data-grid-pro';

import { ListLocations } from 'src/components/_location';

export const LocationList = () => {
  const { dataGridProps, setFilters } = useDataGrid({
    sorters: {
      initial: [
        {
          field: 'name',
          order: 'asc',
        },
      ],
    },
  });
  
  const locationColumns = React.useMemo<GridColDef[]>(() => [], []);
  return (
    <ListLocations {...dataGridProps} columns={locationColumns} setFilters={setFilters} />
  );
};
