/* eslint-env node */

module.exports = {
  env: {
    browser: true,
    es2020: true,
  },
  extends: [
    'eslint:recommended',
    'plugin:@typescript-eslint/recommended',
    'plugin:react/recommended',
    'plugin:react-hooks/recommended',
    'plugin:jsx-a11y/recommended',
    'plugin:prettier/recommended',
  ],
  parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaVersion: 'latest',
    sourceType: 'module',
  },
  plugins: ['react-refresh', 'perfectionist', 'react', 'jsx-a11y', 'prettier'],
  rules: {
    'react-refresh/only-export-components': 'warn',
    'react/display-name': 'off', // Disable react/display-name
    'perfectionist/sort-imports': [
      'warn',
      {
        order: 'asc',
        type: 'line-length',
        'newlines-between': 'always',
        groups: [
          ['builtin', 'external'],
          // Custom Groups
          'custom-mui', // Material UI imports
          'custom-routes', // Routes
          'custom-hooks', // Hooks
          'custom-utils', // Utilities
          'internal', // Internal imports
          'custom-components', // Components
          'custom-sections', // Sections
          'custom-types', // Types
          ['parent', 'sibling', 'index'],
          'object',
          'unknown',
        ],
        'custom-groups': {
          value: {
            'custom-mui': '@mui/**',
            'custom-routes': 'src/routes/**',
            'custom-hooks': 'src/hooks/**',
            'custom-utils': 'src/utils/**',
            'custom-components': 'src/components/**',
            'custom-sections': 'src/sections/**',
            'custom-types': 'src/types/**',
          },
        },
        'internal-pattern': ['src/**'],
      },
    ],
    'arrow-body-style': 'off', // Disable arrow-body-style
    '@typescript-eslint/no-explicit-any': 'off', // Turn off no-explicit-any
    '@typescript-eslint/explicit-module-boundary-types': 'off', // Turn off explicit-module-boundary-types
    '@typescript-eslint/no-unused-vars': 'off', // Turn off no-unused-vars
    '@typescript-eslint/no-non-null-assertion': 'off', // Turn off no-non-null-assertion
    'react/react-in-jsx-scope': 'off', // Turn off react-in-jsx-scope
    'react-hooks/exhaustive-deps': 'warn', // Keep as warning for exhaustive-deps
    'react/no-unescaped-entities': 'off', // Disable no-unescaped-entities
    'prettier/prettier': 'error', // Add Prettier as an error level
    'react/prop-types': 'off', // Disable prop-types as we are using TypeScript
    'jsx-a11y/no-autofocus': 'warn', // Warn on autoFocus usage to improve accessibility

    // Perfectionist
    // https://eslint-plugin-perfectionist.azat.io/
    'perfectionist/sort-named-imports': [
      'warn',
      {
        order: 'asc',
        type: 'line-length',
      },
    ],
    'perfectionist/sort-named-exports': [
      'warn',
      {
        order: 'asc',
        type: 'line-length',
      },
    ],
    'perfectionist/sort-exports': [
      'warn',
      {
        order: 'asc',
        type: 'line-length',
      },
    ],
  },
  settings: {
    react: {
      version: 'detect', // Automatically detect the React version
    },
  },
};
