import * as React from 'react';
import { IUser, IUserIdentity } from 'relic-ui';
import { useForm } from '@refinedev/react-hook-form';
import {
  useCreate,
  useTranslate,
  useInvalidate,
  useGetIdentity,
  IResourceComponentsProps,
} from '@refinedev/core';

import Stack from '@mui/material/Stack';
import Dialog from '@mui/material/Dialog';
import Switch from '@mui/material/Switch';
import TextField from '@mui/material/TextField';
import FormHelperText from '@mui/material/FormHelperText';
import FormControlLabel from '@mui/material/FormControlLabel';

import { useRouter } from 'src/routes/hooks';

import PhoneInput from 'src/components/phone-input';
import { Create } from 'src/components/refine-customs/create';
import { OrganizationDropdown } from 'src/components/dropdown/organizationDropdown';
import { LoginProviderDropdown } from 'src/components/dropdown/loginProviderDropdown';

// ----------------------------------------------------------------------

export const StaffCreate: React.FC<IResourceComponentsProps> = () => {
  const router = useRouter();
  const translate = useTranslate();
  const invalidate = useInvalidate();
  const [open, setOpen] = React.useState(true);

  const handleClose = () => {
    setOpen(false);
    router.push('/staff');
  };

  const { data: currentUser } = useGetIdentity<IUser>();
  const currentIdentity: IUserIdentity =
    currentUser?.userIdentity as IUserIdentity;

  // Initialize useCreate hook
  const { mutate: createPractitioner, isLoading: isCreating } = useCreate();

  const {
    saveButtonProps,
    refineCore: { formLoading, onFinish },
    register,
    control,
    formState: { errors, isSubmitting },
    handleSubmit,
    watch,
  } = useForm();

  const values = watch();

  const onSubmit = async (values: any) => {
    // Ensure the form data is correctly structured
    const practitionerData = {
      givenName: values.givenName,
      surname: values.surname,
      name: `${values.givenName} ${values.surname}`,
      email: values.email,
      mobilePhone: values.mobilePhone,
      organizationId: values.organizationId,
      sendInvite: values.sendInvite,
      enabled: values.enabled,
      displayName: `${values.givenName} ${values.surname}`,
      provider: values.provider || 'msgraph',
    };

    // Use the create method for the "practitioner" resource
    createPractitioner(
      {
        resource: 'practitioners',
        values: practitionerData,
      },
      {
        onSuccess: () => {
          //Refresh the staff list before the form is closed.
          invalidate({
            resource: 'group',
            invalidates: ['list', 'many'],
          });
          //Refresh the staff list before the form is closed.
          invalidate({
            resource: 'staff',
            invalidates: ['list', 'many'],
          });
          handleClose();
        },
      },
    );
  };

  return (
    <Dialog
      fullWidth
      maxWidth="sm"
      open={open}
      onClose={handleClose}
      aria-labelledby="alert-dialog-title"
      aria-describedby="alert-dialog-description"
    >
      <Create
        isLoading={formLoading}
        title={translate('staff.titles.create')}
        saveButtonProps={{
          onClick: handleSubmit(onSubmit),
          disabled: isSubmitting,
        }}
        onClose={handleClose}
      >
        <Stack spacing={2.5} component="form" autoComplete="off">
          <TextField
            {...register('givenName', {
              required: 'This field is required',
            })}
            error={!!errors?.givenName}
            helperText={(errors as any)?.givenName?.message}
            fullWidth
            InputLabelProps={{ shrink: true }}
            type="text"
            label={translate('staff.fields.givenName')}
            name="givenName"
          />

          <TextField
            {...register('surname', {
              required: 'This field is required',
            })}
            error={!!errors?.surname}
            helperText={(errors as any)?.surname?.message}
            fullWidth
            InputLabelProps={{ shrink: true }}
            type="text"
            label={translate('staff.fields.surname')}
            name="surname"
          />

          <TextField
            {...register('email', {
              required: 'This field is required',
              pattern: {
                value: /\S+@\S+\.\S+/,
                message: 'Entered value does not match email format',
              },
            })}
            error={!!errors?.email}
            helperText={(errors as any)?.email?.message}
            fullWidth
            InputLabelProps={{ shrink: true }}
            type="email"
            label={translate('staff.fields.email')}
            name="email"
          />

          <PhoneInput
            control={control}
            name="mobilePhone"
            label={translate('staff.fields.mobilePhone')}
          />

          <OrganizationDropdown
            control={control}
            name="organizationId"
            label={translate('staff.fields.organizationId')}
            error={!!(errors as any)?.organizationId}
            helperText={(errors as any)?.organizationId?.message}
            value={currentIdentity?.portalIdentity?.organizationId as string}
            disabled={currentIdentity.role.name === 'member'}
          />

          <LoginProviderDropdown
            control={control}
            name="provider"
            label={translate('staff.fields.provider')}
            error={!!(errors as any)?.provider}
            helperText={(errors as any)?.provider?.message}
            value="msgraph"
            disabled={false}
          />

          <div>
            <FormControlLabel
              label={translate('staff.fields.status')}
              control={
                <Switch
                  {...register('enabled')}
                  name="enabled"
                  checked={!!values?.enabled}
                />
              }
            />
            {!!errors.enabled && (
              <FormHelperText error sx={{ px: 2 }}>
                {(errors as any)?.enabled?.message}
              </FormHelperText>
            )}
          </div>

          <div>
            <FormControlLabel
              control={
                <Switch
                  {...register('sendInvite')}
                  checked={values.sendInvite}
                  name="sendInvite"
                />
              }
              label={translate('staff.fields.sendInvite')}
            />
            {!!errors.sendInvite && (
              <FormHelperText error sx={{ px: 2 }}>
                {(errors as any)?.sendInvite?.message}
              </FormHelperText>
            )}
          </div>
        </Stack>
      </Create>
    </Dialog>
  );
};
