import * as React from 'react';
import {
  RefreshButton,
  Show as RefineShow,
  ShowProps as RefineShowProps,
} from '@refinedev/mui';
import { EditButton } from 'src/components/refine-customs/edit-btn';

import Button from '@mui/material/Button';

// ----------------------------------------------------------------------

type ShowProps = RefineShowProps & {
  onClose: VoidFunction;
};

export const Show: React.FC<ShowProps> = ({ children, onClose, ...other }) => (
  <RefineShow
    breadcrumb={null}
    goBack={null}
    wrapperProps={{
      sx: {
        borderRadius: 0,
        display: 'flex',
        boxShadow: 'none',
        flexDirection: 'column',
      },
    }}
    contentProps={{
      sx: {
        flex: '1 1 auto',
        overflowY: 'auto',
      },
    }}
    headerProps={{
      sx: {
        pb: 3,
        borderBottom: (theme) => `solid 1px ${theme.palette.divider}`,
        '& .refine-pageHeader-title': {
          typography: 'h6',
        },
      },
    }}
    headerButtonProps={{
      sx: {
        '& a': {
          color: 'inherit',
        },
      },
    }}
    headerButtons={({ editButtonProps, refreshButtonProps }) => (
      <>
        {editButtonProps && <EditButton {...editButtonProps} variant="contained" />}
        <RefreshButton {...refreshButtonProps} variant="outlined" />
      </>
    )}
    footerButtons={() => (
      <>
        {onClose && (
          <Button variant="outlined" onClick={onClose}>
            Close
          </Button>
        )}
      </>
    )}
    footerButtonProps={{
      sx: {
        p: 2,
        justifyContent: 'flex-end',
        borderTop: (theme) => `solid 1px ${theme.palette.divider}`,
      },
    }}
    {...other}
  >
    {children}
  </RefineShow>
);
