# syntax = docker/dockerfile:1

# Refer https://docs.apify.com/sdk/js/docs/guides/docker-images for Apify Docker Reference

# Specify the base Docker image. You can read more about
# the available images at https://crawlee.dev/docs/guides/docker-images
# You can also use any other image from Docker Hub.
FROM apify/actor-node-playwright-chrome:20 AS builder
ENV NODE_ENV=dev
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
USER root
# Install pnpm & change pnpm home folder owner to myuser
RUN npm install -g pnpm
RUN mkdir /pnpm
RUN chown -R myuser:myuser /pnpm
WORKDIR /home/<USER>

# Change user to the one set in the base image.
USER myuser

# Copy just package.json and package-lock.json
# to speed up the build using Docker layer cache.
COPY --chown=myuser ./.npmrc ./.npmrc
COPY --chown=myuser ./package.json ./package.json
COPY --chown=myuser ./pnpm-lock.yaml ./pnpm-lock.yaml
COPY --chown=myuser ./pnpm-workspace.yaml ./pnpm-workspace.yaml
COPY --chown=myuser ./turbo.json ./turbo.json
COPY --chown=myuser ./relic-ui/package.json ./relic-ui/package.json
COPY --chown=myuser ./relic-ui/tsconfig.json ./relic-ui/tsconfig.json
COPY --chown=myuser ./web-crawler/package.json ./web-crawler/package.json
COPY --chown=myuser ./web-crawler/tsconfig.json ./web-crawler/tsconfig.json

# Install all dependencies. Don't audit to speed up the installation.
RUN cd ./relic-ui
RUN ["pnpm", "install"]

# Next, copy the source files using the user set
# in the base image.
COPY --chown=myuser ./relic-ui/ ./relic-ui/
COPY --chown=myuser ./web-crawler/ ./web-crawler/

# Install all dependencies and build the project.
# Don't audit to speed up the installation.
RUN ["pnpm", "run", "build"]

# Create final image
FROM apify/actor-node-playwright-chrome:20
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
ENV NODE_ENV=dev

USER root
# Install pnpm & change pnpm home folder owner to myuser
RUN npm install -g pnpm
RUN mkdir /pnpm
RUN chown -R myuser:myuser /pnpm
WORKDIR /home/<USER>

# [Attempt to resolve Apify build  error] EACCES  Error while trying to symlink
RUN mkdir -p /home/<USER>/relic-ui/node_modules
RUN chown -R myuser:myuser /home/<USER>/relic-ui/node_modules
RUN mkdir -p /home/<USER>/web-crawler/node_modules
RUN chown -R myuser:myuser /home/<USER>/web-crawler/node_modules
# Change user to the one set in the base image.
USER myuser

# Copy only built JS files from builder image
COPY --from=builder --chown=myuser /home/<USER>/relic-ui/dist/ /home/<USER>/relic-ui/dist/
COPY --from=builder --chown=myuser /home/<USER>/web-crawler/dist/ /home/<USER>/web-crawler/dist/

# Copy just package.json and package-lock.json
# to speed up the build using Docker layer cache.
COPY --chown=myuser ./.npmrc ./.npmrc
COPY --chown=myuser ./package.json ./package.json
COPY --chown=myuser ./pnpm-lock.yaml ./pnpm-lock.yaml
COPY --chown=myuser ./pnpm-workspace.yaml ./pnpm-workspace.yaml
COPY --chown=myuser ./turbo.json ./turbo.json
COPY --chown=myuser ./relic-ui/package.json ./relic-ui/package.json
COPY --chown=myuser ./relic-ui/tsconfig.json ./relic-ui/tsconfig.json
COPY --chown=myuser ./web-crawler/package.json ./web-crawler/package.json
COPY --chown=myuser ./web-crawler/tsconfig.json ./web-crawler/tsconfig.json

# Install PNPM packages, skip optional and development dependencies to
# keep the image small. Avoid logging too much and print the dependency
# tree for debugging
RUN cd ./relic-ui
RUN ["pnpm", "install"]
RUN echo "Installed PNPM packages:"
RUN (pnpm list --omit=dev --all || true)
RUN echo "Node.js version:"
RUN node --version
RUN echo "PNPM version:" \
RUN pnpm --version

# Change workdirectory to the web-crawler directory
WORKDIR /home/<USER>/web-crawler/

# Next, copy the remaining files and directories with the source code.
# Since we do this after NPM install, quick build will be really fast
# for most source file changes.
COPY --chown=myuser ./web-crawler/ ./


# Run the image. If you know you won't need headful browsers,
# you can remove the XVFB start script for a micro perf gain.
CMD /home/<USER>/start_xvfb_and_run_cmd.sh && pnpm run start