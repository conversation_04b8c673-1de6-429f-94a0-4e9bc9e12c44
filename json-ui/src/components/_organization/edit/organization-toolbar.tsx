import { useTranslate } from '@refinedev/core';

import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Divider from '@mui/material/Divider';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';

import Iconify from 'src/components/iconify';

type OrganizationEditToolbarProps = {
  organizationName: string;
  onBack: VoidFunction;
  showOrganization: boolean;
  onToggleOrganization: VoidFunction;
  onOpenAdd: (event: React.MouseEvent<HTMLElement, MouseEvent>) => void;
};

export default function OrganizationToolbar({
  onBack,
  onOpenAdd,
  organizationName,
  showOrganization,
  onToggleOrganization,
}: OrganizationEditToolbarProps) {
  const translate = useTranslate();

  return (
    <Stack
      direction={'row'}
      alignItems={'center'}
      sx={{
        pb: 3,
        position: 'relative',
        pt: { xs: 2, lg: 0 },
      }}
    >
      <IconButton onClick={onBack}>
        <Iconify icon="eva:arrow-ios-back-fill" />
      </IconButton>

      <Typography variant="h5" sx={{ flexGrow: 1 }}>
        {organizationName}
      </Typography>

      <Button
        variant="contained"
        startIcon={<Iconify icon={'eva:plus-fill'} />}
        onClick={onOpenAdd}
      >
        {translate('buttons.create')}
      </Button>

      <IconButton
        onClick={onToggleOrganization}
        color={showOrganization ? 'inherit' : 'default'}
        sx={{ ml: 1 }}
      >
        <Iconify
          icon={
            !showOrganization
              ? 'tabler:layout-sidebar-right-collapse'
              : 'tabler:layout-sidebar-right-collapse-filled'
          }
        />
      </IconButton>
      <Divider
        sx={{
          bottom: 0,
          position: 'absolute',
          left: { xs: -8, md: -16, lg: -24 },
          right: { xs: -8, md: -16, lg: -24 },
        }}
      />
    </Stack>
  );
}
