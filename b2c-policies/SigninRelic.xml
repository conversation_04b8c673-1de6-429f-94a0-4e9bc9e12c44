<?xml version="1.0" encoding="utf-8" ?>
<TrustFrameworkPolicy xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns:xsd="http://www.w3.org/2001/XMLSchema"
  xmlns="http://schemas.microsoft.com/online/cpim/schemas/2013/06" PolicySchemaVersion="0.3.0.0" TenantId="patientauth.onmicrosoft.com" PolicyId="B2C_1A_Signin_Relic" PublicPolicyUri="http://patientauth.onmicrosoft.com/B2C_1A_Signin_Relic" DeploymentMode="Development" UserJourneyRecorderEndpoint="urn:journeyrecorder:applicationinsights">
  <!-- for AppInsights, copy these to above DeploymentMode="Development" UserJourneyRecorderEndpoint="urn:journeyrecorder:applicationinsights" 
  -->

  <BasePolicy>
    <TenantId>patientauth.onmicrosoft.com</TenantId>
    <PolicyId>B2C_1A_TrustFrameworkExtensionsRelic</PolicyId>
  </BasePolicy>

  <BuildingBlocks>
    <ClaimsSchema>

      <!-- Custom Relic phone field for Email Login. -->
      <ClaimType Id="relicPhone">
        <DisplayName>Phone Number</DisplayName>
        <DataType>string</DataType>
        <UserHelpText>Phone Number - No hyphens or spaces. Starts with +1</UserHelpText>
        <UserInputType>TextBox</UserInputType>
        <Restriction>
          <Pattern RegularExpression="^\+\d{1}\d{10}$" HelpText="" />
        </Restriction>
      </ClaimType>

      <!-- Custom Relic Email field for Email Login. -->
      <ClaimType Id="relicEmail">
        <DisplayName>Email Address</DisplayName>
        <DataType>string</DataType>
        <UserHelpText>Email address - <EMAIL> format</UserHelpText>
        <UserInputType>EmailBox</UserInputType>
        <Restriction>
          <Pattern RegularExpression="^[a-zA-Z0-9!#$%&amp;'+^_`{}~-]+(?:\.[a-zA-Z0-9!#$%&amp;'+^_`{}~-]+)*@(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]*[a-zA-Z0-9])?\.)+[a-zA-Z0-9](?:[a-zA-Z0-9-]*[a-zA-Z0-9])?$" HelpText="Please enter a valid email address." />
        </Restriction>
      </ClaimType>

      <!-- Signin Email field for Directory Lookup. -->
      <ClaimType Id="signInNames.emailAddress">
        <DisplayName>Email Address</DisplayName>
        <DataType>string</DataType>
        <UserHelpText>Enter Email Address</UserHelpText>
      </ClaimType>

      <!-- ReadOnly Email field for OTP Screen. Display masked email. -->
      <ClaimType Id="readOnlyEmail">
        <DisplayName />
        <DataType>string</DataType>
        <Mask Type="Regex" Regex="(?&lt;=.).(?=.*@)">*</Mask>        
        <UserHelpText />
        <UserInputType>Readonly</UserInputType>
      </ClaimType>

      <!-- Custom Signin Phone Number field for Phone Login. Data Type - phoneNumber -->
      <ClaimType Id="signInNames.phoneNumber">
        <DisplayName>Phone Login</DisplayName>
        <DataType>phoneNumber</DataType>
        <UserHelpText>Enter Phone Number</UserHelpText>
      </ClaimType>
    </ClaimsSchema>

    <ClaimsTransformations>
      <!-- Transformation for copying relic email to sign in name -->
      <ClaimsTransformation Id="RelicEmailToSignInNamesCopy" TransformationMethod="FormatStringClaim">
        <InputClaims>
          <InputClaim ClaimTypeReferenceId="relicEmail" TransformationClaimType="inputClaim" />
        </InputClaims>
        <InputParameters>
          <InputParameter Id="stringFormat" DataType="string" Value="{0}" />
        </InputParameters>
        <OutputClaims>
          <OutputClaim ClaimTypeReferenceId="signInNames.emailAddress" TransformationClaimType="outputClaim" />
        </OutputClaims>
      </ClaimsTransformation>

      <!-- Transformation for copying relic email to read only email field -->
      <ClaimsTransformation Id="RelicEmailToReadOnlyCopy" TransformationMethod="FormatStringClaim">
        <InputClaims>
          <InputClaim ClaimTypeReferenceId="relicEmail" TransformationClaimType="inputClaim" />
        </InputClaims>
        <InputParameters>
          <InputParameter Id="stringFormat" DataType="string" Value="{0}" />
        </InputParameters>
        <OutputClaims>
          <OutputClaim ClaimTypeReferenceId="readOnlyEmail" TransformationClaimType="outputClaim" />
        </OutputClaims>
      </ClaimsTransformation>

      <!-- Transformation for creating usedIdForMFA using upnUserName. userIdForMFA is required for two-factor default screen-->
      <ClaimsTransformation Id="CreateCustomUserIdForMFA" TransformationMethod="FormatStringClaim">
        <InputClaims>
          <InputClaim ClaimTypeReferenceId="upnUserName" TransformationClaimType="inputClaim" />
        </InputClaims>
        <InputParameters>
          <InputParameter Id="stringFormat" DataType="string" Value="{0}@{RelyingPartyTenantId}" />
        </InputParameters>
        <OutputClaims>
          <OutputClaim ClaimTypeReferenceId="userIdForMFA" TransformationClaimType="outputClaim" />
        </OutputClaims>
      </ClaimsTransformation>

      <!-- Transformation used for copying Custom SignInPhone to strong Authentication Phone for two-factor default screen -->
      <ClaimsTransformation Id="CopySignInPhoneIntostrongAuthenticationPhoneNumber" TransformationMethod="ConvertPhoneNumberClaimToString">
        <InputClaims>
          <InputClaim ClaimTypeReferenceId="signInNames.phoneNumber" TransformationClaimType="phoneNumber" />
        </InputClaims>
        <OutputClaims>
          <OutputClaim ClaimTypeReferenceId="strongAuthenticationPhoneNumber" TransformationClaimType="phoneNumberString" />
        </OutputClaims>
      </ClaimsTransformation>

    </ClaimsTransformations>
  </BuildingBlocks>

  <ClaimsProviders>

    <ClaimsProvider>
      <DisplayName>Utility Technical Profiles</DisplayName>
      <TechnicalProfiles>
        
        <!-- Extract Id Tokens and put in the session-->
        <TechnicalProfile Id="RelicExtractClaimsFromHint">
          <DisplayName> My ID Token Hint TechnicalProfile</DisplayName>
          <Protocol Name="None" />
          <Metadata>          
            <!--Sample action required: replace with your endpoint location -->
            <!-- <Item Key="METADATA">https://your-app.azurewebsites.net/.well-known/openid-configuration</Item> -->
            <Item Key="IdTokenAudience">aadB2c</Item>
            <Item Key="issuer">node-services</Item>
          </Metadata>
          <CryptographicKeys>
            <Key Id="client_secret" StorageReferenceId="B2C_1A_IdTokenHintKey" />
          </CryptographicKeys>
          <OutputClaims>
            <!--Sample: Read the email claim from the id_token_hint-->
            <OutputClaim ClaimTypeReferenceId="relicEmail" />
            <OutputClaim ClaimTypeReferenceId="relicPhone" />
          </OutputClaims>
          <UseTechnicalProfileForSessionManagement ReferenceId="SM-AAD" />
        </TechnicalProfile>

        <!-- Session Management -->
        <TechnicalProfile Id="SM-AAD">
          <PersistedClaims>
            <PersistedClaim ClaimTypeReferenceId="relicPhone" />
            <PersistedClaim ClaimTypeReferenceId="relicEmail" />
            <PersistedClaim ClaimTypeReferenceId="readOnlyEmail" />
            <PersistedClaim ClaimTypeReferenceId="signInNames.emailAddress" />
            <PersistedClaim ClaimTypeReferenceId="userPrincipalName" />
          </PersistedClaims>
        </TechnicalProfile>

        <!-- Copy Relic Email to Sign In & Read Only Email. Add them to session. -->
        <TechnicalProfile Id="RelicEmailCopy">
          <DisplayName>Combine country code and national number</DisplayName>
          <Protocol Name="Proprietary" Handler="Web.TPEngine.Providers.ClaimsTransformationProtocolProvider, Web.TPEngine, Version=*******, Culture=neutral, PublicKeyToken=null" />
          <InputClaimsTransformations>
            <InputClaimsTransformation ReferenceId="RelicEmailToSignInNamesCopy" />
            <InputClaimsTransformation ReferenceId="RelicEmailToReadOnlyCopy" />
          </InputClaimsTransformations>
          <OutputClaims>
            <OutputClaim ClaimTypeReferenceId="signInNames.emailAddress" />
            <OutputClaim ClaimTypeReferenceId="readOnlyEmail" />
          </OutputClaims>
          <UseTechnicalProfileForSessionManagement ReferenceId="SM-AAD" />
        </TechnicalProfile>

      </TechnicalProfiles>
    </ClaimsProvider>

    <ClaimsProvider>
      <DisplayName>Phone OTP Core Technical Profiles</DisplayName>
      <TechnicalProfiles>

        <!-- Read Azure AD B2C User using signInPhone. Raise an error if user does not exist. -->
        <TechnicalProfile Id="AAD-UserReadUsingPhoneNumber">
          <Metadata>
            <Item Key="Operation">Read</Item>
            <Item Key="RaiseErrorIfClaimsPrincipalDoesNotExist">true</Item>
            <Item Key="UserMessageIfClaimsPrincipalDoesNotExist">Sorry, your account is not registered for our services. Please contact support.</Item>
          </Metadata>
          <IncludeInSso>false</IncludeInSso>
          <InputClaims>
            <InputClaim ClaimTypeReferenceId="relicPhone" PartnerClaimType="signInNames.phoneNumber" Required="true" />
          </InputClaims>
          <OutputClaims>
            <!-- Required claims -->
            <OutputClaim ClaimTypeReferenceId="objectId" />
            <OutputClaim ClaimTypeReferenceId="authenticationSource" DefaultValue="localAccountAuthentication" />
            <OutputClaim ClaimTypeReferenceId="strongAuthenticationPhoneNumber" />
            <OutputClaim ClaimTypeReferenceId="signInNames.phoneNumber" Required="true" />
            <OutputClaim ClaimTypeReferenceId="signInNames.emailAddress" />
            <!-- Optional claims -->
            <OutputClaim ClaimTypeReferenceId="accountEnabled" />
            <OutputClaim ClaimTypeReferenceId="userPrincipalName" />
            <OutputClaim ClaimTypeReferenceId="displayname" />
          </OutputClaims>
          <IncludeTechnicalProfile ReferenceId="AAD-Common" />
        </TechnicalProfile>

        <!-- Screen where OTP is asked. This is the key screen for the user to login. -->
        <TechnicalProfile Id="PhoneFactor-InputOrVerify-PhoneLogin">
          <DisplayName>Confirm Phone Number via Text</DisplayName>
          <Protocol Name="Proprietary" Handler="Web.TPEngine.Providers.PhoneFactorProtocolProvider, Web.TPEngine, Version=*******, Culture=neutral, PublicKeyToken=null" />
          <Metadata>
            <Item Key="ContentDefinitionReferenceId">api.phonefactor</Item>
            <Item Key="ManualPhoneNumberEntryAllowed">true</Item>
          </Metadata>
          <CryptographicKeys>
            <Key Id="issuer_secret" StorageReferenceId="B2C_1A_TokenSigningKeyContainer" />
          </CryptographicKeys>
          <InputClaimsTransformations>
            <InputClaimsTransformation ReferenceId="CreateRandomUPNUserName" />
            <InputClaimsTransformation ReferenceId="CreateCustomUserIdForMFA" />
            <InputClaimsTransformation ReferenceId="CopySignInPhoneIntostrongAuthenticationPhoneNumber" />
          </InputClaimsTransformations>
          <InputClaims>
            <InputClaim ClaimTypeReferenceId="userIdForMFA" PartnerClaimType="UserId" />
            <InputClaim ClaimTypeReferenceId="strongAuthenticationPhoneNumber" PartnerClaimType="signInNames.phoneNumber" />
          </InputClaims>
          <OutputClaims>
            <OutputClaim ClaimTypeReferenceId="Verified.strongAuthenticationPhoneNumber" PartnerClaimType="Verified.OfficePhone" />
            <OutputClaim ClaimTypeReferenceId="newPhoneNumberEntered" PartnerClaimType="newPhoneNumberEntered" />
            <OutputClaim ClaimTypeReferenceId="isActiveMFASession" />
          </OutputClaims>
          <UseTechnicalProfileForSessionManagement ReferenceId="SM-MFA" />
        </TechnicalProfile>

      </TechnicalProfiles>
    </ClaimsProvider>

    <ClaimsProvider>
      <DisplayName>Email OTP Core Technical Profiles</DisplayName>
      <TechnicalProfiles>

        <!-- Read AAD B2C User record using email address -->
        <TechnicalProfile Id="RelicUserReadUsingEmailAddress">
          <Metadata>
            <Item Key="Operation">Read</Item>
            <Item Key="RaiseErrorIfClaimsPrincipalDoesNotExist">true</Item>
            <Item Key="UserMessageIfClaimsPrincipalDoesNotExist">Sorry, your account is not registered for our services. Please contact support.</Item>
          </Metadata>
          <IncludeInSso>false</IncludeInSso>
          <InputClaims>
            <InputClaim ClaimTypeReferenceId="relicEmail" PartnerClaimType="signInNames.emailAddress" Required="true" />
          </InputClaims>
          <OutputClaims>
            <OutputClaim ClaimTypeReferenceId="objectId" />
            <OutputClaim ClaimTypeReferenceId="authenticationSource" DefaultValue="localAccountAuthentication" />
            <OutputClaim ClaimTypeReferenceId="strongAuthenticationPhoneNumber" />
            <OutputClaim ClaimTypeReferenceId="signInNames.phoneNumber" />
            <OutputClaim ClaimTypeReferenceId="signInNames.emailAddress" Required="true" />
            <OutputClaim ClaimTypeReferenceId="accountEnabled" />
            <OutputClaim ClaimTypeReferenceId="userPrincipalName" />
            <OutputClaim ClaimTypeReferenceId="displayName" />
          </OutputClaims>
          <OutputClaimsTransformations>
            <OutputClaimsTransformation ReferenceId="AssertAccountEnabledIsTrue" />
          </OutputClaimsTransformations>
          <IncludeTechnicalProfile ReferenceId="AAD-Common" />
        </TechnicalProfile>

        <!-- OTP UI / Verify Email UI. Outputs Verified.Email -->
        <TechnicalProfile Id="RelicEmailOtp">
          <DisplayName>Relic Email OTP Screen</DisplayName>
          <Protocol Name="Proprietary" Handler="Web.TPEngine.Providers.SelfAssertedAttributeProvider, Web.TPEngine, Version=*******, Culture=neutral, PublicKeyToken=null" />
          <Metadata>
            <Item Key="ContentDefinitionReferenceId">api.localaccountpasswordreset</Item>
          </Metadata>
          <InputClaims>
            <InputClaim ClaimTypeReferenceId="readOnlyEmail" />
          </InputClaims>
          <OutputClaims>
            <OutputClaim ClaimTypeReferenceId="readOnlyEmail" PartnerClaimType="Verified.Email" Required="true" />
          </OutputClaims>
          <UseTechnicalProfileForSessionManagement ReferenceId="SM-Noop" />
        </TechnicalProfile>

      </TechnicalProfiles>
    </ClaimsProvider>

  </ClaimsProviders>

  <UserJourneys>
    <UserJourney Id="RelicSignInWithEmailJourney">
      <OrchestrationSteps>
        <!--Read the input claims from the id_token_hint-->
        <OrchestrationStep Order="1" Type="GetClaims" CpimIssuerTechnicalProfileReferenceId="RelicExtractClaimsFromHint" />

        <!--Pull record from AAD B2C using phone to ensure that we have the user record-->
        <OrchestrationStep Order="2" Type="ClaimsExchange">
          <Preconditions>
            <Precondition Type="ClaimsExist" ExecuteActionsIf="true">
              <Value>relicEmail</Value>
              <Action>SkipThisOrchestrationStep</Action>
            </Precondition>
          </Preconditions>
          <ClaimsExchanges>
            <ClaimsExchange Id="ReadPhoneUser" TechnicalProfileReferenceId="AAD-UserReadUsingPhoneNumber" />
          </ClaimsExchanges>
        </OrchestrationStep>

        <!--Pull record from AAD B2C using email to ensure that we have the user record-->
        <OrchestrationStep Order="3" Type="ClaimsExchange">
          <Preconditions>
            <Precondition Type="ClaimsExist" ExecuteActionsIf="true">
              <Value>relicPhone</Value>
              <Action>SkipThisOrchestrationStep</Action>
            </Precondition>
          </Preconditions>
          <ClaimsExchanges>
            <ClaimsExchange Id="ReadEmailUser" TechnicalProfileReferenceId="RelicUserReadUsingEmailAddress" />
          </ClaimsExchanges>
        </OrchestrationStep>

        <!--Copy email to read only email field to get the screen ready for email OTP-->
        <OrchestrationStep Order="4" Type="ClaimsExchange">
          <Preconditions>
            <Precondition Type="ClaimsExist" ExecuteActionsIf="true">
              <Value>relicPhone</Value>
              <Action>SkipThisOrchestrationStep</Action>
            </Precondition>
          </Preconditions>
          <ClaimsExchanges>
            <ClaimsExchange Id="CopyEmail" TechnicalProfileReferenceId="RelicEmailCopy" />
          </ClaimsExchanges>
        </OrchestrationStep>

        <!-- Phone Verification Step with OTP -->
        <OrchestrationStep Order="5" Type="ClaimsExchange">
          <Preconditions>
            <Precondition Type="ClaimsExist" ExecuteActionsIf="true">
              <Value>relicEmail</Value>
              <Action>SkipThisOrchestrationStep</Action>
            </Precondition>
          </Preconditions>
          <ClaimsExchanges>
            <ClaimsExchange Id="PhoneFactor-Verify" TechnicalProfileReferenceId="PhoneFactor-InputOrVerify-PhoneLogin" />
          </ClaimsExchanges>
        </OrchestrationStep>

        <!-- Email Verification Step with OTP -->
        <OrchestrationStep Order="6" Type="ClaimsExchange">
          <Preconditions>
            <Precondition Type="ClaimsExist" ExecuteActionsIf="true">
              <Value>relicPhone</Value>
              <Action>SkipThisOrchestrationStep</Action>
            </Precondition>
          </Preconditions>
          <ClaimsExchanges>
            <ClaimsExchange Id="EmailOtp" TechnicalProfileReferenceId="RelicEmailOtp" />
          </ClaimsExchanges>
        </OrchestrationStep>

        <!-- This step reads any user attributes that we may not have received during claims exchange. -->
        <OrchestrationStep Order="7" Type="ClaimsExchange">
          <ClaimsExchanges>
            <ClaimsExchange Id="AADUserReadWithObjectId" TechnicalProfileReferenceId="AAD-UserReadUsingObjectId" />
          </ClaimsExchanges>
        </OrchestrationStep>

        <OrchestrationStep Order="8" Type="SendClaims" CpimIssuerTechnicalProfileReferenceId="JwtIssuer" />

      </OrchestrationSteps>
      <ClientDefinition ReferenceId="DefaultWeb" />
    </UserJourney>
  </UserJourneys>

  <RelyingParty>
    <DefaultUserJourney ReferenceId="RelicSignInWithEmailJourney" />
    <UserJourneyBehaviors>
      <!-- for AppInsight - add your key
      <JourneyInsights TelemetryEngine="ApplicationInsights" InstrumentationKey="...guid..." DeveloperMode="true" ClientEnabled="true" ServerEnabled="true" TelemetryVersion="1.0.0" /> 
    -->
      <JourneyInsights TelemetryEngine="ApplicationInsights" InstrumentationKey="1c3959d5-0561-494e-95d5-adaf198bb783" DeveloperMode="true" ClientEnabled="true" ServerEnabled="true" TelemetryVersion="1.0.0" /> 
      <ScriptExecution>Allow</ScriptExecution>
    </UserJourneyBehaviors>
    <TechnicalProfile Id="PolicyProfile">
      <DisplayName>PolicyProfile</DisplayName>
      <Protocol Name="OpenIdConnect" />
      <InputClaims>
        <InputClaim ClaimTypeReferenceId="relicPhone"/>
        <InputClaim ClaimTypeReferenceId="relicEmail"/>
      </InputClaims>      
      <OutputClaims>
        <OutputClaim ClaimTypeReferenceId="displayName" />
        <OutputClaim ClaimTypeReferenceId="signInNames.emailAddress" />
        <OutputClaim ClaimTypeReferenceId="objectId" PartnerClaimType="sub" />
        <OutputClaim ClaimTypeReferenceId="tenantId" AlwaysUseDefaultValue="true" DefaultValue="{Policy:TenantObjectId}" />
        <OutputClaim ClaimTypeReferenceId="userPrincipalName" />
      </OutputClaims>
      <SubjectNamingInfo ClaimType="sub" />
    </TechnicalProfile>
  </RelyingParty>
</TrustFrameworkPolicy>