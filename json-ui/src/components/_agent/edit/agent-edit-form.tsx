import { Controller, useForm } from 'react-hook-form';

import {
  FormControlLabel,
  Stack,
  Switch,
  TextField,
  Typography,
} from '@mui/material';
import { AgentRoleDropDown } from 'src/components/dropdown/agentRoleDropdown';
import { OrganizationDropdown } from 'src/components/dropdown/organizationDropdown';
import { TrainingDropdown } from 'src/components/dropdown/trainingDropdown';
import { useTranslate } from '@refinedev/core';
import { UseFormReturnType } from '@refinedev/react-hook-form';
import { RelicAgent } from 'relic-ui'

const AgentEditForm = ({
  showEdit,
  agentData,
  agentForm,
}: {
  showEdit: boolean;
  agentData: RelicAgent;
  agentForm: UseFormReturnType<RelicAgent>;
}) => {
  const translate = useTranslate();

  const {
    control,
    formState: { errors },
  } = agentForm;

  return (
    <Stack
      spacing={3}
      component="form"
      autoComplete="off"
      sx={{
        flexDirection: 'column',
        display: showEdit ? 'flex' : 'none',
      }}
    >
      <Controller
        name="name"
        control={control}
        defaultValue={agentData?.name || ''}
        rules={{ required: 'This field is required' }}
        render={({ field }) => (
          <TextField
            {...field}
            fullWidth
            type="text"
            name="name"
            InputLabelProps={{ shrink: true }}
            label={translate('agents.fields.name')}
            error={!!(errors as any)?.name}
            helperText={(errors as any)?.name?.message}
          />
        )}
      />

      <AgentRoleDropDown
        control={control}
        name="role"
        label={translate('agents.fields.role')}
        value={agentData?.role}
        disabled={true}
        error={!!(errors as any)?.role}
        helperText={(errors as any)?.role?.message}
      />
      {/* Organization */}
      <OrganizationDropdown
        control={control}
        name="organizationId"
        label={translate('locations.fields.organizationId')}
        value={agentData?.organizationId}
        error={!!(errors as any)?.organizationId}
        helperText={(errors as any)?.organizationId?.message}
        disabled={true}
      />
      <TrainingDropdown
        control={control}
        name="relicAssistantSetup.kbLinked[0]"
        label={translate('agents.fields.training')}
        error={!!(errors as any)?.relicAssistantSetup?.kbLinked[0]}
        helperText={(errors as any)?.relicAssistantSetup?.kbLinked[0]?.message}
        value={agentData?.relicAssistantSetup?.kbLinked?.[0] ?  agentData?.relicAssistantSetup?.kbLinked[0] : ''}
      />
      <Controller
        name="publicData.bio"
        control={control}
        defaultValue={agentData?.publicData?.bio || ''}
        render={({ field }) => (
          <TextField
            {...field}
            fullWidth
            type="text"
            name="publicData.bio"
            InputLabelProps={{ shrink: true }}
            label={translate('agents.fields.bio')}
            error={!!(errors as any)?.publicData?.bio}
            multiline
            maxRows={4}
            helperText={(errors as any)?.publicData?.bio?.message}
          />
        )}
      />
      <Controller
        name="publicData.avatarUrl"
        control={control}
        defaultValue={agentData?.publicData?.avatarUrl || ''}
        render={({ field }) => (
          <TextField
            {...field}
            fullWidth
            type="text"
            name="publicData.avatarUrl"
            InputLabelProps={{ shrink: true }}
            label={translate('agents.fields.avatarUrl')}
            error={!!(errors as any)?.publicData?.avatarUrl}
            helperText={(errors as any)?.publicData?.avatarUrl?.message}
          />
        )}
      />
      <Controller
        name="publicData.coverUrl"
        control={control}
        defaultValue={agentData?.publicData?.coverUrl || ''}
        render={({ field }) => (
          <TextField
            {...field}
            fullWidth
            type="text"
            name="publicData.coverUrl"
            InputLabelProps={{ shrink: true }}
            label={translate('agents.fields.coverUrl')}
            error={!!(errors as any)?.publicData?.coverUrl}
            helperText={(errors as any)?.publicData?.coverUrl?.message}
          />
        )}
      />
      <Controller
        name="active"
        control={control}
        defaultValue={agentData?.active}
        render={({ field }) => (
          <FormControlLabel
            label={translate('agents.fields.active')}
            control={
              <Switch {...field} name="active" checked={!!field.value} />
            }
          />
        )}
      />
      <Typography variant="body1" fontWeight="bold">
        {translate('agents.setup')}
      </Typography>
      <Controller
        name="azureAssistantSetup.chatParameters.maxResponseLength"
        control={control}
        defaultValue={
          agentData?.azureAssistantSetup?.chatParameters?.maxResponseLength ||
          ''
        }
        render={({ field }) => (
          <TextField
            {...field}
            fullWidth
            type="text"
            name="azureAssistantSetup.chatParameters.maxResponseLength"
            InputLabelProps={{ shrink: true }}
            label={translate('agents.fields.maxResponseLength')}
            error={
              !!(errors as any)?.azureAssistantSetup?.chatParameters
                ?.maxResponseLength
            }
            helperText={
              (errors as any)?.azureAssistantSetup?.chatParameters
                ?.maxResponseLength?.message
            }
          />
        )}
      />
      <Controller
        name="azureAssistantSetup.chatParameters.temperature"
        control={control}
        defaultValue={
          agentData?.azureAssistantSetup?.chatParameters?.temperature || ''
        }
        render={({ field }) => (
          <TextField
            {...field}
            fullWidth
            type="text"
            name="azureAssistantSetup.chatParameters.temperature"
            InputLabelProps={{ shrink: true }}
            label={translate('agents.fields.temperature')}
            error={
              !!(errors as any)?.azureAssistantSetup?.chatParameters
                ?.temperature
            }
            helperText={
              (errors as any)?.azureAssistantSetup?.chatParameters?.temperature
                ?.message
            }
          />
        )}
      />
      <Controller
        name="azureAssistantSetup.chatParameters.topProbabilities"
        control={control}
        defaultValue={
          agentData.azureAssistantSetup.chatParameters.topProbabilities || ''
        }
        render={({ field }) => (
          <TextField
            {...field}
            fullWidth
            type="text"
            name="azureAssistantSetup.chatParameters.topProbabilities"
            InputLabelProps={{ shrink: true }}
            label={translate('agents.fields.topProbabilities')}
            error={
              !!(errors as any)?.azureAssistantSetup?.chatParameters
                ?.topProbabilities
            }
            helperText={
              (errors as any)?.azureAssistantSetup?.chatParameters
                ?.topProbabilities?.message
            }
          />
        )}
      />
      <Controller
        name="azureAssistantSetup.chatParameters.stopSequences"
        control={control}
        defaultValue={
          agentData?.azureAssistantSetup?.chatParameters?.stopSequences || ''
        }
        render={({ field }) => (
          <TextField
            {...field}
            fullWidth
            type="text"
            name="azureAssistantSetup.chatParameters.stopSequences"
            InputLabelProps={{ shrink: true }}
            label={translate('agents.fields.stopSequences')}
            error={
              !!(errors as any)?.azureAssistantSetup?.chatParameters
                ?.stopSequences
            }
            helperText={
              (errors as any)?.azureAssistantSetup?.chatParameters
                ?.stopSequences?.message
            }
          />
        )}
      />
      <Controller
        name="azureAssistantSetup.chatParameters.frequencyPenalty"
        control={control}
        defaultValue={
          agentData?.azureAssistantSetup?.chatParameters?.frequencyPenalty || ''
        }
        render={({ field }) => (
          <TextField
            {...field}
            fullWidth
            type="text"
            name="azureAssistantSetup.chatParameters.frequencyPenalty"
            InputLabelProps={{ shrink: true }}
            label={translate('agents.fields.frequencyPenalty')}
            error={
              !!(errors as any)?.azureAssistantSetup?.chatParameters
                ?.frequencyPenalty
            }
            helperText={
              (errors as any)?.azureAssistantSetup?.chatParameters
                ?.frequencyPenalty?.message
            }
          />
        )}
      />
      <Controller
        name="azureAssistantSetup.chatParameters.presencePenalty"
        control={control}
        defaultValue={
          agentData?.azureAssistantSetup?.chatParameters?.presencePenalty || ''
        }
        render={({ field }) => (
          <TextField
            {...field}
            fullWidth
            type="text"
            name="azureAssistantSetup.chatParameters.presencePenalty"
            InputLabelProps={{ shrink: true }}
            label={translate('agents.fields.presencePenalty')}
            error={
              !!(errors as any)?.azureAssistantSetup?.chatParameters
                ?.presencePenalty
            }
            helperText={
              (errors as any)?.azureAssistantSetup?.chatParameters
                ?.presencePenalty?.message
            }
          />
        )}
      />
      {/* 
       // NOTE:  This part is commented out because will be coming in the future with different approach
      <Controller
        name="relicAssistantSetup.responseFrom"
        control={control}
        defaultValue={agentData?.relicAssistantSetup?.responseFrom}
        render={({ field }) => (
          <Box>
            <Typography variant="body2">
              {translate('agents.fields.responseFrom')}
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Typography variant="body2">
                {translate('agents.fields.training')}
              </Typography>
              <Switch
                {...field}
                name="relicAssistantSetup.responseFrom"
                checked={!!field.value}
              />
              <Typography variant="body2">
                {translate('agents.fields.openAi')}
              </Typography>
            </Box>
          </Box>
        )}
      /> */}
    </Stack>
  );
};

export default AgentEditForm;
