import PDFDocument from 'pdfkit';
import fs from 'fs';
import * as path from 'path';
import { TranslateDocumentOptions } from './types';
import { FontTargetLangMapValue } from './fonts';
import { PDFNet } from '@pdftron/pdfnet-node';
import LicenseKey from './licenseKey';

const collator = new Intl.Collator(undefined, {
    numeric: true,
    sensitivity: 'base',
});

/**
 * printDocument prints pdf document
 * @param options TranslateDocumentOptions print options
 * @param fontNames Map<string, FontTargetLangMapValue> Font mapping
 * @returns pdf file path
 */
export const printDocument = async (options: TranslateDocumentOptions, fontNames: Map<string, FontTargetLangMapValue>): Promise<string> => {
    if (!fs.existsSync(options.sourceFilepath)) {
        throw new Error(`source file not found, path=${options.sourceFilepath}`);
    }

    try {
        await fs.promises.access(options.workingDirectory, fs.constants.F_OK);
        console.debug(`'${options.workingDirectory}' directory exists.`);
    } catch (err) {
        console.debug(`'${options.workingDirectory}' directory does not exist. Creating the directory...`);
        try {
            await fs.promises.mkdir(options.workingDirectory, { recursive: true });
            console.debug(`'${options.workingDirectory}' directory created successfully.`);
        } catch (err) {
            console.error('Error creating directory:', err);
            throw err;
        }
    }

    switch (options.translationType) {
        case 'bilingual': {
            return await printBilingualDocument(options, fontNames);
        }
        case 'mono': {
            return await printMonolingualDocument(options, fontNames);
        }
        default: {
            throw new Error(`invalid translationType ${options.translationType}`);
        }
    }
};

/**
 * printBilingualDocument prints PDF which contains the original texts (sourceLang) and the translated texts (targetLang)
 * @param options TranslateDocumentOptions print options
 * @param fontNames Map<string, FontTargetLangMapValue> Font mapping
 * @returns pdf file path
 */
const printBilingualDocument = async (options: TranslateDocumentOptions, fontNames: Map<string, FontTargetLangMapValue>): Promise<string> => {
    if (options.translationType != 'bilingual') {
        throw new Error(`invalid translationType ${options.translationType}`);
    }
    const inputDir = options.sourceJsonTextblocksFileDir;
    const outputDir = options.translatedJsonTextblocksFileDir;
    const outputFilename = options.filenameWithoutExt;
    const translatedPdfDirPath = options.workingDirectory;
    const translatedPdfPath = path.join(translatedPdfDirPath, `${outputFilename}-${options.translationType}-translated.pdf`);
    try {
        // Check if the translated PDF file exists
        await fs.promises.access(translatedPdfPath, fs.constants.F_OK);
        console.debug(`'${translatedPdfPath}' file exists.`);
    } catch (err) {
        if (err.code === 'ENOENT') {
            console.debug(`'${translatedPdfPath}' file does not exist. Creating PDF...`);
            // Create an empty file to initiate PDF creation
            fs.writeFileSync(translatedPdfPath, '');
            console.debug(`'${translatedPdfPath}' file created for PDF generation.`);
        } else {
            console.error('Error accessing file:', err);
            throw err;
        }
    }

    const printPage = async function (docOptions: TranslateDocumentOptions, doc: PDFDocument, currentPage: number, currentPageNumber: number, originalPath: string, translatedPath: string, fontNames: Map<string, FontTargetLangMapValue>) {

        const textblocks = JSON.parse(fs.readFileSync(originalPath, 'utf-8'));
        const translatedTextBlocks = JSON.parse(fs.readFileSync(translatedPath, 'utf-8'));

        const margins = {
            top: parseFloat(textblocks.page.layout.margins.top),
            left: parseFloat(textblocks.page.layout.margins.left),
            right: parseFloat(textblocks.page.layout.margins.right),
            bottom: parseFloat(textblocks.page.layout.margins.bottom),
        };
        const headersFooters = {
            header: [],
            footer: []
        }

        // const maxHeight = parseFloat(textblocks.page.layout.height) - parseFloat(textblocks.page.layout.margins.bottom);
        const maxWidth = parseFloat(textblocks.page.layout.width) - parseFloat(textblocks.page.layout.margins.left) - parseFloat(textblocks.page.layout.margins.right);

        let seqTextBlock = new Map();
        let maxSeq = 0;
        let mapOfY1 = new Map(); //track Y positions for same line
        // Parse original text blocks
        for (let i = 0; i < textblocks.page.lines.length; i++) {
            const { seq, runningBlock } = textblocks.page.lines[i];
            seqTextBlock.set(seq, textblocks.page.lines[i]);
            maxSeq = Math.max(seq, maxSeq);
            if (['header', 'footer'].includes(runningBlock)) {
                headersFooters[runningBlock].push(textblocks.page.lines[i]);
            }
            if (headersFooters.footer.length > docOptions.numFooterLines) {
                let removedFooters = headersFooters.footer.splice(0, headersFooters.footer.length - docOptions.numFooterLines);
                for (let f of removedFooters) {
                    let t = seqTextBlock.get(f.seq);
                    t.runningBlock = 'body';
                }
            }
            if (headersFooters.header.length > docOptions.numHeaderLines) {
                let removedHeaders = headersFooters.footer.splice(docOptions.numHeaderLines);
                for (let h of removedHeaders) {
                    let t = seqTextBlock.get(h.seq);
                    t.runningBlock = 'body';
                }
            }
        }

        // Parse translated text blocks
        const translatedTextSeqBlock = new Map();
        for (let i = 0; i < translatedTextBlocks.page.lines.length; i++) {
            const { seq } = translatedTextBlocks.page.lines[i];
            translatedTextSeqBlock.set(seq, translatedTextBlocks.page.lines[i]);
        }

        doc.on('pageAdded', function () {
            // Footer
            let yFooter = parseFloat(textblocks.page.layout.height) - margins.bottom;
            for (let footer of headersFooters.footer) {
                if (doc.x + parseFloat(footer.width) > parseFloat(textblocks.page.layout.contentWidth)) {
                    yFooter += 10;
                }
                let text: string = footer.text;
                //test if footer textblock is a possible page number
                try {
                    if (parseInt(text) > 0) {
                        text = `${parseInt(text)}`;
                    }
                    text = text?.replaceAll('-', '')?.trim();
                    let pageNumber = parseFloat(text?.match(/\d+/)?.at(-1));
                    const isNonAlpha = text.replace(/[^a-zA-Z]+/g, '')?.length == 0;
                    if (isNonAlpha && pageNumber > 0) {
                        text = `${currentPageNumber}`;
                        currentPageNumber++;
                    }
                } catch (e) {
                    console.error(e); //do nothing
                }
                footer.fontName = footer.fontName.trim()?.toLowerCase();
                doc
                    .font(fontNames.get(footer.fontName)?.sourceFont.fontFile.identifier)
                    .fontSize(parseFloat(footer.fontSize))
                    .fillColor(footer.color)
                    .text(text, parseFloat(footer.box.x1), yFooter, {
                        width: parseFloat(footer.width),
                        height: parseFloat(footer.height),
                        align: footer.justify
                    });
            }
            doc.y = parseFloat(textblocks.page.layout.margins.top);

            // Header ??
            let yHeader = parseFloat(textblocks.page.layout.margins.top);
            for (let header of headersFooters.header) {
                if (doc.x + parseFloat(header.width) > parseFloat(textblocks.page.layout.contentWidth)) {
                    yHeader += 10;
                }
                let text = header.text;

                header.fontName = header.fontName.trim()?.toLowerCase();
                doc
                    .font(fontNames.get(header.fontName)?.sourceFont.fontFile.identifier)
                    .fontSize(parseFloat(header.fontSize))
                    .fillColor(header.color)
                    .text(text, parseFloat(header.box.x1), yHeader, {
                        width: parseFloat(header.width),
                        height: parseFloat(header.height),
                        align: header.justify
                    });
            }
        });

        doc.addPage({ margins, bufferPages: true });

        for (let i of seqTextBlock.keys()) {
            let { justify, spaceBefore, fontSize, text, color, box, fontName, runningBlock, paraLines } = seqTextBlock.get(i);
            if (['footer', 'header'].includes(runningBlock)) {
                continue;
            }
            //if white, then replace with black to make texts visible in white BG
            if (color == '#FFFFFF') {
                color = '#000000';
            }

            fontName = fontName.trim()?.toLowerCase();
            const _height = parseFloat(box.y2) - parseFloat(box.y1);
            const _width = parseFloat(box.x2) - parseFloat(box.x1) + 5;
            let _spaceBefore = parseFloat(spaceBefore);

            let y = doc.y;
            let cachedY = false;
            if (mapOfY1.get(box.y1)) {
                const d = mapOfY1.get(box.y1);
                y = d.y;
                cachedY = true;
            } else {
                if (doc.y + _spaceBefore + _height > parseFloat(textblocks.page.layout.contentHeight)) {
                    doc.addPage({ margins, bufferPages: true });
                    doc.y = parseFloat(textblocks.page.layout.margins.top);
                }
                //TODO: @Ashish, in cases where spaceBefore=0, we set default gap. 
                // 5 might not be the ideal gap, but this gives you an idea on what to do
                doc.text('', 0, doc.y + Math.max(_spaceBefore, 5));
                y = doc.y;
                mapOfY1.set(box.y1, { y, page: currentPage });
            }
            doc.font(fontNames.get(fontName)?.sourceFont.fontFile.identifier).fontSize(parseFloat(fontSize));
            const t_widthOfString = Math.ceil(doc.widthOfString(text));
            const t_heightOfString = Math.ceil(doc.heightOfString(text, {
                width: t_widthOfString
            }))

            const t_lines = Math.ceil(t_widthOfString / _width);
            const t_height = t_lines * t_heightOfString;

            doc.fillColor(color)
                .text(text, parseFloat(box.x1), y, {
                    width: _width,
                    height: t_height,
                    align: justify
                });

            //translated text
            const translatedText = translatedTextSeqBlock.get(i)?.text;
            if (translatedText) {
                const t_fontSize = parseFloat(fontSize) * 0.7;
                doc.font(fontNames.get(fontName)?.targetLangFont.fontFile.identifier).fontSize(t_fontSize);

                const t_widthOfString = Math.ceil(doc.widthOfString(translatedText));
                const t_heightOfString = Math.ceil(doc.heightOfString(translatedText, {
                    width: t_widthOfString
                }))

                let widthTranslatedText: any = _width;
                if (paraLines == 1 && t_widthOfString + parseFloat(box.x1) <= parseFloat(textblocks.page.layout.contentWidth)) {
                    widthTranslatedText = Math.max(t_widthOfString, _width);
                }

                const t_lines = Math.max(paraLines, Math.ceil(t_widthOfString / widthTranslatedText));
                const t_height = Math.max(t_lines * t_heightOfString, _height);

                if (!cachedY && (paraLines > 1 || widthTranslatedText >= maxWidth) && doc.y + t_height >= parseFloat(textblocks.page.layout.contentHeight)) {
                    doc.addPage({ margins, bufferPages: true });
                    doc.y = parseFloat(textblocks.page.layout.margins.top);
                }
                doc
                    .fillColor(color)
                    .text(translatedText, parseFloat(box.x1), doc.y, {
                        width: widthTranslatedText,
                        height: t_height,
                        align: justify
                    });

            }
        }

        return { currentPage: currentPage + 1, currentPageNumber };
    }

    return new Promise<string>(async (resolve, reject) => {
        const doc = new PDFDocument({ bufferPages: true, autoFirstPage: false });
        try {
            const writeStream = fs.createWriteStream(translatedPdfPath);
            doc.pipe(writeStream);
            writeStream.on('finish', () => {
                console.debug(`translated document creation complete, filepath="${translatedPdfPath}"`);
                resolve(translatedPdfPath);
            });

            const fontDirectory: string = path.join(__dirname, '/fonts');

            for (const fontName of fontNames.entries()) {
                //register source fount
                doc.registerFont(fontName[1].sourceFont.fontFile.identifier, path.join(fontDirectory, fontName[1].sourceFont.fontFile.ttf));

                //register targetlang font
                doc.registerFont(fontName[1].targetLangFont.fontFile.identifier, path.join(fontDirectory, fontName[1].targetLangFont.fontFile.ttf));
            }

            const originalFiles = fs.readdirSync(inputDir).filter(file => file.endsWith('.json')).sort(collator.compare);
            const translatedFiles = fs.readdirSync(outputDir).filter(file => file.endsWith('translated.json')).sort(collator.compare);

            let currentPageNumber = 1;//for page number printing
            let currentPage = 0; //for tracking correct page position, this could increase or decrease

            for (let i = 0; i < translatedFiles.length; i++) {
                const originalPath = path.join(inputDir, originalFiles[i]);
                const translatedPath = path.join(outputDir, translatedFiles[i]);
                let result = await printPage(options, doc, currentPage, currentPageNumber, originalPath, translatedPath, fontNames);
                currentPage = result.currentPage;
                currentPageNumber = result.currentPageNumber;
            }
        } catch (e) {
            reject(e)
        } finally {
            doc.end();
        }
    })
};

/**
 * printMonoLingualDocument prints PDF document which contains the translated texts only (targetLang)
 * @param options TranslateDocumentOptions print options
 * @param fontNames Map<string, FontTargetLangMapValue> Font mapping
 * @returns pdf file path
 */
const printMonolingualDocument = async (options: TranslateDocumentOptions, fontNames: Map<string, FontTargetLangMapValue>): Promise<string> => {
    if (options.translationType != 'mono') {
        throw new Error(`invalid translationType ${options.translationType}`);
    }
    const outputFilename = options.filenameWithoutExt;
    const translatedFiles = fs.readdirSync(options.translatedJsonTextblocksFileDir).filter(file => file.endsWith('translated.json')).sort(collator.compare);

    const _replaceTextsWithTranslations = async function (doc: PDFNet.PDFDoc, currentPage: number, translatedTextBlocks: any) {
        let seqTextBlock = new Map();
        let maxSeq = 0;
        for (let i = 0; i < translatedTextBlocks.page.lines.length; i++) {
            const { seq } = translatedTextBlocks.page.lines[i];
            seqTextBlock.set(seq, translatedTextBlocks.page.lines[i]);
            maxSeq = Math.max(seq, maxSeq);
        }

        const page = await doc.getPage(currentPage);
        const replacer = await PDFNet.ContentReplacer.create();

        for (let i of seqTextBlock.keys()) {
            let { box: { x1, x2, y1, y2 }, text } = seqTextBlock.get(i);
            try {
                if (text) {
                    await replacer.addText(new PDFNet.Rect(parseFloat(x1), parseFloat(y1), parseFloat(x2), parseFloat(y2)), text);
                }
            } catch (err) {
                console.error(`unable to replace text for page=${currentPage}, text=${text}, error=${err}`);
            }
        }

        await replacer.process(page);
        await replacer.destroy();
    }

    const _decreaseFontSize = async function (doc: PDFNet.PDFDoc, currentPage: number, decreaseBy: number) {
        const page = await doc.getPage(currentPage);
        const reader = await PDFNet.ElementReader.create();
        const writer = await PDFNet.ElementWriter.create();
        await reader.beginOnPage(page);
        await writer.beginOnPage(page, 2); //e_replace

        let element = await reader.next();
        while (element !== null) {
            const elementType = await element.getType();
            if (elementType === PDFNet.Element.Type.e_text) {
                if (await element.getTextLength() > 0) {
                    const gs = await element.getGState();
                    let fontSize = await gs.getFontSize();
                    fontSize = fontSize * decreaseBy;
                    const font = await gs.getFont();
                    await gs.setFont(font, fontSize);
                }
                await writer.writeElement(element);
            } else {
                await writer.writeElement(element);
            }
            element = await reader.next();
        }

        await reader.end();
        await writer.end();
        await reader.destroy();
        await writer.destroy();
    }

    const _applyTextAlignments = async function (doc: PDFNet.PDFDoc, currentPage: number, translatedTextBlocks: any) {
        const _searchTextBlockEntry = (text: string) => {
            const i = translatedTextBlocks?.page?.lines?.findIndex((v: any) => !v.found && (v.text == text || v.text.startsWith(text)));
            if (i > -1) {
                translatedTextBlocks.page.lines[i]['found'] = true;
                return translatedTextBlocks.page.lines[i];
            }
            return null;
        }
        const page = await doc.getPage(currentPage);
        const reader = await PDFNet.ElementReader.create();
        const writer = await PDFNet.ElementWriter.create();
        await reader.beginOnPage(page);
        await writer.beginOnPage(page, 2); //e_replace


        let element = await reader.next();
        while (element !== null) {
            const elementType = await element.getType();
            if (elementType === PDFNet.Element.Type.e_text) {
                if (await element.getTextLength() > 0) {
                    const box = await element.getBBox();

                    const found = _searchTextBlockEntry(await element.getTextString());
                    let justify: string = 'none';
                    if (found) {
                        await box.set(Math.max(parseFloat(found.box.x1), box.x1),
                            Math.max(parseFloat(found.box.y1), box.y1),
                            Math.max(parseFloat(found.box.x2), box.x2),
                            Math.max(parseFloat(found.box.y2), box.y2));

                        justify = found.justify;
                    }
                    const textMatrix = await element.getTextMatrix();
                    const fontSize = await (await element.getGState()).getFontSize();
                    const textWidth = await element.getTextLength();
                    let startX = box.x1;
                    let startY = textMatrix.m_v;

                    switch (justify) {
                        case 'center':
                            const centerX = (box.x1 + box.x2) / 2;
                            const centerY = (box.y1 + box.y2) / 2;
                            startX = centerX - (textWidth / 2);
                            startY = centerY - (fontSize / 2); // Adjust vertically as needed
                            await element.setTextMatrixEntries(1, 0, 0, 1, startX, startY);
                            break;
                        case 'left':
                            await element.setTextMatrixEntries(1, 0, 0, 1, startX, startY);
                            break;
                        case 'right':
                            startX = box.x2 - textWidth;
                            await element.setTextMatrixEntries(1, 0, 0, 1, startX, startY);
                            break;

                    }
                }
                await writer.writeElement(element);
            } else {
                await writer.writeElement(element);
            }
            element = await reader.next();
        }

        await reader.end();
        await writer.end();
        await reader.destroy();
        await writer.destroy();
    }

    const _print = async function () {
        console.debug(`running document translation with sourceDocumentId=${options.sourceDocumentId}, language=${options.targetLang}`);
        const outputFilepath = path.join(options.workingDirectory, `${outputFilename}-${options.translationType}-translated.pdf`);
        fs.copyFileSync(options.sourceFilepath, outputFilepath);
        const doc = await PDFNet.PDFDoc.createFromFilePath(outputFilepath);
        try {
            await doc.initSecurityHandler();
            for (let pageNum = 1; pageNum <= await doc.getPageCount(); pageNum++) {
                const translatedTextblocksPath: string = translatedFiles.find(filepath => {
                    const textblockPageNum = path.parse(filepath).name?.replace('-translated', '').split('-')?.at(-1) ?? '';
                    return textblockPageNum == `${pageNum}`;
                });
                if (!translatedTextblocksPath) {
                    throw new Error(`translated textblock for page=${pageNum} not found`);
                }
                const translatedTextBlocks = JSON.parse(fs.readFileSync(path.join(options.translatedJsonTextblocksFileDir, translatedTextblocksPath), 'utf-8'));
                await _decreaseFontSize(doc, pageNum, 0.7);
                await _replaceTextsWithTranslations(doc, pageNum, translatedTextBlocks);
                await _applyTextAlignments(doc, pageNum, translatedTextBlocks);
                console.debug(`translation done for page=${pageNum} sourceDocumentId=${options.sourceDocumentId}, language=${options.targetLang}`);
            }
            await doc.save(outputFilepath, PDFNet.SDFDoc.SaveOptions.e_linearized);
            return outputFilepath;
        } catch (e) {
            throw e;
        } finally {
            await doc.destroy();
        }
    }

    return await PDFNet.runWithCleanup(_print, LicenseKey)
        .then(result => {
            console.debug(`translated document creation complete, filepath="${result}"`);
            return result;
        })
        .catch(err => { throw new Error(`error printing monolingual document, ${err}`) })
        // .finally(async () => await PDFNet.shutdown());
}