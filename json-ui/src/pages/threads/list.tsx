import React from 'react';
import { useDataGrid } from '@refinedev/mui';
import { CrudFilter, useGetIdentity } from '@refinedev/core';
import { IUser, IUserIdentity, RelicChatParticipant } from 'relic-ui';

import { Box, Card } from '@mui/material';
import { GridColDef } from '@mui/x-data-grid-pro';

import ListThreads from 'src/components/_threads/list/threads-list';

export const ThreadsList = () => {
  const { data: currentUser } = useGetIdentity<IUser>();
  const currentIdentity: IUserIdentity =
    currentUser?.userIdentity as IUserIdentity;

  const currentRole: string = currentIdentity?.role?.name
    ? currentIdentity?.role?.name
    : 'member';

  const initialFilters: CrudFilter[] = React.useMemo(() => {
    // Dummy admin filter to return all threads since threadId always has a value
    const adminFilter: CrudFilter = {
      field: 'threadId',
      value: '',
      operator: 'ne',
    };
    const participant: RelicChatParticipant = {
      id: {
        communicationUserId: currentIdentity?.communicationIdentities?.length
          ? (currentIdentity.communicationIdentities[0].userId ?? '')
          : '',
      },
      resourceId: currentIdentity?.id,
      resourceType:
        currentIdentity?.resourceType === 'Patient'
          ? 'Patient'
          : 'Practitioner',
    };
    const memberFilter: CrudFilter = {
      field: 'participant',
      value: encodeURIComponent(JSON.stringify(participant)),
      operator: 'eq',
    };
    return currentRole === 'admin' ? [adminFilter] : [memberFilter];
  }, [currentIdentity, currentRole]);

  const {
    dataGridProps: listThreadsProps,
    filters,
    setFilters,
  } = useDataGrid({
    resource: 'communication/chat/threads',
    sorters: {
      initial: [
        {
          field: 'updateDate',
          order: 'desc',
        },
      ],
    },
    filters: {
      mode: 'server',
      permanent: initialFilters,
    },
  });

  const threadsColumns = React.useMemo<GridColDef[]>(() => [], []);

  return (
    <Card>
      <Box sx={{ height: 'calc(100vh - 96px)', width: '100%' }}>
        <ListThreads
          {...listThreadsProps}
          setFilters={setFilters}
          filters={filters}
          columns={threadsColumns}
        />
      </Box>
    </Card>
  );
};
