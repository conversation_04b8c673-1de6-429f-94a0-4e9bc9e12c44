import React from 'react';
import {
  RelicChatParticipant,
  RelicPatient,
  RelicThreadProvider,
  RelicThreadProviderOptions,
  RelicUserProvider,
  RelicUserProviderOptions,
} from 'relic-ui';

import { getProviderServicesApi } from 'src/providers/utils/auth-utils';
import { getNodeServicesApi } from 'src/utils/app-config';

interface ChatViewWrapperProps {
  children: React.ReactNode;
  currentPatient: RelicPatient;
  accessToken: string;
  idToken: string;
}

/**
 * ChatViewWrapper component provides context providers for user and thread information
 * to its children components. It uses `RelicUserProvider` and `RelicThreadProvider`
 * to pass down the necessary context values.
 *
 * @component
 * @param {object} props - The props for the component.
 * @param {React.ReactNode} props.children - The child components that will be wrapped by the providers.
 * @param {RelicPatient} props.currentPatient - The current patient object containing patient details.
 * @param {string} props.accessToken - The access token for authentication.
 * @param {string} props.idToken - The ID token for authentication.
 *
 * @returns {JSX.Element} The wrapped children components with the necessary context providers.
 */
const ChatViewWrapper: React.FC<ChatViewWrapperProps> = ({
  children,
  currentPatient,
  accessToken,
  idToken,
}: {
  children: React.ReactNode;
  currentPatient: RelicPatient;
  accessToken: string;
  idToken: string;
}): JSX.Element => {
  // RelicUserProviderOptions memoized
  const relicUserProviderOptions: RelicUserProviderOptions = React.useMemo(
    () => ({
      accessToken,
      idToken,
      serviceUri: getProviderServicesApi(getNodeServicesApi()),
    }),
    [accessToken, idToken],
  );

  // RelicThreadProviderOptions memoized
  const relicThreadProviderOptions: RelicThreadProviderOptions =
    React.useMemo(() => {
      const counterPart: RelicChatParticipant = {
        id: {
          communicationUserId: currentPatient.communicationIdentities?.[0]
            .userId as string,
        },
        resourceId: currentPatient?.id,
        resourceType: 'Patient',
        displayName: currentPatient?.name,
      };
      return {
        displayInEnglish: true,
        counterPart: counterPart,
      };
    }, [currentPatient]);

  return (
    <RelicUserProvider {...relicUserProviderOptions}>
      <RelicThreadProvider {...relicThreadProviderOptions}>
        {children}
      </RelicThreadProvider>
    </RelicUserProvider>
  );
};

export default ChatViewWrapper;
