import { Condition, Encounter, Patient, Related<PERSON><PERSON>, ContactPoint } from "@medplum/fhirtypes";
import { RelicOrganization, FHIRCommunication, Gender, RelicCommunicationLanguage, RelicCondition, RelicLink, RelicPatient, ContactPointUse, PatientEverything } from "relic-ui";
import { formatHumanName } from "@medplum/core";
import { Location } from "relic-ui";
import lodashPackage from "lodash";
import * as Handlebars from 'handlebars';
import { formatDistanceToNowStrict } from 'date-fns';
import config from 'config';

const { startCase } = lodashPackage;
/**
 * Converts a RelicPatient object to a FHIR Patient object.
 * @param {RelicPatient} relicPatient - The RelicPatient object to convert.
 * @returns {Patient} - The converted FHIR Patient object.
 */
export function relicPatientToFhirPatient (relicPatient: RelicPatient, organization: RelicOrganization): Patient {
  const telecom: ContactPoint[] = [];
  // eslint-disable-next-line prefer-const
  let { name, gender, email, mobilePhone, homePhone, birthDate, maritalStatus, communicationLanguage, primaryLanguage } = relicPatient;
  communicationLanguage = communicationLanguage ? communicationLanguage : [organization.fhirStore?.defaultLanguage] as RelicCommunicationLanguage[];
  const [firstName, lastName] = name?.split(' ') || [];
  const patientData: Patient = {
    id: relicPatient.id,
    resourceType: 'Patient',
    active: relicPatient.active,
    name: [
      {
        use: 'official',
        family: lastName,
        given: [firstName]
      }
    ],
    gender: transformGender(gender || 'Unknown' ),
    managingOrganization: {
      reference: `Organization/${organization.id}`,
      display: organization.name
    },
    communication: [
      {
        language: {
          coding: [
            {
              system: primaryLanguage?.system || organization.fhirStore?.defaultLanguage.system,
              code: primaryLanguage?.code || organization.fhirStore?.defaultLanguage.code,
              display: primaryLanguage?.display || organization.fhirStore?.defaultLanguage.display
            }
          ]
        },
        preferred: true
      }
    ],
  };

  if (email) {
    telecom.push({ system: 'email', value: email, use: 'home' });
  }
  if (mobilePhone) {
    telecom.push({ system: 'phone', value: mobilePhone, use: 'mobile' });
  }
  if (homePhone) {
    telecom.push({ system: 'phone', value: homePhone, use: 'home' });
  }
  patientData.telecom = telecom;
  if (birthDate) {
    patientData.birthDate = birthDate;
  }
  if (maritalStatus) {
    // Marital status code
    const maritalStatusCode = getMaritalStatusCode(maritalStatus);
    patientData.maritalStatus = {
      coding: [
        {
          system: 'http://terminology.hl7.org/CodeSystem/v3-MaritalStatus',
          code: maritalStatusCode as string,
          display: maritalStatus
        }
      ]
    }
  }
  return patientData;
}

/**
 * Converts a RelicLink object to a FHIR RelatedPerson object.
 *
 * This function transforms a custom RelicLink object into a FHIR RelatedPerson resource.
 *
 * @param {RelicLink} relicLink - The RelicLink object containing the information to be converted into a FHIR RelatedPerson.
 * @param {string} patientId - The identifier of the patient to whom the related person is associated.
 * @returns {RelatedPerson} - The constructed FHIR RelatedPerson object.
 *
 */
export function relicLinkToRelatedPerson(relicLink: RelicLink, patientId:string): RelatedPerson{
  const resource:RelatedPerson = {

    "resourceType": "RelatedPerson",
    "patient": {
      "reference": `Patient/${patientId}`
    },
  };

  if (relicLink.name) {
    const [firstName, lastName] = relicLink.name.split(' ');
    resource.name = [{
      "use": "official",
      "family": lastName,
      "given": [firstName]
    }];
  }

  if (relicLink.relationship) {
    resource.relationship = [
      {
        "coding": [
          {
            "system": "http://terminology.hl7.org/CodeSystem/v3-RoleCode",
            "code": relicLink.relationship.code,
            "display": relicLink.relationship.display
          }
        ]
      }
    ];
  }

  const telecom: ContactPoint[] = [];

  if (relicLink.email) {
    telecom.push({ system: 'email', value: relicLink.email, use: relicLink.use as ContactPointUse });
  }

  if (relicLink.mobilePhone) {
    telecom.push({ system: 'phone', value: relicLink.mobilePhone, use: 'mobile' });
  }

  if (relicLink.homePhone) {
    telecom.push({ system: 'phone', value: relicLink.homePhone, use: 'home' });
  }

  resource.telecom = telecom;

  if (relicLink.gender) {
    resource.gender = transformGender(relicLink.gender);
  }

  if (relicLink.birthDate) {
    resource.birthDate = relicLink.birthDate;
  }

  return resource;

}

/**
 * Converts a FHIR RelatedPerson object to a RelatedPerson object.
 *
 *
 * @param {RelatedPerson} relatedPerson - The FHIR RelatedPerson object to be converted.
 * @returns {RelatedPerson} - The converted RelatedPerson object.
 *
 */
export function relatedPersonToRelicRelatedPerson(relatedPerson:RelatedPerson): RelicLink | null {
  if(!relatedPerson) return null;
  if (!relatedPerson.name) {
    throw new Error('Related person name not found.');
  }
  const link:RelicLink = {
    id: relatedPerson.id,
    resourceType: relatedPerson.resourceType || '',
    relationship: {
      system: relatedPerson.relationship?.[0]?.coding?.[0]?.system || '',
      code: relatedPerson.relationship?.[0]?.coding?.[0]?.code || '',
      display: relatedPerson.relationship?.[0]?.coding?.[0]?.display || ''
    },
    use: relatedPerson.name?.[0]?.use || '',
    name: formatHumanName(relatedPerson.name[0]),
    email: '',
    mobilePhone: '',
    homePhone: '',
    gender: relatedPerson.gender,
    birthDate: relatedPerson.birthDate || ''
  };

  if (relatedPerson.telecom) {
    relatedPerson.telecom.forEach(t => {
      if (t.system === 'email') {
        link.email = t.value;
      } else if (t.system === 'phone') {
        if (t.use === 'mobile') {
          link.mobilePhone = t.value;
        } else if (t.use === 'home') {
          link.homePhone = t.value;
        }
      }
    });
  }

  return link;
}


/**
 * Converts a FHIR Patient object to a RelicPatient object.
 * @param {PatientEverything} patientEverything - The FHIR Patient object to convert.
 * @param {RelicCommunicationLanguage} defaultLanguage - The default language to use for communication.
 * @returns {RelicPatient} - The converted RelicPatient object.
 */
export function patientToRelicPatient(patientEverything: PatientEverything, organization: RelicOrganization): RelicPatient {
  if (!patientEverything) {
    return null;
  }
  const name: string = formatHumanName(patientEverything.Patient.name[0]);
  if (!name) {
    throw new Error('Patient name not found.');
  }
  const email: string = patientEverything.Patient.telecom?.find(t => t.system === "email")?.value || "";
  const mobilePhone: string = patientEverything.Patient.telecom?.find(t => t.system === "phone" && t.use === "mobile")?.value || "";
  const homePhone: string = patientEverything.Patient.telecom?.find(t => t.system === "phone" && t.use === "home")?.value || "";
  const defaultLanguage: RelicCommunicationLanguage = organization.fhirStore?.defaultLanguage;
  const communicationLanguage: RelicCommunicationLanguage[] = patientEverything.Patient.communication != undefined ? patientEverything.Patient.communication.map(comm => communicationLanguageToRelicCommunicationLanguage(comm)) : [defaultLanguage];
  const primaryLanguage: RelicCommunicationLanguage = communicationLanguage.find(comm => comm.preferred) || communicationLanguage[0];
  const relicPatient: RelicPatient = {
    id: patientEverything.Patient.id ?? '',
    resourceType: patientEverything.Patient.resourceType,
    organizationId: patientEverything.Patient.managingOrganization?.reference?.replace('Organization/', '') ?? '',
    active: patientEverything.Patient.active || false,
    name: name,
    email: email,
    emailVerified: true,
    mobilePhone: mobilePhone,
    homePhone: homePhone,
    gender: startCase(patientEverything.Patient.gender),
    birthDate: patientEverything.Patient.birthDate ?? '',
    maritalStatus: getMaritalStatusDisplay(patientEverything.Patient?.maritalStatus?.coding?.[0]?.code || 'UNK'),
    communicationLanguage: communicationLanguage,
    managingOrganization: patientEverything.Patient.managingOrganization ?? {},
    goal: patientEverything.goal ?? '',
    expectation: patientEverything.expectation ?? '',
    primaryLanguage: primaryLanguage,
    patientStatus: patientEverything.Patient.active ? 'Current' : 'Discharged',
    bedLocation: '',
    communicationIdentities: patientEverything.communicationIdentities,
    summary: '',
    weight: '',
    height: '',
  };
  return relicPatient;
}

const MaritalStatus = {
  A: 'Annulled',
  D: 'Divorced',
  I: 'Interlocutory',
  L: 'Legally Separated',
  M: 'Married',
  P: 'Polygamous',
  S: 'Never Married',
  T: 'Domestic Partner',
  U: 'Unmarried',
  W: 'Widowed',
  UNK: 'Unknown',
} as const;

type MaritalStatusType = typeof MaritalStatus[keyof typeof MaritalStatus];

/**
 * Retrieves the display string corresponding to a marital status code.
 * The function looks up a given code in MaritalStatus object.
 * If the code is not found, the function returns null.
 *
 * @param {string} code - The marital status code to be looked up.
 * @returns {MaritalStatusType|null} The display string corresponding to the code, or null if the code is not found.
 */
function getMaritalStatusDisplay(code: string): MaritalStatusType | null {
  code = code.toUpperCase();
  return MaritalStatus[code as keyof typeof MaritalStatus] || null;
}

const MaritalStatusCodes = {
  Annulled: 'A',
  Divorced: 'D',
  Interlocutory: 'I',
  'Legally Separated': 'L',
  Married: 'M',
  Polygamous: 'P',
  'Never Married': 'S',
  'Domestic Partner': 'T',
  Unmarried: 'U',
  Widowed: 'W',
  Unknown: 'UNK',
};




/**
 * Converts a FHIR Condition response to a RelicCondition format.
 *
 * @param {Object} conditionResponse - The FHIR Condition response object to be converted.
 * @returns {Object} A RelicCondition object.
 */
export function conditionToRelicCondition(conditionResponse: Condition): RelicCondition{
  return {
    id: conditionResponse.id!,
    resourceType: conditionResponse.resourceType,
    clinicalStatus: conditionResponse?.clinicalStatus?.coding?.[0]?.display ?? "",
    verificationStatus: conditionResponse?.verificationStatus?.coding?.[0]?.display ?? "",
    code: {
      text: conditionResponse?.code?.coding?.[0]?.display ?? ""
    },
    onsetDateTime: conditionResponse?.onsetDateTime ?? "",
    abatementDateTime: conditionResponse?.abatementDateTime ?? "",
    note: conditionResponse?.note?.[0]?.text ?? ""
  }

}

/**
 * Converts a communication language object from FHIR format to a RelicCommunicationLanguage format.
 *
 * This function takes a communication object from a FHIR Patient resource and translates it into the
 * RelicCommunicationLanguage format. It extracts the system, code, and display properties from the
 * language coding, and also determines the preferred status of the communication language.
 *
 * @param {Object} communication - A communication object from a FHIR Patient resource.
 * @returns {RelicCommunicationLanguage} - The converted communication language object in RelicCommunicationLanguage format.
 *
 */
export function communicationLanguageToRelicCommunicationLanguage(communication:FHIRCommunication):RelicCommunicationLanguage{

  return {
    system: communication.language?.coding?.[0]?.system || '',
    code: communication.language?.coding?.[0]?.code || '',
    display: communication.language?.coding?.[0]?.display || '',
    preferred: communication.preferred || false
  };
}

/**
 * Initializes an Encounter FHIR resource based on the provided location and patient information.
 *
 * @param {Location} location - The location details for the encounter. Facility location where patient is admitted.
 * @param {RelicPatient} patient - The patient
 * @returns {Encounter} - A FHIR Encounter resource.
 *
 */
export function initializeFhirEncounter(location: Location, patient: RelicPatient): Encounter {
  const fhirUrl: string = new URL('/fhir/us/core/StructureDefinition/us-core-encounter', config.get('FHIR.URL')).toString();
  const encounter: Encounter = {
    resourceType: 'Encounter',
    meta: {
      profile: [
        fhirUrl
      ]
    },
    status: 'in-progress',
    class: {
      system: 'http://terminology.hl7.org/CodeSystem/v3-ActCode',
      code: "IMP",
      display: "inpatient encounter"
    },
    priority: {
      coding: [
        {
          system: "http://terminology.hl7.org/CodeSystem/v3-ActPriority",
          code: "EL",
          display: "elective"
        }
      ]
    },
    subject: {
      reference: `Patient/${patient.id}`,
      display: `${patient.name}`
    }
  };

  encounter.location = [
    {
      location: {
        reference: `Location/${location.id}`,
        display: location.name,
      },
      status: 'active',
    },
  ];

  return encounter;

}

/**
 * Transforms a RelicCondition object into a FHIR Condition resource.
 *
 * This function converts a condition from  RelicCondition format into FHIR Condition format.
 *
 * @param {RelicCondition} relicCondition - The RelicCondition object to be converted.
 * @param {RelicPatient} relicPatient - Relic Patient to which condition belongs.
 * @param {string} encounterId - The encounter's unique identifier associated with the condition.
 * @returns {Condition} - A FHIR Condition resource.
 *
 */
export function relicConditionToCondition(relicCondition: RelicCondition, patientEverything: PatientEverything): Condition {

  const condition: Condition = {
    resourceType: 'Condition',
    subject: {
      reference: `Patient/${patientEverything.Patient.id}`,
      display: `${formatHumanName(patientEverything.Patient.name[0])}`
    },
    encounter: {
      "reference": `Encounter/${patientEverything.Encounter.id}`,
    },
    onsetDateTime: relicCondition.onsetDateTime,
    abatementDateTime: relicCondition.abatementDateTime,
    clinicalStatus: {
      coding: [
        {
          system: 'http://terminology.hl7.org/CodeSystem/condition-clinical',
          code: relicCondition.clinicalStatus,
          display: relicCondition.clinicalStatus
        }
      ]
    },
    verificationStatus: {
      coding: [
        {
          system: 'http://terminology.hl7.org/CodeSystem/condition-ver-status',
          code: relicCondition.verificationStatus,
          display: relicCondition.verificationStatus
        }
      ]
    }

  };

  if(relicCondition.code){
    condition.code = {
      coding: [{
        display: relicCondition.code.text
      }]
    }
  }
  if (relicCondition.note) {
    condition.note = [
      {
        text: relicCondition.note
      }
    ];
  }

  return condition;
}

/**
 * Retrieves the corresponding code for a given marital status description.
 *
 * looks up the corresponding code in the
 * `MaritalStatusCodes` object. If the description does not have a corresponding code, the function
 * returns null.
 *
 * @param {string} description - The marital status description to be looked up.
 * @returns {string | null} - The corresponding marital status code, or null if not found.
 */
function getMaritalStatusCode(description: string): string | null {
  const formattedDescription = startCase(description);
  return MaritalStatusCodes[formattedDescription as keyof typeof MaritalStatusCodes] || null;
}

/**
 * Transforms a gender string to a lowercase gender type.
 *
 * This function takes a gender string (e.g., 'Male', 'Female', 'Other', 'Unknown'),
 * converts it to lowercase, and returns it as a `Gender` type. This ensures that
 * the gender is in a consistent format (all lowercase) for further processing.
 *
 * @param {string} gender - The gender string to be transformed.
 * @returns {Gender} - The transformed gender as a lowercase `Gender` type.
 */
function transformGender(gender: string): Gender {
  return gender.toLowerCase() as Gender;
}


/**
 * Create a patient summary using a template and patient data.
 *
 * This function takes a Handlebars template string and a patient object, along with a list of conditions,
 * and returns a formatted string representing the patient summary. The patient's details are inserted
 * into the template at the corresponding placeholders.
 *
 * @param {RelicOrganization} organization - The organization for which the patient summary is being generated.
 * @param {RelicPatient} patient - The patient object.
 * @returns {string} Patient Summary.
 *
 */
export function getPatientSummary(organization: RelicOrganization, patient: RelicPatient): string{
  const template: string = organization.template?.patientSummary || '';
  if (!template) {
    throw new Error('Patient summary template not found.');
  }
  const templateString = Handlebars.compile(template);  
  const replace = {
    name: patient.name,
    sex: patient.gender as string,
    age: calculateAge(patient.birthDate!),
    conditions: getConditions(patient.Condition) ,
    facilityName: patient?.managingOrganization?.display,
    height: patient.height,
    weight: patient.weight,
    goal: patient.goal,
    expectation: patient.expectation
  }
  const result = templateString(replace);
  return result;
}


/**
 * Calculates the age of a person based on their date of birth.
 *
 * @param {string} dob - The date of birth.
 * @returns {string} The age calculated as a string. If 'dob' is invalid, an empty string is returned.
 *
 */
function calculateAge(dateOfBirth: string): string {
  return dateOfBirth
    ? formatDistanceToNowStrict(new Date(dateOfBirth), {
      addSuffix: false,
    })
    : '';
}

/**
 * Gets the condition description from an array of condition objects
 *
 *
 * @param {RelicCondition[]} conditions - An array of condition objects associated with a patient.
 * @returns {string} A comma-separated string listing all condition texts. If there are no conditions
 *                   or all conditions are empty, it returns an empty string.
 *
 */
function getConditions(conditions: RelicCondition[]): string{
  const condition = conditions?.map(condition => condition?.code?.text)
    .filter(text => text !== '');
  return condition?.join(', ').replace(/,*$/, "");
}

