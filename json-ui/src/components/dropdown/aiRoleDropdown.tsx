import React from 'react';
import { RelicAgent } from 'relic-ui';
import { Controller } from 'react-hook-form';
import { useAutocomplete } from '@refinedev/mui';

import { TextField, Autocomplete } from '@mui/material';

interface AiParticipantRoleDropdownProps {
  control: any;
  name: string;
  label: string;
  value: string;
  error: boolean;
  helperText: string;
  onChange?: (value: RelicAgent | null) => void;
  disabled?: boolean;
  organizationId?: string | null;
  currentPatientId?: string;
  setSelectedAgent?: React.Dispatch<React.SetStateAction<RelicAgent | null>>;
}

const AiParticipantRoleDropdown: React.FC<AiParticipantRoleDropdownProps> = ({
  control,
  name,
  label,
  value,
  error,
  helperText,
  onChange,
  disabled,
  organizationId,
  currentPatientId,
  setSelectedAgent,
}) => {
  const { autocompleteProps: agentsAutocompleteProps } = useAutocomplete({
    resource: 'agents',
    filters: [
      {
        field: 'organizationId',
        value: organizationId,
        operator: 'eq',
      },
      {
        field: 'type',
        value: currentPatientId ? 'Patient Agent' : 'Staff Agent',
        operator: 'eq',
      },
    ],
    queryOptions: {
      queryKey: ['agents-autocomplete', organizationId],
      enabled: !!organizationId,
    },
  });

  return (
    <Controller
      control={control}
      name={name}
      defaultValue={value}
      rules={{ required: 'This field is required' }}
      render={({ field }) => {
        const { onChange: fieldOnChange, value: fieldValue } = field;
        return (
          <Autocomplete
            loading={agentsAutocompleteProps.loading}
            options={agentsAutocompleteProps.options}
            onInputChange={agentsAutocompleteProps.onInputChange}
            disabled={disabled}
            autoHighlight
            getOptionLabel={(option: RelicAgent) => {
              return agentsAutocompleteProps.options?.find(
                (agent: RelicAgent) => agent.id === option.id,
              )?.role?.['display-role'];
            }}
            isOptionEqualToValue={(option, value) => {
              return (
                value === undefined ||
                option?.id?.toString() === (value?.id ?? value).toString()
              );
            }}
            onChange={(_, newValue) => {
              fieldOnChange(newValue && newValue.id ? newValue.id : null);
              if (onChange) {
                onChange(newValue ? newValue : null);
              }
              if (setSelectedAgent) {
                setSelectedAgent(newValue ? newValue : null);
              }
            }}
            renderInput={params => (
              <TextField
                {...params}
                disabled={disabled}
                label={label}
                variant="outlined"
                error={error}
                helperText={helperText}
                required
              />
            )}
            value={
              agentsAutocompleteProps.options?.find(
                (agent: RelicAgent) => agent.id === fieldValue,
              ) || null
            }
          />
        );
      }}
    />
  );
};

export default AiParticipantRoleDropdown;
