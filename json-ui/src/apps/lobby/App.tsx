import React from 'react';
import { useTranslation } from 'react-i18next';
import 'react-toastify/dist/ReactToastify.css';
import { ToastContainer } from 'react-toastify';
import dataProvider from '@refinedev/simple-rest';
import { Route, Routes, Outlet } from 'react-router-dom';
import { Refine, CanAccess, Authenticated } from '@refinedev/core';
import { ErrorComponent, RefineSnackbarProvider } from '@refinedev/mui';
import routerBindings, {
  CatchAllNavigate,
  NavigateToResource,
  DocumentTitleHandler,
  UnsavedChangesNotifier,
} from '@refinedev/react-router-v6';

import ForumIcon from '@mui/icons-material/Forum';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';

import ThemeProvider from 'src/theme';
import { Login } from 'src/pages/login';
import { SearchProvider } from 'src/providers';
import { B2CLogin } from 'src/pages/lobby/login';
import { relicDataProvider } from 'src/providers';
import { Sandbox, EmbeddedCall } from 'src/pages/chat';
import { authProviderLobby as authProvider } from 'src/providers';
import { notificationProvider } from 'src/providers/notification-provider';
import { accessControlProvider } from 'src/providers/access-control-provider';

import { ThemedLayoutV2 } from 'src/components/layout';
import { ThemedTitleV2 } from 'src/components/layout/title';
import { DocumentTitle } from 'src/components/_documentTitle';
import { SimpleHeader } from 'src/components/header/simpleHeader';
import UnAuthorized from 'src/components/refine-customs/unauthorized';
import { OrganizationIcon } from 'src/components/icon/OrganizationIcon';

function App() {
  const { t, i18n } = useTranslation();

  const i18nProvider = {
    translate: (key: string, params: object) => t(key, params),
    changeLocale: (lang: string) => i18n.changeLanguage(lang),
    getLocale: () => i18n.language,
  };

  return (
    <ThemeProvider>
      <RefineSnackbarProvider>
        <Refine
          notificationProvider={notificationProvider}
          routerProvider={routerBindings}
          authProvider={authProvider}
          accessControlProvider={accessControlProvider}
          i18nProvider={i18nProvider}
          dataProvider={{
            default: relicDataProvider,
            json_server: dataProvider('https://relic-json-server.vercel.app'),
          }}
          resources={[
            {
              name: 'sandbox',
              meta: {
                label: t('chat.patientAi'),
                icon: <ForumIcon />,
              },
              list: '/sandbox',
            },
          ]}
          options={{
            syncWithLocation: true,
            useNewQueryKeys: true,
            projectId: 'uvSWGo-A848GS-kqC6S9',
            reactQuery: {
              clientConfig: {
                defaultOptions: {
                  queries: {
                    staleTime: 5 * 60 * 1000,
                    retry: false,
                  },
                },
              },
            },
          }}
        >
          <LocalizationProvider dateAdapter={AdapterDateFns}>
            <SearchProvider>
              <Routes>
                <Route
                  element={
                    <Authenticated
                      key="authenticated-inner"
                      fallback={<CatchAllNavigate to="/login" />}
                    >
                      <ThemedLayoutV2
                        Title={({ collapsed }) => (
                          <ThemedTitleV2
                            // collapsed is a boolean value that indicates whether the <Sidebar> is collapsed or not
                            collapsed={collapsed}
                            icon={<OrganizationIcon />}
                            text="Resident Lobby"
                          />
                        )}
                        Header={() => <SimpleHeader sticky />} //Header with search control
                      >
                        <CanAccess fallback={<UnAuthorized />}>
                          <Outlet />
                        </CanAccess>
                      </ThemedLayoutV2>
                    </Authenticated>
                  }
                >
                  <Route
                    index
                    element={<NavigateToResource resource="sandbox" />}
                  />
                  <Route path="sandbox">
                    <Route index element={<Sandbox />} />
                  </Route>
                  <Route path="embedded-call">
                    <Route index element={<EmbeddedCall />} />
                  </Route>

                  <Route path="*" element={<ErrorComponent />} />
                </Route>
                <Route
                  element={
                    <Authenticated
                      key="authenticated-outer"
                      fallback={<Outlet />}
                    >
                      <NavigateToResource />
                    </Authenticated>
                  }
                >
                  <Route path="/login" element={<B2CLogin />} />
                </Route>
              </Routes>
            </SearchProvider>
          </LocalizationProvider>

          <UnsavedChangesNotifier />
          <DocumentTitleHandler handler={DocumentTitle} />
          <ToastContainer />
        </Refine>
      </RefineSnackbarProvider>
    </ThemeProvider>
  );
}

export default App;
