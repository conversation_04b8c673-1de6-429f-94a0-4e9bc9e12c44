import { HttpError, useCustom } from '@refinedev/core';

interface HealthCheckWrapperProps {
  children: (props: {
    isLoading: boolean;
    isError: boolean;
    error: Error | HttpError | null;
    refetch: () => void;
  }) => React.ReactNode;
}

export const HealthCheckWrapper: React.FC<HealthCheckWrapperProps> = ({
  children,
}) => {
  const { isLoading, isError, error, refetch } = useCustom({
    url: 'health',
    method: 'get',
  });

  return (
    <>
      {children({
        isLoading,
        isError,
        error,
        refetch,
      })}
    </>
  );
};
