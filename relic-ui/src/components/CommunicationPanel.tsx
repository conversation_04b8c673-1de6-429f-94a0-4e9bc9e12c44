/* eslint-disable @typescript-eslint/no-explicit-any */
import Toolbar from './toolbar/Toolbar';
import { MessageThread, SendBox, TypingIndicator } from '@azure/communication-react';
import { CommunicationPanelProps } from '../types';
import { Box, Stack } from '@mui/material';
import { useCommunicationProps } from '../hooks/useCommunicationProps';
import { useThreadContext } from './RelicThreadProvider';
import { ThreadState } from '../types';

import ThreadStatus from './thread-status';

export const CommunicationPanel = ({
  pxAboveCommunicationPanel,
  toolbarProps,
  messageThreadStyles: messageThreadStylesProp,
  sendBoxStyles: sendBoxStylesProp,
}: CommunicationPanelProps) => {
  const { threadState, typingParticipants } = useThreadContext();
  const mergedMessageThreadStyles = {
    ...messageThreadStylesProp,
    systemMessageContainer: { marginTop: '10px', marginBottom: '10px' },
  };
  const mergedSendBoxStyles = { ...sendBoxStylesProp };

  const { messageThreadProps, sendBoxProps } = useCommunicationProps();

  return (
    <Stack
      sx={{ flexGrow: 1, height: `calc(${window.innerHeight}px - ${pxAboveCommunicationPanel}px)`, overflow: 'auto' }}
    >
      <Toolbar {...toolbarProps} />
      <Box sx={{ flexGrow: 1, overflow: 'auto' }}>
        <MessageThread {...messageThreadProps} styles={mergedMessageThreadStyles} />
      </Box>
      <Box sx={{ width: '100%' }}>
        {threadState === ThreadState.Idle ? (
          typingParticipants && <TypingIndicator typingUsers={typingParticipants} />
        ) : (
          <ThreadStatus />
        )}
      </Box>
      { threadState !== ThreadState.ReadOnly  && <SendBox {...sendBoxProps} styles={mergedSendBoxStyles} /> }
    </Stack>
  );
};
