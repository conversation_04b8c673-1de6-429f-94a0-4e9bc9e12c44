import { FastifyInstance, FastifyPluginOptions } from 'fastify';
import { IdentityServiceInterface } from './identity.interface';
import {
  IUser,
  IPortalIdentity,
  IUserIdentity,
  IdentityProvider,
  RelicPractitioner,
  CommunicationIdentity,
} from 'relic-ui';
import { SignPayloadType } from '@fastify/jwt';
import config from 'config';
import { Client } from '@microsoft/microsoft-graph-client';
import { ClientSecretCredential } from '@azure/identity';
import { TokenCredentialAuthenticationProvider } from '@microsoft/microsoft-graph-client/authProviders/azureTokenCredentials';
import { ObjectIdentity, PasswordProfile, User } from '@microsoft/microsoft-graph-types';

export class EntraExternalService implements IdentityServiceInterface {
  private fastify: FastifyInstance;
  private entraExternalClient: Client;

  provider: string = 'entra-external';
  issuerUrl: string = config.get('AZURE.AADB2C.ISSUER_URL');
  practitionerGroupId: string = config.get('AZURE.AADB2C.PRACTITIONER_GROUP_ID');
  graphMicrosoftUrl: string = config.get('AZURE.AADB2C.GRAPH_MICROSOFT_URL');

  constructor(fastify: FastifyInstance, options: FastifyPluginOptions) {
    this.fastify = fastify;
    this.entraExternalClient = this.initMSGraphClient();
  }

  private initMSGraphClient(): Client {
    const clientId: string = config.get('AZURE.AADB2C.CLIENT_ID');
    const tenantId: string = config.get('AZURE.AADB2C.TENANT_ID');
    const clientSecret: string = config.get('AZURE.AADB2C.CLIENT_SECRET');
    const graphMicrosoftUrl: string = config.get('AZURE.AADB2C.GRAPH_MICROSOFT_URL');
    const credential = new ClientSecretCredential(tenantId, clientId, clientSecret);
    const url: string = `${graphMicrosoftUrl}/.default`;
    const authProvider = new TokenCredentialAuthenticationProvider(credential, {
      // The client credentials flow requires that you request the
      // /.default scope, and pre-configure your permissions on the
      // app registration in Azure. An administrator must grant consent
      // to those permissions beforehand.
      scopes: [url],
    });

    const graphClient: Client = Client.initWithMiddleware({ authProvider: authProvider });
    return graphClient;
  }

  /**
   * Retrieve MS Graph User by email, mobilePhone or upn
   *
   */
  private async getMyInfo({
    email,
    mobilePhone,
    upn,
  }: {
    email?: string;
    mobilePhone?: string;
    upn?: string;
  }): Promise<User> {
    if (!email && !mobilePhone && !upn) {
      throw this.fastify.httpErrors.badRequest('Provide atleast one identifier: email, mobilePhone, upn');
    }
    const defaultFields: string[] = [
      'id',
      'displayName',
      'userPrincipalName',
      'mail',
      'mobilePhone',
      'employeeType',
      'companyName',
      'accountEnabled',
      'identities',
      'preferredLanguage',
    ];

    if (upn) {
      const user: User = await this.entraExternalClient.api(`/users/${upn}`).select(defaultFields).get();
      if (user) {
        return user;
      }
    }
    if (email) {
      const filterQuery = `identities/any(c:c/issuerAssignedId eq '${email}' and c/issuer eq '${this.issuerUrl}')`;
      const user: User = await this.entraExternalClient.api('/users').filter(filterQuery).select(defaultFields).get();
      if (user) {
        return user;
      }
    }

    if (mobilePhone) {
      const sanitizedPhoneNumber = this.fastify.formatPhoneNumber(mobilePhone);
      const filterQuery = `identities/any(c:c/issuerAssignedId eq '${encodeURIComponent(
        sanitizedPhoneNumber,
      )}' and c/issuer eq '${this.issuerUrl}')`;
      const user: User = await this.entraExternalClient.api('/users').filter(filterQuery).select(defaultFields).get();
      if (user) {
        return user;
      }
    }

    throw this.fastify.httpErrors.notFound('User not found');
  }

  private generateRandomPassword(length = 16) {
    const upperChars: string = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const lowerChars: string = 'abcdefghijklmnopqrstuvwxyz';
    const numbers: string = '0123456789';
    const specialChars: string = '@#$&%*';
    const allChars: string = upperChars + lowerChars + numbers + specialChars;

    let password: string = '';
    password += upperChars[Math.floor(Math.random() * upperChars.length)];
    password += lowerChars[Math.floor(Math.random() * lowerChars.length)];
    password += numbers[Math.floor(Math.random() * numbers.length)];
    password += specialChars[Math.floor(Math.random() * specialChars.length)];

    for (let i = 4; i < length; i++) {
      password += allChars[Math.floor(Math.random() * allChars.length)];
    }

    return password
      .split('')
      .sort(() => 0.5 - Math.random())
      .join('');
  }

  private generateUPN(provider: string, id: string): string {
    if (provider != this.provider) {
      throw this.fastify.httpErrors.badRequest(`provider "${provider}" not supported`);
    }
    return `${provider}.${id}@${this.issuerUrl}`;
  }

  private getIdFromUPN(upn: string): string {
    return upn.split('@')?.[0]?.split('.')?.at(-1) || '';
  }

  private getProviderFromUPN(upn: string): IdentityProvider {
    return upn.split('@')?.[0]?.split('.')?.at(0) as IdentityProvider;
  }

  public async whoAmI(tokenPayload: SignPayloadType): Promise<IUser> {
    if (!tokenPayload) {
      throw this.fastify.httpErrors.unauthorized('Missing or invalid login. Please login again.');
    }
    const { payload: decodedJwtToken } = tokenPayload as { header: object; payload: object };
    const { upn } = decodedJwtToken as {
      upn: string;
    };
    const myEntraProfile: User = await this.getMyInfo({ upn });
    const id: string = this.getIdFromUPN(upn);
    const provider: IdentityProvider = this.getProviderFromUPN(upn);

    let myRelicProfile = await this.fastify.getPractitioner(id);
    if (!myRelicProfile) {
      throw this.fastify.httpErrors.unauthorized('User not found');
    }
    if (!myRelicProfile?.enabled) {
      throw this.fastify.httpErrors.unauthorized('Your account has been disabled. Please contact your administrator.');
    }

    const myRelicOrganization = await this.fastify.orgService.getOrganization(myRelicProfile.organizationId);
    const orgDefaultLanguage =
      myRelicOrganization.supportedLanguages?.find((v) => v.preferred) ||
      myRelicOrganization.fhirStore?.defaultLanguage ||
      myRelicOrganization.supportedLanguages?.[0];

    const name =
      myEntraProfile.displayName || [myEntraProfile.givenName || '', myEntraProfile.surname || ''].join(' ').trim();

    const myPortalIdentity: IPortalIdentity = {
      email: myEntraProfile.mail || '',
      header: true,
      sider: true,
      name: name,
      organizationId: myRelicProfile.organizationId,
      companyId: 'deprecated',
      clientId: 'deprecated',
      mobilePhone: myEntraProfile.mobilePhone || '',
      preferredLanguage: {
        preferred: true,
        code: myEntraProfile.preferredLanguage || orgDefaultLanguage?.code || 'en-US',
        display: myEntraProfile.preferredLanguage || orgDefaultLanguage?.display || 'English',
      },
    };

    const communicationIdentities: CommunicationIdentity[] = [];
    if (myRelicProfile.communicationIdentities && myRelicProfile.communicationIdentities.length > 0) {
      const communicationIdentity = myRelicProfile.communicationIdentities[0];
      if (communicationIdentity) {
        const refreshedCommunicationIdentity = await this.fastify.acs.refreshIdentity(
          myRelicProfile,
          communicationIdentity,
        );
        communicationIdentities.push(refreshedCommunicationIdentity);
      }
    }
    const myUserIdentity: IUserIdentity = {
      id: id,
      resourceType: myEntraProfile.employeeType || 'Practitioner',
      email: myPortalIdentity.email,
      portalIdentity: myPortalIdentity,
      role: { name: 'member' },
      provider,
      communicationIdentities: communicationIdentities,
    };
    const me: IUser = {
      id: myUserIdentity.id,
      name: name,
      avatar: '',
      organizationId: myRelicProfile.organizationId,
      userIdentity: myUserIdentity,
      decodedJwtToken: tokenPayload as object,
    };
    return me;
  }

  public async getUser(practitioner: RelicPractitioner): Promise<User> {
    const upn = this.generateUPN(practitioner.provider, practitioner.id);
    const user: User = await this.getMyInfo({ upn });
    return user;
  }

  public async createUser(practitioner: RelicPractitioner): Promise<User> {
    const upn: string = this.generateUPN(practitioner.provider, practitioner.id);
    const identities: ObjectIdentity[] = [];
    if (practitioner.email) {
      identities.push({
        signInType: 'emailAddress',
        issuer: this.issuerUrl,
        issuerAssignedId: practitioner.email,
      });
    }
    if (practitioner.mobilePhone) {
      identities.push({
        signInType: 'phoneNumber',
        issuer: this.issuerUrl,
        issuerAssignedId: this.fastify.formatPhoneNumber(practitioner.mobilePhone),
      });
    }
    if (identities.length == 0) {
      throw this.fastify.httpErrors.badRequest('Provide atleast one identifier:[ email, mobilePhone ]');
    }

    const passwordProfile: PasswordProfile = {
      password: this.generateRandomPassword(),
      forceChangePasswordNextSignIn: false,
    };

    const user: User = {
      accountEnabled: true,
      userPrincipalName: upn,
      displayName: practitioner.name || practitioner.email,
      passwordProfile,
      identities,
      passwordPolicies: 'DisablePasswordExpiration',
      companyName: practitioner.organizationId,
      employeeType: 'Practitioner',
      mailNickname: upn.split('@')?.[0],
      mobilePhone: practitioner.mobilePhone,
    };

    const newEntraExternalPractitioner: User = await this.entraExternalClient.api('/users').post(user);
    await this.entraExternalClient.api(`/groups/${this.practitionerGroupId}/members/$ref`).post({
      '@odata.id': `${this.graphMicrosoftUrl}/v1.0/directoryObjects/${newEntraExternalPractitioner.id}`,
    });
    return user;
  }

  public async updateUser(practitioner: RelicPractitioner): Promise<User> {
    const userPrincipalName: string = this.generateUPN(practitioner.provider, practitioner.id);
    const existingUser: User = await this.getMyInfo({
      upn: userPrincipalName,
      mobilePhone: practitioner.mobilePhone,
      email: practitioner.email || '',
    });
    const userIdentities = [];
    if (existingUser && existingUser.userPrincipalName !== userPrincipalName) {
      throw this.fastify.httpErrors.conflict(
        'An account with this email or mobile phone already exists. Contact support for assistance.',
      );
    }
    if (practitioner.email) {
      existingUser.mail = practitioner.email;
      userIdentities.push({
        signInType: 'emailAddress',
        issuer: this.issuerUrl,
        issuerAssignedId: practitioner.email,
      });
    }
    if (practitioner.mobilePhone) {
      existingUser.mobilePhone = practitioner.mobilePhone
      userIdentities.push({
        signInType: 'phoneNumber',
        issuer: this.issuerUrl,
        issuerAssignedId: practitioner.mobilePhone,
      });
    }
    const practitionerToBeUpdated: User = {
      ...existingUser,
      displayName: practitioner.name || existingUser.displayName,
      identities: userIdentities,
    };
    const user = await this.entraExternalClient.api(`/users/${userPrincipalName}`).update(practitionerToBeUpdated);
    return user;
  }

  public async deleteUser(practitioner: RelicPractitioner): Promise<void> {
    const userPrincipalName: string = this.generateUPN(practitioner.provider, practitioner.id);
    await this.entraExternalClient.api(`/users/${userPrincipalName}`).delete();
  }
}
