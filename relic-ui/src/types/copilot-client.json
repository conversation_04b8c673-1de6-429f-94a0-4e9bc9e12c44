{"$schema": "http://json-schema.org/draft-07/schema#", "definitions": {"CopilotClient": {"title": "CopilotClient", "type": "object", "properties": {"companyId": {"type": "string", "title": "companyId"}, "createdAt": {"type": "string", "title": "createdAt"}, "customFields": {"$ref": "#/definitions/CustomFields", "title": "customFields"}, "email": {"type": "string", "title": "email"}, "familyName": {"type": "string", "title": "<PERSON><PERSON>ame"}, "firstLoginDate": {"type": "null", "title": "firstLoginDate"}, "givenName": {"type": "string", "title": "<PERSON><PERSON><PERSON>"}, "id": {"type": "string", "title": "id"}, "inviteUrl": {"type": "string", "title": "inviteUrl"}, "lastActiveDate": {"type": ["null", "string"], "title": "lastActiveDate"}, "lastLoginDate": {"type": ["null", "string"], "title": "lastLoginDate"}, "object": {"type": "string", "enum": ["client"], "title": "object"}, "status": {"$ref": "#/definitions/Status", "title": "status"}}, "required": ["companyId", "createdAt", "customFields", "email", "<PERSON><PERSON>ame", "firstLoginDate", "<PERSON><PERSON><PERSON>", "id", "inviteUrl", "lastActiveDate", "lastLoginDate", "object", "status"]}, "CustomFields": {"title": "CustomFields", "type": "object", "properties": {"guid": {"type": "string", "title": "guid"}, "phoneNumber": {"type": "string", "title": "phoneNumber"}, "title": {"type": "string", "title": "title"}}}, "Object": {"type": "string", "enum": ["client"]}, "Status": {"title": "Status", "enum": ["active", "invited", "notInvited"], "type": "string"}}}