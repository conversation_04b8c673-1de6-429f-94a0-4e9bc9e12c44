import { createPlay<PERSON><PERSON>outer, PlaywrightCrawling<PERSON>ontext, RouterH<PERSON><PERSON> } from "crawlee";
import { CrawlRequest } from "relic-ui";
import { RelicCrawlHandler } from "./handler.js";
import { v4 as uuidv4 } from 'uuid';
export class RelicCrawlRouter {
    private handler: RouterHandler<PlaywrightCrawlingContext>;
    private crawlHandler: RelicCrawlHandler;
    private request: CrawlRequest;

    /**
     * Create a new RelicCrawlRouter
     * @param request - Crawl request
     * @returns RelicCrawlRouter
    */
    public static async create(request: CrawlRequest): Promise<RelicCrawlRouter> {
        const router = new RelicCrawlRouter();
        router.request = request;
        await router.initialize();
        return router;
    }

    /**
     * Initialize the router
     */
    private async initialize() {
        try {
            this.crawlHandler = await RelicCrawlHandler.create(this.request);
            this.handler = createPlaywrightRouter();
            this.handler.addHandler('file', this.crawlHandler.handleFile.bind(this.crawlHandler));
            this.handler.addHandler('page', this.crawlHandler.handlePage.bind(this.crawlHandler));
        } catch (err) {
            throw new Error(`Error initializing router: ${err}`);
        }
    }

    /**
     * Get router handler
     * @returns Router handler
     */
    getHandler(): RouterHandler<PlaywrightCrawlingContext> {
        return this.handler;
    }

    /**
     * Cleanup resources
     */
    async cleanup() {
        await this.crawlHandler.cleanup();
    }

    /**
     * Pre navigation hook
     * @param ctx - PlaywrightCrawlingContext
     */
    async preNavigationHook(ctx: PlaywrightCrawlingContext) {
        const { log, page, request } = ctx;
        request.userData.id = request.userData.id || uuidv4();

        log.setOptions({ data: { processId: request.userData.id } });

        page.on('download', async (download) => {
            const filename = download.suggestedFilename()?.replace(/%20/g, ' ');
            request.userData.downloadInfo = {
                url: download.url(),
                filename
            }
            request.userData.isFileLink = true;
            request.skipNavigation = true;
        });

        page.on('response', async (response) => {
            if (response.url() != request.url) return;
            log.info('Response received.', { url: response.url(), status: response.status() });
            if (response.headers()['content-disposition']?.includes('filename')) {
                request.userData.isFileLink = true;
                request.skipNavigation = true;
                const filename = response.headers()['content-disposition']?.split('filename=')[1]?.replace(/"/g, '');
                const downloadLink = response.url();
                request.userData.downloadInfo = { filename, downloadLink };
            }
        });
    }

    /**
     * Error handler
     * @param ctx - PlaywrightCrawlingContext
     * @param error - Error
     */
    async errorHandler(ctx: PlaywrightCrawlingContext, error: Error) {
        await this.crawlHandler.errorHandler(ctx, error);
    }

    /**
     * Failed request handler
     * @param ctx - PlaywrightCrawlingContext   
     * @param error - Error
     */
    async failedRequestHandler(ctx: PlaywrightCrawlingContext, error: Error) {
        await this.crawlHandler.handleFailure(ctx, error);
    }

}
