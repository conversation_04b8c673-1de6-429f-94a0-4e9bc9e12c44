import * as React from 'react';
import { IMaskInput } from 'react-imask';

// ----------------------------------------------------------------------

export type TextMaskProps = {
  onChange: (event: { target: { name: string; value: string } }) => void;
  name: string;
};

const TextMask = React.forwardRef<HTMLInputElement, TextMaskProps>(
  function TextMask(props, ref) {
    const { onChange, ...other } = props;
    return (
      <IMaskInput
        {...other}
        mask="(+1)-************"
        definitions={{
          '#': /[1-9]/,
        }}
        inputRef={ref}
        onAccept={(value: any) =>
          onChange({ target: { name: props.name, value } })
        }
        overwrite
      />
    );
  },
);

export default TextMask;
