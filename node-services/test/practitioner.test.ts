import tap from "tap";
import { fastify } from "../index";
import { MedplumClient, MedplumClientOptions, EmailPasswordLoginRequest, ProfileResource } from '@medplum/core';
import { JSDOM } from 'jsdom';
import { Thread } from "../src/types/communication";
import { Agent } from "../src/types/relicAgent";
import { RelicPatient } from "@/types/relicPatient";
import { IUserIdentity } from "../src/types/portal";

// Create a new JSDOM instance
const { window } = new JSDOM();
// Mock global objects
global.window = window as unknown as Window & typeof globalThis;
global.document = window.document;
global.Element = window.Element;

const sessionStorageMock: Storage = {
  store: {} as Record<string, string>,
  getItem: function (key: string) {
    return this.store[key] || null;
  },
  setItem: function (key: string, value: string) {
    this.store[key] = value.toString();
  },
  removeItem: function (key: string) {
    delete this.store[key];
  },
  clear: function () {
    this.store = {};
  },
  get length() {
    return Object.keys(this.store).length;
  },
  key: function (index: number) {
    return Object.keys(this.store)[index] || null;
  },
};

global.sessionStorage = sessionStorageMock;


// Test Data for Practitioner Testing.
const practitionerEmail: string = "<EMAIL>";
const practitionerPwd: string = "Z1sZ9N3F8tcycDDK";
const practitionerId: string = "33af1448-85c6-4f61-9663-510e4641fbaf";
const organizationId: string = "7760ffcd-64d2-43df-9548-055b257dfaee"; //DEV - Sunrise Senior Living
const rogerRelicId: string = "56671912-90eb-4e43-ac4d-8af8291f1cbd"; //Roger - AI Practitioner
const rogerAcsId: string = "8:acs:8ca4fbc2-e63c-4283-a5b7-feb83074370e_0000001e-212e-fdec-28c5-593a0d002551"
const florenceRelicId: string = "00e5c4c4-2225-4da5-9b0f-cddc6aef726f"; //Florence - AI Practitioner
const englishText: string = "How are you?";
const frenchText: string = "Comment allez-vous ?";
const hindiText: string = "तुम कैसे हो?";

let accessToken: any = "";

const medplumOptions: MedplumClientOptions = {
  verbose: true,
};
const medplum: MedplumClient = new MedplumClient(medplumOptions);

const loginToMedplum = async (
  medplum: MedplumClient,
  email: string,
  password: string
): Promise<ProfileResource> => {
  const loginRequest: EmailPasswordLoginRequest = {
    email,
    password,
  };
  const loginResponse = await medplum.startLogin(loginRequest);
  const userProfile = await medplum.processCode(loginResponse.code as string);
  accessToken = medplum.getActiveLogin()?.accessToken;
  return userProfile;
};

tap.teardown(async () => {
  await fastify.close();
  process.exit(0);
}); //after running all tests
tap.before(async () => { //run before running all tests
  await loginToMedplum(medplum, practitionerEmail, practitionerPwd);
});

tap.test("requests the health check up", async (t: Tap.Test) => {
  const response = await fastify.inject({
    method: "GET",
    url: `/`,
    headers: ({ 
      'x-access-token': accessToken
    }),
  });
  
  t.equal(response.statusCode, 200);
  t.equal(response.body, 'Node Services');
});

tap.test("requests the `/api/practitioners/{id}/chat` with practitioner access-token", async (t: Tap.Test) => {
  const response = await fastify.inject({
    method: "GET",
    url: `/api/practitioners/${practitionerId}/chat`,
    headers: ({
      'x-organization-Id': `${organizationId}`,
      'x-access-token': accessToken
    }),
  });

  t.equal(response.statusCode, 200);
  
  // Parse the response body
  const responseBody = JSON.parse(response.body) as Thread;
  
  // Check if "type" in "threadSubject" is "Caregiver" or "Default"
  const threadSubjectType = responseBody.threadSubject.type;
  t.ok(threadSubjectType === "Default", 'Default practitioner thread Test - Thread type should be "Default"');
});

//Write a test for /api/agents using x-access-token
tap.test("request the `/api/agents/` route for default AI Agent", async (t) => {
  const response = await fastify.inject({
    method: "GET",
    url: `/api/agents/`,
    headers: ({
      'x-access-token': accessToken, 
      'x-organization-id': organizationId
    }),
  });

  t.equal(response.statusCode, 200);
});

//Write a test for /api/agents using x-access-token without org id
tap.test("request the `/api/agents/` route for default AI Agent without organization id", async (t) => {
  const response = await fastify.inject({
    method: "GET",
    url: `/api/agents/`,
    headers: ({
      'x-access-token': accessToken
    }),
  });

  t.equal(response.statusCode, 200, '/api/agents without org id parameters. Get all agents.');
});

//Write a test for /api/agents using x-access-token with org id
tap.test("request the `/api/agents/` route for default AI Agent with organization id", async (t) => {
  const response = await fastify.inject({
    method: "GET",
    url: `/api/agents/`,
    headers: ({
      'x-access-token': accessToken,
      'x-organization-id': organizationId
    }),
  });

  t.equal(response.statusCode, 200, '/api/agents with x-organization-id.');
});

//Write a test for /api/agents using rogerRelicId
tap.test("request the `/api/agents/${rogerRelicId}` route for Roger (AI Agent)", async (t) => {
  const response = await fastify.inject({
    method: "GET",
    url: `/api/agents/${rogerRelicId}`,
    headers: ({
      'x-access-token': accessToken,
      'x-organization-id': organizationId
    }),
  });

  t.equal(response.statusCode, 200);

  // Parse the response body
  const agent = JSON.parse(response.body) as Agent;
  t.ok(agent.id === rogerRelicId, 'Agent Test using rogerRelic Id - Agent id should be equal to rogerRelicId');
});

//Write a test for /api/agents using rogerAcsId
tap.test("request the `/api/agents/${rogerAcsId}` route for Roger (AI Agent) using rogerAcsId", async (t) => {
  const response = await fastify.inject({
    method: "GET",
    url: `/api/agents/${rogerAcsId}`,
    headers: ({
      'x-access-token': accessToken,
      'x-organization-id': organizationId
    }),
  });

  t.equal(response.statusCode, 200);

  // Parse the response body
  const agent = JSON.parse(response.body) as Agent;
  t.ok(agent.id === rogerRelicId, 'Agent Test using rogerAcsId - Agent id should be equal to rogerRelicId');
});

//Write a test for /api/translate route using Deepl - en to fr
tap.test("request the `/api/translate` route using Deepl - en to fr", async (t) => {
  const response = await fastify.inject({
    method: "POST",
    url: `/api/translate`,
    payload: {
      "text": [
        `${englishText}`
      ],
      "source_lang": "en",
      "target_lang": "fr"
    },
    headers: ({
      'x-access-token': accessToken,
      'x-organization-id': organizationId
    }),
  });

  t.equal(response.statusCode, 200);

  // Parse the response body
  const translatedText = JSON.parse(response.body);
  t.ok(translatedText['translation'] === frenchText,);
});

//Write a test for /api/translate route using Azure - en to hi
tap.test("request the `/api/translate` route using Azure - en to hi", async (t) => {
  const response = await fastify.inject({
    method: "POST",
    url: `/api/translate`,
    payload: {
      "text": [
        `${englishText}`
      ],
      "source_lang": "en",
      "target_lang": "hi"
    },
    headers: ({
      'x-access-token': accessToken,
      'x-organization-id': organizationId
    }),
  });

  t.equal(response.statusCode, 200);

  // Parse the response body
  const translatedText = JSON.parse(response.body);
  t.ok(translatedText['translation'] === hindiText,);
});

tap.test("requests the `/api/patients` route with medplum practitioner access-token", async (t: Tap.Test) => {
  const response = await fastify.inject({
    method: "GET",
    url: `/api/patients`,
    headers: ({
      'x-access-token': accessToken
    }),
  });

  t.equal(response.statusCode, 200);
  
  const responseBody = JSON.parse(response.body) as RelicPatient[];
  t.ok(responseBody.length > 0, 'patient list required');
  t.equal(0, responseBody.filter(p => !p.active)?.length, 'active patients only');
  t.equal(0, responseBody.filter(p => p.resourceType != 'Patient')?.length, 'Patient resources only');
});

tap.test("request whoami for medplum user", async (t: Tap.Test) => {
  const response = await fastify.inject({
    method: "GET",
    url: `/api/whoami`,
    headers: ({ 
      'x-access-token': accessToken
    }),
  });
  
  t.equal(response.statusCode, 200);
  
  const responseBody = JSON.parse(response.body) as IUserIdentity;
  t.equal(responseBody.provider, 'medplum','incorrect identity provider');
  t.equal(responseBody.resourceType, 'Practitioner','incorrect resourceType');
  t.ok(responseBody.communicationIdentities, 'communicationIdentities is required');
  t.ok(responseBody.id, 'id is required');
  t.ok(responseBody.portalIdentity, 'portalIdentity is required');
});