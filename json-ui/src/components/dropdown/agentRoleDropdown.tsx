import React from 'react';
import { Role } from 'relic-ui';
import { Control, Controller } from 'react-hook-form';

import TextField from '@mui/material/TextField';
import Autocomplete from '@mui/material/Autocomplete';

interface AgentRoleDropdownProps {
  control: Control<any>;
  name: string;
  label: string;
  value: Role | null;
  error: boolean;
  helperText: string;
  onChange?: (value: Role | null) => void;
  disabled?: boolean;
}

const assistantRoles: Role[] = [
  {
    'practitioner-role': 'Dietitian (occupation)',
    'display-role': 'Dietitian',
  },
  {
    'practitioner-role': 'Hospital Manager',
    'display-role': 'Facility Manager',
  },
  {
    'practitioner-role': 'Physiotherapist',
    'display-role': 'Physiotherapist',
  },
  {
    'practitioner-role': 'Social Worker',
    'display-role': 'Social Worker',
  },
];

export const AgentRoleDropDown: React.FC<AgentRoleDropdownProps> = ({
  control,
  name,
  label,
  value,
  error,
  helperText,
  onChange,
  disabled,
}) => {
  return (
    <Controller
      control={control}
      name={name}
      defaultValue={value}
      rules={{ required: 'This field is required' }}
      render={({ field }) => {
        const { onChange: fieldOnChange, value: fieldValue } = field;
        return (
          <Autocomplete
            options={assistantRoles}
            disabled={disabled}
            {...field}
            getOptionLabel={(option: Role) => option['display-role'] || ''}
            isOptionEqualToValue={(option, value) => {
              return option['display-role'] === value['display-role'];
            }}
            onChange={(_, newValue) => {
              fieldOnChange(newValue ? newValue : null);
              if (onChange) {
                onChange(newValue ? newValue : null);
              }
            }}
            renderInput={params => (
              <TextField
                {...params}
                disabled={disabled}
                label={label}
                variant="outlined"
                error={error}
                helperText={helperText}
                required
              />
            )}
            value={fieldValue || null}
          />
        );
      }}
    />
  );
};
