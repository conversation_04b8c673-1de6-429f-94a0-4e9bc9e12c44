{"pages": {"login": {"title": "Melden Sie sich bei Ihrem <PERSON> an", "signin": "Einloggen", "signup": "Anmelden", "divider": "oder", "fields": {"email": "Email", "password": "Passwort"}, "errors": {"validEmail": "Ungültige E-Mail-Adresse"}, "buttons": {"submit": "<PERSON><PERSON><PERSON><PERSON>", "forgotPassword": "Passwort vergessen?", "noAccount": "Sie haben kein Konto?", "rememberMe": "<PERSON><PERSON><PERSON> dich an mich"}}, "forgotPassword": {"title": "Haben Sie Ihr Passwort vergessen?", "fields": {"email": "Email"}, "errors": {"validEmail": "Ungültige E-Mail-Adresse"}, "buttons": {"submit": "Anweisungen zum Zurücksetzen senden"}}, "register": {"title": "Registrieren Sie sich für Ihr Konto", "fields": {"email": "Email", "password": "Passwort"}, "errors": {"validEmail": "Ungültige E-Mail-Adresse"}, "buttons": {"submit": "Registrieren", "haveAccount": "Ein Konto haben?"}}, "updatePassword": {"title": "Kennwort aktualisieren", "fields": {"password": "Neues Passwort", "confirmPassword": "Bestätige neues Passwort"}, "errors": {"confirmPasswordNotMatch": "Passwörter stimmen nicht überein"}, "buttons": {"submit": "Aktualisieren"}}, "error": {"info": "<PERSON>e haben vergessen, {{action}} component zu {{resource}} hinzufügen.", "404": "Leider existiert diese Seite nicht.", "resource404": "Haben <PERSON>e die {{resource}} resource erstellt?", "backHome": "Zurück"}}, "actions": {"list": "Aufführen", "create": "<PERSON><PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON>", "show": "Zeigen"}, "buttons": {"create": "<PERSON><PERSON><PERSON><PERSON>", "save": "Speichern", "logout": "Abmelden", "delete": "Löschen", "edit": "<PERSON><PERSON><PERSON>", "cancel": "Abbrechen", "confirm": "<PERSON><PERSON>?", "filter": "Filter", "clear": "Löschen", "refresh": "<PERSON><PERSON><PERSON><PERSON>", "show": "Zeigen", "undo": "Undo", "import": "Importieren", "clone": "Klon", "notAccessTitle": "Sie haben keine zugriffsberechtigung"}, "warnWhenUnsavedChanges": "Nicht gespeicherte Änderungen werden nicht übernommen.", "notifications": {"success": "Erfolg", "error": "<PERSON><PERSON> (status code: {{statusCode}})", "undoable": "Sie haben {{seconds}} Sekunden Zeit für Undo.", "createSuccess": "{{resource}} erfolgreich erstellt.", "createError": "<PERSON><PERSON> beim <PERSON> {{resource}} (status code: {{statusCode}})", "deleteSuccess": "{{resource}} erfolgreich gel<PERSON>t.", "deleteError": "<PERSON><PERSON> beim <PERSON> {{resource}} (status code: {{statusCode}})", "editSuccess": "{{resource}} erfolgreich bearbeitet.", "editError": "<PERSON><PERSON> beim <PERSON> {{resource}} (status code: {{statusCode}})", "importProgress": "{{processed}}/{{total}} importiert"}, "loading": "Wird geladen", "tags": {"clone": "Klon"}, "dashboard": {"title": "Dashboard"}, "blog_posts": {"blog_posts": "Blogbeiträge", "fields": {"id": "Id", "title": "Titel", "content": "Inhalt", "status": "Status", "createdAt": "Erstellt am", "category": "<PERSON><PERSON><PERSON>"}, "titles": {"create": "<PERSON><PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON>", "list": "Einträge", "show": "Eintrag zeigen"}}, "categories": {"categories": "<PERSON><PERSON><PERSON>", "fields": {"id": "Id", "title": "Titel", "createdAt": "Erstellt am"}, "titles": {"create": "<PERSON><PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON>", "list": "Einträge", "show": "Eintrag zeigen"}}, "documentTitle": {"default": "refine", "suffix": " | refine", "blog_posts": {"list": "Blogbeiträge | refine", "show": "#{{id}} Show Blogeintrag | refine", "edit": "#{{id}} Edit Blogeintrag | refine", "create": "Create new Blogeintrag | refine", "clone": "#{{id}} Clone Blogeintrag | refine"}, "categories": {"list": "Kategorien | refine", "show": "#{{id}} Zeigen Kategorien | refine", "edit": "#{{id}} Bearbeiten Kategorien | refine", "create": "Neue Kategorie <PERSON>n | refine", "clone": "#{{id}} Klon Kategorie | refine"}}, "table": {"actions": "Aktionen"}, "confirmTitle": {"delete.staff": "Stab löschen"}}