{"name": "{{indexName}}", "fields": [{"name": "id", "type": "Edm.String", "searchable": true, "filterable": true, "retrievable": true, "stored": true, "sortable": true, "facetable": true, "key": true, "analyzerName": "keyword", "synonymMaps": []}, {"name": "parent_id", "type": "Edm.String", "searchable": true, "filterable": true, "retrievable": true, "stored": true, "sortable": true, "facetable": true, "key": false, "analyzerName": "standard.lucene", "synonymMaps": []}, {"name": "content", "type": "Edm.String", "searchable": true, "filterable": false, "retrievable": true, "stored": true, "sortable": false, "facetable": false, "key": false, "analyzerName": "standard.lucene", "synonymMaps": []}, {"name": "filepath", "type": "Edm.String", "searchable": true, "filterable": true, "retrievable": true, "stored": true, "sortable": false, "facetable": false, "key": false, "synonymMaps": [], "analyzerName": "keyword"}, {"name": "title", "type": "Edm.String", "searchable": true, "filterable": true, "retrievable": true, "stored": true, "sortable": false, "facetable": false, "key": false, "analyzerName": "standard.lucene", "synonymMaps": []}, {"name": "url", "type": "Edm.String", "searchable": false, "filterable": false, "retrievable": true, "stored": true, "sortable": false, "facetable": false, "key": false, "synonymMaps": []}, {"name": "last_updated", "type": "Edm.String", "searchable": false, "filterable": false, "retrievable": true, "stored": true, "sortable": false, "facetable": false, "key": false, "synonymMaps": []}, {"name": "contentVector", "type": "Collection(Edm.Single)", "searchable": true, "filterable": false, "retrievable": true, "stored": true, "sortable": false, "facetable": false, "key": false, "vectorSearchDimensions": 1536, "vectorSearchProfileName": "default-profile", "synonymMaps": []}, {"name": "deleted", "type": "<PERSON><PERSON><PERSON>", "filterable": true, "retrievable": true}, {"name": "originalFilepath", "type": "Edm.String", "filterable": true, "retrievable": true, "searchable": true}], "scoringProfiles": [], "corsOptions": null, "suggesters": [], "analyzers": ["keyword"], "normalizers": [], "tokenizers": [], "tokenFilters": [], "charFilters": [], "encryptionKey": null, "semanticSearch": {"configurations": [{"name": "default", "prioritizedFields": {"titleField": {"name": "title"}, "contentFields": [{"name": "content"}], "keywordsFields": []}}]}, "vectorSearch": {"algorithms": [{"name": "default-hnsw", "kind": "hnsw", "hnswParameters": {"metric": "cosine", "m": 4, "efConstruction": 400, "efSearch": 1000}}], "profiles": [{"name": "default-profile", "algorithmConfigurationName": "default-hnsw", "vectorizerName": "default-text-vectorizer", "compressionName": null}], "vectorizers": [{"vectorizerName": "default-text-vectorizer", "kind": "azureOpenAI", "parameters": {"resourceUrl": "{{openai_endpoint}}", "deploymentId": "text-embedding-ada-002", "apiKey": "{{openai_api_key}}", "modelName": "text-embedding-ada-002", "authIdentity": null}, "customWebApiParameters": null, "aiServicesVisionParameters": null, "amlParameters": null}], "compressions": [{"compressionName": "use-scalar", "kind": "scalarQuantization", "scalarQuantizationParameters": {"quantizedDataType": "int8"}, "rerankWithOriginalVectors": true, "defaultOversampling": 10}, {"compressionName": "use-binary", "kind": "binaryQuantization", "rerankWithOriginalVectors": true, "defaultOversampling": 10}]}}