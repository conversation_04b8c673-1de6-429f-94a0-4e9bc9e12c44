import { Queue } from "bullmq";
import { RelicMessageQueueParams } from "relic-ui";

/**
 * Singleton Redis queue connection
 */
let redisQueue: Queue;

/**
 * Get redis queue connection
 * @param relicMessageQueueParams 
 * @returns redis queue
 */
export async function getRedisQueue(relicMessageQueueParams: RelicMessageQueueParams) {
    if (!redisQueue) {
        await initializeRedisQueue(relicMessageQueueParams);
    }
    return redisQueue;
}


/**
* Initialize redis queue
* Connect to existing Redis Queue identified by the queueName
* @param relicMessageQueueParams 
*/
async function initializeRedisQueue(relicMessageQueueParams: RelicMessageQueueParams) {
    if (!relicMessageQueueParams) {
        return;
    }
    const { queueName, host, port, password, tlsServername, maxRetriesPerRequest } = relicMessageQueueParams;
    if (!queueName || !host || !port || !password) {
        return;
    }
    redisQueue = new Queue(queueName, {
        connection: {
            port,
            host,
            password,
            tls: {
                servername: tlsServername || host,
            },
            connectTimeout: 20000,
            maxRetriesPerRequest: maxRetriesPerRequest || 3,
            retryStrategy: function (times: number) {
                return Math.max(Math.min(Math.exp(times), 20000), 1000);
            }
        },
        defaultJobOptions: {
            removeOnComplete: true,
            removeOnFail: true,
            attempts: maxRetriesPerRequest || 3,
            backoff: { type: 'exponential', delay: 500 }
        }
    });
}