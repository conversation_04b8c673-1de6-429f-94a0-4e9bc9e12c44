import { test, expect } from '@playwright/test';
import { common } from '../../../common';

test('Medplum-Login-Failure-Invalid-Username', async ({ page }) => {

    await page.goto(common.facilityPortal);
    await page.waitForURL(common.facilityPortal);
    await page.getByRole('link', { name: 'Medplum' }).click();
    await page.getByPlaceholder('<EMAIL>').fill('<EMAIL>');
    await page.getByRole('button', { name: 'Next' }).click();
    await page.getByLabel('Password *').click();
    await page.getByLabel('Password *').fill(common.medplumLoginCredentials.password);
    await page.getByRole('button', { name: 'Sign in' }).click();
    await expect(page.getByText('User not found')).toBeVisible();
    
  });


test('Medplum-Login-Failure-Invalid-Password', async ({ page }) => {

  await page.goto(common.facilityPortal);
  await page.getByRole('link', { name: 'Medplum' }).click();
  await page.getByPlaceholder('<EMAIL>').fill(common.medplumLoginCredentials.username);
  await page.getByRole('button', { name: 'Next' }).click();
  await page.getByLabel('Password *').click();
  await page.getByLabel('Password *').fill('INVALID_PASSWORD');
  await page.getByRole('button', { name: 'Sign in' }).click();
  await expect(page.getByText('Email or password is invalid')).toBeVisible();
 

  });

test('Medplum-Login-Successfully', async ({ page }) => {
    await page.goto(common.facilityPortal);
    await page.getByRole('link', { name: 'Medplum' }).click();
    await page.getByPlaceholder('<EMAIL>').fill(common.medplumLoginCredentials.username);
    await page.getByRole('button', { name: 'Next' }).click();
    await page.getByLabel('Password *').click();
    await page.getByLabel('Password *').fill(common.medplumLoginCredentials.password);
    await page.getByRole('button', { name: 'Sign in' }).click();
    await expect(page.getByRole('link', { name: 'Relic AI Portal' })).toBeVisible();
 });  