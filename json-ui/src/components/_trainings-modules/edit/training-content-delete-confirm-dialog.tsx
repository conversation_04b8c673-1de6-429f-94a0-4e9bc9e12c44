import { useTranslate } from '@refinedev/core';

import { Button, Dialog, DialogTitle, DialogActions } from '@mui/material';

interface Props {
  openDel: boolean;
  handleCloseDel: () => void;
  handleOnDeleteConfirm: () => void;
}

const TrainingContentDeleteConfirm = ({
  openDel,
  handleCloseDel,
  handleOnDeleteConfirm,
}: Props) => {
  const translate = useTranslate();
  return (
    <Dialog
      fullWidth
      maxWidth="xs"
      open={!!openDel}
      onClose={handleCloseDel}
      aria-labelledby="alert-dialog-title"
      aria-describedby="alert-dialog-description"
    >
      <DialogTitle id="alert-dialog-title">
        {translate('buttons.confirm', 'Are you sure?')}
      </DialogTitle>

      <DialogActions>
        <Button onClick={handleCloseDel} variant="outlined">
          {translate('buttons.cancel', 'Cancel')}
        </Button>
        <Button
          variant="contained"
          onClick={() => {
            handleOnDeleteConfirm();
            handleCloseDel();
          }}
        >
          {translate('buttons.delete', 'Delete')}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default TrainingContentDeleteConfirm;
