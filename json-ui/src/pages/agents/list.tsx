import React from 'react';
import { useDataGrid } from '@refinedev/mui';
import { IUser, RelicAgent, IUserIdentity } from 'relic-ui';
import { CrudFilter, useGetIdentity } from '@refinedev/core';

import { Box, Card } from '@mui/material';
import { GridColDef } from '@mui/x-data-grid-pro';

import { ListAgents } from 'src/components/_agent';

export const AgentsList = () => {
  const { data: currentUser } = useGetIdentity<IUser>();
  const currentIdentity: IUserIdentity =
    currentUser?.userIdentity as IUserIdentity;

  const currentRole: string = currentIdentity?.role?.name
    ? currentIdentity?.role?.name
    : 'member';

  const initialFilters: CrudFilter[] = React.useMemo(() => {
    const filter: CrudFilter = {
      field: 'organizationId',
      value: currentIdentity?.portalIdentity?.organizationId,
      operator: 'eq',
    };
    return [filter];
  }, [currentIdentity?.portalIdentity?.organizationId, currentRole]);

  const { dataGridProps: listAgentProps, setFilters } = useDataGrid<RelicAgent>(
    {
      resource: 'agents',
      sorters: {
        initial: [
          {
            field: 'name',
            order: 'asc',
          },
        ],
      },
      filters: {
        permanent: initialFilters,
      },
      queryOptions: {
        cacheTime: 0,
        staleTime: 0,
      },
    },
  );

  const agentsColumns = React.useMemo<GridColDef[]>(() => [], []);

  return (
    <Card>
      <Box sx={{ height: 'calc(100vh - 96px)', width: '100%' }}>
        <ListAgents
          {...listAgentProps}
          setFilters={setFilters}
          columns={agentsColumns}
        />
      </Box>
    </Card>
  );
};
