import { Test, TestingModule } from '@nestjs/testing';
import { ChatClientManager } from './chat-client-manager.service';
import { ConfigService } from '@nestjs/config';

jest.mock('@azure/communication-common', () => {
  return {
    AzureCommunicationTokenCredential: jest.fn().mockImplementation((token) => {
      return { token };
    }),
  };
});

jest.mock('@azure/communication-identity', () => {
  return {
    CommunicationIdentityClient: jest.fn().mockImplementation(() => {
      return {
        getToken: jest.fn().mockImplementation(() => {
          const randomToken = 'mock_token_' + Math.random().toString(36).substring(2, 15); // converts it to a base-36 alphanumeric string
          return Promise.resolve({
            token: randomToken,
            expiresOn: new Date(new Date().getTime() + 60 * 60 * 1000), // 1 hour from now
          });
        }),
      };
    }),
  };
});

jest.mock('@azure/communication-chat', () => {
  return {
    ChatClient: jest.fn().mockImplementation((endpointUrl, tokenCredential) => {
      const url = endpointUrl;
      const token = tokenCredential.token;
      return {
        url,
        token,
      };
    }),
  };
});

describe('ChatClientManager', () => {
  let service: ChatClientManager;
  let mockConfigService: ConfigService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ChatClientManager,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string) => {
              if (key === 'COMMUNICATION_SERVICES_CONNECTION_STRING') {
                return 'endpoint=https://mock_endpoint;accesskey=mock_key';
              }
            }),
          },
        },
      ],
    }).compile();

    service = module.get<ChatClientManager>(ChatClientManager);
    mockConfigService = module.get<ConfigService>(ConfigService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should initialize with connection string and endpoint', () => {
    expect(mockConfigService.get).toHaveBeenCalledWith('COMMUNICATION_SERVICES_CONNECTION_STRING');
  });

  it('should retrieve a new client if none is cached', async () => {
    const chatClient = await service.getChatClientForUser('user1');
    expect(chatClient).toBeDefined();
  });

  it('should return cached client if not expired', async () => {
    const firstClient = await service.getChatClientForUser('user1');
    const secondClient = await service.getChatClientForUser('user1');
    expect(secondClient).toBe(firstClient);
  });

  it('should return different clients for user1 and user2', async () => {
    const chatClientUser1 = await service.getChatClientForUser('user1');
    const chatClientUser2 = await service.getChatClientForUser('user2');
    expect(chatClientUser1).not.toBe(chatClientUser2);
  });
});
