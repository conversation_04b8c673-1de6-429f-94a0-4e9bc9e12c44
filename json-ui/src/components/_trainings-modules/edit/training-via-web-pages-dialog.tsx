import { useCreate, useShow, useTranslate } from '@refinedev/core';
import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useParams } from 'react-router-dom';

import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import {
  Alert,
  Box,
  Dialog,
  Stack,
  TextField,
  Typography,
} from '@mui/material';
import { Create } from 'src/components/refine-customs/create';

// URL validation regex
const URL_REGEX = /^https:\/\/[^\s]+$/;
const MAX_URLS_THRESHOLD = 100;

// Define URLLike type to match backend
interface URLLike {
  id?: string;
  url: string;
  sourceDocumentId?: string;
  children?: URLLike[];
}

const TrainingViaWebPagesDialog = ({
  open,
  onClose,
  refetchTrainingContent,
}: {
  open: boolean;
  onClose: VoidFunction;
  refetchTrainingContent: VoidFunction;
}) => {
  const [error, setError] = useState<string | null>(null);
  const [sitemapUrls, setSitemapUrls] = useState<URLLike[]>([]);
  const [rawUrls, setRawUrls] = useState<string>('');
  const [urlsExceedThreshold, setUrlsExceedThreshold] = useState(false);

  const { id } = useParams();
  const translate = useTranslate();

  const { query } = useShow({
    resource: 'trainings/modules',
    id: id,
  });

  const { mutate, isLoading } = useCreate({
    resource: `trainings/modules/${id}/contents`,
    invalidates: ['list'],
  });

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
  } = useForm({
    mode: 'all',
    reValidateMode: 'onChange',
    defaultValues: {
      folderTitle: '',
      webPages: '',
    },
  });

  // Reset state when dialog is closed
  useEffect(() => {
    if (!open) {
      setError(null);
      setSitemapUrls([]);
      setRawUrls('');
      setUrlsExceedThreshold(false);
      reset();
    }
  }, [open, reset]);

  // Parse urls on change
  useEffect(() => {
    if (rawUrls) {
      const urlList = rawUrls
        .split('\n')
        .map(url => url.trim())
        .filter(url => url !== '')
        .map(url => ({ url }));

      setSitemapUrls(urlList);
    }
  }, [rawUrls]);

  // Check if URLs exceed threshold
  useEffect(() => {
    setUrlsExceedThreshold(sitemapUrls.length > MAX_URLS_THRESHOLD);
  }, [sitemapUrls]);

  const handleUrlsChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    setRawUrls(event.target.value);
  };

  const validateUrls = async (urlList: URLLike[]): Promise<boolean> => {
    // Basic format validation
    for (const urlItem of urlList) {
      if (!URL_REGEX.test(urlItem.url)) {
        setError(`Invalid URL format: ${urlItem.url}`);
        return false;
      }
    }

    // Check for duplicates
    const uniqueUrls = new Set(urlList.map(item => item.url));
    if (uniqueUrls.size !== urlList.length) {
      setError('Duplicate URLs found. Please remove duplicates.');
      return false;
    }

    return true;
  };

  const onSubmit = async (data: any) => {
    setError(null);

    if (sitemapUrls.length === 0) {
      setError('Please provide at least one URL');
      return;
    }

    if (sitemapUrls.length > MAX_URLS_THRESHOLD) {
      setError(
        `Number of URLs exceeds the maximum limit of ${MAX_URLS_THRESHOLD}`,
      );
      return;
    }

    // Validate URLs
    const isValid = await validateUrls(sitemapUrls);
    if (!isValid) {
      return;
    }

    try {
      const containerName = query?.data?.data?.storage?.containerName;

      if (!containerName) {
        setError('Training module container not found.');
        return;
      }

      if (sitemapUrls.length === 0) {
        setError('Please provide at least one URL');
        return;
      }

      // First URL is always the default URL
      const mainUrl = sitemapUrls[0].url;

      const payloadData = {
        filename: data.folderTitle,
        type: 'folder',
        url: mainUrl,
        sitemap: sitemapUrls,
        crawler: {
          configuration: {
            maxRequestsPerCrawl: 100, // Default value
            useReadableContent: true, // Default value
            crawlEmbeddedLinks: false, // For folder type, we don't crawl embedded links
          },
        },
        splitRequests: sitemapUrls.length > 100, // Split if more than default maxRequestsPerCrawl
      };

      mutate(
        {
          values: payloadData,
          successNotification: () => ({
            message: `Successfully added Training Content`,
            type: 'success',
          }),
          errorNotification: (error: any) => ({
            message: `Error adding Training Content: ${error.message}`,
            type: 'error',
          }),
        },
        {
          onSuccess: () => {
            refetchTrainingContent();
            handleClose();
          },
          onError: (error: any) => {
            setError(
              error?.message ||
                'An error occurred while adding training content',
            );
          },
        },
      );
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    }
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  return (
    <Dialog
      fullWidth
      maxWidth="md"
      open={open}
      onClose={handleClose}
      aria-labelledby="alert-dialog-title"
      aria-describedby="alert-dialog-description"
    >
      <Create
        isLoading={isSubmitting || isLoading}
        title={translate('trainings-content.titles.add-via-web-pages')}
        onClose={handleClose}
        saveButtonProps={{
          onClick: handleSubmit(onSubmit),
          disabled: isSubmitting || isLoading,
          loading: isSubmitting || isLoading,
        }}
      >
        <Typography sx={{ typography: 'subtitle3', mb: 2 }}>
          {translate('trainings-content.subtitles.add-via-web-pages')}
        </Typography>
        <Stack spacing={2.5} component="form" autoComplete="off">
        {error && (
          <Alert severity="error">
            {error.split('\n').map((line, index) => (
              <span key={index}>
                {line}
                <br />
              </span>
            ))}
          </Alert>
        )}

          <TextField
            {...register('folderTitle', {
              required: 'This field is required',
              maxLength: {
                value: 200,
                message: 'Title cannot exceed 200 characters',
              },
            })}
            error={!!errors.folderTitle}
            helperText={errors.folderTitle?.message as string}
            fullWidth
            required
            label={translate('trainings-content.fields.folder-title')}
            name="folderTitle"
          />

          <Box>
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'space-between',
                mb: 1,
              }}
            >
              <Typography variant="subtitle1">
                {translate('trainings-content.fields.web-pages')}
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                {urlsExceedThreshold ? (
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      color: 'error.main',
                    }}
                  >
                    <ErrorOutlineIcon fontSize="small" sx={{ mr: 0.5 }} />
                    <Typography variant="caption">
                      {`Exceeds limit of ${MAX_URLS_THRESHOLD} URLs`}
                    </Typography>
                  </Box>
                ) : (
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      color: 'success.main',
                    }}
                  >
                    <CheckCircleOutlineIcon fontSize="small" sx={{ mr: 0.5 }} />
                    <Typography variant="caption">
                      {`${sitemapUrls.length} URLs (max ${MAX_URLS_THRESHOLD})`}
                    </Typography>
                  </Box>
                )}
              </Box>
            </Box>

            <TextField
              {...register('webPages')}
              fullWidth
              multiline
              rows={5}
              onChange={handleUrlsChange}
              placeholder="Enter URLs, one per line"
              error={urlsExceedThreshold}
              helperText={
                urlsExceedThreshold
                  ? `Number of URLs exceeds the maximum limit of ${MAX_URLS_THRESHOLD}`
                  : 'Enter one URL per line'
              }
              sx={{
                '& .MuiOutlinedInput-root': {
                  fontFamily: 'monospace',
                },
              }}
            />
          </Box>
        </Stack>
      </Create>
    </Dialog>
  );
};

export default TrainingViaWebPagesDialog;
