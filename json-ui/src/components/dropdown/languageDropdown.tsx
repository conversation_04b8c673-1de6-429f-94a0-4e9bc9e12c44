import React, { useState, useEffect } from 'react';
import { Control, Controller } from 'react-hook-form';
import { availableLanguages, RelicCommunicationLanguage } from 'relic-ui';

import TextField from '@mui/material/TextField';
import Autocomplete from '@mui/material/Autocomplete';

interface LanguageDropdownProps {
  control: Control<any>;
  name: string;
  label: string;
  value: RelicCommunicationLanguage | null;
  error: boolean;
  helperText: string;
  onChange?: (value: RelicCommunicationLanguage | null) => void;
  disabled?: boolean;
}

const placeholder: RelicCommunicationLanguage = {
  display: 'Select Language',
  code: '',
  system: '',
};

export const LanguageDropdown: React.FC<LanguageDropdownProps> = ({
  control,
  name,
  label,
  value,
  error,
  helperText,
  onChange,
  disabled,
}) => {
  const [dropdownLanguages, setDropdownLanguages] = useState<
    RelicCommunicationLanguage[]
  >([]);

  useEffect(() => {
    if (
      value &&
      availableLanguages.some(
        (lang: RelicCommunicationLanguage) => lang.code === value.code,
      )
    ) {
      setDropdownLanguages([placeholder, ...availableLanguages]);
    } else {
      // Merge the missing language with the available languages
      const mergedLanguages = [
        placeholder,
        ...availableLanguages,
        {
          display: value?.display,
          code: value?.code,
          system: value?.system,
        },
      ];
      setDropdownLanguages(mergedLanguages);
    }
  }, [value]);

  return (
    <Controller
      control={control}
      name={name}
      defaultValue={value}
      rules={{ required: 'This field is required' }}
      render={({ field }) => {
        const { onChange: fieldOnChange, value: fieldValue } = field;
        return (
          <Autocomplete
            options={dropdownLanguages}
            disabled={disabled}
            {...field}
            getOptionLabel={(option: RelicCommunicationLanguage) =>
              option.display || ''
            }
            isOptionEqualToValue={(option, value) => {
              return option.code === value.code;
            }}
            onChange={(_, newValue) => {
              fieldOnChange(newValue ? newValue : null);
              if (onChange) {
                onChange(newValue ? newValue : null);
              }
            }}
            renderInput={params => (
              <TextField
                {...params}
                margin="normal"
                disabled={disabled}
                label={label}
                variant="outlined"
                error={error}
                helperText={helperText}
                required
              />
            )}
            value={value ? value : placeholder}
          />
        );
      }}
    />
  );
};
