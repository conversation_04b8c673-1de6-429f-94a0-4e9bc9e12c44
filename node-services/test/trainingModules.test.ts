import tap from "tap";
import { fastify } from "../index";
import { MedplumClient, MedplumClientOptions, EmailPasswordLoginRequest, ProfileResource } from '@medplum/core';
import { JSDOM } from 'jsdom';

const { window } = new JSDOM();
global.window = window as unknown as Window & typeof globalThis;
global.document = window.document;
global.document = window.document;

const sessionStorageMock: Storage = {
    store: {} as Record<string, string>,
    getItem: function (key: string) {
        return this.store[key] || null;
    },
    setItem: function (key: string, value: string) {
        this.store[key] = value.toString();
    },
    removeItem: function (key: string) {
        delete this.store[key];
    },
    clear: function () {
        this.store = {};
    },
    get length() {
        return Object.keys(this.store).length;
    },
    key: function (index: number) {
        return Object.keys(this.store)[index] || null;
    },
};

global.sessionStorage = sessionStorageMock;

const trainingModulesApiEndpoint: string = '/api/trainings/modules';
const practitionerEmail: string = '<EMAIL>';
const practitionerPwd: string = 'Z1sZ9N3F8tcycDDK';
let accessToken: any;

const trainingModuleName: string = `test-training-${Date.now()}`;
let trainingModuleId: string = '';

const medplumOptions: MedplumClientOptions = {
    verbose: true,
};
const medplum: MedplumClient = new MedplumClient(medplumOptions);
const loginToMedplum = async (
    medplum: MedplumClient,
    email: string,
    password: string
): Promise<ProfileResource> => {
    const loginRequest: EmailPasswordLoginRequest = {
        email,
        password,
    };
    const loginResponse = await medplum.startLogin(loginRequest);
    const userProfile = await medplum.processCode(loginResponse.code as string);
    accessToken = medplum.getActiveLogin()?.accessToken.toString();
    return userProfile;
};

tap.teardown(async () => {
    await fastify.close();
    process.exit(0);
});

tap.before(async () => {
    await loginToMedplum(medplum, practitionerEmail, practitionerPwd);
});

tap.test("create training modules with POST /api/trainings/modules, should return success", async (t: Tap.Test) => {
    const response = await fastify.inject({
        method: "POST",
        url: trainingModulesApiEndpoint,
        payload: {
            "name": trainingModuleName,
            "description": "unit-test training module"
        },
        headers: ({
            'x-access-token': accessToken
        }),
    });

    t.equal(response.statusCode, 200);
    const responseBody = JSON.parse(response.body);
    t.ok(responseBody.id, 'training id is null/undefined');
    trainingModuleId = responseBody.id;
    t.equal(responseBody.name, trainingModuleName, 'training module name mismatch');
    t.ok(responseBody.storage.containerName, 'containerName is null/undefined');
    t.ok(responseBody.storage.datasourceName, 'datasourceName is null/undefined');
    t.ok(responseBody.crawler.id, 'crawler not assigned');
});

tap.test("get training module with GET /api/trainings/modules/:id, should return success", async (t: Tap.Test) => {
    t.ok(trainingModuleId, 'training module not yet created');
    const response = await fastify.inject({
        method: "GET",
        url: `${trainingModulesApiEndpoint}/${trainingModuleId}`,
        headers: ({
            'x-access-token': accessToken
        }),
    });

    t.equal(response.statusCode, 200);
    const responseBody = JSON.parse(response.body);
    t.equal(responseBody.id, trainingModuleId, 'training id mismatch');
    t.equal(responseBody.name, trainingModuleName, 'training name mismatch');
    t.ok(responseBody.storage.containerName, 'containerName is null/undefined');
    t.ok(responseBody.storage.datasourceName, 'datasourceName is null/undefined');
    t.ok(responseBody.crawler.id, 'crawler not assigned');
});

tap.test("delete training module with DELETE /api/trainings/modules/:id, should return success", async (t: Tap.Test) => {
    t.ok(trainingModuleId, 'training not yet created');
    const response = await fastify.inject({
        method: "DELETE",
        url: `${trainingModulesApiEndpoint}/${trainingModuleId}`,
        headers: ({
            'x-access-token': accessToken
        }),
    });

    t.equal(response.statusCode, 200);
});