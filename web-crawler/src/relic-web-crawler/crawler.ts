import { FinalStatistics, PlaywrightCrawler, RequestOptions, RequestQueue, PlaywrightCrawlerOptions, Dataset } from "crawlee";
import { chromium } from 'playwright-extra';
import stealthPlugin from 'puppeteer-extra-plugin-stealth';
import { CrawlRequest, RelicCrawlerOptions, ScraperOutput, URLLike } from "relic-ui";
import { DEFAULT_TIMEOUT_SECS } from "./constants.js";
import { cleanUrl, isFileLink, toPlaywrightCrawlerOptions } from "./utils.js";
import { RelicCrawlRouter } from "./router.js";
import { v4 as uuidv4 } from 'uuid';
import { RelicMetaData } from "./types.js";
import { getRedisQueue } from "./queue.js";

export class RelicWebCrawler {
    private crawler: PlaywrightCrawler;
    private router: RelicCrawlRouter;
    private request: CrawlRequest;
    private parentId: string; //use parentId for estimator output notification
    private lastErrorObject: Error; //track last error for estimator status

    /**
     * Create a new RelicWebCrawler instance
     * @returns RelicWebCrawler
     */
    public static async create(request: CrawlRequest): Promise<RelicWebCrawler> {
        const crawler = new RelicWebCrawler();

        //keep track of parent id, use for estimator output notification
        if (!request.urls?.[0]?.id) {
            request.urls[0].id = uuidv4();
        }
        crawler.parentId = request.urls[0].id;
        crawler.request = request;
        await crawler.initialize();
        return crawler;
    }

    /**
     * Initialize the crawler
     */
    private async initialize() {
        try {
            const { options, proxyConfiguration } = this.request;
            chromium.use(stealthPlugin());
            this.router = await RelicCrawlRouter.create(this.request);
            const createCrawlerOptions: PlaywrightCrawlerOptions = {
                proxyConfiguration,
                requestHandlerTimeoutSecs: DEFAULT_TIMEOUT_SECS,
                navigationTimeoutSecs: DEFAULT_TIMEOUT_SECS,
                requestHandler: this.router.getHandler(),
                // @ts-ignore
                maxRequestsPerCrawl: 500,
                minConcurrency: 1,
                maxConcurrency: 1,
                maxRequestRetries: 1,
                maxRequestsPerMinute: 10,
                ignoreIframes: true,
                launchContext: {
                    launcher: chromium as any,
                    launchOptions: {
                        headless: true,
                        acceptDownloads: true,
                        args: [
                            '--disable-gpu', // Mitigates the "crashing GPU process" issue in Docker containers
                        ]
                    },
                },
                retryOnBlocked: true,
                browserPoolOptions: {
                    useFingerprints: true,
                    fingerprintOptions: {
                        fingerprintGeneratorOptions: {
                            browsers: [
                                { name: 'firefox' as any },
                                { name: 'chrome' as any },
                                { name: 'safari' as any }
                            ],
                            devices: ['desktop' as any],
                            operatingSystems: ['windows' as any, 'macos' as any, 'linux' as any],
                        },
                    },
                },
                useSessionPool: true,
                sessionPoolOptions: {
                    blockedStatusCodes: [],
                },
                ...toPlaywrightCrawlerOptions(options),
                errorHandler: this.router.errorHandler.bind(this.router),
                failedRequestHandler: this.router.failedRequestHandler.bind(this.router),
                preNavigationHooks: [
                    this.router.preNavigationHook.bind(this.router)
                ]
            }

            this.crawler = new PlaywrightCrawler(createCrawlerOptions);
        } catch (err) {
            console.error(err, 'Error initializing crawler');
            this.lastErrorObject = err as Error;
            throw this.lastErrorObject;
        }
    }

    /**
     * Run the crawler
     * @returns Final statistics
    */
    async run(): Promise<FinalStatistics> {
        try {
            const addRequests = async function (crawler: PlaywrightCrawler, currentUrl: URLLike) {
                const id = currentUrl.id || uuidv4();
                const userData: RelicMetaData = {
                    id,
                    depth: currentUrl.depth || 0,
                    sourceDocumentId: currentUrl.sourceDocumentId || id,
                    children: currentUrl.children || []
                }

                await crawler.addRequests([
                    {
                        url: cleanUrl(currentUrl.url),
                        method: 'GET',
                        label: isFileLink(currentUrl.url) ? 'file' : 'page',
                        userData
                    } as RequestOptions
                ]);
            }
            const promises = [];
            //add root parent requests only, children will be added to the request queue when parent page processing is successful
            this.request.urls.forEach(url => promises.push(addRequests(this.crawler, url)));
            await Promise.allSettled(promises);
            return await this.crawler.run();
        } catch (err) {
            console.error('Error running crawler', err);
            this.lastErrorObject = err as Error;
            throw this.lastErrorObject;
        } finally {
            await this.sendSitemapToQueue();
        }
    }

    /**
     * Cleanup resources
     * 
     * When Actor is run in Apify, RequestQueue and Dataset are automatically cleaned up after 30 days
    */
    async cleanup() {
        await (await RequestQueue.open()).drop();
        await (await Dataset.open()).drop();
        await this.router.cleanup();
        await this.crawler.stats.resetStore();
    }

    /**
     * Get result as heirarchy of urls
     * @returns Array of urls
     */
    private async getSitemap(): Promise<Array<URLLike>> {
        try {
            const datasetItems = (await (await Dataset.open()).getData()).items;
            const requestQueue = await RequestQueue.open();
            const urlMap = new Map<string, URLLike>();

            //for url estimation, also include urls not in dataset
            let request: any;
            while ((request = await requestQueue.fetchNextRequest())) {
                datasetItems.push({ id: request.userData.id || uuidv4(), url: request.url, sourceDocumentId: request.userData.sourceDocumentId, children: [] });
                await requestQueue.markRequestHandled(request);
            }

            // First pass - create map of all URLs
            datasetItems.forEach((data) => {
                urlMap.set(data.id, {
                    id: data.id,
                    url: data.url,
                    sourceDocumentId: data.sourceDocumentId,
                    children: []
                });
            });

            // Second pass - build hierarchy
            datasetItems.forEach(data => {
                if (data.id !== data.sourceDocumentId) {
                    const parent = urlMap.get(data.sourceDocumentId);
                    if (parent) {
                        const child = urlMap.get(data.id);
                        if (child) {
                            parent.children.push(child);
                        }
                    }
                }
            });

            // Return only root level URLs (those that are their own source)
            return Array.from(urlMap.values()).filter(url =>
                url.id === url.sourceDocumentId
            );
        } catch (err) {
            console.error('Error getting sitemap', err);
            this.lastErrorObject = err as Error;
            throw this.lastErrorObject;
        }
    }

    /**
     * Send estimator output to queue
     * 
     * If isEstimatorRun=false, do nothing
     * 
     * if isEstimatorRun=true and relicMessageQueueParams is provided, send notification with status 'sitemap_ready' or 'sitemap_failed'
     */
    private async sendSitemapToQueue() {
        if (!this.request.isEstimatorRun) {
            return;
        }
        const redisQueue = await getRedisQueue(this.request.relicMessageQueueParams);
        if (!redisQueue) {
            return;
        }
        const result: Partial<ScraperOutput> = {
            id: this.parentId,
        }
        try {
            if (this.lastErrorObject) {
                result.errorDetails = this.lastErrorObject.message || 'Estimator failed.';
                result.status = 'sitemap_failed';
            } else {
                result.sitemap = await this.getSitemap();
                result.status = 'sitemap_ready';
            }
        } catch (err) {
            result.status = 'sitemap_failed';
            result.errorDetails = `Failed to get sitemap: ${err}`;
        } finally {
            const jobId = `${this.parentId}-${new Date().getTime()}`;
            console.info(`Sending notification to queue with jobId: ${jobId}`);
            await redisQueue.add(this.request.relicMessageQueueParams.jobName, result, { jobId })
                .then(() => {
                    console.info(`Notification sent to queue with jobId: ${jobId}`);
                })
                .catch((err) => {
                    console.error(`Error sending notification to queue with jobId: ${jobId}`, err);
                });
        }
    }

}
