import { useTranslate } from '@refinedev/core';
import { RefreshButton } from '@refinedev/mui';

import { Box, Button } from '@mui/material';
import Divider from '@mui/material/Divider';
import IconButton from '@mui/material/IconButton';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';

import Iconify from 'src/components/iconify';

// ----------------------------------------------------------------------

type TrainingModulesViewToolbarProps = {
  title?: React.ReactNode;
  onBack: VoidFunction;
  showTrainingModulesDetails: boolean;
  onToggleTrainingModules: VoidFunction;
  refetchTrainingContent: VoidFunction;
  onOpenAdd: (event: React.MouseEvent<HTMLElement, MouseEvent>) => void;
};

export default function TrainingModulesViewToolbar({
  onBack,
  title,
  showTrainingModulesDetails,
  onToggleTrainingModules,
  onOpenAdd,
  refetchTrainingContent,
}: TrainingModulesViewToolbarProps) {
  const translate = useTranslate();

  return (
    <Stack
      direction={'row'}
      alignItems={'center'}
      sx={{
        pb: 3,
        zIndex: 9,
        position: 'relative',
        pt: { xs: 2, lg: 0 },
        background: 'background.paper',
      }}
    >
      <IconButton onClick={onBack}>
        <Iconify icon="eva:arrow-ios-back-fill" />
      </IconButton>

      <Typography variant="h5" sx={{ flexGrow: 1 }}>
        {title}
      </Typography>
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
        <RefreshButton onClick={refetchTrainingContent} />

        <Button
          variant="contained"
          startIcon={<Iconify icon={'eva:plus-fill'} />}
          onClick={onOpenAdd}
        >
          {translate('buttons.create')}
        </Button>
      </Box>
      <IconButton
        onClick={onToggleTrainingModules}
        color={showTrainingModulesDetails ? 'inherit' : 'default'}
        sx={{ ml: 1 }}
      >
        <Iconify
          icon={
            !showTrainingModulesDetails
              ? 'tabler:layout-sidebar-right-collapse'
              : 'tabler:layout-sidebar-right-collapse-filled'
          }
        />
      </IconButton>
      <Divider
        sx={{
          bottom: 0,
          position: 'absolute',
          left: { xs: -8, md: -16, lg: -24 },
          right: { xs: -8, md: -16, lg: -24 },
        }}
      />
    </Stack>
  );
}
