import { S } from 'fluent-json-schema';
import { requestContext } from '@fastify/request-context';
import { relicRequire } from '../../../utils/common-js';
import { OrganizationFilters, RelicOrganization, IUserIdentity, RelicIdParam } from 'relic-ui';
import { FastifyInstance, FastifyPluginOptions, FastifyReply, FastifyRequest } from 'fastify';

const { RelicIdParamSchema, RelicOrganizationSchema } = relicRequire('relic-ui/schema');

/**
 * Organization route definition to provide various organization-related services.
 *
 * @param {FastifyInstance} fastify - fastify instance
 * @param {any} options - fastify options
 * @param {any} next - fastify next
 */
const organizationRoutes = async (fastify: FastifyInstance, options: FastifyPluginOptions) => {
  /**
   * Route for creating an organization.
   * POST` /organizations implementation
   *
   * @param {string} "/organizations" - implemented path
   * @returns {any} Request handler function
   */
  fastify.post(
    '/',
    {
      schema: {
        body: RelicOrganizationSchema,
        description: 'Create an Organization / Tenant',
        tags: ['Organization Service'],
        summary: 'Create an Organization / Tenant',
      },
    },
    async function (request: any, reply: any): Promise<RelicOrganization> {
      const relicOrganization = request.body as RelicOrganization;
      fastify.log.info(`relicOrganization: ${JSON.stringify(relicOrganization)}`);
      if (!relicOrganization) {
        throw new Error('Invalid request body. Missing organization details.');
      }
      const result = await fastify.orgService.createOrganization(relicOrganization);
      return reply.code(201).send(result);
    },
  );

  fastify.patch(
    '/:id',
    {
      schema: {
        body: RelicOrganizationSchema,
        description: 'Patch an Organization / Tenant',
        tags: ['Organization Service'],
        summary: 'Patch an Organization / Tenant',
      },
    },
    async function (request: FastifyRequest, reply: FastifyReply): Promise<RelicOrganization> {
      const relicOrganization: RelicOrganization = request.body as RelicOrganization;
      if (!relicOrganization || !relicOrganization.id) {
        throw new Error('Invalid request body. Missing organization details.');
      }
      return await request.server.orgService.updateOrganization(relicOrganization);
    },
  );

  fastify.get(
    '/',
    {
      schema: {
        querystring: S.object()
          // define a prop ids that can container an array of strings
          .prop(
            'id',
            S.array()
              .items(S.string())
              .description(
                'Sample value: 7760ffcd-64d2-43df-9548-055b257dfaee (id i.e. organization id)\nWhere to get: id field present in organization collection ',
              ),
          )
          .prop('active', S.boolean().default(true))
          .prop(
            'name',
            S.string().description(
              'Sample value: TEST - Sunrise Senior Living (i.e. organization name),\nWhere to get: name field present in organization collection',
            ),
          )
          .prop(
            'type',
            S.string().description(
              'Sample value: prov (i.e. type of organization),\nWhere to get: type field present in organization collection',
            ),
          )
          .prop('_start', S.number().default(0))
          .prop('_end', S.number().default(25))
          .prop('_order', S.string().enum(['asc', 'desc']).default('asc'))
          .prop('_sort', S.string().default('name').description('Sample value: name, type, website'))
          .prop('_search', S.string().maxLength(255).description('Search by facility name')),
        description: 'Fetch Organizations / Tenants',
        tags: ['Organization Service'],
        summary: 'Fetch Organizations / Tenants',
      },
      /**
       * Pre-handler function to process and validate query parameters.
       * Converts 'active' parameter from string to boolean and constructs a filters object.
       * The filters object is attached to the request for use in the route handler.
       *
       * @param {FastifyRequest} request - The request object.
       * @param {FastifyReply} reply - The reply object.
       */
      preHandler: async function (request: any, reply: any) {
        let { id, active, name, _start, _end, _sort, _order, _search } = request.query;
        const filter = {} as OrganizationFilters;
        const sortOptions = {};
        // Ensure 'ids' is an array
        const idArray = Array.isArray(id) ? id : [id];
        const validIdArray = idArray.filter((id) => id !== undefined);
        if (validIdArray && validIdArray.length > 0) {
          filter.id = { $in: validIdArray };
        }
        filter.active = active;
        if (name !== undefined) {
          filter.name = name;
        }
        sortOptions[_sort] = _order === 'asc' ? 1 : -1;
        request.filters = {
          filter,
          _count: _end - _start,
          sortOptions,
          _offset: _start,
        };
        if (_search != null && _search != '') {
          request.filters.filter = {
            $and: [{ name: { $regex: _search, $options: 'i' } }, { active: active }],
          };
        }

        const whoami = requestContext.get('whoami') as IUserIdentity;
        if (whoami.role.name != 'admin') {
          request.filters.filter.id = requestContext.get('organizationId');
        }
      },
    },
    async function (request: any, reply: any): Promise<RelicOrganization[] | []>{
      const response = await fastify.orgService.getOrganizations(request.filters);
      let totalCount = await fastify.orgService.getOrganizationCount(request.filters);
      reply.header('x-total-count', totalCount);
      return response;
    }
  );
  
  fastify.get(
    '/:id',
    {
      schema: {
        description: 'Fetch Organization Detail by ID',
        tags: ['Organization Service'],
        summary: 'Fetch Organization Detail by ID',
        params: RelicIdParamSchema,
      },
    },
    async function (request: FastifyRequest, reply: FastifyReply): Promise<RelicOrganization> {
      const params = request.params as RelicIdParam;
      const relicOrganization = await request.server.orgService.getOrganization(params.id);
      fastify.log.info(`relicOrganization: ${JSON.stringify(relicOrganization)}`);
      return relicOrganization;
    },
  );

  fastify.delete(
    '/:id',
    {
      schema: {
        params: RelicIdParamSchema,
        description:
          'Warning: for Unit Test ONLY. Delete an Organization / Tenant',
        tags: ['Organization Service'],
        summary:
          'Warning: for Unit Test ONLY. Delete an Organization / Tenant',
      },
    },
    async function (request: FastifyRequest, reply: FastifyReply): Promise<void> {
      const params = request.params as RelicIdParam;
      return await request.server.orgService.deleteOrganization(params.id);
    },
  );
};

// Export as a plugin for Fastify
export default organizationRoutes;
