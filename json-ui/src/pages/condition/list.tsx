import React from "react";
import {
    useMany,
    useTranslate,
    IResourceComponentsProps,
} from "@refinedev/core";
import {
    List,
    DateField,
    EditButton,
    ShowButton,
    useDataGrid,
} from "@refinedev/mui";

import { GridColDef, DataGridPro } from "@mui/x-data-grid-pro";

export const ConditionList: React.FC<IResourceComponentsProps> = () => {
    const translate = useTranslate();
    const { dataGridProps } = useDataGrid();

    const { data: patientData, isLoading: patientIsLoading } = useMany({
        resource: "Patients",
        ids: dataGridProps?.rows?.map((item: any) => item?.PatientId) ?? [],
        queryOptions: {
            enabled: !!dataGridProps?.rows,
            staleTime: 5 * 60 * 1000, 
        },
    });

    const columns = React.useMemo<GridColDef[]>(
        () => [
            {
                field: "id",
                headerName: translate("conditions.fields.id"),
                minWidth: 50,
            },
            {
                field: "resourceType",
                flex: 1,
                headerName: translate("conditions.fields.resourceType"),
                minWidth: 200,
            },
            {
                field: "clinicalStatus",
                flex: 1,
                headerName: translate("conditions.fields.clinicalStatus"),
                minWidth: 200,
            },
            {
                field: "verificationStatus",
                flex: 1,
                headerName: translate("conditions.fields.verificationStatus"),
                minWidth: 200,
            },
            {
                field: "onsetDateTime",
                flex: 1,
                headerName: translate("conditions.fields.onsetDateTime"),
                minWidth: 250,
                renderCell: function render({ value }) {
                    return <DateField value={value} />;
                },
            },
            {
                field: "abatementDateTime",
                flex: 1,
                headerName: translate("conditions.fields.abatementDateTime"),
                minWidth: 250,
                renderCell: function render({ value }) {
                    return <DateField value={value} />;
                },
            },
            {
                field: "note",
                flex: 1,
                headerName: translate("conditions.fields.note"),
                minWidth: 200,
            },
            {
                field: "PatientId",
                flex: 1,
                headerName: translate("conditions.fields.PatientId"),
                minWidth: 300,
                renderCell: function render({ value }) {
                    return patientIsLoading ? (
                        <>Loading...</>
                    ) : (
                        patientData?.data?.find((item) => item.id === value)
                            ?.name
                    );
                },
            },
            {
                field: "actions",
                headerName: translate("table.actions"),
                sortable: false,
                renderCell: function render({ row }) {
                    return (
                        <>
                            <EditButton hideText recordItemId={row.id} />
                            <ShowButton hideText recordItemId={row.id} />
                        </>
                    );
                },
                align: "center",
                headerAlign: "center",
                minWidth: 80,
            },
        ],
        [translate, patientData?.data],
    );

    return (
        <List>
            <DataGridPro pagination {...dataGridProps} columns={columns} autoHeight />
        </List>
    );
};
