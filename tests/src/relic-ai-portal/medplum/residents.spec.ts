import { test, expect } from './test.setup';
import { common, searchAndDeletePatients,deleteConditionByDisplayName, medplum, createPatient, getPatient, createPatientCondition, deleteCondition, deletePatient } from '../../../common';
import { Condition, Organization, Patient, Practitioner } from '@medplum/fhirtypes'

test.skip('Medplum-Add Resident', async ({ page }) => {
  
  let jsonPatient: Patient = await getPatient(1);
  const familyName = jsonPatient.name?.[0]?.family || ''
  const givenName = jsonPatient.name?.[0]?.given?.[0] || ''
  const birthDate = jsonPatient.birthDate;
  const email = jsonPatient.telecom?.[0].value;
  const phone = jsonPatient.telecom?.[1].value;
  const org = jsonPatient.managingOrganization?.display;
  const maritalStatus = jsonPatient.maritalStatus?.coding?.[0]?.display;

  try {



    await page.getByRole('link', { name: 'Add' }).click();
    await page.getByLabel('Name').fill(`${givenName} ${familyName}`);
    await page.getByRole('textbox', { name: 'Date of Birth' }).click();
    await page.getByRole('textbox', { name: 'Date of Birth' }).fill('01/01/1962');
   // await page.getByTestId(":r6h:").fill(`01/01/1962`);
    await page.getByLabel('Gender').click();
    await page.getByRole('option', { name: 'Male', exact: true }).click();
    await page.getByLabel('Marital Status').click();
    await page.getByRole('option', { name: `${maritalStatus}`, exact: true }).click();
    await page.getByLabel('Email Address').fill(`${email}`);
    await page.getByLabel('Mobile Phone').fill(`${phone}`);
    await page.getByPlaceholder('Choose a option').click();
    await page.getByRole('option', { name: `${org}` }).click();
    await page.getByRole('button', { name: 'Save' }).click();
    await expect(page.getByRole('alert')).toContainText('Successfully added Residents');

    await deletePatient(jsonPatient);

  }
  catch (error) {
    console.error('Error Medplum-Search Resident-Success', error);
    await deletePatient(jsonPatient);
    throw error;
  }

});

test('Medplum-Search Existing Resident', async ({ page }) => {

  await expect(page.getByRole('heading', { name: 'Residents' })).toBeVisible();
  await page.getByPlaceholder('Search...').click();
  await page.getByPlaceholder('Search...').fill(`mia`);
  await expect(page.getByRole('gridcell', { name: `Mia Hernandez` })).toBeVisible();

});

test.skip('Medplum-Search Residents-Special Characters', async ({ page }) => {

  await expect(page.getByRole('heading', { name: 'Residents' })).toBeVisible();
  await page.getByPlaceholder('Search...').click();
  await page.getByPlaceholder('Search...').fill('$$');
  await expect(page.getByRole('gridcell', { name: `Ava$$ Rodriguez` })).toBeVisible();
});

test('Medplum-Search Residents-Deletion Search', async ({ page }) => {

  await expect(page.getByRole('heading', { name: 'Residents' })).toBeVisible();
  await page.getByPlaceholder('Search...').click();
  await page.getByPlaceholder('Search...').fill('Mia');
  await page.getByPlaceholder('Search...').press('Enter');
  await page.getByPlaceholder('Search...').fill('');
  await page.getByRole('heading', { name: 'Residents' }).click();
});


test('Medplum-Search New Resident', async ({ page }) => {

  let jsonPatient: Patient = await getPatient(0);
  const familyName = jsonPatient.name?.[0]?.family || ''
  const givenName = jsonPatient.name?.[0]?.given?.[0] || ''

  try {

    let newPatient: Patient = await createPatient(0);

    await expect(page.getByRole('heading', { name: 'Residents' })).toBeVisible();
    await page.getByPlaceholder('Search...').click();
    await page.getByPlaceholder('Search...').fill(`${givenName}`);
    await expect(page.getByRole('gridcell', { name: `${givenName} ${familyName}` })).toBeVisible();

    await deletePatient(jsonPatient);

  }
  catch (error) {
    console.error('Error Medplum-Search Resident-Success', error);
    await deletePatient(jsonPatient);
    throw error;
  }
});


test('Medplum-Delete Resident', async ({ page }) => {
  let jsonPatient: Patient = await getPatient(0);
  const familyName = jsonPatient.name?.[0]?.family || ''
  const givenName = jsonPatient.name?.[0]?.given?.[0] || ''

  try {

    let newPatient: Patient = await createPatient(0);



    await page.getByPlaceholder('Search...').fill(`${givenName}`);
    await expect(page.getByRole('rowgroup')).toContainText(`${givenName} ${familyName}`);
    await page.getByRole('row', { name: `${givenName} ${familyName} 01` }).getByRole('button').click();
    await page.getByRole('row', { name: `${givenName} ${familyName} 01` }).getByRole('button').click();
    await page.getByTestId('refine-delete-button').click();
    await expect(page.getByText('CancelDelete')).toBeVisible();
    await page.getByRole('button', { name: 'Delete' }).click();
    await expect(page.getByText('Successfully deleted Residents')).toBeVisible();
    await expect(page.locator('#root')).toContainText('Successfully deleted Residents');
  }
  catch (error) {
    console.error('Error Medplum-Search Resident-Success', error);
    await deletePatient(jsonPatient);
    throw error;
  }
});

test.skip('Medplum-Resident Conditions-Add New', async ({ page }) => {

  let jsonPatient: Patient = await getPatient(2);
  const familyName = jsonPatient.name?.[0]?.family || ''
  const givenName = jsonPatient.name?.[0]?.given?.[0] || ''

  try {

    let newPatient: Patient = await createPatient(2);

    await page.getByPlaceholder('Search...').fill(`${givenName}`);
    await page.getByRole('gridcell', { name: `${givenName} ${familyName}` }).click();
    await page.getByRole('tab', { name: 'Conditions' }).click();

    await expect(page.getByRole('button', { name: 'Add' })).toBeVisible();
    await page.getByRole('button', { name: 'Add' }).click();

    await expect(page.getByRole('menuitem', { name: 'New Condition' })).toBeVisible();
    await page.getByRole('menuitem', { name: 'New Condition' }).click();

    await expect(page.locator('#alert-dialog-title')).toContainText('New Condition');
    await page.getByRole('textbox', { name: 'Code' }).click();
    await page.getByRole('textbox', { name: 'Code' }).fill('atfCondition');
    await page.getByRole('textbox', { name: 'Note' }).click();
    await page.getByRole('textbox', { name: 'Note' }).fill('atfCondition note');
    await page.getByLabel('Choose a option').click();
    await page.getByRole('option', { name: 'Active', exact: true }).click();
    await page.getByRole('button', { name: 'Add' }).click();
    await expect(page.getByRole('alert')).toContainText('Successfully added condition');

    await deletePatient(jsonPatient);
    await deleteConditionByDisplayName('atfCondition');
  }
  catch (error) {
    console.error('Error Medplum-Search Resident-Success', error);
    await deletePatient(jsonPatient);
    await deleteConditionByDisplayName('atfCondition');
    throw error;
  }
});


test.skip('Medplum-Resident Conditions-Delete', async ({ page }) => {
  let jsonPatient: Patient = await getPatient(3);
  const familyName = jsonPatient.name?.[0]?.family || ''
  const givenName = jsonPatient.name?.[0]?.given?.[0] || ''
  let condID: string = ''

  try {
    let newCondition: Condition = await createPatientCondition(3, 0);
    condID = newCondition.id || ''


    await page.getByPlaceholder('Search...').fill(`${givenName}`);
    await page.getByRole('gridcell', { name: `${givenName} ${familyName}` }).click();
    await page.getByRole('tab', { name: 'Conditions' }).click();

    await expect(page.getByRole('rowgroup')).toContainText('Hypertension');
    await expect(page.getByTestId('refine-delete-button').first()).toBeVisible();
    await page.getByTestId('refine-delete-button').first().click();
    await expect(page.locator('#alert-dialog-title')).toContainText('Delete Condition');
    await page.getByRole('button', { name: 'Delete' }).click();
    await expect(page.getByRole('alert')).toContainText('Successfully deleted condition');
    await deleteCondition(condID);
    await deletePatient(jsonPatient);

  }
  catch (error) {
    console.error('Error Medplum-Search Resident-Success', error);
    await deleteCondition(condID);
    await deletePatient(jsonPatient);
    throw error;
  }
});

test('Medplum-Resident List-Successful', async ({ page }) => {

  
  await expect(page.getByRole('heading', { name: 'Residents' })).toBeVisible();
  await page.getByRole('link', { name: 'Residents' }).click();
  await expect(page.getByText('Rows per page:')).toBeVisible();
  await page.getByLabel('Go to next page').click();
});

test('Medplum-Resident List-Sort by Name (Page 1)', async ({ page }) => {
  await expect(page.getByRole('heading', { name: 'Residents' })).toBeVisible();
  await page.getByRole('link', { name: 'Residents' }).click();
  await expect(page.getByText('Rows per page:')).toBeVisible();
});

test('Medplum-Resident List-Sort by Name (Page 2)', async ({ page }) => {
  await expect(page.getByRole('heading', { name: 'Residents' })).toBeVisible();
  await page.getByLabel('Go to next page').click();
});


test('Medplum-Resident Details-DOB and Age is visible.', async ({ page }) => {
  
  await expect(page.getByRole('heading', { name: 'Residents' })).toBeVisible();
  await page.getByPlaceholder('Search...').click();
  await page.getByPlaceholder('Search...').fill('Lopez');
  await page.getByPlaceholder('Search...').press('Enter');
  await page.getByRole('gridcell', { name: 'David Lopez' }).click();
  await expect(page.getByText('Date of Birth')).toBeVisible();
  await expect(page.locator('form')).toContainText('01/01/1960');
  await expect(page.getByText('(65 years old)')).toBeVisible();
});

test('Medplum-Resident Details-Phone is mandatory', async ({ page }) => {
  
  await expect(page.getByRole('heading', { name: 'Residents' })).toBeVisible();
  await page.getByPlaceholder('Search...').click();
  await page.getByPlaceholder('Search...').fill('Rodriguez');
  await page.getByPlaceholder('Search...').press('Enter');
  await page.getByRole('gridcell', { name: 'Ava$$ Rodriguez' }).click();
  await expect(page.getByText('Mobile Phone')).toBeVisible();
  await page.getByText('Mobile PhoneUS+').click();
  await expect(page.locator('form')).toContainText('Mobile PhoneUS+1');
  // Check if there exists any element with text starting with '(+1)'

  // const locator = page.locator('text=/^\\(\\+1\\)/');
  
  // // Ensure at least one element matches
  // const count = await locator.count();
  // if (count > 0) {
  //     console.log(`Found ${count} element(s) starting with '(+1)'.`);
  // } else {
  //     throw new Error("No text found starting with '(+1)'.");
  // }

});




test('Medplum-Edit Resident-Successfully', async ({ page }) => {
  
  await expect(page.getByRole('heading', { name: 'Residents' })).toBeVisible();
  await page.getByPlaceholder('Search...').click();
  await page.getByPlaceholder('Search...').fill('Rodriguez');
  await page.getByPlaceholder('Search...').press('Enter');
  await page.getByRole('gridcell', { name: 'Ava$$ Rodriguez' }).click();
  await page.locator('.MuiCardHeader-action > .MuiButtonBase-root').click();
  await page.getByRole('button', { name: 'Edit' }).click();
  await page.getByLabel('Primary Language *').click();
  await page.getByRole('option', { name: 'Thai' }).click();
  await page.getByRole('button', { name: 'Save' }).click();
});

test('Medplum-Edit Resident-Error message displays', async ({ page }) => {
  
  await expect(page.getByRole('heading', { name: 'Residents' })).toBeVisible();
  await page.getByPlaceholder('Search...').click();
  await page.getByPlaceholder('Search...').fill('Rodriguez');
  await page.getByPlaceholder('Search...').press('Enter');
  await page.getByRole('gridcell', { name: 'Ava$$ Rodriguez' }).click();
  await page.locator('.MuiCardHeader-action > .MuiButtonBase-root').click();
  await page.getByRole('button', { name: 'Edit' }).click();
  await page.getByLabel('Email Address').fill('');
  await page.getByRole('button', { name: 'Save' }).click();
  await expect(page.getByText('This field is required').nth(0)).toBeVisible();
 
});

test('Medplum-Interpretor Call-Phone number validation', async ({ page }) => {
  
  await expect(page.getByRole('heading', { name: 'Residents' })).toBeVisible();
  await page.getByPlaceholder('Search...').click();
  await page.getByPlaceholder('Search...').fill('Rodriguez');
  await page.getByPlaceholder('Search...').press('Enter');
  await page.getByRole('gridcell', { name: 'Ava$$ Rodriguez' }).click();
  await page.getByLabel('Voice').click();
  await expect(page.getByLabel('Voice Conversation')).toContainText('Voice Conversation using Phone');
  await page.getByTestId('PhoneIcon').click();
  await page.getByLabel('Phone Number').fill('4441234567');
  await expect(page.getByRole('button', { name: 'Start Phone Call' })).toBeVisible();
});

test('Medplum-Interpretor Call-Primary Language validation', async ({ page }) => {

  await expect(page.getByRole('heading', { name: 'Residents' })).toBeVisible();
  await page.getByPlaceholder('Search...').click();
  await page.getByPlaceholder('Search...').fill('Rodriguez');
  await page.getByPlaceholder('Search...').press('Enter');
  await page.getByRole('gridcell', { name: 'Ava$$ Rodriguez' }).click();
  await page.getByLabel('Voice').click();
  await expect(page.locator('#language-autocomplete-label')).toBeVisible();
});

test.afterAll('Clean up Test Data', async () => {
  console.log('Clean up test data');

  let jsonPatient: Patient = await getPatient(0);
  let familyName: string = jsonPatient.name?.[0]?.family || ''
  let givenName: string = jsonPatient.name?.[0]?.given?.[0] || ''
  await searchAndDeletePatients(givenName,familyName);

  jsonPatient = await getPatient(1);
  familyName = jsonPatient.name?.[0]?.family || ''
  givenName = jsonPatient.name?.[0]?.given?.[0] || ''
  await searchAndDeletePatients(givenName,familyName);

  jsonPatient = await getPatient(2);
  familyName = jsonPatient.name?.[0]?.family || ''
  givenName = jsonPatient.name?.[0]?.given?.[0] || ''
  await searchAndDeletePatients(givenName,familyName);

  jsonPatient = await getPatient(3);
  familyName = jsonPatient.name?.[0]?.family || ''
  givenName = jsonPatient.name?.[0]?.given?.[0] || ''
  await searchAndDeletePatients(givenName,familyName);
});


