import { FastifyPluginAsync } from "fastify";
import fs from 'fs';
import config from 'config';
import { suffixFilename } from "../../utils/file";
import * as deepl from 'deepl-node';
import axios, { AxiosRequestConfig } from 'axios';
import { v4 as uuidv4 } from 'uuid';

const translationPlugin: FastifyPluginAsync = async (encapsulatedInstance: any, options: any) => {
    /**
     * Description
     * @param {string} sourceFile - Full path of local source file.
     * @param {string} sourceLang - Source file language code per https://developers.deepl.com/docs/api-reference/languages
     * @param {string} targetLang - Target file language code per https://developers.deepl.com/docs/api-reference/languages
     * @returns {Promise<string>} - Full path of local translated file. File name is suffixed with target language.
     */
    encapsulatedInstance.decorate('translateDocumentWithDeepl', async function (sourceFile: string, sourceLang: string, targetLang: string): Promise<string> {

        //Check if source file exists locally else throw error.
        if (!fs.existsSync(sourceFile)) {
            encapsulatedInstance.log.error(`File ${sourceFile} does not exist locally.`);
            throw new Error(`File ${sourceFile} does not exist locally.`);
        }

        //Create deepl Translator instance & target file name
        const options: deepl.TranslatorOptions = { maxRetries: 3, minTimeout: 10000 };
        const translator = new deepl.Translator(config.get("DEEPL.API_KEY"), options);
        const targetFileName: string = suffixFilename(sourceFile, ' - ' + targetLang, true);

        //Translate document
        try {
            //Translate the file and get the translated file locally. Local File system constraint due to DeepL JS SDK design.
            const documentStatus: deepl.DocumentStatus = await translator.translateDocument(
                sourceFile,
                targetFileName,
                sourceLang as deepl.SourceLanguageCode,
                targetLang as deepl.TargetLanguageCode,
            )
            if (documentStatus.status === 'done') {
                encapsulatedInstance.log.info(`Translation done for ${targetLang}.`);
            } else {
                encapsulatedInstance.log.error(`Translation failed for ${targetLang} with status ${documentStatus.status}`);
                throw new Error(`Deepl could not translate to ${targetLang} with status ${documentStatus.status}`);
            }
        } catch (error) {
            encapsulatedInstance.log.error(error);
            throw new Error(`Translation failed for ${targetLang} with error ${error.message}`);
        }
        return targetFileName;
    });

    /**
    * Description
    * @param {string} text - Text that is to be translated.
    * @returns {Promise<string>} - detected source language.
    */
    encapsulatedInstance.decorate('detectLanguage', async function (text: string): Promise<string> {
        let result: string;
        const key: string = config.get("AZURE_TRANSLATION.API_KEY");
        const endpoint: string = config.get("AZURE_TRANSLATION.ENDPOINT");
        const url = '/detect';
        const method = 'post';

        // Add your location, also known as region. The default is global.
        // This is required if using an Azure AI multi-service resource.
        const location: string = config.get("AZURE_TRANSLATION.LOCATION");

        const inputText: string = text[0];
        const params: URLSearchParams = new URLSearchParams();
        params.append("api-version", "3.0");

        const azureConfigObj = azureConfig(endpoint, key, location, url, method, params, inputText);

        try {
            const response = await axios(azureConfigObj);
            result = response.data;
        } catch (error) {
            throw error;
        }
        return result;
    });

    /**
     * Description
     * @param {string} text - Text that is to be translated.
     * @param {string} sourceLang - Source file language code per https://developers.deepl.com/docs/api-reference/languages
     * @param {string} targetLang - Target file language code per https://developers.deepl.com/docs/api-reference/languages
     * @returns {Promise<string>} - Translated text.
     */
    encapsulatedInstance.decorate('translateWithDeepL', async function (text: string, sourceLang: deepl.SourceLanguageCode, targetLang: deepl.TargetLanguageCode): Promise<deepl.TextResult> {
        //Create deepl Translator instance
        const options: deepl.TranslatorOptions = { maxRetries: 3, minTimeout: 10000 };
        const translator = new deepl.Translator(config.get("DEEPL.API_KEY"), options);
        try {
            return await translator.translateText(text, sourceLang, targetLang);
        } catch (error) {
            throw error;
        }
    });

    /**
    * Description
    * @param {string} text - Text that is to be translated.
    * @param {string} sourceLang - Source file language code per https://developers.deepl.com/docs/api-reference/languages
    * @param {string} targetLang - Target file language code per https://developers.deepl.com/docs/api-reference/languages
    * @returns {Promise<string>} - Translated text.
    */
    encapsulatedInstance.decorate('translateWithAzure', async function (text: string, sourceLang: deepl.SourceLanguageCode, targetLang: deepl.TargetLanguageCode): Promise<string> {
        let result: string;
        const key: string = config.get("AZURE_TRANSLATION.API_KEY");
        const endpoint: string = config.get("AZURE_TRANSLATION.ENDPOINT");
        const url = '/translate';
        const method = 'post';

        // Add your location, also known as region. The default is global.
        // This is required if using an Azure AI multi-service resource.
        const location: string = config.get("AZURE_TRANSLATION.LOCATION");
        const inputText: string = text[0];
        const params: URLSearchParams = new URLSearchParams();
        params.append("api-version", "3.0");
        params.append("from", sourceLang);
        params.append("to", targetLang);

        const azureConfigObj = azureConfig(endpoint, key, location, url, method, params, inputText);

        try {
            const response = await axios(azureConfigObj);
            result = response.data;
        } catch (error) {
            throw error;
        }

        return result;
    });

    const azureConfig = function (
        endpoint: string,
        key: string,
        location: string,
        url: string,
        method: string,
        params: URLSearchParams,
        inputText: string
    ): AxiosRequestConfig {
        return {
            baseURL: endpoint,
            url: url,
            method: method,
            headers: {
                'Ocp-Apim-Subscription-Key': key,
                'Ocp-Apim-Subscription-Region': location,
                'Content-type': 'application/json',
                'X-ClientTraceId': uuidv4().toString(),
            },
            params: params,
            data: [{ 'text': inputText }],
            responseType: 'json',
        };
    }
}

export default translationPlugin;