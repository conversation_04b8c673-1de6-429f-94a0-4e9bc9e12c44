import { useTranslate } from '@refinedev/core';
import { useAutocomplete, useDataGrid } from '@refinedev/mui';
import { useForm } from '@refinedev/react-hook-form';
import React from 'react';
import { FieldValues } from 'react-hook-form';
import { useLocation } from 'react-router-dom';
import { TrainingModule } from 'relic-ui';

import { <PERSON><PERSON>, Drawer, IconButton, Stack, TextField } from '@mui/material';
import { GridColDef } from '@mui/x-data-grid-pro';
import { DatePicker } from '@mui/x-date-pickers';

import { useParams, useRouter } from 'src/routes/hooks';

import { usePopover } from 'src/hooks/use-popover';

import TrainingModulesAddPopover from 'src/components/_trainings-modules/edit/training-modules-add-popover';
import TrainingModulesDetailsPopover from 'src/components/_trainings-modules/edit/training-modules-details-popover';
import TrainingModulesSidebar from 'src/components/_trainings-modules/edit/training-modules-sidebar';
import TrainingModulesTabs from 'src/components/_trainings-modules/edit/training-modules-tabs';
import TrainingModulesViewToolbar from 'src/components/_trainings-modules/edit/training-modules-toolbar';
import ListTrainingsContent from 'src/components/_trainings-modules/edit/trainings-content-list';
import { AgentRoleDropDown } from 'src/components/dropdown/agentRoleDropdown';
import { OrganizationDropdown } from 'src/components/dropdown/organizationDropdown';
import { PractitionerDropdown } from 'src/components/dropdown/practitionerDropdown';
import Iconify from 'src/components/iconify';
import { Edit } from 'src/components/refine-customs/edit';

const DRAWER_WIDTH = {
  lg: 380,
  md: 320,
  sm: 280,
  xs: '100%',
};

const TABS = [
  {
    value: 'training-contents',
    label: 'trainings-modules.tabs.training-contents',
  },
];

export const TrainingsModulesEdit = () => {
  const router = useRouter();
  const { id } = useParams();

  const {
    control,
    saveButtonProps,
    refineCore: { queryResult, onFinish },
    register,
    handleSubmit,
    formState: { errors, isLoading },
  } = useForm({
    refineCoreProps: {
      redirect: false,
      resource: 'trainings/modules',
      id: id,
      action: 'edit',
      queryOptions: {
        enabled: !!id,
      },
    },
  });

  const [showTrainingModulesDetails, setShowTrainingModulesDetails] =
    React.useState(true);

  const trainingModulesDetails: TrainingModule = queryResult?.data
    ?.data as TrainingModule;

  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const initialTab = queryParams.get('tab') || TABS[0].value;
  const [showEdit, setShowEdit] = React.useState(false);

  const [currentTab, setCurrentTab] = React.useState(
    TABS.find(tab => tab.value === initialTab)?.value,
  );
  const [showAddTrainingContentDialogue, setShowAddTrainingContentDialogue] =
    React.useState(false);

  const popoverDetails = usePopover();
  const popoverAdd = usePopover();

  const translate = useTranslate();

  const handleChangeTab = React.useCallback(
    (_: React.SyntheticEvent, newValue: string) => {
      setCurrentTab(newValue);
    },
    [],
  );

  const {
    dataGridProps: ListTrainingsContentProps,
    filters,
    setFilters,
    tableQuery: listTrainingContentQuery,
  } = useDataGrid({
    resource: `trainings/modules/${id}/contents`,
    filters: {
      mode: 'server', // Enable server-side filtering
    },
    pagination: { pageSize: 10 },
    syncWithLocation: false, // Disable syncing with URL parameters
  });

  const trainingsContentColumns = React.useMemo<GridColDef[]>(() => [], []);

  const handleBack = () => {
    router.back();
  };

  const refetchTrainingContent = React.useCallback(() => {
    listTrainingContentQuery.refetch();
  }, [listTrainingContentQuery]);

  const { autocompleteProps: organizationAutocompleteProps } = useAutocomplete({
    resource: 'organizations',
    defaultValue: trainingModulesDetails?.organizationId,
  });

  const { autocompleteProps: practitionerAutocompleteProps } = useAutocomplete({
    resource: 'staff',
    filters: [
      {
        field: 'ids',
        value: trainingModulesDetails?.createdBy?.id,
        operator: 'eq',
      },
    ],
  });

  const onSubmit = async (values: FieldValues) => {
    const payload = {
      description: values.description,
      name: values.name,
      role: values.role,
    };
    await onFinish(payload);
    setShowEdit(false);
  };

  const renderEditForm = (
    <Stack
      spacing={3}
      component="form"
      autoComplete="off"
      sx={{
        flexDirection: 'column',
        display: showEdit ? 'flex' : 'none',
      }}
    >
      <OrganizationDropdown
        control={control}
        name="organizationId"
        label={translate('trainings-modules.fields.organizationId')}
        value={trainingModulesDetails?.organizationId}
        error={!!(errors as any)?.organizationId}
        helperText={(errors as any)?.organizationId?.message}
        disabled={true}
      />
      <TextField
        {...register('name', {
          required: 'This field is required',
        })}
        fullWidth
        type="text"
        name="name"
        InputLabelProps={{ shrink: true }}
        label={translate('trainings-modules.fields.name')}
        error={!!(errors as any)?.name}
        helperText={(errors as any)?.name?.message}
      />
      <TextField
        {...register('description', {
          required: 'This field is required',
        })}
        fullWidth
        type="text"
        name="description"
        multiline
        minRows={4}
        maxRows={4}
        InputLabelProps={{ shrink: true }}
        label={translate('trainings-modules.fields.description')}
        error={!!(errors as any)?.description}
        helperText={(errors as any)?.description?.message}
      />

      <AgentRoleDropDown
        control={control}
        name="role"
        label={translate('trainings-modules.fields.role')}
        value={trainingModulesDetails?.role ?? null}
        error={!!(errors as any)?.role}
        helperText={(errors as any)?.role?.message}
      />
      <TextField
        fullWidth
        type="text"
        name="storage.containerName"
        value={trainingModulesDetails?.storage?.containerName}
        multiline
        inputProps={{
          readOnly: true,
        }}
        InputLabelProps={{ shrink: true }}
        label={translate('trainings-modules.fields.storage')}
        error={!!(errors as any)?.description}
        helperText={(errors as any)?.description?.message}
        disabled={true}
      />

      <DatePicker
        label={translate('trainings-modules.fields.created_on')}
        defaultValue={
          trainingModulesDetails?.createDate
            ? new Date(trainingModulesDetails.createDate)
            : null
        }
        disabled
      />

      <PractitionerDropdown
        control={control}
        name="createdBy"
        label={translate('trainings-modules.fields.created_by')}
        value={trainingModulesDetails?.createdBy?.id ?? ''}
        error={!!(errors as any)?.createdBy}
        helperText={(errors as any)?.createdBy?.message}
        disabled={true}
      />
    </Stack>
  );

  return (
    <>
      <TrainingModulesDetailsPopover
        popover={popoverDetails}
        onOpenEdit={() => {
          setShowEdit(true);
          popoverDetails.onClose();
        }}
        deleteButtonProps={{
          onSuccess: () => {
            popoverDetails.onClose();
            router.back();
          },
        }}
      />
      <TrainingModulesAddPopover
        popover={popoverAdd}
        refetchTrainingContent={refetchTrainingContent}
      />

      <TrainingModulesViewToolbar
        refetchTrainingContent={refetchTrainingContent}
        onOpenAdd={popoverAdd.onOpen}
        onBack={handleBack}
        onToggleTrainingModules={() =>
          setShowTrainingModulesDetails(!showTrainingModulesDetails)
        }
        showTrainingModulesDetails={showTrainingModulesDetails}
        title={<>{trainingModulesDetails?.name}</>}
      />
      <Stack
        sx={{
          ...(showTrainingModulesDetails && {
            width: {
              sm: `calc(100% - ${DRAWER_WIDTH.sm}px)`,
              md: `calc(100% - ${DRAWER_WIDTH.md}px)`,
              lg: `calc(100% - ${DRAWER_WIDTH.lg}px)`,
            },
          }),
        }}
      >
        <TrainingModulesTabs
          tabOptions={TABS}
          currentTab={currentTab as string}
          onChangeTab={handleChangeTab}
        />

        {!isLoading && currentTab === 'training-contents' && (
          <ListTrainingsContent
            {...ListTrainingsContentProps}
            columns={trainingsContentColumns}
            refetchTrainingContent={refetchTrainingContent}
            filters={filters}
            setFilters={setFilters}
          />
        )}

        <Drawer
          anchor="right"
          variant="persistent"
          open={showTrainingModulesDetails}
          PaperProps={{
            sx: {
              zIndex: 999,
              top: 8 * 18.5,
              boxSizing: 'border-box',
              width: {
                xs: DRAWER_WIDTH.xs,
                sm: DRAWER_WIDTH.sm,
                md: DRAWER_WIDTH.md,
                lg: DRAWER_WIDTH.lg,
              },
            },
          }}
          sx={{
            flexShrink: 0,
            width: {
              xs: DRAWER_WIDTH.xs,
              sm: DRAWER_WIDTH.sm,
              md: DRAWER_WIDTH.md,
              lg: DRAWER_WIDTH.lg,
            },
          }}
        >
          <Edit
            saveButtonProps={{
              ...saveButtonProps,
              onClick: handleSubmit(onSubmit),
            }}
            breadcrumb={null}
            goBack={null}
            wrapperProps={{
              sx: {
                flexGrow: 1,
                borderRadius: 0,
                boxShadow: 'none',
                overflowY: 'auto',
                mb: `${8 * 18.5}px`,
              },
            }}
            headerButtonProps={{
              sx: { display: 'none' },
            }}
            headerProps={{
              title: translate('trainings-modules.details'),
              action: (
                <>
                  {showEdit ? (
                    <Button
                      variant="outlined"
                      startIcon={<Iconify icon={'eva:close-fill'} />}
                      onClick={() => setShowEdit(false)}
                    >
                      Cancel
                    </Button>
                  ) : (
                    <IconButton onClick={popoverDetails.onOpen}>
                      <Iconify icon="eva:more-vertical-fill" />
                    </IconButton>
                  )}
                </>
              ),
            }}
            footerButtonProps={{
              ...(!showEdit && {
                sx: { display: 'none' },
              }),
            }}
            deleteButtonProps={{
              confirmTitle: translate('patients.titles.delete'),
              confirmContent: translate('content.confirm.delete'),
            }}
          >
            {showEdit && !isLoading && renderEditForm}

            <TrainingModulesSidebar
              trainingModulesDetails={trainingModulesDetails}
              showEdit={showEdit}
              organizationAutocompleteProps={organizationAutocompleteProps}
              practitionerAutocompleteProps={practitionerAutocompleteProps}
            />
          </Edit>
        </Drawer>
      </Stack>
    </>
  );
};
