import type { ChatMessage, ChatMetadata, WidgetData } from 'relic-ui';
import type { AcsChatMessageReceivedInThreadEventData } from '@azure/eventgrid';
import type { ChatCompletionMessageParam } from 'openai/resources';

// Messages sent between Thread Call Start and Thread Call End are considered speech messages.
export const isSpeechMessage = (message: ChatMessage | AcsChatMessageReceivedInThreadEventData): boolean => {
  const metadata = message?.metadata as ChatMetadata;
  if (metadata?.callConnectionId) {
    return true;
  }
  return false;
};

// Notifications are generated by the call service to notify users of call start and call end on a thread.
export const isNotification = (message: ChatMessage | AcsChatMessageReceivedInThreadEventData): boolean => {
  const metadata = message?.metadata as ChatMetadata;
  if (metadata?.type === 'widget') {
    const widgetData = JSON.parse(metadata.widgetData) as WidgetData;
    return widgetData.widget === 'CallNotificationWidget';
  }
  return false;
};

// Widget messages are generated by user interface widget or AI agents.
export const isWidgetMessage = (message: ChatMessage | AcsChatMessageReceivedInThreadEventData): boolean => {
  if (message.type !== 'text') {
    return false;
  }
  return message?.metadata?.type === 'widget';
};

// Chat messages are sent by users through a chat interface's SendBox or AI agents.
export const isChatMessage = (message: ChatMessage | AcsChatMessageReceivedInThreadEventData): boolean => {
  if (message.type !== 'text') {
    return false;
  }
  return message?.metadata?.type === 'chat';
};

// Call messages are generated from interactions during phone or web calls.
export const isCallMessage = (message: ChatMessage | AcsChatMessageReceivedInThreadEventData): boolean => {
  if (message.type !== 'text') {
    return false;
  }
  return message?.metadata?.type === 'call';
};

// Summary messages are generated by a summarization service to provide a concise overview.
export const isSummaryMessage = (message: ChatMessage | AcsChatMessageReceivedInThreadEventData): boolean => {
  if (message.type !== 'text') {
    return false;
  }
  return message?.metadata?.type === 'summary';
};

/**
 * Creates a message object formatted for OpenAI's input requirements.
 *
 * @param role - The role of the message sender (e.g., 'system', 'user', 'assistant').
 * @param content - The content of the message.
 * @returns The formatted message object for OpenAI.
 */
export const createOpenAiMessage = (
  role: 'system' | 'user' | 'assistant',
  content: string,
): ChatCompletionMessageParam => {
  return {
    role,
    content,
  };
};
