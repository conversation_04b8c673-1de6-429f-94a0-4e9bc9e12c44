('use strict');

/**
 * Relic Care Plan Type for managing PCC Patients Care Plan
*/

type RelicCarePlanFocusGoals = {
  goalId: string;
  status: string;
  description: string;
  createdDate: string;
  createdBy: string;
  revisionDate: string;
  revisionBy: string;
  targetDate: string;
  initiatedDate: string;
  resolvedDate: string;
}

type RelicCarePlanFocusInterventions = {
  interventionId: string;
  status: string;
  description: string;
  instruction: string;
  frequency: string;
  createdDate: string;
  createdBy: string;
  revisionDate: string;
  revisionBy: string;
  initiatedDate: string;
}

export type RelicFocus = {
  focusId: string;
  description: string;
  createdDate: string;
  status?: string;
  createdBy: string;
  initiatedDate: string;
  revisionDate: string;
  revisionBy: string;
  resolvedDate: string;
  goals: RelicCarePlanFocusGoals[];
  interventions: RelicCarePlanFocusInterventions[];
};

export type RelicCarePlan = {
  id: string;
  status?: 'active' | 'closed';
  facId: string;
  nextReviewDate: string;
  patientId: string;
  revisionBy: string;
  revisonDate: string;
  createdBy: string;
  createdDate: string;
  closedBy: string;
  closedDate: string;
  closureReason: string;
  focuses?: RelicFocus[];
};
