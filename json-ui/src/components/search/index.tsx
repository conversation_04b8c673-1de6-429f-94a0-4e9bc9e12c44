import { useLocation } from 'react-router-dom';
import React, { useState, useEffect } from 'react';

import SearchIcon from '@mui/icons-material/Search';
import { Box, Stack, TextField, InputAdornment } from '@mui/material';

import { useSearch } from 'src/hooks/use-search';
import { useDebounce } from 'src/hooks/use-debounce';

export const Search: React.FC = () => {
  const { globalSearch, setGlobalSearch } = useSearch();
  const location = useLocation();
  const [error, setError] = useState(false);
  const [searchValue, setSearchValue] = useState(globalSearch);

  const debouncedSetGlobalSearch = useDebounce((value: string) => {
    setGlobalSearch(value);
  }, 300);

  useEffect(() => {
    setSearchValue('');
    setGlobalSearch('');
  }, [location.pathname, setGlobalSearch]);

  // Keep local search value in sync with global search
  useEffect(() => {
    setSearchValue(globalSearch);
  }, [globalSearch]);

  const handleSearch: React.ChangeEventHandler<HTMLInputElement> = e => {
    const alphanumericRegex = /^[a-zA-Z0-9 ]*$/;
    const value = e.target.value;

    if (alphanumericRegex.test(value)) {
      setError(false);
      setSearchValue(value); // Update local state immediately
      debouncedSetGlobalSearch(value); // Debounce the global state update
    } else {
      setError(true);
    }
  };

  const handleKeyDown: React.KeyboardEventHandler<HTMLInputElement> = e => {
    if (e.key === 'Enter') {
      e.currentTarget.blur();
    }
  };

  return (
    <Stack
      direction={{ xs: 'column', sm: 'row' }}
      justifyContent="space-between"
      alignItems="center"
      gap={{ xs: '8px', sm: '24px' }}
    >
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'flex-end',
          mt: 2,
          mr: 2,
          width: '100%',
        }}
      >
        <TextField
          placeholder="Search..."
          size="small"
          sx={{
            width: '100%',
            maxWidth: { sm: 290 },
            minWidth: { sm: 290 },
          }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
            'aria-label': 'Search input',
          }}
          value={searchValue} // Use local state for immediate feedback
          onChange={handleSearch}
          onKeyDown={handleKeyDown}
          variant="standard"
          error={error}
          helperText={
            error ? 'Use only alphanumeric characters for search.' : ''
          }
        />
      </Box>
    </Stack>
  );
};
