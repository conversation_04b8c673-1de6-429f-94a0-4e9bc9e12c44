import { useBack } from '@refinedev/core';

import LockOutlinedIcon from '@mui/icons-material/LockOutlined';
import { Box, Button, Container, Typography } from '@mui/material';

const UnAuthorized = () => {
  const goBack = useBack();

  return (
    <Container maxWidth="sm" sx={{ textAlign: 'center', my: 3 }}>
      <Box
        display="flex"
        flexDirection="column"
        alignItems="center"
        justifyContent="center"
      >
        <LockOutlinedIcon color="error" sx={{ fontSize: 80 }} />
        <Typography variant="h4" gutterBottom>
          Access Denied
        </Typography>
        <Typography variant="body1" color="textSecondary" paragraph>
          You do not have the permissions to view this page.
        </Typography>
        <Button
          variant="contained"
          color="primary"
          onClick={goBack}
          sx={{ mt: 2 }}
        >
          Back
        </Button>
      </Box>
    </Container>
  );
};

export default UnAuthorized;
