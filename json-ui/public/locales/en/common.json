{"pages": {"login": {"title": "Relic AI Assistants", "b2ctitle": "Welcome Residents!", "signin": "Sign in", "signup": "Sign up", "divider": "or", "fields": {"email": "Email", "password": "Password"}, "errors": {"validEmail": "Invalid email address"}, "buttons": {"submit": "<PERSON><PERSON>", "forgotPassword": "Forgot password?", "noAccount": "Don’t have an account?", "rememberMe": "Remember me"}}, "forgotPassword": {"title": "Forgot your password?", "fields": {"email": "Email"}, "errors": {"validEmail": "Invalid email address"}, "buttons": {"submit": "Send reset instructions"}}, "register": {"title": "Sign up for your account", "fields": {"email": "Email", "password": "Password"}, "errors": {"validEmail": "Invalid email address"}, "buttons": {"submit": "Register", "haveAccount": "Have an account?"}}, "updatePassword": {"title": "Update password", "fields": {"password": "New Password", "confirmPassword": "Confirm new password"}, "errors": {"confirmPasswordNotMatch": "Passwords do not match"}, "buttons": {"submit": "Update"}}, "error": {"info": "You may have forgotten to add the {{action}} component to {{resource}} resource.", "404": "Sorry, the page you visited does not exist.", "resource404": "Are you sure you have created the {{resource}} resource.", "backHome": "Back Home"}}, "my-account": {"title": "My Account", "fields": {"name": "Name", "email": "Email Address", "phone": "Phone", "organization": "Organization", "provider": "Provider"}, "buttons": {"update": "Update", "updating": "Updating..."}, "errors": {"required": "This field is required"}, "tabs": {"conversations": "Conversations", "documents": "Documents"}}, "actions": {"list": "List", "create": "Add", "edit": "Edit", "show": "Show"}, "buttons": {"create": "Add", "save": "Save", "logout": "Logout", "delete": "Delete", "edit": "Edit Info", "cancel": "Cancel", "confirm": "Are you sure?", "filter": "Filter", "clear": "Clear", "refresh": "Refresh", "show": "View Details", "undo": "Undo", "import": "Import", "clone": "<PERSON><PERSON>", "notAccessTitle": "You don't have permission to access", "markInactive": "<PERSON> inactive", "btnConfirm": "Confirm", "translate": "Translate", "retry": "Retry", "upload": "Upload", "remove": "Remove", "remove-all": "Remove All", "next": "Next", "back": "Back", "submit": "Submit", "select-all": "Select All", "deselect-all": "Deselect All", "crawl": "Crawl", "show-links": "Show Links", "edit-urls": "Edit URLs"}, "warnWhenUnsavedChanges": "Are you sure you want to leave? You have unsaved changes.", "notifications": {"success": "Successful", "error": "Error (status code: {{statusCode}})", "undoable": "You have {{seconds}} seconds to undo", "createSuccess": "Successfully added {{resource}}", "createError": "There was an error adding {{resource}} (status code: {{statusCode}})", "deleteSuccess": "Successfully deleted {{resource}}", "deleteError": "Error when deleting {{resource}} (status code: {{statusCode}})", "editSuccess": "Successfully edited {{resource}}", "editError": "Error when editing {{resource}} (status code: {{statusCode}})", "importProgress": "Importing: {{processed}}/{{total}}", "markInactiveSuccess": "Successfully marked {{resource}} as inactive", "markInactiveError": "Error when marking {{resource}} as inactive (status code: {{statusCode}})"}, "loading": "Loading", "tags": {"clone": "<PERSON><PERSON>"}, "dashboard": {"title": "Dashboard"}, "blog_posts": {"blog_posts": "Blog Posts", "fields": {"id": "Id", "title": "Title", "content": "Content", "status": "Status", "createdAt": "Created At", "category": "Category"}, "titles": {"create": "Create Blog Post", "edit": "Edit Blog Post", "list": "Blog Posts", "show": "Show Blog Post"}}, "categories": {"categories": "Categories", "fields": {"id": "Id", "title": "Title", "createdAt": "Created At"}, "titles": {"create": "Create Category", "edit": "Edit Category", "list": "Categories", "show": "Show Category"}}, "staff": {"title": "Staff Members", "fields": {"givenName": "First Name", "surname": "Last Name", "name": "Name", "provider": "Provider", "email": "Email Address", "mobilePhone": "Mobile Phone", "organizationId": "Organization", "inviteAccepted": "Invite Accepted", "status": "Status", "statusValues": {"disabled": "Disabled", "enabled": "Enabled"}, "lastSuccessfulSignInDateTime": "Last Login Date", "sendInvite": "Invite Staff Member"}, "placeholders": {"organizationId": "Select your organization"}, "tooltips": {"organizationId": "Organizations for staff members. Facilities that you have permission for will be shown."}, "titles": {"create": "Add Staff Member", "edit": "Edit Staff Member", "list": "Staff Members", "show": "Staff Member Details", "delete": "Delete Staff Member"}}, "patients": {"patients": "Residents", "newPatient": "New Resident", "fields": {"name": "Name", "birthDate": "Date of Birth", "gender": "Gender", "maritalStatus": "Marital Status", "email": "Email Address", "mobilePhone": "Mobile Phone", "homePhone": "Home Phone", "link": "Relative", "active": "Active", "organizationId": "Organization", "sendInvite": "<PERSON><PERSON><PERSON>", "primaryLanguage": "Primary Language", "location": "Location"}, "titles": {"details": "Resident Details", "delete": "Delete Patient Member"}, "status": {"new": "New", "current": "Current", "discharged": "Discharged"}}, "conditions": {"conditions": "Conditions", "fields": {"code": "Code", "note": "Note", "clinicalStatus": "Status"}, "titles": {"create": "New Condition", "delete": "Delete Condition"}}, "allergyintolerances": {"allergyintolerances": "Allergies", "fields": {"code": "Code", "note": "Note", "clinicalStatus": "Status"}, "titles": {"create": "New Allergie", "delete": "Delete Allergie"}}, "chat": {"staffAi": "Compliance Asst.", "interpreterAi": "Interpreter Asst.", "patientAi": "Resident Asst."}, "documentTitle": {"default": "Relic Care", "suffix": "", "blog_posts": {"list": "Blog Posts | refine", "show": "#{{id}} Show Blog Post | refine", "edit": "#{{id}} Edit Blog Post | refine", "create": "Create new Blog Post | refine", "clone": "#{{id}} Clone Blog Post | refine"}, "categories": {"list": "Categories | refine", "show": "#{{id}} Show Category | refine", "edit": "#{{id}} Edit Category | refine", "create": "Create new Category | refine", "clone": "#{{id}} Clone Category | refine"}}, "table": {"actions": "Actions"}, "placeholder": {"facility": "Select a Facility"}, "content": {"confirm": {"delete": "Are you sure you want to delete?"}}, "selects": {"empty": "Choose a option", "gender": {"male": "Male", "female": "Female", "other": "Other", "incognito": "Do not disclose"}, "maritalStatus": {"unmarried": "Unmarried", "married": "Married", "annulled": "Annulled", "divorced": "Divorced", "interlocutory": "Interlocutory", "legally_separated": "Legally Separated", "polygamous": "Polygamous", "never_married": "Never Married", "domestic_partner": "Domestic partner", "widowed": "Widowed", "unknown": "Unknown"}, "clinicalStatus": {"active": "Active", "recurrence": "Recurrence", "relapse": "Relapse", "inactive": "Inactive", "remission": "Remission", "resolved": "Resolved"}}, "status": {"active": "Active", "invited": "Invited", "inactive": "Inactive", "notInvited": "Not Invited", "enabled": "Enabled", "disabled": "disabled"}, "organizations": {"organizations": "Organizations", "newOrganization": "New Organization", "fields": {"id": "Id", "name": "Organization Name", "type": "Organization Type", "fax": "Fax", "phone": "Phone", "iconImageUrl": "Logo", "fallbackColor": "Brand Color", "template": "Template", "active": "Active", "website": "Website", "location": "Location", "language": "Default Language for Patients", "resourceType": "Resource Type", "copilotCompany": "Copilot Company", "patientSummary": "Patient Summary Template", "welcomeSms": "Welcome Sms Template", "welcomeEmail": "Welcome Email Template", "postalCode": "Postal Code", "state": "State", "facilityCode": "Code", "pointClickCareId": "PointClickCare Org Id"}, "titles": {"delete": "Delete Facility"}}, "locations": {"locations": "Location", "newLocation": "New Location", "fields": {"id": "Id", "status": "Status", "name": "Name", "phone": "Phone No.", "fax": "Fax No.", "organizationId": "Organization"}, "titles": {"delete": "Delete location", "edit": "Edit Location", "create": "New Location"}}, "documents": {"documents": "Documents", "newDocument": "New Document", "fields": {"id": "Id", "upload": "Upload Document", "name": "Document Name", "language": "Document Language", "dateCreated": "Created on", "organizationName": "Organization", "source": "Document for Translation", "target": "Translated Document", "translationType": "Document Format"}, "titles": {"create": "New Document", "delete": "Delete Document", "edit": "Edit", "generating_pdf": "Generating PDF"}}, "facilities": {"facilities": "Facilities", "newFacility": "New Facility", "fields": {"id": "Id", "facilityCode": "Facility Code", "facilityName": "Facility Name", "postalCode": "Postal Code", "state": "State", "active": "Active", "addressLine1": "Address Line1", "addressLine2": "Address Line2", "bedCount": "Bed Count", "billingStyleCountry": "Billing Style Country", "city": "City", "country": "Country", "emailAddress": "Email Address", "environment": "Environment", "facilityStatus": "Facility Status", "fax": "Fax", "headOffice": "Head Office", "healthType": "Health Type", "orgDbType": "Organization Type", "orgName": "Organization Name", "orgUuid": "Organization UUID", "phone": "Phone", "timeZone": "Time Zone", "timeZoneOffset": "Time Zone Offset", "enabled": "Enabled"}, "titles": {"create": "New Facility", "delete": "Delete", "edit": "Edit", "list": "Facilities"}}, "careplan": {"careplans": "Care Plans", "titles": {"translate": "Translate Care Plan", "delete": "Delete Care Plan", "view": "View Care Plan", "edit": "Edit Care Plan", "create": "Create Care Plan"}, "fields": {"nextReviewDate": "Next Review Date", "createdBy": "Created By", "revisedBy": "Revised By", "dateInitiated": "Date Initiated", "dateModified": "Date Modified", "revisionBy": "Revised By", "admissionDate": "Admission Date", "patientName": "Patient Name", "dob": "Date of Birth", "physician": "Physician", "facility": "Facility", "location": "Location", "allergies": "Allergies", "resident": "Resident", "page": "Page", "of": "of", "position": "Position", "frequencyResolved": "Freq/Resolved", "notAvailable": "N/A", "focus": {"description": "Focus", "status": "Status", "goals": {"description": "Goal", "status": "Goal Status", "targetDate": "Target Date", "dateInitiated": "Date Initiated", "revisionDate": "Revision on"}, "interventions": {"description": "Intervention", "frequency": "Frequency", "status": "Intervention Status", "dateInitiated": "Date Initiated"}}}, "actions": {"translate": "Translate", "retry": "Retry Translation", "view": "View PDF", "generatePdf": "Generate PDF"}, "messages": {"noDocumentAvailable": "No document available for this care plan.", "loading": "Loading Care Plan...", "deleteConfirm": "Are you sure you want to delete this care plan?", "translationSuccess": "Translation completed successfully.", "translationFailed": "Failed to translate care plan.", "pdfGenerationInProgress": "Generating PDF...", "pdfGenerationError": "Error generating PDF.", "deleteSuccess": "Care plan deleted successfully.", "deleteError": "Error deleting care plan.", "noFocusItems": "No focus items available."}, "errors": {"loadFailure": "Failed to load care plan data.", "validLanguage": "Please select a valid language.", "uploadFailed": "Failed to upload document."}, "helper": {"chooseLanguage": "Select a language for translation", "translationType": "<PERSON>ose “Retain Original” to maintain the original document format."}}, "agents": {"agents": "AI Assistants", "details": "Asst. Details", "setup": "<PERSON><PERSON><PERSON>", "title": "AI Assistant", "newAgent": "New Agent", "createTemplate": "Create Template", "createSamples": "Create Samples", "editTemplate": "Edit Template", "editSamples": "<PERSON>", "edit": {"alert": {"title": "<PERSON>", "agentEditAlert": "Assistants belonging to other organizations cannot be edited. Add your own AI Assistant before editing."}}, "fields": {"name": "Name", "type": "Type", "systemPrompt": "System Prompt", "organizationId": "Organization", "active": "Active", "role": "Role", "maxResponseLength": "Max Response Length", "bio": "Bio", "avatarUrl": "Avatar Url", "coverUrl": "Cover Url", "temperature": "Temperature", "knowledgeBase": "Knowledgebase", "topProbabilities": "Top Probabilities", "stopSequences": "Stop Sequences", "frequencyPenalty": "Frequency Penalty", "presencePenalty": "Presence Penalty", "event": "Event", "greetingTemplate": "Greeting Template", "chatbotResponse": "Asst. Response", "userInput": "User Input", "responseFrom": "Response From", "openAi": "OpenAI", "training": "Training"}, "tabs": {"prompt": "Prompt", "samples": "<PERSON><PERSON>", "templates": "Templates", "knowledgeBase": "Knowledgebase"}, "titles": {"create": "New AI Assistant"}}, "threads": {"threads": "Conversations", "fields": {"ai-participant": "AI Assistant", "participant": "Participant", "language": "Language", "title": "Title", "organization": "Organization", "invited-via": "Invited Via", "status": "Status", "resident": "Resident", "invitation": "Invite via", "cellphone": "Cellphone", "email": "Email"}, "titles": {"mine": "My Conversations", "create": "New Conversation", "details": "Conversations Details"}, "status": {"all-conversations": "All Conversations", "my-conversations": "My Conversations"}}, "trainings-modules": {"label": "Training Modules", "details": "Training Module Details", "titles": {"create": "New Training Module"}, "fields": {"name": "Name", "description": "Description", "role": "Role", "storage": "Storage", "organizationId": "Organization", "created_by": "Created By", "created_on": "Created On"}, "tabs": {"training-contents": "Training Contents", "delete": "Delete Training Module"}}, "trainings-content": {"label": "Training Content", "add-via-file": "Training via File", "add-via-browsing": "Training via Browsing", "add-via-web-pages": "Training via Web Pages", "fields": {"contentType": "Content Type", "content": "Content", "folder_name": "Folder Name", "starting_url": "Starting Url", "status": "Status", "last_update": "Updated", "note": "Note", "relevance-check": "Include only Readable Content (Recommended)", "file_description": "Click on \"Content\" link to view what your AI Assistant read from the file.", "browsed_description": "Click on \"Content\" link to view what your AI Assistant read from the file.", "folder_description": "This folder contains web pages read by your AI Assistant.", "status_labels": {"file": {"pending": "Waiting to start reading...", "completed": "Completed"}, "url_root": {"pending": "Waiting to start browsing...", "estimating_sitemap": "Browsing links and listing them...", "inprogress": "Reading web page...", "completed": "Completed"}, "url_leaf": {"pending": "Waiting to start reading...", "completed": "Completed"}, "folder": {"pending": "Waiting to start reading...", "inprogress": "Reading web page...", "completed": "Completed"}}, "max-request-per-crawl": "Max Request Per Crawl (1 to 300)", "parent-training-content": "Parent", "training-content": "Content", "readable-content": "Include only Readable Content (Recommended)", "max-requests": "Max links to browse (20 to 200)", "title": "Title", "links": "Links", "web-pages": "Web Pages *", "sitemaps": "Web Pages", "folder-title": "Folder Title", "drop-or-select-file": "Drop or select file", "drop-or-select-files": "Drop or select files", "drop-files-here": "Drop files here or click to browse", "urls-helper-text": "You can only remove existing URLs, not add new ones."}, "estimating-note": "Sitemap estimation for this training content is currently in progress. The sitemap will be available once the estimation process is complete and the status changes to Sitemap Ready.", "options": {"url": "URL", "file": "File"}, "titles": {"create": "New Training Content", "view": "View Training Content", "view_estimating": "View Browsed Content (Estimating)", "view_file": "View File Content", "view_browsed": "View Browsed Content", "view_folder": "View Folder", "add-via-file": "Add Training via File", "add-via-browsing": "Add Training via Browsing", "add-via-web-pages": "Add Training via Web Pages", "crawl-content": "Add Training via Crawled Sitemap"}, "subtitles": {"add-via-file": "Your AI Assistant will read submitted file(s). The knowledge gained from these files will be prioritized during interactions.", "add-via-browsing": "Your AI Assistant will browse the web from starting url and read all web pages linked from starting url. The knowledge gained from these web pages will be prioritized during interactions.", "add-via-web-pages": "Your AI Assistant will read these web pages. The knowledge gained from these web pages will be prioritized during interactions."}}, "trainings": {"label": "Trainings", "fields": {"name": "Name", "status": "Status", "last_update_on": "Last Update On", "description": "Description", "organizationId": "Organization"}, "titles": {"create": "New Training", "update-modules": "Update Mo<PERSON>les"}, "tabs": {"training-modules": "Training Modules"}, "messages": {"add-modules-info": "Please perform additions and deletions as separate actions; both cannot be done simultaneously."}}, "demo": {"fields": {"name": "Name", "email": "Email", "mobilePhone": "Phone", "facility_type": "Facility Type", "facility": "Facility", "state": "State", "scenario": "<PERSON><PERSON><PERSON>", "persona": "<PERSON>a", "primaryLanguage": "Language", "experience_via": "Experience Via"}}, "onboarding": {"fields": {"givenName": "First Name", "surname": "Last Name", "email": "Email"}, "buttons": {"submit": "Submit"}, "title": "Register for Relic AI", "messages": {"success": "Welcome, {{name}}!", "error": "Error during registration."}}, "reports": {"careplan": {"allergies": "Allergies", "dob": "Date of Birth", "physician": "Physician", "facility": "Facility", "resident": "Resident", "admissionDate": "Admission Date", "location": "Location", "page": "Page", "of": "of", "focus": "Focus", "goal": "Goal", "interventions": "Interventions", "position": "Position", "frequencyResolved": "Frequency/Resolved", "dateInitiated": "Date Initiated", "revisionDate": "Revision On", "createdBy": "Created By", "revisionBy": "Revision By", "targetDate": "Target Date", "notAvailable": "N/A", "noFocusItems": "No focus items available"}}}