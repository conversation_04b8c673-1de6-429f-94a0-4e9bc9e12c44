import React from 'react';
import ReactDOMServer from 'react-dom/server';
import { compile } from '@fileforge/react-print';
import {
  CSS,
  Tailwind,
  PageBottom,
  PageNumber,
  PagesNumber,
} from '@fileforge/react-print';
import {
  RelicPatient,
  RelicDocument,
  PccPatientCarePlan,
  PccCarePlanFocusResponse,
} from 'relic-ui';

import { generateAndUploadPdf } from '../../../utils/pdf-utils';

interface CarePlanProps {
  relicPatient: RelicPatient;
  relicDocument: RelicDocument;
  pccPatientCarePlan: PccPatientCarePlan;
  labels: any;
}

const CarePlanTemplate = ({
  relicPatient,
  relicDocument,
  pccPatientCarePlan,
  labels,
}: CarePlanProps) => {
  // const translations = labels[relicDocument.language] || labels.en;
  const carePlan: PccPatientCarePlan = pccPatientCarePlan;
  const focuses: PccCarePlanFocusResponse[] = carePlan.focuses || [];
  const languageCode = relicDocument.language.toLowerCase();
  let fontUrl = '';
  let fontFamily = '';
  let fontSize = '';
  switch (languageCode) {
    case 'en':
    case 'en-us':
      break;
    case 'my':
      fontUrl =
        'https://fonts.googleapis.com/css2?family=Noto+Sans+Myanmar:wght@100;200;300;400;500;600;700;800;900&display=swap';
      fontFamily = '"Noto Sans Myanmar", sans, sans-serif';
      fontSize = '13px';
      break;
    default:
      break;
  }

  return (
    <>
      {/* Set up international fonts for the print template */}
      {fontUrl && (
        <CSS>{`
          @import url('${fontUrl}');
        `}</CSS>
      )}
      {fontFamily && fontSize && (
        <CSS>{`
          html,
          body {
            font-family: ${fontFamily};
            font-size: ${fontSize};
          }
        `}</CSS>
      )}
      {/* Set up CSS for the print template */}
      <CSS>{`
        @page { 
          size: a4 landscape;
          margin: 0.5in 0.5in 1.8in 0.5in;
        }
        .content {
          flex: 1;
        }
      }
      `}</CSS>
      <Tailwind>
        {/* Footer Section - PageBottom */}
        <PageBottom>
          <br />
          <table className="w-full table-fixed border-collapse bg-white border border-black text-sm">
            <tbody>
              <tr>
                <td className="border border-black bg-gray-200 p-1 w-1/12">
                  {labels.allergies}
                </td>
                <td className="border border-black p-1 w-4/12">
                  {relicPatient?.name}
                </td>
                <td className="border border-black bg-gray-200 p-1 w-2/12">
                  {labels.dob}
                </td>
                <td colSpan={2} className="border border-black p-1 w-2/12">
                  {relicPatient?.birthDate}
                </td>
                <td className="border border-black bg-gray-200 p-1 w-1/12">
                  {labels.physician}
                </td>
                <td className="border border-black p-1 w-2/12">
                  {relicPatient?.pccPractitionerName}
                </td>
              </tr>
              <tr>
                <td className="border border-black bg-gray-200 p-1 w-2/12">
                  {labels.facility}
                </td>
                <td colSpan={6} className="border border-black p-1 w-10/12">
                  {relicPatient?.pccFacilityName}
                </td>
              </tr>
              <tr>
                <td className="border border-black bg-gray-200 p-1 w-1/12">
                  {labels.resident}
                </td>
                <td className="border border-black p-1 w-4/12">
                  {relicPatient?.pccPatient?.lastName},{' '}
                  {relicPatient?.pccPatient?.firstName}{' '}
                  {relicPatient?.pccPatient?.middleName} (
                  {relicPatient?.pccPatient?.medicalRecordNumber})
                </td>
                <td className="border border-black bg-gray-200 p-1 w-2/12">
                  {labels.admissionDate}
                </td>
                <td colSpan={2} className="border border-black p-1 w-2/12">
                  {relicPatient?.pccPatient?.admissionDate}
                </td>
                <td className="border border-black bg-gray-200 p-1 w-1/12">
                  {labels.location}
                </td>
                <td className="border border-black p-1 w-2/12">
                  {relicPatient?.pccPatient?.unitDesc}{' '}
                  {relicPatient?.pccPatient?.roomDesc}
                  {relicPatient?.pccPatient?.bedDesc}
                </td>
              </tr>
            </tbody>
          </table>
          <table className="w-full table-fixed border-collapse bg-white text-sm">
            <tbody>
              <tr>
                <td className="text-left p-1 w-7/12 text-sm font-bold">
                  {'en-us' !== relicDocument.language.toLowerCase() &&
                    'en' !== relicDocument.language.toLowerCase() &&
                    labels.aiDisclaimer}
                </td>
                <td className="text-right p-1 w-3/12">
                  {relicPatient?.pccPatient?.lastName},{' '}
                  {relicPatient?.pccPatient?.firstName}{' '}
                  {relicPatient?.pccPatient?.middleName} (
                  {relicPatient?.pccPatient?.medicalRecordNumber})
                </td>
                <td className="text-right font-bold p-1 w-2/12">
                  <span className="pr-1">{labels.page}</span>
                  <PageNumber counterStyle="decimal" />
                  <span className="pl-1 pr-1">{labels.of}</span>
                  <PagesNumber counterStyle="decimal" />
                </td>
              </tr>
            </tbody>
          </table>
        </PageBottom>
        <div>
          {/* Main Content Section */}
          <main className="content p-0">
            <table className="w-full border-collapse border border-black text-sm">
              <thead>
                <tr>
                  <th className="w-3/12 border border-black p-0">
                    {labels.focus}
                  </th>
                  <th className="w-3/12 border border-black p-0">
                    {labels.goal}
                  </th>
                  <th className="w-5/12 border border-black p-0 border-l-0 border-r-0">
                    {labels.interventions}
                  </th>
                  <th className="w-1/12 border border-black p-0 border-l-0 border-r-0">
                    {labels.frequencyResolved}
                  </th>
                </tr>
              </thead>
              <tbody>
                {focuses && focuses.length > 0 ? (
                  focuses.map(focus => (
                    <tr key={focus.focusId}>
                      {/* Focus Column */}
                      <td className="w-3/12 border border-black p-0 pl-2 pr-1 align-top">
                        &bull; {focus.description}
                        <br />
                        <br />
                        <span className="text-xs">
                          {labels.dateInitiated}:{' '}
                          {focus.initiatedDate
                            ? new Date(focus.initiatedDate).toLocaleDateString()
                            : ''}
                          <br />
                          {labels.createdBy}: {focus.createdBy}
                          <br />
                          {labels.revisionDate}:{' '}
                          {focus.revisionDate
                            ? new Date(focus.revisionDate).toLocaleDateString()
                            : ''}
                          <br />
                          {labels.revisionBy}: {focus.revisionBy}
                        </span>
                      </td>

                      {/* Goals Column */}
                      <td className="w-3/12 border border-black p-0 pl-2 pr-1 align-top">
                        {focus.goals && focus.goals.length > 0 ? (
                          focus.goals.map(goal => (
                            <div key={goal.goalId}>
                              &bull; {goal.description}
                              <br />
                              <br />
                            </div>
                          ))
                        ) : (
                          <div>{labels.noFocusItems}</div>
                        )}
                      </td>

                      {/* Interventions Column */}
                      <td
                        colSpan={3}
                        className="w-6/12 p-0 align-top border border-black border-l-0 border-r-0"
                      >
                        <table className="w-full border-collapse text-sm">
                          {focus.interventions &&
                          focus.interventions.length > 0 ? (
                            focus.interventions.map(intervention => (
                              <tr key={intervention.interventionId}>
                                <td className="w-10/12 p-0 pl-2 pr-1 align-top border-l-0 border-r-0">
                                  <div key={intervention.interventionId}>
                                    &bull; {intervention.description}
                                    <br />
                                    <br />
                                  </div>
                                </td>
                                <td className="w-2/12 p-0 pl-2 pr-1 align-top border-l-0 border-r-0">
                                  <div
                                    key={`${intervention.interventionId}-freq`}
                                  >
                                    {intervention.frequency}
                                    <br />
                                  </div>
                                </td>
                              </tr>
                            ))
                          ) : (
                            <div>{labels.noFocusItems}</div>
                          )}
                        </table>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td
                      colSpan={5}
                      className="border border-black p-0 text-center"
                    >
                      {labels.noFocusItems}
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </main>
          <br />
        </div>
      </Tailwind>
    </>
  );
};

export const CarePlanDesign = (
  relicPatient: RelicPatient,
  relicDocument: RelicDocument,
  labels: any,
) => {
  const carePlanData: PccPatientCarePlan = relicDocument?.data
    ?.carePlan as PccPatientCarePlan;

  return (
    <>
      <CarePlanTemplate
        relicPatient={relicPatient}
        relicDocument={relicDocument}
        pccPatientCarePlan={carePlanData}
        labels={labels}
      />
    </>
  );
};

/**
 * Generates a PDF report for a care plan and upload it to Azure Blob Storage.
 *
 * @param relicPatient - The patient data associated with the care plan.
 * @param relicDocument - The document containing care plan details.
 * @param organizationId - The ID of the organization for Azure Blob storage path.
 * @param filename - The name for the generated PDF file.
 *
 * @returns A Promise that resolves to the URL of the uploaded PDF report.
 */
export const CarePlanReport = async (
  relicPatient: RelicPatient,
  relicDocument: RelicDocument,
  labels: any,
): Promise<RelicDocument> => {
  //Do not remove tempHtml, it is used to ensure that ReactDomServer is loaded in memory.
  const tempHtml = ReactDOMServer.renderToString(
    CarePlanDesign(relicPatient, relicDocument, labels),
  );
  //compile internally uses ReactDomServer.renderToString
  const reportHtml = await compile(
    CarePlanDesign(relicPatient, relicDocument, labels),
  );
  return generateAndUploadPdf(relicDocument, reportHtml);
};
