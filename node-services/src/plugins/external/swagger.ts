import fp from 'fastify-plugin';
import fastifySwaggerUi from '@fastify/swagger-ui';
import fastifySwagger from '@fastify/swagger';
import { FastifySchema } from 'fastify';
import config from 'config';

export default fp(async function (fastify) {
  /**
   * A Fastify plugin for serving Swagger (OpenAPI v2) or OpenAPI v3 schemas
   *
   * @see {@link https://github.com/fastify/fastify-swagger}
   */
  await fastify.register(fastifySwagger, {
    swagger: {
      info: {
        title: 'Node Services',
        description: 'Relic AI Node Services',
        version: '1.0.0'
      },
      schemes: ['http', 'https'],
      consumes: ['application/json'],
      produces: ['application/json', 'text/html'],
      security: [
        { accessToken: [] },
        { idToken: [] } 
      ],
      securityDefinitions: {
        accessToken: {
          type: 'apiKey',
          name: 'x-access-token',
          description: 'Access Token obtained via OAuth 2.0',
          in: 'header'
        },
        idToken: {
          type: 'apiKey',
          name: 'x-id-token',
          description: 'Id Token obtained via OAuth 2.0',
          in: 'header'
        }
      },
    },
    transform: ({ schema, url, route }) => {
      let transformedUrl = url;
      let transformedSchema = schema;

      // Replace /api/pcc/, /api/medplum/, /local/pcc/, /local/medplum/, /api/entra/, /api/entra-external/ 
      // With /api/{provider}/ in the documented path
      transformedUrl = transformedUrl
        .replace(/^\/api\/pcc\//, '/api/{provider}/')
        .replace(/^\/api\/medplum\//, '/api/{provider}/')
        .replace(/^\/local\/pcc\//, '/local/{provider}/')
        .replace(/^\/local\/medplum\//, '/local/{provider}/')
        .replace(/^\/api\/entra\//, '/api/{provider}/')
        .replace(/^\/api\/entra-external\//, '/api/{provider}/');

      // Document /auth/ proxy routes for OAuth 2.0 token service
      if (transformedUrl.includes('/auth/')) {
        transformedSchema = {
          ...(schema || {}),
          tags: ['Guest Service'],
          description: 'OAuth 2.0 Token Service proxy from supported providers. This service is unsecured and does not require authentication token.',
          summary: 'Unsecured - OAuth 2.0 Token Service proxy from supported providers.'
        } as FastifySchema;
      }

      // Document /flowise/ proxy routes
      if (transformedUrl.includes('/flowise/')) {
        transformedSchema = {
          ...(schema || {}),
          tags: ['Communication Service'],
          description: 'Communication service to interact with Flowise Flows.',
          summary: 'Communication service to interact with Flowise Flows.'
        } as FastifySchema;
      }

      return { schema: transformedSchema, url: transformedUrl };
    }
  });

  if (config.get('RELIC.ENV') !== 'production') {
    /**
     * A Fastify plugin for serving Swagger UI.
     *
     * @see {@link https://github.com/fastify/fastify-swagger-ui}
     */
    await fastify.register(fastifySwaggerUi, {
      routePrefix: '/docs'
    })
  }

})
