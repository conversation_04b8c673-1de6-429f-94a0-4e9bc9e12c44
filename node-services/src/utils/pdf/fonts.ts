import path from "path";
import { TranslateDocumentOptions } from "./types";
import * as fs from 'fs';
import { BlobServiceClient } from "@azure/storage-blob";

const AZ_STORAGE_FONT_CONTAINER: string = 'fonts';
const ROOT_FONT_DIR = path.join(__dirname, AZ_STORAGE_FONT_CONTAINER);
const FALLBACK_FONT_FAMILY = 'calibri';

export class Font {
    name: string; //ex arial-regular, calibri-regular, aptos black
    _internalName: string;
    family: string; //ex. arial, calibri
    fontFile: {
        ttf: string; //arial-calibri.ttf
        identifier: string; //arial-calibri, to be used for font registration
    };
    weight: number;
    alternativeNames: Set<string>; //timesnewroman, times-new-roman
    style: {
        isRegular: boolean;
        isBold: boolean;
        isItalic: boolean;
        isBlack: boolean;
        isLight: boolean;
        isExtra: boolean; //extra or ultra
        isSemi: boolean; //semi or demi
        isMedium: boolean;
        isMono: boolean;
        isCondensed: boolean;
    }
    styleCriteria: Set<string>;

    constructor(name: string, filepath?: string) {
        this.name = name.toLowerCase();
        this._internalName = this.name?.replaceAll(' ', '-');
        this.fontFile = {
            ttf: filepath ?? '',
            identifier: name
        }
        this.styleCriteria = new Set<string>();
        this.style = {
            isBold: this._isBold(),
            isItalic: this._isItalic(),
            isBlack: this._isBlack(),
            isLight: this._isLight(),
            isExtra: this._isExtra(),
            isSemi: this._isSemi(),
            isMedium: this._isMedium(),
            isMono: this._isMono(),
            isCondensed: this._isCondensed(),
            isRegular: false
        };

        this.style.isRegular = !(
            this.style.isBold ||
            this.style.isItalic ||
            this.style.isBlack ||
            this.style.isLight ||
            this.style.isExtra ||
            this.style.isSemi ||
            this.style.isMedium ||
            this.style.isMono ||
            this.style.isCondensed
        )

        if (this.style.isBold) { this.styleCriteria.add('bold') }
        if (this.style.isItalic) { this.styleCriteria.add('italic') }
        if (this.style.isBlack) { this.styleCriteria.add('black') }
        if (this.style.isLight) { this.styleCriteria.add('light') }
        if (this.style.isExtra) { this.styleCriteria.add('extra') }
        if (this.style.isSemi) { this.styleCriteria.add('semi') }
        if (this.style.isMedium) { this.styleCriteria.add('medium') }
        if (this.style.isMono) { this.styleCriteria.add('mono') }
        if (this.style.isCondensed) { this.styleCriteria.add('cond'); }
        if (this.style.isRegular) { this.styleCriteria.add('regular') }

        this.family = this._internalName
            .replace('bold', '')
            .replace('italic', '')
            .replace('black', '')
            .replace('heavy', '')
            .replace('light', '')
            .replace('extra', '')
            .replace('ultra', '')
            .replace('semi', '')
            .replace('demi', '')
            .replace('medium', '')
            .replace('mono', '')
            .replace('condensed', '')
            .replace('cond', '')
            .replace('regular', '')
            .replace('narrow', '')
            .replace('mt', '')
            .replace('ps', '')
            .trim();
        if (this.family.endsWith('-')) {
            this.family = this.family?.replaceAll('-', ' ')?.trim()?.replaceAll(' ', '-');
        }
        this.alternativeNames = new Set();
        this.alternativeNames.add(this._internalName);
        this.alternativeNames.add(this.family);
        this.alternativeNames.add(this._internalName?.replaceAll('-', ''));
    }
    _isBold() { return this._internalName?.includes('bold') }
    _isItalic() { return this._internalName?.includes('italic') }
    _isBlack() { return this._internalName?.includes('black') || this._internalName?.includes('heavy') }
    _isLight() { return this._internalName?.includes('light') }
    _isExtra() { return this._internalName?.includes('extra') || this._internalName?.includes('ultra') }
    _isSemi() { return this._internalName?.includes('semi') || this._internalName?.includes('demi') }
    _isMedium() { return this._internalName?.includes('medium') }
    _isMono() { return this._internalName?.includes('mono') }
    _isCondensed() { return this._internalName?.includes('cond') || this._internalName?.includes('condensed') || this._internalName?.includes('narrow') }

    setWeight(weight: number) {
        this.weight = weight; //we can use font-weight to determine if bold or not
    }
    //from textblocs, use fontItalic
    setItalic(isItalic: boolean) {
        this.style.isItalic = this.style.isItalic ?? isItalic;
        if (this.style.isItalic) {
            this.styleCriteria.add('italic');
        }
    }
}
export type FontTargetLangMapValue = {
    fontname: string;
    targetLang: string;
    sourceFont: Font;
    targetLangFont: Font;
}

export async function getDocumentFontsInfo(docOptions: TranslateDocumentOptions): Promise<Map<string, FontTargetLangMapValue>> {
    const requiredFonts: Font[] = extractFonts(docOptions);
    const supportedFonts: Font[] = await getSupportedFonts(docOptions);
    const docFontsMap: Map<string, FontTargetLangMapValue> = matchFonts(requiredFonts, supportedFonts, docOptions.targetLang);
    return docFontsMap;
}

export async function downloadFonts(docOptions: TranslateDocumentOptions, fonts: Map<string, FontTargetLangMapValue>): Promise<void> {
    const azureBlobServiceClient: BlobServiceClient = docOptions.azureServiceClient;
    fs.mkdirSync(ROOT_FONT_DIR, { recursive: true });
    const containerClient = azureBlobServiceClient.getContainerClient(AZ_STORAGE_FONT_CONTAINER);

    for (const font of fonts.entries()) {
        const fontInfo: FontTargetLangMapValue = font[1];

        //download source font
        let blobName = fontInfo.sourceFont.fontFile.ttf;
        let localFilePath = path.join(ROOT_FONT_DIR, blobName);
        await downloadFontIfUpdated(containerClient, blobName, localFilePath);

        //download target language font
        blobName = fontInfo.targetLangFont.fontFile.ttf;
        localFilePath = path.join(ROOT_FONT_DIR, blobName);
        await downloadFontIfUpdated(containerClient, blobName, localFilePath);
    }
}

async function downloadFontIfUpdated(containerClient: any, blobName: string, localFilePath: string) {
    const blobClient = containerClient.getBlobClient(blobName);
    const blobProperties = await blobClient.getProperties();
    const blobLastModified = new Date(blobProperties.lastModified);

    if (fs.existsSync(localFilePath)) {
        const localFileStats = fs.statSync(localFilePath);
        const localFileLastModified = new Date(localFileStats.mtime);

        if (localFileLastModified >= blobLastModified) {
            console.debug(`font file (${blobName}) is up-to-date in ${ROOT_FONT_DIR}`);
            return;
        }
    }

    await blobClient.downloadToFile(localFilePath);
    console.debug(`font file (${blobName}) downloaded successfully.`);
}

function extractFonts(docOptions: TranslateDocumentOptions): Font[] {
    const textblocksFiles = fs.readdirSync(docOptions.sourceJsonTextblocksFileDir).filter(file => file.endsWith('.json'));
    const fontSet: Set<string> = new Set<string>();
    const fonts: Font[] = [];

    for (let i = 0; i < textblocksFiles.length; i++) {
        const filepath = path.join(docOptions.sourceJsonTextblocksFileDir, textblocksFiles[i]);
        const textblocks = JSON.parse(fs.readFileSync(filepath, 'utf-8'));
        for (let i = 0; i < textblocks.page.lines.length; i++) {
            let fontname = textblocks.page.lines[i].fontName?.trim();
            let fontWeight = Number(textblocks.page.lines[i].fontWeight);
            let fontItalic = textblocks.page.lines[i].fontItalic == 'true';
            if (!fontSet.has(fontname)) {
                const font = new Font(fontname);
                font.setWeight(Number(fontWeight));
                font.setItalic(fontItalic);
                fonts.push(font);
                fontSet.add(fontname);
            }
        }
    }

    return fonts;
}

async function getSupportedFonts(docOptions: TranslateDocumentOptions): Promise<Font[]> {
    const azureBlobServiceClient: BlobServiceClient = docOptions.azureServiceClient;
    const containerClient = azureBlobServiceClient.getContainerClient(AZ_STORAGE_FONT_CONTAINER);
    const fonts: Font[] = [];
    for await (const blob of containerClient.listBlobsFlat()) {
        fonts.push(new Font(path.parse(blob.name).name, blob.name));
    }
    return fonts;
}


//if language, then use language as family
function matchFonts(fonts: Font[], supportedFonts: Font[], language?: string): Map<string, FontTargetLangMapValue> {
    const fontMapping: Map<string, FontTargetLangMapValue> = new Map();
    const fallbackFonts: Font[] = supportedFonts.filter(f => f.family == FALLBACK_FONT_FAMILY);

    const _getSimilarFont = function (font: Font, candidates: Font[]) {
        if (candidates.length == 0) {
            return null;
        }
        let closestFonts: Map<number, Font[]> = new Map();
        let maxMatchedCriteria = 0;
        for (let i = 0; i < candidates.length; i++) {
            let matchedCriteria = 0;
            const candidateStyles = Array.from(candidates[i].styleCriteria);
            for (let j = 0; j < candidateStyles.length; j++) {
                if (font.styleCriteria.has(candidateStyles[j])) {
                    matchedCriteria++;
                }
            }
            if (matchedCriteria > 0) {
                maxMatchedCriteria = Math.max(maxMatchedCriteria, matchedCriteria);
                const f = (closestFonts.get(matchedCriteria) ?? []);
                f.push(candidates[i]);
                closestFonts.set(matchedCriteria, f);
            }
        }
        // check if there is a matching font
        if (maxMatchedCriteria > 0) {
            const similarFonts = closestFonts.get(maxMatchedCriteria)?.filter(f => f.styleCriteria.size <= font.styleCriteria.size);
            if (font.style.isItalic) {
                return similarFonts?.filter(f => f.style.isItalic)?.at(0) ?? similarFonts?.at(0);
            }
            return similarFonts?.at(0);
        } else {
            const fallbackFont = candidates.find(f => f.style.isRegular);
            if (font.weight <= 400) {
                candidates = candidates.filter(f => f.style.isLight || f.style.isMono || f.style.isCondensed || f.style.isRegular) ?? candidates;
            }
            if (font.weight >= 500) {
                candidates = candidates.filter(f => f.style.isBlack || f.style.isBold || f.style.isMedium) ?? candidates;
            }

            candidates = candidates.filter(f => f.style.isItalic == font.style.isItalic) ?? candidates;
            return candidates.at(0) ?? fallbackFont;
        }
    }


    for (let x = 0; x < fonts.length; x++) {
        if (fontMapping.has(fonts[x].name)) {
            continue;
        }
        const fontMapValue: FontTargetLangMapValue = {
            fontname: fonts[x].name,
            targetLang: language,
            sourceFont: null,
            targetLangFont: null
        };

        fontMapValue.sourceFont = _getSimilarFont(fonts[x], supportedFonts.filter(f => f.family == fonts[x].family)) ??  //match by same font family
            _getSimilarFont(fonts[x], supportedFonts.filter(f => {
                for (let n of Array.from(f.alternativeNames)) {
                    if (fonts[x].alternativeNames.has(n)) {
                        return true;
                    }
                }
            })) ?? // then try to match using alt names
            _getSimilarFont(fonts[x], fallbackFonts); // then try to match with fallback fonts

        //now match the target language font using the source font style criteria
        if (language) {
            const targetLangFonts = supportedFonts.filter(f => f.family == language);
            fontMapValue.targetLangFont = _getSimilarFont(fonts[x], targetLangFonts) ??  //match by same font family
                fontMapValue.sourceFont; // if nothing matches, use source font
        }

        fontMapping.set(fonts[x].name, fontMapValue);
    }
    return fontMapping;
}