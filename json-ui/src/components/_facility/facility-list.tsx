import { List } from '@refinedev/mui';
import React, { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { useNavigation } from '@refinedev/core';
import { PccFacility, RelicOrganization } from 'relic-ui';
import { CrudFilter, useOnError, useTranslate } from '@refinedev/core';

import {
  GridColDef,
  DataGridPro,
  gridClasses,
  GridCellParams,
  DataGridProProps,
} from '@mui/x-data-grid-pro';

import { useRouter } from 'src/routes/hooks';

import { useSearch } from 'src/hooks/use-search';
import { useResponsive } from 'src/hooks/use-responsive';

import Label from 'src/components/label';
import { RowActionsPopover } from 'src/components/list';
import { DataGridToolbar } from 'src/components/list/data-grid-toolbar';

type SetFilterBehavior = 'merge' | 'replace';
interface FacilityListProps extends DataGridProProps {
  currentOrganizationsData?: RelicOrganization;
  setFilters: ((filters: CrudFilter[], behavior?: SetFilterBehavior) => void) &
    ((setter: (prevFilters: CrudFilter[]) => CrudFilter[]) => void);
}
export default function ListFacilities({
  currentOrganizationsData,
  setFilters,
  ...dataGridProps
}: FacilityListProps) {
  const translate = useTranslate();

  const router = useRouter();

  const navigation = useNavigation();

  const location = useLocation();

  const { globalSearch } = useSearch();

  const { mutate: onError } = useOnError();

  const upMd = useResponsive('up', 'md');

  const [viewButtonEl, setViewButtonEl] =
    React.useState<HTMLButtonElement | null>(null);

  const columns = React.useMemo<GridColDef[]>(
    () => [
      {
        field: 'facilityName',
        flex: 1,
        align: 'left',
        headerAlign: 'left',
        headerName: translate('facilities.fields.facilityName'),
        minWidth: 150,
      },
      {
        field: 'facilityCode',
        flex: 1,
        align: 'left',
        headerAlign: 'left',
        headerName: translate('facilities.fields.facilityCode'),
        type: 'number',
      },
      {
        field: 'postalCode',
        flex: 1,
        align: 'left',
        headerAlign: 'left',
        headerName: translate('facilities.fields.postalCode'),
      },
      {
        field: 'state',
        flex: 1,
        align: 'left',
        headerAlign: 'left',
        headerName: translate('facilities.fields.state'),
      },
      {
        field: 'enabled',
        headerName: translate('facilities.fields.enabled'),
        align: 'left',
        headerAlign: 'left',
        renderCell: function render({ value }) {
          return (
            <Label variant="soft" color={value ? 'success' : 'error'}>
              {translate(value ? 'status.enabled' : 'status.disabled')}
            </Label>
          );
        },
      },
      {
        field: 'actions',
        hideable: false,
        headerName: translate('table.actions'),
        sortable: false,
        renderCell: function render({ row }) {
          return (
            <>
              <RowActionsPopover
                rowId={row.id}
                editButtonProps={{
                  onClick: () => {
                    router.push(`/facilities/edit/${row.id}`);
                  },
                }}
              />
            </>
          );
        },
        align: 'center',
        headerAlign: 'center',
        minWidth: 10,
      },
    ],
    [router, translate],
  );

  const handleOnCellClick = (params: GridCellParams) => {
    const facility: PccFacility = params.row as PccFacility;
    facility.id = facility.id || `${facility.orgUuid}-${facility.facId}`;
    if (params.field !== 'actions') {
      //set current location in navigation history, so we can return from edit dialog using history.goBack()
      router.push(`/facilities/edit/${facility.id}`);
    }
  };

  const Toolbar = React.useCallback(
    () => (
      <DataGridToolbar
        setViewButtonEl={setViewButtonEl}
        title={translate('facilities.titles.list')}
      />
    ),
    [translate],
  );

  // Render the grid for facility Page
  function renderGrid() {
    return (
      <>
        <DataGridPro
          {...dataGridProps}
          pagination
          sx={{
            '& .MuiDataGrid-cell': {
              display: 'flex',
              alignSelf: 'center',
              height: '100%',
              border: 'none',
            },
          }}
          autoHeight
          disableColumnMenu
          columns={columns}
          localeText={{ toolbarColumns: 'View' }}
          onCellClick={handleOnCellClick}
          getRowId={row => row.facilityName}
          columnVisibilityModel={{
            facilityCode: upMd,
            postalCode: upMd,
            state: upMd,
          }}
          slots={
            currentOrganizationsData
              ? undefined
              : {
                  toolbar: Toolbar,
                }
          }
          slotProps={{
            panel: {
              anchorEl: viewButtonEl,
            },
            toolbar: {
              onClick: (e: React.MouseEvent) => {
                e.stopPropagation();
                setViewButtonEl(null);
              },
            },
          }}
        />
      </>
    );
  }

  // Render the grid for facilty Tab
  function renderGridTransHeader() {
    return (
      <List
        headerProps={{
          sx: {
            display: 'none',
          },
        }}
        contentProps={{
          sx: {
            p: '0 !important',
          },
        }}
        breadcrumb={null}
        wrapperProps={{
          sx: {
            boxShadow: 'none',
            background: 'transparent',
            [`& .${gridClasses.root}`]: {
              [`& .${gridClasses.cell}`]: {
                py: 1,
              },
              [`& .${gridClasses.columnHeader}`]: {
                bgcolor: 'transparent',
              },
              [`& .${gridClasses.columnHeaders}`]: {
                bgcolor: 'transparent',
                borderBottomStyle: 'dashed',
              },
            },
          },
        }}
      >
        {renderGrid()}
      </List>
    );
  }

  useEffect(() => {
    try {
      if (globalSearch.length > 0) {
        setFilters([
          {
            field: 'search',
            value: globalSearch.length > 0 ? globalSearch : '',
            operator: 'contains',
          },
        ]);
      } else {
        setFilters([]);
      }
    } catch (error) {
      onError(error);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [globalSearch]);

  return (
    <>
      {currentOrganizationsData && currentOrganizationsData.id
        ? renderGridTransHeader()
        : renderGrid()}
    </>
  );
}
