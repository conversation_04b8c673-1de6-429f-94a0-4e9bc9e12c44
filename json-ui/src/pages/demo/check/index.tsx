import { useEffect } from 'react';

import { useRouter } from 'src/routes/hooks';

const CheckThread = () => {
  const router = useRouter();

  useEffect(() => {
    const threadId = localStorage.getItem('threadId');
    const targetPath = threadId
      ? `/scenario?threadId=${threadId}&auto=voice`
      : '/scenario';

    router.push(targetPath);
  }, [router]);

  return null;
};

export default CheckThread;
