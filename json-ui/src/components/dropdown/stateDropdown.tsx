import { availableStates } from 'relic-ui';
import React, { useState, useEffect } from 'react';
import { Control, Controller } from 'react-hook-form';

import TextField from '@mui/material/TextField';
import Autocomplete from '@mui/material/Autocomplete';

interface StateDropdownProps {
  control: Control<any>;
  name: string;
  label: string;
  value: State | null;
  error: boolean;
  helperText: string;
  onChange?: (value: string | null) => void;
  disabled?: boolean;
  required?: boolean;
}

interface State {
  code: string;
  display: string;
}

const placeholder = {
  display: 'Select State',
  code: '',
};

export const StateDropdown: React.FC<StateDropdownProps> = ({
  control,
  name,
  label,
  value,
  error,
  helperText,
  onChange,
  disabled,
  required,
}) => {
  const [dropdownLanguages, setDropdownLanguages] = useState<State[]>([]);

  useEffect(() => {
    if (
      value &&
      availableStates.some((lang: State) => lang.code === value.code)
    ) {
      setDropdownLanguages([placeholder, ...availableStates]);
    } else {
      // Merge the missing language with the available languages
      const mergedLanguages = [
        placeholder,
        ...availableStates,
        {
          display: value?.display,
          code: value?.code,
        },
      ];
      setDropdownLanguages(mergedLanguages);
    }
  }, [value]);

  return (
    <Controller
      control={control}
      name={name}
      defaultValue={value}
      rules={required ? { required: 'This field is required' } : undefined}
      render={({ field }) => {
        const { onChange: fieldOnChange, value: fieldValue } = field;
        return (
          <Autocomplete
            options={dropdownLanguages}
            disabled={disabled}
            {...field}
            getOptionLabel={(option: State) => option.display || ''}
            isOptionEqualToValue={(option, value) => {
              return option.code === value.code;
            }}
            onChange={(_, newValue) => {
              fieldOnChange(newValue ? newValue : null);
              if (onChange) {
                onChange(newValue ? newValue.code : null);
              }
            }}
            renderInput={params => (
              <TextField
                {...params}
                disabled={disabled}
                label={label}
                variant="outlined"
                error={error}
                helperText={helperText}
                required={required}
                margin="normal"
              />
            )}
            value={value ? value : placeholder}
          />
        );
      }}
    />
  );
};
