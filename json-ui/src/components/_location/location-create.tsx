import { useState } from 'react';
import { RelicOrganization } from 'relic-ui';
import { useForm } from '@refinedev/react-hook-form';
import { useCreate, useTranslate } from '@refinedev/core';

import Stack from '@mui/material/Stack';
import Switch from '@mui/material/Switch';
import Dialog from '@mui/material/Dialog';
import TextField from '@mui/material/TextField';
import FormHelperText from '@mui/material/FormHelperText';
import FormControlLabel from '@mui/material/FormControlLabel';

import { Create } from 'src/components/refine-customs/create';
import { OrganizationDropdown } from 'src/components/dropdown/organizationDropdown';

import PhoneInput from '../phone-input';
type Props = {
  open: boolean;
  onClose: VoidFunction;
};

export default function CreateLocation({ open, onClose }: Props) {
  const translate = useTranslate();
  const [status, setStatus] = useState(true);
  const { mutate } = useCreate();

  const {
    saveButtonProps,
    register,
    handleSubmit,
    reset,
    control,
    refineCore: { formLoading, queryResult },
    formState: { errors },
  } = useForm();

  const currentOrganizationsData = queryResult?.data?.data as RelicOrganization;

  const onSubmit = handleSubmit(async values => {
    try {
      const updatedValues = {
        resourceType: 'Location',
        status: status ? 'active' : 'inactive',
        organizationId: currentOrganizationsData?.id,
        name: values.locationName,
        phone: values.locationPhone,
        fax: values.locationFax,
      };

      mutate({
        resource: 'locations',
        values: {
          ...updatedValues,
        },
      });
      reset();
      onClose();
    } catch (error) {
      console.error(error);
    }
  });

  return (
    <>
      <Dialog
        fullWidth
        maxWidth="sm"
        open={open}
        onClose={onClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <Create
          isLoading={formLoading}
          saveButtonProps={{ ...saveButtonProps, onClick: onSubmit }}
          onClose={onClose}
          title={translate('locations.titles.create')}
        >
          <Stack spacing={2.5}>
            <TextField
              {...register('locationName', {
                required: 'This field is required',
              })}
              error={!!(errors as any)?.locationName}
              helperText={(errors as any)?.locationName?.message}
              fullWidth
              required
              InputLabelProps={{ shrink: true }}
              type="text"
              label={translate('locations.fields.name')}
              name="locationName"
            />
            {/* Phone Field */}
            <PhoneInput
              control={control}
              label={translate('locations.fields.phone')}
              name="locationPhone"
            />
            {/* Fax Field */}
            <PhoneInput
              control={control}
              label={translate('locations.fields.fax')}
              name="locationFax"
            />

            <OrganizationDropdown
              control={control}
              name="organizationId"
              label={translate('locations.fields.organizationId')}
              error={!!(errors as any)?.organizationId}
              helperText={(errors as any)?.organizationId?.message}
              value={currentOrganizationsData?.id ?? ''}
              disabled
            />
            <div>
              <FormControlLabel
                label={translate('locations.fields.status')}
                control={
                  <Switch
                    {...register('status', {
                      value: true,
                    })}
                    name="status"
                    checked={status}
                    onChange={() => setStatus(!status)}
                  />
                }
              />
              {!!errors.status && (
                <FormHelperText error sx={{ px: 2 }}>
                  {(errors as any)?.status?.message}
                </FormHelperText>
              )}
            </div>
          </Stack>
        </Create>
      </Dialog>
    </>
  );
}

// () => setStatus(!!values.status)
