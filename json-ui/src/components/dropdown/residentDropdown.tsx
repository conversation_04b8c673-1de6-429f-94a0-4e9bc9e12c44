import React from 'react';
import { RelicPatient } from 'relic-ui';
import { Controller } from 'react-hook-form';
import { useAutocomplete } from '@refinedev/mui';

import { TextField, Autocomplete } from '@mui/material';

interface ResidentDropdownProps {
  control: any;
  name: string;
  label: string;
  value: string;
  error: boolean;
  helperText: string;
  onChange?: (patient?: RelicPatient | null) => void;
  disabled?: boolean;
}

export const ResidentDropdown: React.FC<ResidentDropdownProps> = ({
  control,
  name,
  label,
  value,
  error,
  helperText,
  onChange,
  disabled,
}) => {
  if (disabled && !value) {
    throw new Error('When disabled is true, value must not be empty.');
  }

  const { autocompleteProps: residentAutocompleteProps } = useAutocomplete({
    resource: 'patients',
    filters: disabled
      ? [
          {
            field: 'id',
            value: value,
            operator: 'eq',
          },
        ]
      : [],
  });

  return (
    <Controller
      control={control}
      name={name}
      defaultValue={value}
      rules={{ required: 'This field is required' }}
      render={({ field }) => {
        const { onChange: fieldOnChange, value: fieldValue } = field;
        
        // Find the selected option from current options or use the provided value
        // fieldValue contains the patient ID (from form field), not the full object
        const selectedOption = residentAutocompleteProps.options?.find(
          resident => resident.id === fieldValue,
        );
        
        // Custom onInputChange handler to prevent unnecessary refetches
        const handleInputChange = (event: React.SyntheticEvent, newInputValue: string, reason: string) => {
          // Only call the original onInputChange if it's a user typing (not selection)
          if (reason === 'input' && residentAutocompleteProps.onInputChange) {
            residentAutocompleteProps.onInputChange(event, newInputValue, reason);
          }
        };
        
        return (
          <Autocomplete
            loading={residentAutocompleteProps.loading}
            options={residentAutocompleteProps.options}
            onInputChange={handleInputChange}
            disabled={disabled}
            autoHighlight
            getOptionLabel={(option: RelicPatient) =>
              residentAutocompleteProps.options?.find(
                resident => resident.id === option.id,
              )?.name ?? ''
            }
            isOptionEqualToValue={(option, value) => {
              return (
                value === undefined ||
                option?.id?.toString() === (value?.id ?? value).toString()
              );
            }}
            onChange={(_, newValue) => {
              const patientId = newValue ? newValue.id : null;
              fieldOnChange(patientId);
              if (onChange) {
                onChange(newValue);
              }
            }}
            renderInput={params => (
              <TextField
                {...params}
                disabled={disabled}
                label={label}
                variant="outlined"
                error={error}
                helperText={helperText}
                required
              />
            )}
            value={selectedOption || null}
          />
        );
      }}
    />
  );
};
