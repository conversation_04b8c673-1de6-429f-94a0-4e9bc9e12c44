import { RelicDocument } from 'relic-ui';
import { FieldValues } from 'react-hook-form';
import { useForm } from '@refinedev/react-hook-form';

import Dialog from '@mui/material/Dialog';
import TextField from '@mui/material/TextField';

import { Edit } from 'src/components/refine-customs/edit';
interface Props {
  open: boolean;
  onClose: VoidFunction;
  documentDetails: RelicDocument;
}

const EditDocument = ({ open, onClose, documentDetails }: Props) => {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    refineCore: { onFinish, formLoading },
  } = useForm<FieldValues>({
    defaultValues: documentDetails,
    refineCoreProps: {
      resource: 'documents',
      id: documentDetails.id,
      action: 'edit',
      redirect: false,
    },
  });

  const onSubmit = async (values: FieldValues) => {
    await onFinish(values);
    onClose();
  };

  return (
    <Dialog
      fullWidth
      maxWidth="sm"
      open={open}
      onClose={onClose}
      aria-labelledby="alert-dialog-title"
      aria-describedby="alert-dialog-description"
    >
      <Edit
        resource="documents"
        isLoading={formLoading}
        saveButtonProps={{
          onClick: handleSubmit(onSubmit),
          disabled: isSubmitting,
        }}
        onClose={onClose}
        title={'Edit Document Details'}
        deleteButtonProps={{ hidden: true }}
      >
        <TextField
          label={'Document Name'}
          variant="outlined"
          fullWidth
          margin="normal"
          {...register('filename')}
          error={Boolean(errors.filename)}
          helperText={(errors as any).filename?.message}
        />
        <TextField
          label={'Document Header'}
          variant="outlined"
          fullWidth
          margin="normal"
          {...register('header')}
          error={Boolean(errors.header)}
          helperText={(errors as any).header?.message}
        />
        <TextField
          label={'Document Footer'}
          variant="outlined"
          fullWidth
          margin="normal"
          {...register('footer')}
          error={Boolean(errors.footer)}
          helperText={(errors as any).footer?.message}
        />
      </Edit>
    </Dialog>
  );
};

export default EditDocument;
