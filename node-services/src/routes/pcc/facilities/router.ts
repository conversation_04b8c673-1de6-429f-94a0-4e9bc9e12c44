import { S } from 'fluent-json-schema';
import { FastifyInstance, FastifyPluginOptions } from 'fastify';

export default async function pccFacilitiesRouter (fastify: FastifyInstance, options: FastifyPluginOptions) {
    fastify.get('/', {
        schema: {
            ...options.schema,
            query: S.object()
                .prop('_start', S.number().default(0))
                .prop('_end', S.number().default(25))
                .prop('_search', S.string().maxLength(255).description('Search facilities by facilityName, facilityCode, state or postalCode'))
                .prop('_order', S.string().enum(['asc', 'desc']).default('asc'))
                .prop('_sort', S.string().enum(['facilityCode', 'facilityName', 'state', 'postalCode']).default('facilityCode'))
                .prop('relicOrganizationId', S.string()),
            description: 'Get a list of facilities for a Relic Organization Id.',
            summary: 'API returns list of PointClickCare facilities for a given Relic Organization Id mapped to PointClickCare organization',
        }
    }, async function (req: any, rep: any) {
        const { relicOrganizationId } = req.query;
        const { total, data } = await fastify.searchPccFacilities(req.query, relicOrganizationId);
        rep.header['x-total-count'] = total;
        return data;
    });

    fastify.patch('/:id', {
        schema: {
            ...options.schema,
            body: S.object()
                .prop('orgUuid', S.string().required().description('PointClickCare organization id'))
                .prop('facId', S.number().required().description('PointClickCare facility id'))
                .prop('enabled', S.boolean().default(false).description('Enable or disable facility')),
            description: 'Save PCC facility details into RelicCare',
            summary: 'Enable or disable PCC facility'
        }
    }, async function (req: any, rep: any) {
        const { orgUuid, facId, enabled } = req.body;
        return await fastify.savePccFacility(orgUuid, facId, enabled);
    });

    fastify.get('/:id', {
        schema: {
            ...options.schema,
            params: S.object()
                .prop('id', S.string().required().description('Relic Care facility id')),
            description: 'API returns PointClickCare facility details',
            summary: 'API returns PointClickCare facility details'
        },
    }, async function (req: any, rep: any) {
        const { id } = req.params;
        return await fastify.ehrService.getFacilityById(id);
    });
}

// export const autoPrefix = '/facilities';