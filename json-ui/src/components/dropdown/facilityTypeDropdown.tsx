import React from 'react';
import { Control, Controller } from 'react-hook-form';

import TextField from '@mui/material/TextField';
import Autocomplete from '@mui/material/Autocomplete';

interface FacilityTypeDropdownProps {
  control: Control<any>;
  name: string;
  label: string;
  value: string | null;
  error: boolean;
  helperText: string;
  onChange?: (value: string | null) => void;
  disabled?: boolean;
  required?: boolean;
}
const facilityType = [
  {
    code: 'skilled_nursing_facility',
    display: 'Skilled Nursing Facility',
  },
  {
    code: 'senior_living',
    display: 'Senior Living',
  },
  {
    code: 'hospital',
    display: 'Hospital',
  },
  {
    code: 'hospice',
    display: 'Hospice',
  },
  {
    code: 'home_health',
    display: 'Home Health',
  },
  {
    code: 'intermediate_care_facility',
    display: 'Intermediate Care Facility',
  },
];

export const FacilityTypeDropdown: React.FC<FacilityTypeDropdownProps> = ({
  control,
  name,
  label,
  value,
  error,
  helperText,
  onChange,
  disabled,
  required,
}) => {
  return (
    <Controller
      control={control}
      name={name}
      defaultValue={value}
      rules={required ? { required: 'This field is required' } : undefined}
      render={({ field }) => {
        const { onChange: fieldOnChange, value: fieldValue } = field;
        return (
          <Autocomplete
            options={facilityType}
            disabled={disabled}
            {...field}
            getOptionLabel={option => option.display || ''}
            isOptionEqualToValue={(option, value) => {
              return option.code === value.code;
            }}
            onChange={(_, newValue) => {
              fieldOnChange(newValue ? newValue : null);
              if (onChange) {
                onChange(newValue ? newValue.code : null);
              }
            }}
            renderInput={params => (
              <TextField
                {...params}
                margin="normal"
                disabled={disabled}
                label={label}
                variant="outlined"
                error={error}
                helperText={helperText}
                required={required}
              />
            )}
            value={facilityType.find(type => type.code === fieldValue)}
          />
        );
      }}
    />
  );
};
