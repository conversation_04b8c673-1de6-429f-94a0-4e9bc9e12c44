import { Actor } from "apify";
import { RelicWebCrawler } from "./relic-web-crawler/crawler.js";
import { CrawlRequest } from "relic-ui";

Actor.main(async () => {
    const request = await Actor.getInputOrThrow() as CrawlRequest;
    const proxyConfiguration = await Actor.createProxyConfiguration({ useApifyProxy: true });
    const crawler = await RelicWebCrawler.create({ ...request, proxyConfiguration });
    const stats = await crawler.run()
    console.info(`Crawler finished with stats: ${JSON.stringify(stats)}`);
});