/**
 * Formats a phone number into the (+1)-XXX-XXX-XXXX format.
 *
 * @param phone - A string representing the phone number in E.164 format (e.g., "+19495015943").
 * @returns A formatted phone number string in the (+1)-XXX-XXX-XXXX format, or the original input if invalid.
 */
export function formatPhoneNumber(phone: string | undefined): string {
  if (!phone) {
    return '-';
  }
  const regex = /^\+(\d)(\d{3})(\d{3})(\d{4})$/;
  const match = phone.match(regex);

  if (!match) {
    console.warn('Invalid phone number format:', phone);
    // Return original input if not a valid format
    return phone;
  }

  const [, country, area, prefix, line] = match;
  return `(+${country})-${area}-${prefix}-${line}`;
}
