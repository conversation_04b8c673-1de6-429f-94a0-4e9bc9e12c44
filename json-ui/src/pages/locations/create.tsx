import * as React from 'react';

import { useRouter } from 'src/routes/hooks';

import { CreateLocation } from 'src/components/_location';

export const LocationCreate = () => {
  const router = useRouter();
  const [open, setOpen] = React.useState(true);

  const onCloseLocation = () => {
    setOpen(false);
    router.push('/locations');
  };

  return (
    <>{open && <CreateLocation open={open} onClose={onCloseLocation} />}</>
  );
};
