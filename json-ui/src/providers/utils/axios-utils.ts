import { AxiosRequestConfig } from 'axios';
import { IUserIdentity } from 'relic-ui';
import { getActiveLogin } from './auth-utils';

interface CustomError extends Error {
  status?: number;
}

export async function convertToNodeServicesRequest(
  axiosRequest: AxiosRequestConfig,
): Promise<AxiosRequestConfig> {
  const userIdentity: IUserIdentity = JSON.parse(
    localStorage.getItem('userIdentity') || '{}',
  );
  const activeLogin = getActiveLogin();
  if (!activeLogin) {
    const customError: CustomError = {
      name: 'Authentication Error',
      message: 'No active login found. Kindly re-login.',
      status: 401,
    };
    throw customError;
  }
  const headers = axiosRequest.headers ?? {};

  // Add headers to the request
  if (activeLogin) {
    headers['x-access-token'] = activeLogin.accessToken || '';
    headers['x-id-token'] = activeLogin.idToken || '';
    headers['x-organization-id'] =
      activeLogin.organizationId ||
      headers['x-organization-id'] ||
      axiosRequest.data?.organizationId ||
      userIdentity.portalIdentity?.organizationId ||
      '';
    // 'x-provider' can be used to override the provider in the request
    activeLogin.provider = headers['x-provider'] ?? activeLogin.provider; 
  }
  axiosRequest.headers = headers;

  // Ensure URLs are updated with organization Id based on header
  if (headers['x-organization-id']) {
    axiosRequest.url = axiosRequest.url?.replace(
      ':organizationId',
      headers['x-organization-id'],
    );
  }
  // Add provider to the URL if not already present
  if (
    axiosRequest.url?.includes('/api/') &&
    !axiosRequest.url?.includes(`/api/${activeLogin.provider}`)
  ) {
    axiosRequest.url = axiosRequest.url.replace(
      /\/api\/([^/]+)/,
      `/api/${activeLogin.provider}/$1`,
    );
  }
  // Replace search_like with _search
  if (axiosRequest.url?.includes('search_like')) {
    axiosRequest.url = axiosRequest.url.replace(/search_like/, '_search');
  }
  // Replace title_like (used by Refine Autocomplete) with _search
  if (axiosRequest.url?.includes('title_like')) {
    axiosRequest.url = axiosRequest.url.replace(/title_like/, '_search');
  }
  // Replace /staff with /practitioners
  if (axiosRequest.url?.includes('staff')) {
    axiosRequest.url = axiosRequest.url.replace('/staff', '/practitioners');
  }
  return axiosRequest;
}
