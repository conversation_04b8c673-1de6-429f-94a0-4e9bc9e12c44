/**
 * Call-Automation Callback Events
 */
import type { SerializedCommunicationIdentifier } from '@azure/communication-common';
import type { AcsIncomingCallEventData } from '@azure/eventgrid';

export interface ParticipantsUpdatedEventData {
  participants: Participant[];
  sequenceNumber: number;
  version: string;
  callConnectionId: string;
  serverCallId: string;
  correlationId: string;
  publicEventType: 'Microsoft.Communication.ParticipantsUpdated';
}

export interface Participant {
  identifier: any; // TODO: Identifier;
  isMuted: boolean;
}

export function isParticipantsUpdatedEventData(data: any): data is ParticipantsUpdatedEventData {
  return data.publicEventType === 'Microsoft.Communication.ParticipantsUpdated';
}

export interface CallConnectedEventData {
  version: string;
  callConnectionId: string;
  serverCallId: string;
  correlationId: string;
  publicEventType: 'Microsoft.Communication.CallConnected';
}

export function isCallConnectedEventData(data: any): data is CallConnectedEventData {
  return data.publicEventType === 'Microsoft.Communication.CallConnected';
}

export interface RecognizeCompletedEventData {
  operationContext: string;
  resultInformation: {
    code: number;
    subCode: number;
    message: string;
  };
  recognitionType: string;
  speechResult: {
    speech: string;
  };
  version: string;
  callConnectionId: string;
  serverCallId: string;
  correlationId: string;
  publicEventType: 'Microsoft.Communication.RecognizeCompleted';
}

export function isRecognizeCompletedEventData(data: any): data is RecognizeCompletedEventData {
  return data.publicEventType === 'Microsoft.Communication.RecognizeCompleted';
}

export interface RecognizeFailedEventData {
  operationContext: string;
  resultInformation: {
    code: number;
    subCode: number;
    message: string;
  };
  version: string;
  callConnectionId: string;
  serverCallId: string;
  correlationId: string;
  publicEventType: 'Microsoft.Communication.RecognizeFailed';
}

export function isRecognizeFailedEventData(data: any): data is RecognizeFailedEventData {
  return data.publicEventType === 'Microsoft.Communication.RecognizeFailed';
}

export interface CallDisconnectedEventData {
  version: string;
  callConnectionId: string;
  serverCallId: string;
  correlationId: string;
  publicEventType: 'Microsoft.Communication.CallDisconnected';
}

export function isCallDisconnectedEventData(data: any): data is CallDisconnectedEventData {
  return data.publicEventType === 'Microsoft.Communication.CallDisconnected';
}

export interface PlayCompletedEventData {
  operationContext: string;
  resultInformation: {
    code: number;
    subCode: number;
    message: string;
  };
  version: string;
  callConnectionId: string;
  serverCallId: string;
  correlationId: string;
  publicEventType: 'Microsoft.Communication.PlayCompleted';
}

export function isPlayCompletedEventData(data: any): data is PlayCompletedEventData {
  return data.publicEventType === 'Microsoft.Communication.PlayCompleted';
}

export interface PlayStartedEventData {
  operationContext: string;
  resultInformation: {
    code: number;
    subCode: number;
    message: string;
  };
  version: string;
  callConnectionId: string;
  serverCallId: string;
  correlationId: string;
  publicEventType: 'Microsoft.Communication.PlayStarted';
}

export function isPlayStartedEventData(data: any): data is PlayStartedEventData {
  return data.publicEventType === 'Microsoft.Communication.PlayStarted';
}

export interface PlayFailedEventData {
  failedPlaySourceIndex: number;
  operationContext: string;
  resultInformation: {
    code: number;
    subCode: number;
    message: string;
  };
  version: string;
  callConnectionId: string;
  serverCallId: string;
  correlationId: string;
  publicEventType: 'Microsoft.Communication.PlayFailed';
}

export function isPlayFailedEventData(data: any): data is PlayFailedEventData {
  return data.publicEventType === 'Microsoft.Communication.PlayFailed';
}

/**
 * Azure Communication Service Incoming Call Event Data
 * This extends the base AcsIncomingCallEventData with additional custom context
 */
export interface IncomingCallEventData extends AcsIncomingCallEventData {
  // Explicitly define the from and to properties that come from AcsIncomingCallEventData
  context: string;
  to: SerializedCommunicationIdentifier;
  from: SerializedCommunicationIdentifier;
  // Additional metadata that might be present
  metadata?: {
    callConnectionId?: string;
    threadId?: string;
    [key: string]: any;
  };
}

/**
 * Type guard to check if data is an incoming call event
 */
export function isIncomingCallEventData(data: any): data is IncomingCallEventData {
  return (
    data &&
    typeof data.incomingCallContext === 'string' &&
    data.from &&
    data.to
  );
}

/**
 * Type guard to check if incoming call has custom context with thread ID
 */
export function hasThreadIdInCustomContext(data: IncomingCallEventData): boolean {
  return !!(
    data.customContext?.sipHeaders?.['User-To-User']
  );
}

/**
 * Extract thread ID from incoming call event data
 */
export function extractThreadIdFromIncomingCall(data: IncomingCallEventData): string {
  return data.customContext?.sipHeaders?.['User-To-User'] || 'NEW_THREAD';
}