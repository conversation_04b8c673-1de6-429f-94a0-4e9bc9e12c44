import { DateField } from '@refinedev/mui';
import { CrudFilters } from '@refinedev/core';
import React, { useMemo, useEffect } from 'react';
import { useMany, useOnError, useTranslate } from '@refinedev/core';

import { Box } from '@mui/material';
import {
  GridColDef,
  DataGridPro,
  DataGridProProps,
} from '@mui/x-data-grid-pro';

import { useRouter } from 'src/routes/hooks';

import { useSearch } from 'src/hooks/use-search';

import { areFiltersEqual } from 'src/utils/filter-utils';

import { NewButton } from 'src/components/refine-customs/new-btn';
import { DataGridToolbar } from 'src/components/list/data-grid-toolbar';

interface ListTrainingsProps extends DataGridProProps {
  setFilters: ((filters: CrudFilters) => void) &
    ((setter: (prevFilters: CrudFilters) => CrudFilters) => void);
  filters: CrudFilters;
}

const ListTrainings = ({
  setFilters,
  filters,
  ...dataGridProps
}: ListTrainingsProps) => {
  const translate = useTranslate();
  const router = useRouter();

  const { globalSearch } = useSearch();

  const { mutate: onError } = useOnError();

  useEffect(() => {
    try {
      if (globalSearch.length > 0) {
        setFilters([
          {
            field: 'search',
            value: globalSearch.length > 0 ? globalSearch : '',
            operator: 'contains',
          },
        ]);
      } else {
        setFilters([]);
      }
    } catch (error) {
      onError(error);
    }
  }, [onError, globalSearch, setFilters]);

  const { data: organizationData, isLoading: organizationIsLoading } = useMany({
    resource: 'organizations',
    ids: dataGridProps?.rows?.map((item: any) => item?.organizationId) ?? [],
    queryOptions: {
      enabled: !!dataGridProps?.rows,
    },
  });

  const columns = React.useMemo<GridColDef[]>(
    () => [
      {
        field: 'name',
        headerName: translate('trainings.fields.name'),
        flex: 1,
      },
      {
        field: 'organizationId',
        flex: 2,
        headerName: translate('trainings.fields.organizationId'),
        renderCell: function render({ value }) {
          return organizationIsLoading ? (
            <>Loading...</>
          ) : (
            organizationData?.data?.find(item => item.id === value)?.name
          );
        },
        sortable: false,
      },
      {
        field: 'updateDate',
        headerName: translate('trainings.fields.last_update_on'),
        flex: 1,
        renderCell: function render({ value }) {
          return (
            <DateField
              sx={{ display: 'inline' }}
              value={value}
              format="MMMM DD YYYY, h:mm a"
            />
          );
        },
      },
    ],
    [translate, organizationIsLoading, organizationData?.data],
  );

  const [viewButtonEl, setViewButtonEl] =
    React.useState<HTMLButtonElement | null>(null);

  const displayColumns = useMemo(() => {
    const filteredColumns = columns;

    return filteredColumns;
  }, [columns]);

  const Toolbar = React.useCallback(
    () => (
      <DataGridToolbar
        title={translate('trainings.label')}
        exportButton={false}
        setViewButtonEl={setViewButtonEl}
        actions={
          <>
            <NewButton
              href={'/trainings/create'}
              label={translate('buttons.create')}
            />
          </>
        }
      />
    ),
    [translate],
  );

  const handleOnCellClick = React.useCallback(
    (params: any) => {
      if (params.field !== 'actions') {
        router.push(`/trainings/edit/${params.id}?name=${params?.row?.name}`);
      }
    },
    [router],
  );

  function renderGrid() {
    return (
      <>
        <DataGridPro
          {...dataGridProps}
          getRowId={row => row?.id}
          disableColumnMenu
          columns={displayColumns}
          pagination
          sx={{
            '& .MuiDataGrid-cell': {
              display: 'flex',
              alignSelf: 'center',
              height: '100%',
              border: 'none',
            },
          }}
          onCellClick={handleOnCellClick}
          localeText={{ toolbarColumns: 'View' }}
          slots={{
            toolbar: Toolbar,
          }}
          slotProps={{
            panel: {
              anchorEl: viewButtonEl,
            },
            toolbar: {
              onClick: (e: React.MouseEvent) => {
                e.stopPropagation();
                setViewButtonEl(null);
              },
            },
          }}
        />
      </>
    );
  }

  return <Box>{renderGrid()}</Box>;
};

const MemoizedListTrainings = React.memo(ListTrainings);

export default MemoizedListTrainings;
