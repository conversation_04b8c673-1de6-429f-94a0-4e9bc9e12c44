import { MessageThreadStrings } from '@azure/communication-react';

// Helper function to format time.
const formatTimeForChatMessage = (messageDate: Date): string => {
  return messageDate.toLocaleTimeString([], { hour: 'numeric', minute: '2-digit' });
};

// Helper function to format date.
const formatDateForChatMessage = (messageDate: Date): string => {
  return messageDate.toLocaleDateString();
};

/**
 * Formats a timestamp for a chat message relative to the current date, using localized strings.
 */
export const formatTimestampForChatMessage = (messageDate: Date, todayDate: Date): string => {
  // Validate or provide default values for dateStrings to avoid errors.
  const dateStrings = {
    ...{
      yesterday: 'Yesterday',
      sunday: 'Sunday',
      monday: 'Monday',
      tuesday: 'Tuesday',
      wednesday: 'Wednesday',
      thursday: 'Thursday',
      friday: 'Friday',
      saturday: 'Saturday',
    },
  } as MessageThreadStrings;

  const startOfDay = new Date(todayDate.setHours(0, 0, 0, 0));
  const yesterdayDate = new Date(new Date(startOfDay).setDate(startOfDay.getDate() - 1));
  const weekDay = todayDate.getDay();
  const firstDayOfTheWeekDate = new Date(new Date(startOfDay).setDate(startOfDay.getDate() - weekDay));

  if (messageDate > startOfDay) {
    return formatTimeForChatMessage(messageDate);
  } else if (messageDate > yesterdayDate) {
    return `${dateStrings.yesterday} ${formatTimeForChatMessage(messageDate)}`;
  } else if (messageDate > firstDayOfTheWeekDate) {
    return `${dayToDayName(messageDate.getDay(), dateStrings)} ${formatTimeForChatMessage(messageDate)}`;
  } else {
    return `${formatDateForChatMessage(messageDate)} ${formatTimeForChatMessage(messageDate)}`;
  }
};

// Helper function to convert day index to day name.
const dayToDayName = (day: number, dateStrings: MessageThreadStrings): string => {
  switch (day) {
    case 0:
      return dateStrings.sunday;
    case 1:
      return dateStrings.monday;
    case 2:
      return dateStrings.tuesday;
    case 3:
      return dateStrings.wednesday;
    case 4:
      return dateStrings.thursday;
    case 5:
      return dateStrings.friday;
    case 6:
      return dateStrings.saturday;
    default:
      console.error(`Invalid day [${day}] passed to dayToDayName`);
      return 'Unknown Day';
  }
};
