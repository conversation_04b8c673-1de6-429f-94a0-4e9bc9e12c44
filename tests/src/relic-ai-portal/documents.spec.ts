import { test, expect } from '@playwright/test';
import { common } from '../../common';

test.beforeEach('Medplum-Login', async ({ page }) => {
    await page.goto(common.facilityPortal);
    await page.waitForURL(common.facilityPortal);
    await page.getByRole('link', { name: 'Medplum' }).click();
    await page.getByPlaceholder('<EMAIL>').fill(common.medplumLoginCredentials.username);
    await page.getByRole('button', { name: 'Next' }).click();
    await page.getByLabel('Password *').click();
    await page.getByLabel('Password *').fill(common.medplumLoginCredentials.password);
    await page.getByRole('button', { name: 'Sign in' }).click();
    await expect(page.getByRole('link', { name: 'Relic AI Portal' })).toBeVisible();
  });

test('Common-Search and Expand Translations', async ({ page }) => {

    await expect(page.getByRole('list')).toContainText('Documents',{timeout:30000});
    await page.getByRole('link', { name: 'Documents' }).click();
    await page.getByPlaceholder('Search...').fill('zieth');
    await expect(page.getByRole('gridcell', { name: 'see children Zieth-Admission' })).toBeVisible();
    const rowGroupText = await page.getByRole('rowgroup').textContent();
    expect(rowGroupText).toContain('Zieth-Admission Form');
    await page.click('button[aria-label="see children"]');

});

test('Common-Delete and Translate via Retain Original Format', async ({ page }) => {

    await expect(page.getByRole('list')).toContainText('Documents',{timeout:30000});
    await page.getByRole('link', { name: 'Documents' }).click();
    await page.getByPlaceholder('Search...').fill('zieth');
    await page.getByRole('gridcell', { name: 'see children Zieth-Admission' }).getByLabel('see children').click();
    await page.getByText('Zieth-Admission Form.pdf').click();
    await page.getByRole('combobox', { name: 'Translate To' }).fill('hindi');
    await page.getByRole('option', { name: 'Hindi' }).click();
    await page.getByRole('button', { name: 'Translate' }).click();
    await expect(page.getByRole('alert')).toContainText('Successfully started translation.');
    await expect(page.getByRole('list')).toContainText('Documents',{timeout:30000});
    await page.getByRole('button', { name: 'Refresh' }).click();
    const rowGroupText = await page.getByRole('rowgroup').textContent();
    expect(rowGroupText).toContain('Zieth-Admission Form');
    await page.getByText('Zieth-Admission Form-orig-fmt-HI.pdf').click();
    await expect(page.getByText('DeleteCancelDone')).toBeVisible();
    await page.getByTestId('refine-delete-button').click();
    await expect(page.getByText('CancelDelete')).toBeVisible();

    await page.getByRole('button', { name: 'Delete' }).click();
    await expect(page.getByText('Successfully Deleted Zieth-')).toBeVisible();

});

test('Common-Delete and Translate via Modified Format', async ({ page }) => {

      await expect(page.getByRole('list')).toContainText('Documents',{timeout:30000});
      await page.getByRole('link', { name: 'Documents' }).click();
      await page.getByPlaceholder('Search...').fill('zieth');
      await page.getByRole('gridcell', { name: 'see children Zieth-Admission' }).getByLabel('see children').click();
      await page.getByText('Zieth-Admission Form.pdf').click();
      await page.getByRole('combobox', { name: 'Translate To' }).fill('hindi');
      await page.getByRole('option', { name: 'Hindi' }).click();
      await page.getByLabel('Modified').check();
      await page.getByRole('button', { name: 'Translate' }).click();
      await expect(page.getByRole('alert')).toContainText('Successfully started translation.');
      await expect(page.getByRole('list')).toContainText('Documents',{timeout:30000});
      await page.getByRole('button', { name: 'Refresh' }).click();  
      const rowGroupText = await page.getByRole('rowgroup').textContent();
      expect(rowGroupText).toContain('Zieth-Admission Form');  
      await page.getByText('Zieth-Admission Form-mod-fmt-HI.pdf').click();
      await expect(page.getByText('DeleteCancelDone')).toBeVisible();
      await page.getByTestId('refine-delete-button').click();
      await expect(page.getByText('CancelDelete')).toBeVisible();

      await page.getByRole('button', { name: 'Delete' }).click();
      await expect(page.getByText('Successfully Deleted Zieth-')).toBeVisible();  

});
