import { useDelete, useTranslate, useCustom, useApiUrl } from '@refinedev/core';
import { DateField } from '@refinedev/mui';
import React, { useEffect, useState } from 'react';
import { TrainingContent } from 'relic-ui';

import { Folder } from '@mui/icons-material';
import DataObject from '@mui/icons-material/DataObject';
import DeleteIcon from '@mui/icons-material/Delete';
import DescriptionIcon from '@mui/icons-material/Description';
import LinkIcon from '@mui/icons-material/Link';
import PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';
import {
  Box,
  Button,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  Grid,
  Link,
  Typography,
  Alert,
  TextField,
} from '@mui/material';

import TrainingContentDeleteConfirm from './training-content-delete-confirm-dialog';

type Props = {
  showDialog: boolean;
  handleCloseDialog: VoidFunction;
  selectedTrainingContent: TrainingContent;
  refetchTrainingContent: VoidFunction;
  sourceTrainingContent: TrainingContent;
};

const TrainingContentViewDetails = ({
  showDialog,
  handleCloseDialog,
  refetchTrainingContent,
  selectedTrainingContent,
  sourceTrainingContent,
}: Props) => {
  const translate = useTranslate();
  const [deleteDialog, setDeleteDialog] = React.useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { mutate } = useDelete();

  // Create a wrapped handle close function that resets the state
  const handleClose = () => {
    setLoading(false);
    setError(null);
    handleCloseDialog();
  };

  const {
    data: contentData,
    isLoading: fetchContentLoading,
    refetch: refetchContent,
    isFetching: isFetchingContent,
  } = useCustom({
    url: selectedTrainingContent
      ? `trainings/modules/${selectedTrainingContent?.moduleId}/contents/${selectedTrainingContent?.id}`
      : '',
    method: 'get',
    queryOptions: {
      enabled: !!selectedTrainingContent?.id && !!showDialog,
    },
  });

  // Extract the current content from the API response or use props as fallback
  const currentContent = contentData?.data
    ? (contentData.data as TrainingContent)
    : selectedTrainingContent;

  const isEstimating = currentContent?.status === 'estimating_sitemap';
  const isRoot = currentContent?.id === currentContent?.sourceDocumentId;
  const contentType = currentContent?.type?.toLowerCase();
  const hasSitemap =
    currentContent?.sitemap && currentContent.sitemap.length > 0;

  // Effect to manage loading state and trigger refetch when dialog opens
  useEffect(() => {
    if (showDialog && selectedTrainingContent?.id) {
      setLoading(true);
      setError(null);
      refetchContent();
    }
  }, [showDialog, selectedTrainingContent?.id, refetchContent]);

  // Update state when data changes or loading completes
  useEffect(() => {
    if (contentData) {
      setLoading(false);
    } else if (
      fetchContentLoading === false &&
      isFetchingContent === false &&
      !contentData
    ) {
      setLoading(false);
      if (!error) {
        setError('No data available');
      }
    }
  }, [contentData, fetchContentLoading, isFetchingContent, error]);

  const getFileIcon = (type: string | undefined) => {
    if (!type) return <LinkIcon fontSize="small" />;
    switch (type.toLowerCase()) {
      case 'url':
        return <LinkIcon fontSize="small" />;
      case 'folder':
        return <Folder fontSize="small" />;
      case 'application/pdf':
        return <PictureAsPdfIcon fontSize="small" />;
      case 'application/json':
        return <DataObject fontSize="small" />;
      case 'application/msword':
      case 'application/vnd.ms-excel.sheet.macroEnabled.12':
      case 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
      case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
      case 'text/csv':
      case 'text/plain':
        return <DescriptionIcon fontSize="small" />;
      default:
        return <DescriptionIcon fontSize="small" />;
    }
  };

  const renderTrainingContent = (content: TrainingContent) => {
    if (!content) return '-';
    return (
      <Link
        sx={{
          display: 'flex',
          alignItems: 'center', // Changed from 'start' to 'center' for vertical alignment
          gap: 0.5,
          color: 'primary.main',
          textDecoration: 'none',
          '&:hover': {
            textDecoration: 'underline',
          },
        }}
        href={content.url}
        target="_blank"
        rel="noopener noreferrer"
      >
        {getFileIcon(content.type)}
        <Typography
          noWrap
          sx={{ flex: 1, overflow: 'hidden', textOverflow: 'ellipsis' }}
        >
          {content.filename || 'View Document'}
        </Typography>
      </Link>
    );
  };

  const renderField = (
    labelKey: string,
    value: any,
    labelContent?: TrainingContent,
  ) => (
    <>
      <Grid item xs={3} sm={2}>
        {labelContent?.jsonContentUrl ? (
          <Link
            sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 0.5,
              color: 'primary.main',
              textDecoration: 'none',
              '&:hover': {
                textDecoration: 'underline',
              },
            }}
            href={labelContent.jsonContentUrl}
            target="_blank"
            rel="noopener noreferrer"
          >
            <Typography
              variant="subtitle2"
              sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
            >
              {translate(labelKey)}
              <Box sx={{ display: { xs: 'none', sm: 'inline-flex' } }}>
                <DataObject fontSize="small" />
              </Box>
            </Typography>
          </Link>
        ) : (
          <Typography variant="subtitle2" color="text.secondary">
            {translate(labelKey)}
          </Typography>
        )}
      </Grid>
      <Grid item xs={9} sm={10}>
        {typeof value === 'object' ? (
          value
        ) : (
          <Typography variant="body1">{value || '-'}</Typography>
        )}
      </Grid>
    </>
  );

  const getStatusText = (status: string) => {
    if (!status) return '-';

    const getStatusKey = () => {
      if (contentType === 'url') {
        return isRoot ? 'url_root' : 'url_leaf';
      }
      if (
        (contentType && contentType.startsWith('application/')) ||
        contentType?.startsWith('text/')
      ) {
        return 'file';
      }

      return 'folder';
    };

    const statusKey = status as
      | 'pending'
      | 'failed'
      | 'done'
      | 'inprogress'
      | 'sitemap_ready'
      | 'estimating_sitemap';

    switch (statusKey) {
      case 'pending':
        return translate(
          `trainings-content.fields.status_labels.${getStatusKey()}.pending`,
        );
      case 'failed':
        return 'Failed';
      case 'done':
        return translate(
          `trainings-content.fields.status_labels.${getStatusKey()}.completed`,
        );
      case 'inprogress':
        return translate(
          `trainings-content.fields.status_labels.${getStatusKey()}.inprogress`,
        );
      case 'sitemap_ready':
        return translate(
          `trainings-content.fields.status_labels.${getStatusKey()}.completed`,
        );
      case 'estimating_sitemap':
        return translate(
          `trainings-content.fields.status_labels.url_root.estimating_sitemap`,
        );
      default:
        return status?.toUpperCase() || 'Unknown';
    }
  };

  const trainingContentStatusBadge = (value: string) => {
    let color;
    const text = getStatusText(value);

    switch (value) {
      case 'pending':
        color = '#FFA500'; // Orange for Pending
        break;
      case 'failed':
        color = '#FF4C4C'; // Soft Red for Failed
        break;
      case 'done':
        color = '#4CAF50'; // Green for Completed
        break;
      case 'inprogress':
        color = '#1E90FF'; // Blue for In Progress
        break;
      case 'sitemap_ready':
        color = '#32CD32'; // Lime Green for Ready
        break;
      case 'estimating_sitemap':
        color = '#DAA520'; // Goldenrod for Estimating
        break;
      default:
        color = '#A9A9A9'; // Light Gray for Unknown Status
    }
    return (
      <Typography variant="body1" sx={{ color: color }}>
        {text}
      </Typography>
    );
  };

  const handleDelete = () => {
    mutate(
      {
        resource: 'trainings/modules',
        id: `${currentContent.moduleId}/contents/${currentContent.id}`,
      },
      {
        onSuccess: () => {
          handleClose();
          refetchTrainingContent?.();
        },
      },
    );
  };

  const getDialogTitle = () => {
    if (isEstimating) {
      return translate('trainings-content.titles.view_estimating');
    }

    if (contentType === 'url') {
      return translate('trainings-content.titles.view_browsed');
    }

    if (contentType === 'folder') {
      return translate('trainings-content.titles.view_folder');
    }

    return translate('trainings-content.titles.view_file');
  };

  const getContentLabel = () => {
    if (contentType === 'folder') {
      return translate('trainings-content.fields.folder_name');
    }
    if (contentType === 'url' && isRoot) {
      return translate('trainings-content.fields.starting_url');
    }
    return translate('trainings-content.fields.content');
  };

  const renderDescription = () => {
    if (contentType === 'folder') {
      return translate('trainings-content.fields.folder_description');
    }
    if (contentType === 'url') {
      return translate('trainings-content.fields.browsed_description');
    }
    return translate('trainings-content.fields.file_description');
  };

  const renderContent = (content: TrainingContent) => {
    if (!content) return '-';

    // For folders, render as plain text
    if (content.type?.toLowerCase() === 'folder') {
      return (
        <Typography>{content.title || content.filename || '-'}</Typography>
      );
    }

    // For other types, render as a link
    return (
      <Link
        sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 0.5,
          color: 'primary.main',
          textDecoration: 'none',
          '&:hover': {
            textDecoration: 'underline',
          },
        }}
        href={content.url}
        target="_blank"
        rel="noopener noreferrer"
      >
        {getFileIcon(content.type)}
        <Typography
          noWrap
          sx={{ flex: 1, overflow: 'hidden', textOverflow: 'ellipsis' }}
        >
          {content.filename || 'View Document'}
        </Typography>
      </Link>
    );
  };

  const renderSitemap = () => {
    if (!currentContent?.sitemap || currentContent.sitemap.length === 0) {
      return <Typography variant="body2">No sitemap available</Typography>;
    }

    const sitemapText = currentContent.sitemap.map(item => item.url).join('\n');

    return (
      <TextField
        multiline
        fullWidth
        variant="outlined"
        InputProps={{
          readOnly: true,
        }}
        value={sitemapText}
        minRows={5}
        maxRows={10}
        sx={{
          fontFamily: 'monospace',
          '& .MuiOutlinedInput-root': {
            backgroundColor: '#f5f5f5',
            padding: '4px',
            '& fieldset': {
              borderColor: 'rgba(0, 0, 0, 0.12)',
            },
          },
          '& .MuiInputBase-input': {
            padding: '2px 4px',
          },
        }}
      />
    );
  };

  return (
    <>
      <Dialog
        fullWidth
        maxWidth="md"
        open={!!showDialog}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle id="alert-dialog-title">
          {getDialogTitle()}
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            {renderDescription()}
          </Typography>
        </DialogTitle>
        <Divider />

        <DialogContent sx={{ py: 3 }}>
          {fetchContentLoading || loading || isFetchingContent ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', py: 5 }}>
              <CircularProgress />
            </Box>
          ) : error ? (
            <Alert severity="error">{error}</Alert>
          ) : (
            <Grid
              container
              rowSpacing={2}
              columnSpacing={{ xs: 1, sm: 2, md: 3 }}
            >
              {sourceTrainingContent &&
                renderField(
                  'trainings-content.fields.parent-training-content',
                  renderContent(sourceTrainingContent),
                  sourceTrainingContent,
                )}
              {renderField(
                getContentLabel(),
                renderContent(currentContent),
                currentContent,
              )}
              {hasSitemap &&
                renderField(
                  'trainings-content.fields.sitemaps',
                  renderSitemap(),
                )}
              {renderField(
                'trainings-content.fields.status',
                trainingContentStatusBadge(currentContent?.status),
              )}
              {isEstimating &&
                renderField(
                  'trainings-content.fields.note',
                  <Typography variant="body2" color="text.secondary">
                    {translate('trainings-content.estimating-note')}
                  </Typography>,
                )}
              {renderField(
                'trainings-content.fields.last_update',
                <DateField
                  sx={{ display: 'inline' }}
                  value={
                    currentContent.lastProcessingDate ??
                    currentContent.updateDate
                  }
                  format="MMMM DD YYYY, h:mm a"
                />,
              )}
            </Grid>
          )}
        </DialogContent>

        <Divider />
        <DialogActions sx={{ p: 2 }}>
          {!isEstimating && (
            <Button
              variant="outlined"
              color="error"
              onClick={() => {
                setDeleteDialog(true);
              }}
              sx={{ display: 'flex', gap: 1 }}
            >
              <DeleteIcon fontSize={'small'} />
              {translate('buttons.delete', 'Delete')}
            </Button>
          )}

          <Button onClick={handleClose} variant="contained">
            {translate('buttons.close', 'Close')}
          </Button>
        </DialogActions>
      </Dialog>

      <TrainingContentDeleteConfirm
        openDel={deleteDialog}
        handleCloseDel={() => setDeleteDialog(false)}
        handleOnDeleteConfirm={handleDelete}
      />
    </>
  );
};

const MemorisedTrainingContentViewDetails = React.memo(
  TrainingContentViewDetails,
);

export default MemorisedTrainingContentViewDetails;
