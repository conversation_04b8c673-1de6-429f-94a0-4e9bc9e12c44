/**
 * StandardSearchQuery common query parameters based on frontend request
 */
export type StandardSearchQuery = {
    _start: number; //min 0, default 0
    _end: number; //min 1, default 25
    _order: 'asc' | 'desc';
    _sort: string; //depends on the api
    _search: string; //depends on the api
    id?: string;
}

/**
 * RelicSearchQuery to be used by all API as filter data type
 * 
 * StandardSearchQuery and custom query parameters
 */
export type RelicSearchQuery = StandardSearchQuery & {
    organizationId?: string;
    foreignFields?: Array<{ key: string, value: any }>;
}
