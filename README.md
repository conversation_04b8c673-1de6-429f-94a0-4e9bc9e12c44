# Services
Micro Services required for Relic Care Application. This file is out of date and needs an update.

# Service Deployment 

The Dockerfile for deploying individual services is available at `app/Dockerfile`.

```bash
# To Test image locally follow below instructions where testharness is a sample service name
$ cd testharness
$ docker build --tag testharness -f app/Dockerfile .
% docker run --detach --publish 3000:3000 testharness   # run in background
# The application would be available at http://localhost:3000/
```

The following is the process to build & push these services to Azure Container Registry. All services are currently deployed in resource group "ai-counsellor" using container registry "aicounsellor".

```bash
# To build and push to azure registry follow below instructures where testharness is a sample service name
$ az acr build --resource-group ai-counsellor --registry aicounsellor --image testharness:latest  -f app/Dockerfile  .
```

Then, go to Azure Container Apps console and create a container app service using this image. This is a one time exercise per service.

- Enable ingress and allow access from anywhere (if you want to make this API public)

# Currently Deployed Endpoints & Continuous Integration

End points deployed in Azure Container Apps are listed below:

1. testharness at https://testharness.calmbay-07fbcdc7.eastus.azurecontainerapps.io/
2. datapatientapi at https://datapatientapi.calmbay-07fbcdc7.eastus.azurecontainerapps.io/

CI is enabled using Github Workflows that are available in .github/workflows folder. The workflow is triggered on individual git push so anytime any changes are committed to github for the services listed in this section, those changes would get automatically deployed and will be available at the end points.

The deployment status can be seen at https://github.com/reliccare/services/actions/workflows/deploy-services.yml. 

