import React from 'react';

import Button, { ButtonProps, buttonClasses } from '@mui/material/Button';

import { RouterLink } from 'src/routes/components';

import { useResponsive } from 'src/hooks/use-responsive';

import Iconify from 'src/components/iconify';

// ----------------------------------------------------------------------

export type NewButtonProps = ButtonProps & {
  label: string;
  href: string;
};

export const NewButton: React.FC<NewButtonProps> = ({ label, href, sx, ...other }) => {
  const upSm = useResponsive('up', 'sm');

  return (
    <Button
      component={RouterLink}
      href={href}
      variant="contained"
      startIcon={<Iconify icon={'eva:plus-fill'} />}
      sx={{
        [`& .${buttonClasses.startIcon}`]: {
          ...(!upSm && {
            m: 0,
          }),
        },
        ...sx,
      }}
      {...other}
    >
      {upSm && label}
    </Button>
  );
};
