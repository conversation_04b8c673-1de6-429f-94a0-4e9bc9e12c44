import React from 'react';
import { Control, Controller } from 'react-hook-form';

import TextField from '@mui/material/TextField';
import Autocomplete from '@mui/material/Autocomplete';

interface ExperienceViaDropdownProps {
  control: Control<any>;
  name: string;
  label: string;
  value: string | null;
  error: boolean;
  helperText: string;
  onChange?: (value: string | null) => void;
  disabled?: boolean;
}

const communicationMethod = [
  {
    code: 'phone',
    display: 'AI Assistant will call your Telephone Number.',
  },
  {
    code: 'browser',
    display: 'Use Voice or Text on your Browser.',
  },
];

export const ExperienceViaDropdown: React.FC<ExperienceViaDropdownProps> = ({
  control,
  name,
  label,
  value,
  error,
  helperText,
  onChange,
  disabled,
}) => {
  return (
    <Controller
      control={control}
      name={name}
      defaultValue={value}
      rules={{ required: 'This field is required' }}
      render={({ field }) => {
        const { onChange: fieldOnChange, value: fieldValue } = field;
        return (
          <Autocomplete
            options={communicationMethod}
            disabled={disabled}
            {...field}
            getOptionLabel={option => option.display || ''}
            isOptionEqualToValue={(option, value) => {
              return option.code === value.code;
            }}
            onChange={(_, newValue) => {
              fieldOnChange(newValue && newValue.code ? newValue.code : null);
              if (onChange) {
                onChange(newValue ? newValue.code : null);
              }
            }}
            renderInput={params => (
              <TextField
                {...params}
                margin="normal"
                disabled={disabled}
                label={label}
                variant="outlined"
                error={error}
                helperText={helperText}
                required
              />
            )}
            value={communicationMethod.find(
              type => type.code === fieldValue || null,
            )}
          />
        );
      }}
    />
  );
};
