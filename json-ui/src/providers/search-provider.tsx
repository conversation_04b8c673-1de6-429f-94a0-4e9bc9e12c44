import React, { useState, useContext, useCallback, createContext } from 'react';

interface SearchContextType {
  globalSearch: string;
  setGlobalSearch: (value: string) => void;
  clearSearch: () => void;
}

const SearchContext = createContext<SearchContextType>({
  globalSearch: '',
  setGlobalSearch: () => {
    throw new Error('SearchContext must be used within SearchProvider');
  },
  clearSearch: () => {
    throw new Error('SearchContext must be used within SearchProvider');
  },
});

export const SearchProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [globalSearch, setGlobalSearch] = useState('');

  const clearSearch = useCallback(() => {
    setGlobalSearch('');
  }, []);

  return (
    <SearchContext.Provider
      value={{
        globalSearch,
        setGlobalSearch,
        clearSearch,
      }}
    >
      {children}
    </SearchContext.Provider>
  );
};

export { SearchContext };
