/* eslint-disable  @typescript-eslint/no-unused-vars */
import { Injectable } from '@nestjs/common';
import { Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import type { ChatThreadClient, SendTypingNotificationOptions } from '@azure/communication-chat';
import { ChatClientManager } from './chat-client-manager.service';
import { NodeService } from '../node/node.service';
import { AgentService } from '../agent/agent.service';
import type { AgentResponse, ConversationState } from '../types';
import type {
  AcsChatMessageReceivedInThreadEventData,
  AcsChatThreadPropertiesUpdatedEventData,
} from '@azure/eventgrid';
import type {
  Topic,
  Thread,
  Suggestion,
  WidgetRequest,
  WidgetData,
  ChatMetadata,
  MessageIntl,
  JsonString,
  Citation,
  CitationWidgetData,
  RelicChatParticipant,
} from 'relic-ui';
import { optionYes, optionNo } from './chat.constants';
import { delay } from '../utils/common.utils';
import {
  getThreadAssistant,
  getAiAssistant,
  getMessageSenderParticipant,
  getParticipant,
} from 'src/utils/participant.utils';
import { ThreadService } from 'src/thread/thread.service';
import { ThreadSpeechService } from 'src/thread/thread.speech';

@Injectable()
export class ChatService {
  private readonly logger = new Logger(ChatService.name);

  constructor(
    private chatClientManager: ChatClientManager,
    private configService: ConfigService,
    private nodeService: NodeService,
    private agentService: AgentService,
    private threadService: ThreadService,
    private threadSpeechService: ThreadSpeechService,
  ) {}

  private async switchAgent(
    senderChatThreadClient: ChatThreadClient,
    currentAgentAcsId: string,
    newAgentAcsId: string,
    newAgentName: string,
  ): Promise<void> {
    // remove the current agent
    await delay(50);

    try {
      await senderChatThreadClient.removeParticipant({
        communicationUserId: currentAgentAcsId,
      });
      await delay(50);
    } catch (error) {
      this.chatClientManager.handleError(
        senderChatThreadClient.threadId,
        error,
        'Failed to remove agent from the thread.',
      );
    }

    try {
      // add agent to the thread
      await senderChatThreadClient.addParticipants({
        participants: [
          {
            id: { communicationUserId: newAgentAcsId },
            displayName: newAgentName,
          },
        ],
      });
      await delay(50); // wait Tk msec until add participants take effect
    } catch (error) {
      this.chatClientManager.handleError(senderChatThreadClient.threadId, error, 'Failed to add agent to the thread.');
    }
  }

  private async handleWidgetResponse(
    userMessage: AcsChatMessageReceivedInThreadEventData,
    thread: Thread,
  ): Promise<any> {
    // topic
    const topic = await this.chatClientManager.getTopic(userMessage.threadId); //JSON.parse(property.topic)

    // chat thread client of agent
    const currentAgentAcsId = topic.currentAgentAcsId;
    const threadId = userMessage.threadId;
    const agentChatThreadClient = await this.chatClientManager.getChatThreadClientForAiAssistant(threadId);

    // widgetResponse
    const widgetResponse = JSON.parse(userMessage?.metadata?.widgetData);
    if (topic.interactiveWidget.requestMessageId !== widgetResponse.requestMessageId)
      throw new Error('Assertion failed: Message IDs do not match');

    /*
     * Step 0. Update metadata of the WidgetRequest message
     */
    const requestMessageId = widgetResponse.requestMessageId;
    const requestMessage = await agentChatThreadClient.getMessage(widgetResponse.requestMessageId);
    const oldWidgetRequest = JSON.parse(requestMessage.metadata.widgetData);
    const updatedWidgetRequest = {
      ...oldWidgetRequest,
      responseMessageId: userMessage.messageId,
      response: widgetResponse.response,
    };
    const updatedMetadata = {
      ...requestMessage.metadata,
      widgetData: JSON.stringify(updatedWidgetRequest),
    };
    agentChatThreadClient.updateMessage(requestMessageId, {
      metadata: updatedMetadata,
    });

    /*
     * Step 1. Take Action (NOTE: currently assuming agentSwitch action)
     */
    //const senderAcsId = "8:acs:8ca4fbc2-e63c-4283-a5b7-feb83074370e_0000001b-08f8-b7a2-35f3-343a0d000706"  // Use AI Assistant ACS ID
    const senderParticipant = getMessageSenderParticipant(thread.participants, userMessage);
    const senderChatThreadClient = await this.chatClientManager.getChatThreadClientForParticipant(
      thread.threadId,
      senderParticipant,
    );

    const action = widgetResponse.action; // assert action.type === "agentSwitch"
    const response = widgetResponse.response;
    let conversationState: ConversationState;
    let newTopic: Topic;
    if (response.value || response.value === 'yes') {
      // trigger agent switch since the user selected to switch
      const newAgentAcsId = widgetResponse.actionParams.to;
      const newAgent = this.agentService
        .getAgentList()
        .find((agent) => agent.communicationIdentities?.find((p) => p.userId === newAgentAcsId));
      const newAgentName = newAgent?.publicData?.name ?? newAgent?.publicData?.name ?? 'Agent';

      await this.switchAgent(senderChatThreadClient, currentAgentAcsId, newAgentAcsId, newAgentName);

      const { interactiveWidget, ...restOfTopic } = topic;
      newTopic = {
        ...restOfTopic,
        currentAgentAcsId: newAgentAcsId,
      };
      conversationState = 'AfterAgentSwitchAccepted';
    } else {
      const { interactiveWidget, ...restOfTopic } = topic;
      newTopic = {
        ...restOfTopic,
      };
      conversationState = 'AfterAgentSwitchDeclined';
    }

    // update topic (i.e. delte interactiveWidget)  // TODO this should happen also for value=fale
    await senderChatThreadClient.updateTopic(JSON.stringify(newTopic));

    return { conversationState };
  }

  private async handleWidgetMetadata(
    userMessage: AcsChatMessageReceivedInThreadEventData,
    thread: Thread,
  ): Promise<ConversationState> {
    const widgetData = JSON.parse(userMessage?.metadata?.widgetData);
    if (widgetData?.type === 'WidgetResponse') {
      const res = await this.handleWidgetResponse(userMessage, thread);
      return res.conversationState;
    } else {
      throw new Error('Unexpected widget data type');
    }
  }

  private async handleChatMetadata(): Promise<ConversationState> {
    return 'Normal';
  }

  private getSwitchToAgentId = (currentAgentId: string, content: string): string | undefined => {
    // switch agent if content mentions the name of another agent
    // return undefined if content does not suggest agent switch
    // TODO: improve this logic (what happens if the patient name is Emily etc?)
    for (const agent of this.agentService.getAgentList()) {
      // skip if `agent` is System Agent (AI Assistant)
      if (agent.type === 'System Agent') {
        continue;
      }
      // skip if `agent` is the current agent
      if (agent.communicationIdentities && agent.communicationIdentities.find((p) => p.userId === currentAgentId)) {
        continue;
      }

      const agentName = agent.publicData && agent.publicData.name;
      if (agentName !== '' && content.includes(agentName)) {
        return agent.communicationIdentities && agent.communicationIdentities?.[0]?.userId;
      }
    }
    return undefined;
  };

  private async createAgentSwitchMetadata(
    agentMessageIntl: JsonString<MessageIntl> | undefined,
    agentAcsId: string,
    switchToAgentId: string,
  ): Promise<ChatMetadata> {
    const agentSwitchParams = {
      type: 'AgentSwitchParams',
      to: switchToAgentId,
    };

    if (agentMessageIntl) {
      const locale = JSON.parse(agentMessageIntl).locale;
      const [intlYes, intlNo] = await Promise.all([
        this.createOptionIntl(optionYes, locale),
        this.createOptionIntl(optionNo, locale),
      ]);
      optionYes.intl = intlYes;
      optionNo.intl = intlNo;
    }

    const widgetRequest: WidgetRequest = {
      type: 'WidgetRequest',
      widget: 'SuggestionWidget',
      responseMessageId: null,
      action: 'agentSwitch',
      actionParams: agentSwitchParams,
      requestedById: agentAcsId,
      requestOptions: [optionYes, optionNo],
      response: null,
    };

    return {
      type: 'widget',
      widgetData: JSON.stringify(widgetRequest) as JsonString<WidgetData>,
      ...(agentMessageIntl !== undefined && { messageIntl: agentMessageIntl }),
    };
  }

  private async createOptionIntl(option: Suggestion, locale: string): Promise<any> {
    const [label, ...altTextArray] = await Promise.all([
      this.nodeService.translate(option.label, locale),
      ...option.altText.map((text) => this.nodeService.translate(text, locale)),
    ]);

    return {
      locale,
      label: label.translation ?? `TRANSLATION_ERROR_${option.label}`,
      altText: altTextArray.map((result) => result.translation).filter((text) => text !== undefined),
    };
  }

  //Todo: Citations do not support multi-lingual conversations since citations front-end may not be able to support the same.
  private async createCitationsMetadata(
    agentMessageIntl: JsonString<MessageIntl> | undefined,
    citations: Citation[],
  ): Promise<ChatMetadata> {
    const widgetData: CitationWidgetData = {
      widget: 'CitationWidget',
      citations,
    };

    return {
      type: 'widget',
      widgetData: JSON.stringify(widgetData) as JsonString<WidgetData>,
    };
  }

  private async createMessageMetadata(
    agentMessage: string,
    agentAcsId: string,
    userMessage?: AcsChatMessageReceivedInThreadEventData,
    citations?: Citation[],
  ): Promise<ChatMetadata> {
    let messageMetaData: ChatMetadata = {
      type: 'chat',
    };
    let agentMessageIntl: JsonString<MessageIntl> | undefined;

    const switchToAgentId = this.getSwitchToAgentId(agentAcsId, agentMessage);
    // 1. Create AgentSwitch metadata if agent message suggests agent switch
    if (switchToAgentId) {
      messageMetaData = await this.createAgentSwitchMetadata(agentMessageIntl, agentAcsId, switchToAgentId);
    }

    // 2. Create Citations metadata if citations are present
    if (citations && citations.length > 0) {
      messageMetaData = await this.createCitationsMetadata(agentMessageIntl, citations);
    }

    // 3. Add Agent MessageIntl if the user message has it
    if (userMessage?.metadata?.messageIntl) {
      const requestMessageIntl: MessageIntl = JSON.parse(userMessage.metadata.messageIntl);
      messageMetaData.messageIntl = await this.agentService.createMessageIntl(agentMessage, requestMessageIntl);
    }

    // 4. Add callconnectionId if the user messge has it
    if (userMessage?.metadata?.callConnectionId) {
      messageMetaData = {
        ...messageMetaData,
        callConnectionId: userMessage.metadata.callConnectionId,
      };
    }

    // Return default metadata
    return messageMetaData as ChatMetadata;
  }

  private isNotActionableEvent(senderAcsId: string, thread: Thread, threadAssistant: RelicChatParticipant): boolean {
    if (!thread) {
      return true;
    }
    if (!threadAssistant) {
      return true;
    }
    // Check if senderAcsId is Agent
    if (this.agentService.getAgentIdList().includes(senderAcsId)) {
      return true; // Do nothing if sender is Agent
    }
    return false;
  }

  // Process incoming chat message
  async processChatMessageReceivedInThread(userMessage: AcsChatMessageReceivedInThreadEventData) {
    const senderAcsId = userMessage.senderCommunicationIdentifier.communicationUser.id;
    const threadId = userMessage.threadId;
    const thread = await this.nodeService.getThreadById(threadId);
    const threadAssistant = getThreadAssistant(thread.participants);
    if (this.isNotActionableEvent(senderAcsId, thread, threadAssistant)) {
      return;
    }
    const threadClient = await this.chatClientManager.getChatThreadClientForParticipant(threadId, threadAssistant);
    const typingNotificationOptions: SendTypingNotificationOptions = {
      senderDisplayName: threadAssistant.displayName,
    };
    threadClient.sendTypingNotification(typingNotificationOptions);

    let conversationState: ConversationState;
    switch (userMessage?.metadata?.type) {
      case 'widget':
        conversationState = await this.handleWidgetMetadata(userMessage, thread);
        break;
      case 'chat':
        conversationState = await this.handleChatMetadata();
        break;
      default:
        throw new Error('Unsupported metadata type');
    }

    // Get response
    const agentResponse: AgentResponse = await this.agentService.generateResponse(thread, userMessage, conversationState);
    const agentMessage = agentResponse.content;
    const agentAcsId = agentResponse.agentAcsId;
    const currentAgentParticipant = getParticipant(thread.participants, agentAcsId);
    const citations = agentResponse.citations;

    // Determine if agent switch is needed
    const switchToAgentId = this.getSwitchToAgentId(agentAcsId, agentMessage);

    // Create message metadata
    const metadata = await this.createMessageMetadata(agentMessage, agentAcsId, userMessage, citations);

    // Send agent message
    const messageId = await this.agentService.sendRelicMessage(
      threadId,
      agentMessage,
      currentAgentParticipant,
      undefined,
      metadata,
    );

    // POST PROCESS for SuggestionWidgetRequest (update topic for suggestion widget)
    if (switchToAgentId) {
      const widgetRequest: WidgetRequest = JSON.parse(metadata.widgetData);
      await this.chatClientManager.updateTopic(threadId, null, null, false, messageId, widgetRequest);
    }
  }

  // Process chat thread properties update for greeting
  // Used for greeting the user when the user views the chat thread.
  async maybeWelcome(threadProperties: AcsChatThreadPropertiesUpdatedEventData) {
    const senderAcsId = threadProperties.editedByCommunicationIdentifier.rawId;
    const threadId = threadProperties.threadId;
    const thread = await this.nodeService.getThreadById(threadId);
    const threadAssistant = getThreadAssistant(thread.participants) ?? getAiAssistant(thread.participants);
    if (this.isNotActionableEvent(senderAcsId, thread, threadAssistant)) {
      return;
    }
    const threadClient = await this.chatClientManager.getChatThreadClientForParticipant(threadId, threadAssistant);
    const topic = JSON.parse(threadProperties.properties.topic.toString()) as Topic;

    if (topic.threadMessage && topic.threadMessage.includes('HELLO')) {
      // Only one time greeting is done by checking isZeroMessageThread but can be modified.
      if (await this.threadService.isZeroMessageThread(thread, threadAssistant)) {
        // There are no messages in the thread. Greet the user!
        const typingNotificationOptions: SendTypingNotificationOptions = {
          senderDisplayName: threadAssistant.displayName,
        };
        threadClient.sendTypingNotification(typingNotificationOptions);
        await this.agentService.sendGreeting(threadProperties.threadId, threadProperties);
      }
      // Clear the HELLO message
      const { threadMessage, ...updatedTopic } = topic;
      await threadClient.updateTopic(JSON.stringify(updatedTopic));
    }
  }

  // Send recognized messages to the chat thread.
  // Messages are sent from specific sender.
  async sendRecognizedMessage(threadId: string, callConnectionId: string, message: string): Promise<string> {
    const { chatLanguage } = await this.threadSpeechService.getThreadSpeechMap(threadId);
    //Default messageIntl and messageRequest assuming English message
    let messageIntl: JsonString<MessageIntl>;
    let messageRequest = { content: message };
    //Override messageIntl and messageRequest if recognized message is in an international language
    if (chatLanguage.code != 'en-US' && chatLanguage.code != 'en') {
      messageIntl = JSON.stringify({
        locale: chatLanguage.code,
        content: message,
      }) as JsonString<MessageIntl>;
      const { translation } = await this.nodeService.translate(message, 'en', chatLanguage.code); // Interpreted to English
      messageRequest = { content: translation };
    }
    const chatMetadata: ChatMetadata = {
      type: 'chat',
      callConnectionId: callConnectionId,
      messageIntl,
    };
    const onCallParticipant = await this.threadSpeechService.getOnCallParticipant(threadId);
    const result = await this.agentService.sendRelicMessage(
      threadId,
      messageRequest.content,
      onCallParticipant,
      undefined,
      chatMetadata,
    );
    return result;
  }
}
