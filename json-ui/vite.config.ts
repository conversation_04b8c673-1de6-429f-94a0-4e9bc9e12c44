import { webUpdateNotice } from '@plugin-web-update-notification/vite';
import react from '@vitejs/plugin-react';
import path from 'path';
import type { ConfigEnv } from 'vite';
import { defineConfig, loadEnv } from 'vite';
import mkcert from 'vite-plugin-mkcert';
import mpaPlugin from 'vite-plugin-mpa';
import { viteStaticCopy } from 'vite-plugin-static-copy';
import AutoMoveIndex from './post-plugin';

// ----------------------------------------------------------------------

// @ts-expect-error - mpaPlugin is not typed
const mpa = mpaPlugin.default;

export default defineConfig((userConfig: ConfigEnv) => {
  const { mode } = userConfig;
  // Load env file based on `mode` in the current working directory.
  // Refer https://vitejs.dev/config/#using-environment-variables-in-config for more details
  const env = loadEnv(mode, process.cwd(), 'VITE');
  return {
    define: {
      __APP_ENV__: JSON.stringify(env.APP_ENV),
    },
    server: {
      https: true as unknown as undefined, // Workaround since mkcert is not Type compatible with Vite 5 yet
    },
    plugins: [
      react(),
      viteStaticCopy({
        targets: [
          {
            src: 'public/templates/b2c.html',
            dest: 'templates',
          },
        ],
      }),
      webUpdateNotice({
        logVersion: true,
        checkInterval: 5 * 60 * 1000,
        notificationProps: {
          title: 'Portal Update',
          description:
            'Portal Application has been updated, please refresh the page to continue.',
          buttonText: 'Refresh',
          dismissButtonText: 'Dismiss',
        },
      }),
      mpa({
        open: '/',
        scanDir: 'src/apps',
        scanFile: 'index.{js,ts,jsx,tsx}',
        rewrites: [
          {
            from: /^(?!(?:\/demo|\/lobby|\?demo=|\?lobby=))/,
            // match root/index app - exclude /lobby/ and /demo/ and query string ?lobby= and ?demo=
            to: '/src/apps/index/index.html',
          },
          {
            from: /^\/lobby\/template\/(.*)$/, // match lobby/template path - include /template/anything
            to: '/templates/b2c.html',
          },
          {
            from: /^(\/lobby|\?lobby=)/, // match lobby app - include /lobby/ and query string ?lobby=
            to: '/src/apps/lobby/index.html',
          },
          {
            from: /^(\/demo|\?demo=)/, // match demo app - include /demo/ and query string ?demo=
            to: '/src/apps/demo/index.html',
          },
        ],
      }),
      AutoMoveIndex(),
      mkcert(),
    ],
    resolve: {
      alias: [
        {
          find: /^~(.+)/,
          replacement: path.join(process.cwd(), 'node_modules/$1'),
        },
        {
          find: /^src(.+)/,
          replacement: path.join(process.cwd(), 'src/$1'),
        },
      ],
    },
  };
});
