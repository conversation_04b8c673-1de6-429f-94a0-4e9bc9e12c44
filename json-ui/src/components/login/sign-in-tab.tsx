import React from 'react';

import { MsalInstance } from 'src/providers/utils';
import { loginRequestEntra } from 'src/providers/utils/auth-requests';

import AuthButton from './auth-button';
import MedplumButton from '/images/medplum-login-button.png';
import MicrosoftIcon from '/images/microsoft-login-button.png';
import PointClickCareIcon from '/images/pcc-login-button.png';
const SignInTab = ({
  title,
  content,
  isLoading,
}: {
  title: React.ReactNode;
  content: React.ReactNode;
  isLoading?: boolean;
}) => {
  const redirectUri = window.location.origin + '/patients';

  const pccLoginUri = 'https://connect.pointclickcare.com/auth/login';
  const medplumLoginUri = 'https://api.medplum.com/oauth2/authorize';

  const state = Math.random().toString(36);
  localStorage.setItem('pccAuthState', state);

  const medplumState = Math.random().toString(36);
  localStorage.setItem('medplumAuthState', medplumState);

  const entraAuthState = Math.random().toString(36);
  localStorage.setItem('entraAuthState', entraAuthState);

  const pccAuthUri =
    window.location.hostname === 'localhost'
      ? `${pccLoginUri}?client_id=${
          import.meta.env.VITE_PCC_LOCALHOST_CLIENT_ID
        }&response_type=code&redirect_uri=${redirectUri}&scope=openid&state=${state}`
      : `${pccLoginUri}?client_id=${
          import.meta.env.VITE_PCC_CLIENT_ID
        }&response_type=code&redirect_uri=${redirectUri}&scope=openid&state=${state}`;

  const medplumAuthUri =
    window.location.hostname === 'localhost'
      ? `${medplumLoginUri}?client_id=${
          import.meta.env.VITE_MEDPLUM_LOCALHOST_CLIENT_ID
        }&response_type=code&redirect_uri=${redirectUri}&scope=openid&state=${medplumState}`
      : `${medplumLoginUri}?client_id=${
          import.meta.env.VITE_MEDPLUM_CLIENT_ID
        }&response_type=code&redirect_uri=${redirectUri}&scope=openid&state=${medplumState}`;

  const handleEntraLogin = async () => {
    const entraClient = await MsalInstance.getClient('entra');

    // check if this is an active MSAL login session
    const accounts = entraClient.getAllAccounts();
    const tokenResponse = await entraClient.handleRedirectPromise();

    if (!tokenResponse || accounts?.length === 0) {
      // perform a fresh MSAL login
      await entraClient.loginRedirect({
        ...loginRequestEntra,
        state: entraAuthState,
      });
    } else {
      //Refresh the token of existing MSAL login session.
      await entraClient.acquireTokenRedirect({
        ...loginRequestEntra,
        account: accounts[0],
        state: entraAuthState,
      });
    }
  };
  return (
    <>
      {title}
      {content}
      <AuthButton
        id="pcc-login-btn"
        href={pccAuthUri}
        src={PointClickCareIcon}
        alt="PointClickCare"
        isLoading={isLoading}
      />
      <AuthButton
        id="microsoft-login-btn"
        href="#"
        onClick={handleEntraLogin}
        src={MicrosoftIcon}
        alt="Microsoft"
        isLoading={isLoading}
      />
      <AuthButton
        id="medplum-login-btn"
        href={medplumAuthUri}
        src={MedplumButton}
        alt="Medplum"
        isLoading={isLoading}
      />
    </>
  );
};

export default SignInTab;
