import { S } from "fluent-json-schema";
import { FastifyRequest, FastifyReply, FastifyInstance, FastifyPluginOptions } from "fastify";
import { relicRequire } from "../../../utils/common-js";
import { createRelicAgent, deactivateRelicAgent, getRelicAgentById, getRelicAgents, updateRelicAgent } from "./handler";
import { AgentFor, ThreadContext } from "relic-ui";
import Handlebars from 'handlebars';
import { IUserIdentity, RelicAgent, RelicAgentQuery, DefaultAgent, RelicOrganization, PatientEverything, RelicPatient } from "relic-ui";
import { conditionToRelicCondition, getPatientSummary, patientToRelicPatient } from "../../../types/relicPatientConversion";
import { requestContext } from "@fastify/request-context";

declare module 'fastify' {
  interface FastifyContextConfig {
    access?: {
      resource: string;
      action: string;
    };
  }
}

// Instead of importing from 'relic-ui', we define the agent types here as import is giving Error [ERR_REQUIRE_ESM]
export const PatientAgentType:string = 'Patient Agent';
export const StaffAgentType:string = 'Staff Agent';
export const SystemAgentType:string = 'System Agent';
export const AgentTypes:string[] = [PatientAgentType, StaffAgentType, SystemAgentType];

const createAgentRequestJsonSchema = S.object()
    .prop("resourceType", S.string().default('Practitioner'))
    .prop("organizationId", S.string().required())
    .prop("name", S.string().required())
    .prop("mobilePhone", S.string().description('Phone number of the agent'))
    .prop("email", S.string().format("email").description('Email id of the agent'))
    .prop("version", S.string())
    .prop("type", S.enum(AgentTypes).required())
    .prop("active", S.boolean().default(true))
    .prop("role", S.object()
        .prop("practitioner-role", S.string())
        .prop("display-role", S.string())
    )
    .prop("azureAssistantSetup", S.object())
    .prop("relicAssistantSetup", S.object())
    .prop("env", S.string().default('main'))
    .prop("publicData", S.object())

const updateAgentRequestJsonSchema = S.object()  
    .prop("resourceType", S.string().default("Practitioner"))
    .prop("name", S.string())
    .prop("mobilePhone", S.string().description('Phone number of the agent'))
    .prop("email", S.string().format("email").description('Email id of the agent'))
    .prop("version", S.string())
    .prop("type", S.enum(AgentTypes))
    .prop("active", S.boolean().default(true))
    .prop("role", S.object()
        .prop("practitioner-role", S.string())
        .prop("display-role", S.string())
    )
    .prop("azureAssistantSetup", S.object())
    .prop("relicAssistantSetup", S.object())
    .prop("env", S.string().default('main'))
    .prop("publicData", S.object())

// Schema definition for RelicAgentQuery. Can be imported from 'relic-ui'.
const { RelicAgentQuerySchema } = relicRequire('relic-ui/schema');

const agentDefaultPreHandlerHook = async function (
  req: FastifyRequest<{ Querystring: RelicAgentQuery}>,
  rep: FastifyReply,
) {
  const whoami: IUserIdentity = requestContext.get('whoami');
  if (['Patient', 'Practitioner'].includes(whoami.resourceType)) {
    if (!(whoami.resourceType === 'Practitioner' && whoami.role.name === 'admin')) {
      //admin practitioner can access all org
      req.query.organizationId = requestContext.get('organization').id;
    }
    if (whoami.resourceType === 'Patient') {
      req.query.type = PatientAgentType;
      req.query.organizationId = requestContext.get('organization').id;
    }
    //agent persona tailored to the caller.
    const agentFor: AgentFor = {
      id: whoami?.id,
      resourceType: whoami?.resourceType,
    };
    requestContext.set('agentFor', agentFor);
  }
};

const agentQueryPreHandlerHook = async function(req:any, rep:any){
    const {id, type, organizationId, _sort, _order, _start, _end, _search} = req.query;
    req.filter = {};
    if(type) req.filter.type = type;
    if(organizationId) req.filter.organizationId = organizationId;
    const whoami = requestContext.get('whoami') as IUserIdentity;
    if (whoami.role.name != 'admin') {
      req.filter.organizationId = requestContext.get('organizationId');
    }
    if(id){
        req.filter.id = id;
    }
    if (_end !== undefined && _start !== undefined) {
        req.filter._count = (_end - _start);
        req.filter._offset = _start;
    }
    if (_sort) {
        req.filter._sort = _sort === 'role' 
        ? { 'role.display-role': _order === 'asc' ? 1 : -1 } 
        : { [_sort]: _order === 'asc' ? 1 : -1 };
    }
    if (_search) {
        req.filter._search = _search
    }
};

const agentUpdatePersonaOnSendHook = async function(req:any, rep:any, payload:any){
    try{
        const relicAgent:RelicAgent = JSON.parse(payload);
        let patientSummary:string = '';
        let agentSummary:string = '';
        let agentPerspective:string = relicAgent?.perspective ?? '';
        const agentFor:AgentFor = requestContext.get('agentFor');
        const template:string = relicAgent?.azureAssistantSetup?.systemPrompt ?? `{{agentPerspective}}\n{{patientSummary}}\n{{agentSummary}}`;
        const templateString = Handlebars.compile(template); 
        const organization:RelicOrganization = requestContext.get('organization') as RelicOrganization;
        agentSummary = req.server.getAgentSummary(organization, relicAgent);
        if(agentFor){
            if(agentFor.resourceType === 'Patient'){
                const patientEverything:PatientEverything = await req.server.fetchPatientById(agentFor.id);
                const patient:RelicPatient = patientToRelicPatient(patientEverything, organization);
                patient.Condition = (patientEverything.Conditions)?.map(c => conditionToRelicCondition(c));
                patientSummary = getPatientSummary(organization, patient);
            }
        }     
        const replace:ThreadContext = {
            patientSummary: patientSummary ?? '',
            agentSummary: agentSummary ?? '',
            agentPerspective: agentPerspective
        }
        relicAgent.azureAssistantSetup.systemPrompt = templateString(replace);
        return JSON.stringify(relicAgent);
    }catch(e){}
    return payload;
};
const transformIdPreHandlerHook = async function(req:any, rep:any){
    if(req.params.id === 'aiassistant' || req.query.id === 'aiassistant'){
        const agent: RelicAgent = await req.server.getSystemAgent();
        req.params.id = agent.id;
        req.query.id = agent.id;
        req.query.organizationId = agent.organizationId;
        req.query.type = agent.type;    
    }
    if(req.params.id === 'default' || req.query.id === 'default'){
        const me = requestContext.get('whoami') as IUserIdentity;
        const myOrganization = requestContext.get('organization') as RelicOrganization;
        const myDefaultAgents = myOrganization?.endpoints?.find(e => e.defaultAgents)?.defaultAgents;
        let agent: DefaultAgent;
        if (me.resourceType === 'Patient') {
            agent = myDefaultAgents?.find(a => a.type === PatientAgentType);
        }
        if (me.resourceType === 'Practitioner') {
            agent = myDefaultAgents?.find(a => a.type === StaffAgentType);
        }
        req.params.id = agent.id;
        req.query.id = agent.id;
        req.query.organizationId = myOrganization.id;
        req.query.type = agent.type;
    }
};
const cleanUpRequestObjectPreHandlerHook = async function(req:any, rep:any){
    req.body = Object.fromEntries(Object.entries(req.body).filter(([_, v]) => v != null));
};
const refreshCommunicationIdentities = async function(req:any, rep:any, payload:any){
    const agent:RelicAgent = JSON.parse(payload);
    if(agent.active){
        for(let identity of agent.communicationIdentities){
            identity = await req.server.acs.refreshIdentity(agent, identity);
        }
    }
    return JSON.stringify(agent);
};

const relicAgentRoutes = async (encapsulatedRouter:FastifyInstance, options:FastifyPluginOptions) => {

    encapsulatedRouter.get("/",{
        schema:{
            ...options.schema,
            description: `API returns a list of agents based on the user's credentials.\n
            If user is Patient, only Patient Agents within user's organization are returned.\n 
            If user is Practitioner, only Staff Agents within user's organization are returned.\n
            If ClientApplication, all agent types are returned.`,
            tags: ['Agent Service'],
            summary: "Get list of agents",
            query: RelicAgentQuerySchema
        },
        config: {
          access: {
            resource: "agents",
            action: "list"
          }
        },
        preHandler: [
            agentDefaultPreHandlerHook,
            transformIdPreHandlerHook,
            agentQueryPreHandlerHook
        ]
    }, getRelicAgents);

    encapsulatedRouter.get("/:id",{
        schema:{
            description: `Get an agent based on id, acs id or thread id\n
            if id='aiassistant' then API 'agents/aiassistant' will provide AI Assistant\n
            if id='default' then API 'agents/default' will provide default agent for the logged in user\n`,
            tags: ['Agent Service'],
            summary: "Get an agent based on id, acs id or thread id",
            params: S.object()
            .prop('id', S.string().description("Id can be ACS userId or id\nSample value(acsId): 8:acs:8ca4fbc2-e63c-4283-a5b7-feb83074370e_0000001b-57e2-3536-ac00-343a0d000c9a\nWhere to get: acsId is communicationIdentities.[0].userId field in practitioners collection\nSample value(threadId): 19:cKMSKJASjA7gesKN2HGDE_3C2HVM7cl_BolHlFlRRGE1@thread.v2\nWhere to get: threadId present in threads collection\nSample value(reliccare id i.e. in this case agent id ): d4651019-ded9-4f6c-a2ee-f39c0caf84b0\nWhere to get: id field present in agents collection"))
        },
        config: {
          access: {
            resource: "agents",
            action: "read"
          }
        },
        preHandler: [
            agentDefaultPreHandlerHook,
            transformIdPreHandlerHook,
            agentQueryPreHandlerHook
        ],
        onSend: [
            agentUpdatePersonaOnSendHook,
            refreshCommunicationIdentities
        ]
    }, getRelicAgentById);

    encapsulatedRouter.post("/",{ 
        schema: {
            description: "API returns newly created agent",
            tags: ['Agent Service'],
            summary: `Create an agent`,
            body: createAgentRequestJsonSchema
        },
        config: {
          access: {
            resource: "agents",
            action: "create"
          }
        }
    }, createRelicAgent);

    encapsulatedRouter.patch("/:id",{ 
        schema:{
            description: "Updated an agent identified by id.Id can be an ACS user id or reliccare id.",
            tags: ['Agent Service'],
            summary: `API returns updated agent details`,
            body: updateAgentRequestJsonSchema
        },
        config: {
          access: {
            resource: "agents",
            action: "update"
          }
        },
        preHandler: [
            cleanUpRequestObjectPreHandlerHook
        ],
        onSend: [
            refreshCommunicationIdentities
        ]
    }, updateRelicAgent);

    encapsulatedRouter.delete("/:id",{
        schema:{
            description: "API deactivates an agent identified by id. Id can be ACS userId or id",
            tags: ['Agent Service'],
            summary: `Deactivate an agent`,
            params: S.object()
            .prop('id', S.string().description("Id can be ACS userId or id\nSample value: 8:acs:8ca4fbc2-e63c-4283-a5b7-feb83074370e_0000001b-57e2-3b1a-28f4-343a0d000dc\nWhere to get: acsId field present in globalAgents collection"))        },
        config: {
          access: {
            resource: "agents",
            action: "delete"
        },
        }
    }, deactivateRelicAgent);

}

export default relicAgentRoutes