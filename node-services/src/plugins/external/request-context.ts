import { <PERSON><PERSON><PERSON>, IUser, IUserIdentity, RelicOrganization } from 'relic-ui';
import { fastifyRequestContext } from '@fastify/request-context';

declare module '@fastify/request-context' {
  interface RequestContextData {
    whoami: IUserIdentity; // User identity
    accessToken: string; // Access token
    organizationId: string; // Organization ID
    decodedJwtToken: object; // Decoded JWT token
    user: IUser; //current request user
    organization: RelicOrganization;
    agentFor: AgentFor; // Agent for the request. To be fixed.
  }
}
/**
 * This plugin introduces thread-local request-scoped HTTP context.
 *
 * @see {@link https://github.com/fastify/fastify-request-context}
 */
export default fastifyRequestContext;
