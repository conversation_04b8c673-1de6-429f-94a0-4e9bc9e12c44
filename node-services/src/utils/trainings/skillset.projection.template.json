{"selectors": [{"targetIndexName": "{{indexName}}", "parentKeyFieldName": "parent_id", "sourceContext": "/document/pages/*", "mappings": [{"name": "contentVector", "source": "/document/pages/*/contentVector", "sourceContext": null, "inputs": []}, {"name": "content", "source": "/document/pages/*", "sourceContext": null, "inputs": []}, {"name": "title", "source": "/document/title", "sourceContext": null, "inputs": []}, {"name": "url", "source": "/document/url", "sourceContext": null, "inputs": []}, {"name": "filepath", "source": "/document/filepath", "sourceContext": null, "inputs": []}, {"name": "last_updated", "source": "/document/last_updated", "sourceContext": null, "inputs": []}, {"name": "deleted", "source": "/document/deleted", "sourceContext": null, "inputs": []}, {"name": "originalFilepath", "source": "/document/originalFilepath", "sourceContext": null, "inputs": []}]}], "parameters": {"projectionMode": "skipIndexingParentDocuments"}}