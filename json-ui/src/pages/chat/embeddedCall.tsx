import { IResourceComponentsProps, useGetIdentity } from '@refinedev/core';
import { useEffect, useMemo, useState } from 'react';
import type {
  CommunicationPanelProps,
  IUser,
  RelicThreadProviderOptions,
  RelicUserProviderOptions,
} from 'relic-ui';
import {
  EmbeddedCallPanel,
  RelicThreadProvider,
  RelicUserProvider,
  ThreadState,
} from 'relic-ui';

// @mui
import { useTheme } from '@mui/material';
import Stack from '@mui/material/Stack';

import { useResponsive } from 'src/hooks/use-responsive';

import { getProviderServicesApi } from 'src/providers/utils/auth-utils';
import { getNodeServicesApi } from 'src/utils/app-config';

// ----------------------------------------------------------------------
export const EmbeddedCall: React.FC<IResourceComponentsProps> = () => {
  const { data: authUser } = useGetIdentity<IUser>();
  const theme = useTheme();
  const [accessToken, setAccessToken] = useState<string>('');
  const [idToken, setIdToken] = useState<string>('');
  const upMd = useResponsive('up', 'md');

  // Update access token when authUser changes
  useEffect(() => {
    if (authUser?.accessToken) {
      setAccessToken(authUser.accessToken);
      setIdToken(authUser?.idToken as string);
    }
  }, [authUser]);

  // RelicUserProviderOptions memoized
  const relicUserProviderOptions: RelicUserProviderOptions = useMemo(
    () => ({
      accessToken,
      idToken,
      serviceUri: getProviderServicesApi(getNodeServicesApi()),
    }),
    [accessToken, idToken],
  );

  // RelicThreadProviderOptions memoized
  const relicThreadProviderOptions: RelicThreadProviderOptions = useMemo(
    () => ({
      displayInEnglish: true,
      threadState: ThreadState.Connecting,
    }),
    [],
  );

  // Custom styling memoized
  const customMessageThreadStyles = useMemo(
    () => ({
      chatMessageContainer: {
        backgroundColor: theme.palette.background.neutral,
        color: theme.palette.text.primary,
        fontFamily: theme.typography.fontFamily,
        fontSize: `${theme.typography.body2.fontSize}px`,
      },
      myChatMessageContainer: {
        backgroundColor: theme.palette.primary.lighter,
        color: theme.palette.text.primary,
        fontFamily: theme.typography.fontFamily,
        fontSize: `${theme.typography.body2.fontSize}px`,
      },
      chatContainer: {
        maxWidth: '100%',
      },
      citationStyles: {
        tooltip: {
          backgroundColor: '#F7F7F7',
          textColor: 'rgba(0, 0, 0, 0.87)',
          borderColor: '#dadde9',
          hoverBackgroundColor: '#e6e6e6',
          titleColor: '#000000',
          linkColor: 'green',
        },
        link: {
          hoverBackgroundColor: '#c8d4fa',
          textColor: '#202020',
          titleColor: '#000000',
          linkColor: '#000000',
        },
        reference: {
          textColor: '#202020',
          backgroundColor: '#b6b6b6',
          titleColor: '#000000',
        },
      },
    }),
    [theme],
  );

  const customSendBoxStyles = useMemo(
    () => ({
      textField: {
        fontFamily: theme.typography.fontFamily,
        fontSize: `${theme.typography.body2.fontSize}px`,
      },
    }),
    [theme],
  );

  const toolbarProps = useMemo(
    () => ({
      showResponseManagement: true,
    }),
    [],
  );

  const communicationPanelProps: CommunicationPanelProps = useMemo(
    () => ({
      pxAboveCommunicationPanel: upMd ? 112 : 72,
      toolbarProps: {
        ...toolbarProps,
        callType: 'new-window-call',
      },
      messageThreadStyles: customMessageThreadStyles,
      sendBoxStyles: customSendBoxStyles,
    }),
    [upMd, toolbarProps, customMessageThreadStyles, customSendBoxStyles],
  );

  return (
    <RelicUserProvider {...relicUserProviderOptions}>
      <RelicThreadProvider {...relicThreadProviderOptions}>
        <Stack direction="row" sx={{ flexGrow: 1 }}>
          {accessToken && toolbarProps && <EmbeddedCallPanel />}
        </Stack>
      </RelicThreadProvider>
    </RelicUserProvider>
  );
};
