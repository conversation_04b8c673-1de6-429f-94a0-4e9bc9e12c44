import { test, expect } from './test.setup';

test('Medplum-Organization List', async ({ page }) => {
    await page.getByRole('button', { name: 'My Organization' }).click();
    await expect(page.getByRole('list')).toContainText('Organizations');
    await page.getByRole('link', { name: 'Organizations' }).click();
});

test('Medplum-Organization List-Validate Organization Form', async ({ page }) => {
    await page.getByRole('button', { name: 'My Organization' }).click();
    await expect(page.getByRole('list')).toContainText('Organizations');
    await page.getByRole('link', { name: 'Organizations' }).click();
    await page.getByRole('heading', { name: 'Organizations' }).click();
    await page.getByRole('link', { name: 'Add' }).click();
    await page.getByRole('heading', { name: 'Create Organization' }).click();
    await expect(page.locator('label').filter({ hasText: 'Organization Name *' })).toBeVisible();
    await expect(page.locator('label').filter({ hasText: 'Organization Type *' })).toBeVisible();
    await expect(page.locator('label').filter({ hasText: 'Website *' })).toBeVisible();
    await expect(page.locator('label').filter({ hasText: 'Default Language for Patients' })).toBeVisible();
    await expect(page.locator('label').filter({ hasText: 'Phone' })).toBeVisible();
    await page.getByRole('button', { name: 'Save' }).click();
});

test.skip('Medplum-Organization List-Sort by Name', async ({ page }) => {
    await page.getByRole('button', { name: 'My Organization' }).click();
    await expect(page.getByRole('list')).toContainText('Organizations');
    await page.getByRole('link', { name: 'Organizations' }).click();
    await page.getByRole('button', { name: 'Sort' }).click();
});

test('Medplum-Search Organization-Successfully', async ({ page }) => {
    await page.getByRole('button', { name: 'My Organization' }).click();
    await expect(page.getByRole('list')).toContainText('Organizations');
    await page.getByRole('link', { name: 'Organizations' }).click();
    await page.getByPlaceholder('Search...').click();
    await page.getByPlaceholder('Search...').fill('sunrise');
    await expect(page.getByRole('rowgroup')).toContainText('DEV - Sunrise Senior Living');
});

test('Medplum-Enable/Disable Facility-Successfully', async ({ page }) => {
  await page.getByRole('button', { name: 'My Organization' }).click();
  await page.getByRole('link', { name: 'Organizations' }).click();
  await expect(page.getByRole('heading', { name: 'Organizations' })).toBeVisible();
  await page.getByRole('gridcell', { name: 'Contoso Healthcare (PCC)' }).click();
  await expect(page.getByRole('heading', { name: 'Contoso Healthcare (PCC)' })).toBeVisible();
  await page.getByRole('tab', { name: 'Facilities' }).click();
  await page.getByRole('gridcell', { name: '(train) FACILITY_22' }).click();
  await page.getByLabel('Enabled').uncheck();
  await page.getByRole('button', { name: 'Save' }).click();
  await expect(page.getByText('Successfully edited Facilities')).toBeVisible();
  await page.getByRole('gridcell', { name: '(train) FACILITY_22' }).click();
  await page.getByLabel('Enabled').check();
  await page.getByRole('button', { name: 'Save' }).click();
  await expect(page.getByText('Successfully edited Facilities')).toBeVisible(); 
});

// test.skip('Medplum - create new organization', async ({ page }) => {

//     await page.goto(common.facilityPortal);
//     await page.getByLabel('Email').fill(common.loginCredentials.username);
//     await page.getByPlaceholder('●●●●●●●●').fill(common.loginCredentials.password);
//     await page.getByRole('button', { name: 'Sign in' }).click();
//     await expect(page.getByRole('link', { name: 'Organizations' })).toBeVisible();
//     await page.getByRole('link', { name: 'Organizations' }).click();
//     await expect(page.getByRole('link', { name: 'Add' })).toBeVisible();
//     await page.getByRole('link', { name: 'Add' }).click();
//     await expect(page.locator('div').filter({ hasText: /^Create Organization$/ }).first()).toBeVisible();
//     await expect(page.getByRole('button', { name: 'Save' })).toBeVisible();

// });

// test.skip('Medplum - Search an organization', async ({ page }) => {

//     await page.goto(common.facilityPortal);
//     await page.getByLabel('Email').fill(common.loginCredentials.username);
//     await page.getByPlaceholder('●●●●●●●●').fill(common.loginCredentials.password);
//     await page.getByRole('button', { name: 'Sign in' }).click();
//     await expect(page.getByRole('link', { name: 'Organizations' })).toBeVisible();
//     await page.getByRole('link', { name: 'Organizations' }).click();
//     await expect(page.getByPlaceholder('Search...')).toBeVisible();
//     await expect(page.getByTestId('SearchIcon')).toBeVisible();
//     await page.getByPlaceholder('Search...').click();
//     await page.getByPlaceholder('Search...').fill('point');
//     await expect(page.getByRole('rowgroup')).toContainText('PointClickCare Sandbox');

// });

// test.skip('Medplum - View details of an organization', async ({ page }) => {
//     await page.goto(common.facilityPortal);
//     await page.getByLabel('Email').fill(common.loginCredentials.username);
//     await page.getByPlaceholder('●●●●●●●●').fill(common.loginCredentials.password);
//     await page.getByRole('button', { name: 'Sign in' }).click();
//     await expect(page.getByRole('link', { name: 'Organizations' })).toBeVisible();
//     await page.getByRole('link', { name: 'Organizations' }).click();

//     await expect(page.getByRole('rowgroup')).toContainText('PointClickCare Sandbox');
//     await page.getByRole('gridcell', { name: 'PointClickCare Sandbox' }).click();
//     await expect(page.locator('div').filter({ hasText: /^Facilities$/ }).first()).toBeVisible();
//     await expect(page.getByRole('main')).toContainText('Organization Type');
//     await expect(page.getByText('Website', { exact: true })).toBeVisible();
//     await expect(page.getByText('Default Language for Patients', { exact: true })).toBeVisible();
//     await expect(page.getByText('PointClickCare Org Id').first()).toBeVisible();

// });
