import { PccFacility } from 'relic-ui';
import React, { useState } from 'react';
import { FieldValues } from 'react-hook-form';
import { useForm } from '@refinedev/react-hook-form';
import { TextFieldComponent as TextField } from '@refinedev/mui';
import { useTranslate, IResourceComponentsProps } from '@refinedev/core';

import Box from '@mui/material/Box';
import Stack from '@mui/material/Stack';
import Dialog from '@mui/material/Dialog';
import Switch from '@mui/material/Switch';
import Skeleton from '@mui/material/Skeleton';
import FormControlLabel from '@mui/material/FormControlLabel';

import { useParams, useRouter } from 'src/routes/hooks';

import { Edit } from 'src/components/refine-customs/edit';

export const FacilityEdit: React.FC<IResourceComponentsProps> = () => {
  const translate = useTranslate();

  const router = useRouter();

  const [open, setOpen] = useState(true);

  const handleClose = () => {
    setOpen(false);
    router.back();
  };

  const { id } = useParams();

  const {
    watch,
    register,
    saveButtonProps,
    refineCore: { queryResult, onFinish },
    handleSubmit,
  } = useForm({
    refineCoreProps: {
      resource: 'facilities',
      id: id,
      redirect: false,
      queryOptions: {
        enabled: id ? true : false,
        staleTime: 0,
        cacheTime: 0,
      },
    },
  });

  const values = watch();

  const currentFacility = queryResult?.data?.data as PccFacility;

  const rows = [
    {
      label: translate('facilities.fields.facilityCode'),
      value: currentFacility?.facilityCode,
    },
    {
      label: translate('facilities.fields.facilityName'),
      value: currentFacility?.facilityName,
    },
    {
      label: translate('facilities.fields.postalCode'),
      value: currentFacility?.postalCode,
    },
    {
      label: translate('facilities.fields.state'),
      value: currentFacility?.state,
    },
    {
      label: translate('facilities.fields.enabled'),
      value: currentFacility?.enabled,
    },
  ];

  const onSubmit = async (values: FieldValues) => {
    values.id = currentFacility?.id;
    values.orgUuid = currentFacility?.orgUuid;
    values.facId = currentFacility?.facId;
    if (!currentFacility?.enabled && values.enabled) {
      // facility was enabled
      values.disabled = false;
      await onFinish(values);
    }
    if (currentFacility?.enabled && !values.enabled) {
      // facility was disabled
      values.disabled = true;
      await onFinish(values);
    }
    handleClose();
  };

  return (
    <Dialog
      fullWidth
      maxWidth="sm"
      open={open}
      onClose={handleClose}
      aria-labelledby="alert-dialog-title"
      aria-describedby="alert-dialog-description"
    >
      <Edit
        saveButtonProps={{
          ...saveButtonProps,
          onClick: handleSubmit(onSubmit),
        }}
        onClose={handleClose}
        deleteButtonProps={{ hidden: true }}
        title={translate('facilities.titles.edit')}
      >
        <Stack spacing={2.5} component="form" autoComplete="off">
          {rows.map(row => (
            <Stack
              key={row.label}
              spacing={1}
              alignItems="flex-start"
              direction={{ xs: 'column', sm: 'row' }}
              sx={{
                marginTop:
                  row.label === translate('facilities.fields.enabled') &&
                  !queryResult?.isLoading
                    ? '-10px'
                    : '0px',
              }}
            >
              <Box
                component={'span'}
                sx={{
                  typography: 'subtitle2',
                  minWidth: 160,
                  alignItems: 'center',
                  marginTop:
                    row.label === translate('facilities.fields.enabled') &&
                    !queryResult?.isLoading
                      ? '10px'
                      : '0px',
                }}
              >
                {row.label}
              </Box>

              {queryResult?.isLoading ? (
                <Skeleton
                  variant="rectangular"
                  sx={{ width: 1, height: 16, borderRadius: 0.5 }}
                />
              ) : (
                (row.label === translate('facilities.fields.enabled') && (
                  <FormControlLabel
                    control={
                      <Switch
                        {...register('enabled')}
                        checked={values.enabled || false}
                        name="enabled"
                      />
                    }
                    label={translate('facilities.fields.enabled')}
                  />
                )) || <TextField value={row.value} />
              )}
            </Stack>
          ))}
        </Stack>
      </Edit>
    </Dialog>
  );
};
