import React from 'react';
import { Action, useResourceParams } from '@refinedev/core';
import { ThemedLayoutContextProvider } from '@refinedev/mui';
import type { RefineThemedLayoutV2Props } from '@refinedev/mui';

import Box from '@mui/material/Box';

import { ThemedSiderV2 as DefaultSider } from './sider';
import { ThemedHeaderV2 as DefaultHeader } from './header';
import { Resource } from '@refinedev/core/dist/hooks/router/use-go';

export const ThemedLayoutV2: React.FC<RefineThemedLayoutV2Props> = ({
  Sider,
  Header,
  Title,
  Footer,
  OffLayoutArea,
  children,
  initialSiderCollapsed,
}) => {
  const SiderToRender = Sider ?? DefaultSider;
  const HeaderToRender = Header ?? DefaultHeader;
  const { resource, action } = useResourceParams();
  //Todo: Based on resource & action, we can fix main component's height or remove the height attribute.
  //This helps with chat rendering.

  return (
    <ThemedLayoutContextProvider initialSiderCollapsed={initialSiderCollapsed}>
      <Box display="flex" flexDirection="row">
        <SiderToRender Title={Title} />
        <Box
          sx={[
            {
              display: 'flex',
              flexDirection: 'column',
              flex: 1,
              minHeight: '100vh',
              overflow: 'auto',
            },
          ]}
        >
          <HeaderToRender />
          <Box
            component="main"
            sx={{
              p: { xs: 1, md: 2, lg: 3 },
              flexGrow: 1,
              height: 'calc(100vh - 64px)',
              bgcolor: (theme) => theme.palette.background.default,
            }}
          >
            {children}
          </Box>
          {Footer && <Footer />}
        </Box>
        {OffLayoutArea && <OffLayoutArea />}
      </Box>
    </ThemedLayoutContextProvider>
  );
};
