import { useTranslate } from '@refinedev/core';

import Tab from '@mui/material/Tab';
import Tabs from '@mui/material/Tabs';
import Stack from '@mui/material/Stack';
import Divider from '@mui/material/Divider';

// ----------------------------------------------------------------------

type TrainingModulesEditTabsProps = {
  currentTab: string;
  tabOptions: {
    value: string;
    label: string;
  }[];

  onChangeTab: (event: React.SyntheticEvent, newValue: string) => void;
};

export default function TrainingModulesTabs({
  currentTab,
  tabOptions,
  onChangeTab,
}: TrainingModulesEditTabsProps) {
  const translate = useTranslate();

  return (
    <Stack
      direction={'row'}
      alignItems={'center'}
      justifyContent={'space-between'}
      sx={{
        position: 'relative',
      }}
    >
      <Tabs value={currentTab} onChange={onChangeTab}>
        {tabOptions.map(tab => (
          <Tab
            key={tab.value}
            value={tab.value}
            label={translate(tab.label)}
            sx={{ minHeight: 72 }}
          />
        ))}
      </Tabs>

      <Divider
        sx={{
          bottom: 0,
          position: 'absolute',
          left: { xs: -8, md: -16, lg: -24 },
          right: { xs: -8, md: -16, lg: -24 },
        }}
      />
    </Stack>
  );
}
