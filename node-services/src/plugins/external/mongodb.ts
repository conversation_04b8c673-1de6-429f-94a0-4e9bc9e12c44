import mongodb, { FastifyMongodbOptions } from '@fastify/mongodb'
import config from 'config';

export const autoConfig: FastifyMongodbOptions = {
    name: 'reliccare',
    forceClose: true,
    url: `mongodb://${config.get('COSMOSDB.HOST')}:${config.get('COSMOSDB.PORT')}/${config.get('COSMOSDB.DB_NAME')}?ssl=true&replicaSet=globaldb&retryWrites=false`,
    auth: {
        username: config.get('COSMOSDB.USER') as string,
        password: config.get('COSMOSDB.PASSWORD') as string
    }
}

export default mongodb;
