import { useOne, useCustom } from '@refinedev/core';
import { RelicPatient, RelicDocument, PccPatientCarePlan } from 'relic-ui';

import { CarePlanDesign } from './pcc/care-plan';
import carePlanJson from './pcc/care-plan-data-2.json';

export const ReportDesign = () => {
  //Data Population
  const patientId = '227694';
  const documentId = '0e208587-2b44-436a-9662-ea22507b2145';

  const { data: patientData, isLoading: patientLoading } = useOne({
    resource: 'patients',
    id: patientId,
  });

  const { data: documentData, isLoading: documentLoading } = useOne({
    resource: 'documents',
    id: documentId,
  });

  const { data: reportLabelsData, isLoading: isReportLabelsLoading } =
    useCustom({
      url: `/locales/reports.en-us.json`,
      method: 'get',
    });

  console.log('fetching report', documentData?.data);
  //Data Population end

  const relicPatient =
    (patientData?.data as RelicPatient) ??
    (carePlanJson.relicPatient as RelicPatient);
  const relicDocument =
    (documentData?.data as RelicDocument) ??
    (carePlanJson.relicDocument as unknown as RelicDocument);

  return (
    <>
      {!patientLoading &&
        !documentLoading &&
        !isReportLabelsLoading &&
        CarePlanDesign(
          relicPatient,
          relicDocument,
          reportLabelsData?.data?.labels?.careplan,
        )}
    </>
  );
};
