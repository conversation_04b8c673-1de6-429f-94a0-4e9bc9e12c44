/**
 * Delays the execution for a given number of milliseconds.
 * @param ms Number of milliseconds to delay.
 * @returns A promise that resolves after the specified delay.
 */
export function delay(ms: number): Promise<void> {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

export function formatPhoneNumber(phoneNumber: string): string {
  // Strip all non-digit characters for safety
  const digits = phoneNumber.replace(/\D/g, '');

  // Check if number includes country code (e.g., '1' for US)
  const hasCountryCode = digits.length === 11 && digits.startsWith('1');
  const localNumber = hasCountryCode ? digits.substring(1) : digits;

  // Format assuming a 10-digit number
  if (localNumber.length === 10) {
    return `(${localNumber.substring(0, 3)}) ${localNumber.substring(3, 6)}-${localNumber.substring(6)}`;
  }

  return phoneNumber;
}

export function calculateDuration(startTime, endTime) {
  // Validate input types
  if (typeof startTime !== 'number' || typeof endTime !== 'number') {
    return 'Error: Both startTime and endTime must be numbers.';
  }

  // Validate that times are not negative and are reasonably within the range JavaScript Date can handle
  if (startTime < 0 || endTime < 0 || startTime > 8640000000000000 || endTime > 8640000000000000) {
    return 'Error: Invalid time values. Ensure timestamps are positive and within JavaScript date range.';
  }

  // Calculate duration in seconds
  let durationSeconds = Math.floor((endTime - startTime) / 1000);

  // Check for negative duration
  if (durationSeconds < 0) {
    return 'Error: End Time is before Start Time. Please check the input values.';
  }

  // Convert seconds to hours, minutes, and seconds
  const hours = Math.floor(durationSeconds / 3600);
  durationSeconds %= 3600;
  const minutes = Math.floor(durationSeconds / 60);
  const seconds = durationSeconds % 60;

  // Generate a human-friendly duration string
  const parts = [];
  if (hours > 0) parts.push(`${hours}h`);
  if (minutes > 0 || hours > 0) parts.push(`${minutes}m`);
  parts.push(`${seconds}s`);

  return parts.join(' ');
}
