{
    "extends": "fastify-tsconfig",
    "compilerOptions": {
        // "declaration": true /* Generates corresponding '.d.ts' file. */,
        // "removeComments": true /* Do not emit comments to output. */,
        // "emitDecoratorMetadata": true /* Enables experimental support for emitting type metadata for decorators. */,
        // "experimentalDecorators": true /* Enables experimental support for decorators. */,
        // "allowSyntheticDefaultImports": true /* Allow default imports from modules with no default export. This does not affect code emit, just type checking. */,
        // "resolveJsonModule": true /* Include modules imported with '.json' extension. */,
        "module": "CommonJS" /* Specify module code generation: 'CommonJS', 'ESNext', 'ES6', 'ES2015', 'AMD', 'System', 'UMD', or 'ES2020'. */,
        "moduleResolution": "node" /* Specify module resolution strategy: 'node' or 'classic'. */,
        // "target": "ES2021" /* Specify ECMAScript target version. */,
        // "sourceMap": true /* Generates corresponding '.map' file. */,
        "outDir": "dist" /* Specify an output folder for all emitted files. */,
        "rootDir": "./" /* Specify the root folder within your source files. */,
        "baseUrl": "./" /* Specify the base directory to resolve non-relative module names. */,
        // "incremental": true /* Enable incremental compilation. */,
        // "skipLibCheck": true /* Skip type checking all .d.ts files. */,
        // "strictNullChecks": false /* When type checking, take into account whether values can be null or undefined. */,
        // "strictBindCallApply": false /* Check that the arguments for 'bind', 'call', and 'apply' methods match the original function's parameter types. */,
        "paths": {
            "@/types/*": ["src/types/*"],
            "@/utils/*": ["src/utils/*"]
        },
        // "esModuleInterop": true /* Emit additional JavaScript to ease support for importing CommonJS modules. This enables 'allowSyntheticDefaultImports' for type compatibility. */,
        "strict": false /* Enable all strict type-checking options. */,
        "noUncheckedIndexedAccess": true,                 /* Add 'undefined' to a type when accessed using an index. */
    },
    "include": ["src/**/*.ts", "main.ts", "types/**/*.d.ts", "src/**/*.json", "src/**/*.js", "config/*.json", "index.ts"],
    "exclude": ["node_modules"]
}
