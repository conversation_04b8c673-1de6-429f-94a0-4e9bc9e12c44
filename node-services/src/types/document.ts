export type RelicDocument = {
  id?: string;
  organizationId: string;
  language: string;
  /**
   * 'url' - for crawl requests
   * 'folder' - for folder content with sitemap
   * files will use their content-type (ie. application/pdf, application/msword, etc)
   */
  type: string | 'url' | 'folder';
  url: string;
  filename: string; //filename during upload from browser or json-ui, and used for display
  documentId: string; //filename in azure storage
  isUploaded?: boolean;
  sourceDocumentId?: string;
  createdBy?: DocumentUsers;
  createDate?: Date;
  updatedBy?: DocumentUsers;
  updateDate?: Date;
  deleted?: boolean;
  access?: DocumentUsers[];
  hasTranslatedFiles?: boolean; //not saved in DB, displayed only
  header: number;
  footer: number;
  status: 'pending' | 'done' | 'failed' | 'inprogress';
  errorDetails?: string;
  lastTranslateAttemptDate?: Date;
  translationType: 'mono' | 'bilingual';
  metadata?: PccMetaData;
  data?: { [key: string]: any };
};

export type PccMetaData = {
  createdBy: string;
  createdDate: string;
  revisionBy: string;
  revisionDate: string;
  nextReviewDate: string;
};

export type DocumentUsers = {
  id: string;
  resourceType: string;
};