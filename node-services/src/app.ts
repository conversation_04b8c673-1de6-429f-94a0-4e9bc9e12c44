import path from 'path';
import { FastifyInstance, FastifyPluginOptions } from 'fastify';
import config from 'config';
import fastifyAutoload from '@fastify/autoload';
import tenantPlugin from './plugins/tenant/tenantPlugin';

// Connfigured Node Services App as a plugin.
export default async function nodeServiceApp(fastify: FastifyInstance, opts: FastifyPluginOptions) {
    // delete opts.skipOverride // This option only serves testing purpose

    fastify.log.info('External Plugins loading...');
    /**
     * Loading all External plugins from plugins/external folder.
     * These are registered first as application plugins will depend on them.
     * Refer https://github.com/fastify/fastify-autoload for more details
     */
    await fastify.register(fastifyAutoload, {
        dir: path.join(__dirname, 'plugins/external'),
        options: { ...opts }
    })
    fastify.log.info('External plugins loaded successfully.')

    fastify.log.info('Application Plugins loading...');
    /**
     * Loading all Application plugins from plugins/app folder.
     * Refer https://github.com/fastify/fastify-autoload for more details
     */
    await fastify.register(fastifyAutoload, {
        dir: path.join(__dirname, 'plugins/app'),
        encapsulate: false,
        options: {
            templatesDir: path.join(__dirname, '/templates/email'),
            sendgridApiKey: config.get('MAIL.SENDGRID.API_KEY'),
            ...opts
        }
    })

    fastify.log.info('Application Plugins loaded successfully.')

    fastify.log.info('Tenant Plugin loading...');

    await fastify.register(tenantPlugin, opts);

    fastify.log.info('Tenant Plugin loaded successfully.')

    // Root route for Node Services
    fastify.get(
      '/',
      {
        schema: {
          tags: ['Guest Service'],
          description: 'Node Services Root Route. This route is not secured and does not require authentication token.',
          summary: 'Unsecured - Node Services Root Route',
        },
      },
      (req, reply) => {
        reply.send('Node Services');
      },
    );

    fastify.setErrorHandler((err, request, reply) => {
        //if statusCode not set or 5XX, attempt set correct http status
        if (!(err.statusCode) || err.statusCode >= 500) {
            //check medplum errors
            const msg = (err.message ?? JSON.stringify(err.message) ?? '')?.toLowerCase();
            if (msg?.includes('unauthenticated')) {
                err.statusCode = 401;
            }
            if (msg?.includes('gone') || msg?.includes('not found')) {
                err.statusCode = 404;
            }
        }
        fastify.log.error(
            {
                err,
                request: {
                    method: request.method,
                    url: request.url,
                    query: request.query,
                    params: request.params
                }
            },
            'Unhandled error occurred'
        )

        reply.code(err.statusCode ?? 500)

        let message = 'Internal Server Error'
        if (err.statusCode && err.statusCode < 500) {
            message = err.message
        }

        return { message }
    })

    // An attacker could search for valid URLs if your 404 error handling is not rate limited.
    fastify.setNotFoundHandler(
        {
            preHandler: fastify.rateLimit({
                max: 3,
                timeWindow: 500
            })
        },
        (request, reply) => {
            request.log.warn(
                {
                    request: {
                        method: request.method,
                        url: request.url,
                        query: request.query,
                        params: request.params
                    }
                },
                'Resource not found'
            )

            reply.code(404)

            return { message: 'Not Found' }
        }
    )
};
