import { IUser } from 'relic-ui';
import { RelicAgent, RelicOrganization } from 'relic-ui';
import { useTranslate, useGetIdentity } from '@refinedev/core';

import Popover from '@mui/material/Popover';
import MenuItem from '@mui/material/MenuItem';

import { ReturnType as PopoverProps } from 'src/hooks/use-popover';

import Iconify from 'src/components/iconify';
import { CreateLocation } from 'src/components/_location';
import CreateAgent from 'src/components/_agent/create/agent-create';

import CreateThread from 'src/components/_threads/create/threads-create';

type OrganizationToolbarPopoverProps = {
  popover: PopoverProps;
  openLocation: boolean;
  openAgent: boolean;
  openThread: boolean;
  onOpenLocation: VoidFunction;
  onCloseLocation: VoidFunction;
  onOpenAgent: VoidFunction;
  onCloseAgent: VoidFunction;
  onOpenThread: VoidFunction;
  onCloseThread: VoidFunction;
  updateCurrentTab: (newState: string) => void;
  agentList: RelicAgent[];
  currentOrganization?: RelicOrganization;
};

export default function ToolbarPopover({
  popover,
  openLocation,
  onOpenThread,
  onOpenLocation,
  openAgent,
  openThread,
  onOpenAgent,
  onCloseAgent,
  onCloseLocation,
  onCloseThread,
  updateCurrentTab,
  agentList,
  currentOrganization,
}: OrganizationToolbarPopoverProps) {
  const translate = useTranslate();
  const { data: currentUser } = useGetIdentity<IUser>();

  return (
    <>
      <Popover
        open={!!popover.open}
        anchorEl={popover.open}
        onClose={popover.onClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        slotProps={{
          paper: {
            sx: {
              mt: 0.5,
              minWidth: 160,
            },
          },
        }}
      >
        <MenuItem onClick={onOpenAgent}>
          <Iconify width={18} icon={'eva:plus-fill'} sx={{ mr: 1 }} />
          {translate('agents.titles.create')}
        </MenuItem>

        <MenuItem onClick={onOpenThread}>
          <Iconify width={18} icon={'eva:plus-fill'} sx={{ mr: 1 }} />
          {translate('threads.titles.create')}
        </MenuItem>

        {currentUser &&
          currentUser.userIdentity &&
          currentUser.userIdentity.provider &&
          currentUser.userIdentity.provider === 'medplum' && (
            <MenuItem onClick={onOpenLocation}>
              <Iconify width={18} icon={'eva:plus-fill'} sx={{ mr: 1 }} />
              {translate('locations.titles.create')}
            </MenuItem>
          )}

      </Popover>
      {openLocation && (
        <CreateLocation open={openLocation} onClose={onCloseLocation} />
      )}
      {openAgent && (
        <CreateAgent
          open={openAgent}
          onClose={onCloseAgent}
          agentList={agentList}
          currentOrganization={currentOrganization}
        />
      )}
      {openThread && (
        <CreateThread
          open={openThread}
          onClose={onCloseThread}
          currentOrganization={currentOrganization}
        />
      )}
    </>
  );
}
