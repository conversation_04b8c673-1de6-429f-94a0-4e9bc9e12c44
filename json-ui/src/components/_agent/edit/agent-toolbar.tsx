import { useTranslate } from '@refinedev/core';

import { Stack, Divider, IconButton, Typography, Button } from '@mui/material';

import Iconify from 'src/components/iconify';

type AgentEditToolbarProps = {
  agentName?: string;
  onBack: VoidFunction;
  showAgent: boolean;
  onToggleAgent: VoidFunction;
  onOpenAdd: (event: React.MouseEvent<HTMLElement, MouseEvent>) => void;
};

export default function AgentToolbar({
  agentName,
  onBack,
  showAgent,
  onToggleAgent,
  onOpenAdd,
}: AgentEditToolbarProps) {
  const translate = useTranslate();

  return (
    <Stack
      direction={'row'}
      alignItems={'center'}
      sx={{
        pb: 3,
        zIndex: 9,
        position: 'relative',
        pt: { xs: 2, lg: 0 },
        background: 'background.paper',
      }}
    >
      <IconButton onClick={onBack}>
        <Iconify icon="eva:arrow-ios-back-fill" />
      </IconButton>

      <Typography variant="h5" sx={{ flexGrow: 1 }}>
        {agentName}
      </Typography>

      <Button
        variant="contained"
        startIcon={<Iconify icon={'eva:plus-fill'} />}
        onClick={onOpenAdd}
      >
        {translate('buttons.create')}
      </Button>

      <IconButton
        onClick={onToggleAgent}
        color={showAgent ? 'inherit' : 'default'}
        sx={{ ml: 1 }}
      >
        <Iconify
          icon={
            !showAgent
              ? 'tabler:layout-sidebar-right-collapse'
              : 'tabler:layout-sidebar-right-collapse-filled'
          }
        />
      </IconButton>
      <Divider
        sx={{
          bottom: 0,
          position: 'absolute',
          left: { xs: -8, md: -16, lg: -24 },
          right: { xs: -8, md: -16, lg: -24 },
        }}
      />
    </Stack>
  );
}
