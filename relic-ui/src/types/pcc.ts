import type { components } from "./pccOpenapi"

export type PccUserInfo = components["schemas"]["User"];
  
export type PccFacility = components["schemas"]["Facility"] & {
  id: string;
  enabled: boolean;
  relicOrganizationId?: string;
};

export type PccPatient = components["schemas"]["Patient"] & components["schemas"]["PatientDetails"];

export type PccCondition = components["schemas"]["Condition"];

export type PccPractitioner = components["schemas"]["Practitioner"];

export type PccPatientCarePlan = components["schemas"]["GetCarePlan"];

export type PccCarePlanFocus = components["schemas"]["GetFocus"];

export type PccCarePlanFocusResponse = components["schemas"]["GetFocusResponse"];

export type PccCarePlanFocusGoals = components["schemas"]["GetGoal"];

export type PccCarePlanFocusInterventions = components["schemas"]["GetIntervention"];

