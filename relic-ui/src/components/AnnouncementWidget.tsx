//import { Divider } from 'antd';
import React from 'react';
import { Divider } from '@fluentui/react-components';

// TODO: import from somewhere else
interface AnnouncementWidgetProps {
  messageProps: {
    message: {
      content: string;
    };
  };
}

const AnnouncementWidget = ({ messageProps }: AnnouncementWidgetProps) => {
  return (
    // <Divider plain className='relic-facility-divider'>
    <Divider appearance="brand">{messageProps.message.content} </Divider>
  );
};
export default AnnouncementWidget;
