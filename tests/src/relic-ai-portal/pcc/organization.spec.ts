import { test, expect } from './test.setup';
import { common } from '../../../common';

test('PCC-Organization List-Success', async ({ page }) => {
  await page.getByRole('button', { name: 'My Organization' }).click();
  await page.getByRole('link', { name: 'Organizations' }).click();
  await page.getByRole('gridcell', { name: 'Contoso Healthcare (PCC)' }).click();
});

test('PCC-Organization Search-Success', async ({ page }) => {
  await page.getByRole('button', { name: 'My Organization' }).click();
  await page.getByRole('link', { name: 'Organizations' }).click();
  await page.getByPlaceholder('Search...').click();
  await page.getByPlaceholder('Search...').fill('Contoso');
  await page.getByPlaceholder('Search...').press('Enter');
  const contosoHealthcare= page.getByRole('gridcell', { name: 'Contoso Healthcare (PCC)' });
  await contosoHealthcare.click();
  await expect(page.getByRole('heading', { name: 'Contoso Healthcare (PCC)' })).toBeVisible(); 
});

test('PCC-Enable/Disable Facility-Successfully', async ({ page }) => {
  await page.getByRole('button', { name: 'My Organization' }).click();
  await page.getByRole('link', { name: 'Organizations' }).click();
  await page.waitForSelector('.MuiDataGrid-row--firstVisible', { state: 'visible' });
  const contosoHealthcare = page.getByRole('gridcell', { name: 'Contoso Healthcare (PCC)' });
  await contosoHealthcare.waitFor();
  await contosoHealthcare.click();
  await page.getByRole('tab', { name: 'Facilities' }).click();
  await page.waitForSelector('.MuiDataGrid-row--firstVisible', { state: 'visible' });
  const facility22 = page.getByRole('gridcell', { name: '(train) FACILITY_22' });
  await facility22.waitFor();
  await facility22.click();
  await expect(page.getByRole('heading', { name: 'Access Denied' })).toBeVisible();
  await expect(page.getByRole('main')).toContainText('You do not have the permissions to view this page.');
});

test('PCC-Organization List-Validate Organization Form', async ({ page }) => {
  await page.getByRole('button', { name: 'My Organization' }).click();
  await page.getByRole('link', { name: 'Organizations' }).click();
  await page.getByRole('link', { name: 'Add' }).click();
  await expect(page.getByRole('heading', { name: 'Access Denied' })).toBeVisible();
  await page.getByText('Access DeniedYou do not have').click();
});