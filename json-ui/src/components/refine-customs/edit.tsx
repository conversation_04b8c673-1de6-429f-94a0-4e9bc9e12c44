import * as React from 'react';
import {
  SaveButton,
  RefreshButton,
  DeleteButtonProps,
  Edit as RefineEdit,
  RefreshButtonProps,
  EditProps as RefineEditProps,
} from '@refinedev/mui';

import Box from '@mui/material/Box';
import Button from '@mui/material/Button';

import { ConfirmDeleteDialog } from 'src/components/refine-customs/delete-btn';

// ----------------------------------------------------------------------

type EditProps = RefineEditProps & {
  onClose?: VoidFunction;
  deleteButtonProps?: DeleteButtonProps & {
    confirmContent?: string;
  };
  refreshButtonProps?: RefreshButtonProps;
};

export const Edit: React.FC<EditProps> = ({
  onClose,
  children,
  deleteButtonProps: deleteButtonInEditProps,
  refreshButtonProps: refreshButtonInEditProps,
  ...other
}) => (
  <RefineEdit
    goBack={null}
    breadcrumb={null}
    wrapperProps={{
      sx: {
        borderRadius: 0,
        display: 'flex',
        boxShadow: 'none',
        flexDirection: 'column',
      },
    }}
    contentProps={{
      sx: {
        flex: '1 1 auto',
        overflowY: 'auto',
      },
    }}
    headerProps={{
      sx: {
        pb: 3,
        borderBottom: theme => `solid 1px ${theme.palette.divider}`,
        '& .refine-pageHeader-title': {
          typography: 'h6',
        },
      },
    }}
    headerButtonProps={{
      sx: {
        '& a': {
          color: 'inherit',
        },
      },
    }}
    headerButtons={({ refreshButtonProps }) => (
      <>
        {refreshButtonProps && !refreshButtonInEditProps?.hidden && (
          <RefreshButton
            {...refreshButtonProps}
            {...refreshButtonInEditProps}
            variant="outlined"
          />
        )}
      </>
    )}
    footerButtons={({ saveButtonProps, deleteButtonProps }) => (
      <>
        {!deleteButtonInEditProps?.hidden && deleteButtonProps && (
          <>
            <ConfirmDeleteDialog
              deleteButtonProps={{
                ...deleteButtonProps,
                ...deleteButtonInEditProps,
              }}
            />
          </>
        )}

        <Box sx={{ flexGrow: 1 }} />

        {onClose && (
          <Button variant="outlined" onClick={onClose}>
            Cancel
          </Button>
        )}

        {saveButtonProps && (
          <SaveButton {...saveButtonProps} variant="contained" />
        )}
      </>
    )}
    footerButtonProps={{
      sx: {
        p: 2,
        borderTop: theme => `solid 1px ${theme.palette.divider}`,
      },
    }}
    {...other}
  >
    {children}
  </RefineEdit>
);
