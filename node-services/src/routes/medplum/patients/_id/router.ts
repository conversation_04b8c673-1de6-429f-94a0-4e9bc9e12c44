'use strict';
import { S } from 'fluent-json-schema';
import { editPatientSchema } from '../../../../types/patientSchema';
import { Endpoint, IUserIdentity, Thread } from 'relic-ui';
import { NewThread, ThreadsWithCount, RelicChatParticipant } from 'relic-ui';
import { RelicPatient, PatientEverything } from 'relic-ui';
import { relicPatientToFhirPatient } from '../../../../types/relicPatientConversion';
import { FastifyInstance, FastifyPluginOptions, FastifyReply, FastifyRequest } from 'fastify';
import { requestContext } from '@fastify/request-context';

/**
 * Patient Request Router
 * @param {FastifyInstance} fastify - Fastify instance router.
 * @param {FastifyPluginOptions} options - Fastify options as needed
 */
export default async function patientRoutes(fastify: FastifyInstance, options: FastifyPluginOptions) {
  fastify.get(
    '/',
    {
      schema: {
        ...options.schema,
        params: S.object().prop(
          'id',
          S.string()
            .default('d1d4b57d-5c8b-423a-9791-e4007b2e7eb3')
            .description(
              'Sample value: d1d4b57d-5c8b-423a-9791-e4007b2e7eb3 (id i.e. patient id),\nWhere to get: id field present in patients collection',
            )
            .minLength(1),
        ),
        description: 'Get a patient for an Organization / Facility',
        summary: 'Get a patient for an Organization / Facility',
      },
    },
    async function (req: FastifyRequest, rep: FastifyReply): Promise<RelicPatient> {
      const { id } = req.params as { id: string };
      return await req.server.ehrService.getPatientById(id);
    },
  );

  fastify.patch(
    '/',
    {
      schema: {
        ...options.schema,
        description: 'Update Patient API. This API will update a FHIR patient.',
        summary: 'Update a Patient admitted in an Organization / Facility',
        body: editPatientSchema,
      },
    },
    async function updateSingle(req: FastifyRequest, rep: FastifyReply): Promise<RelicPatient> {
      //TODO: To be fixed. cb1-803 to resolve update Patient error
      const { id: patientId } = req.params as { id: string };
      const relicPatient = await req.server.ehrService.getPatientById(patientId);
      const organization = requestContext.get('organization');
      const { email, mobilePhone, homePhone } = req.body as object as {
        email: string;
        mobilePhone: string;
        homePhone: string;
      };
      const fhirPatient = relicPatientToFhirPatient(
        {
          ...relicPatient,
          ...(req.body as object),
        },
        organization,
      );
      return await req.server.updatePatient(patientId, {
        patientData: fhirPatient,
        email,
        mobilePhone,
        homePhone,
      });
    },
  );

  fastify.delete(
    '/',
    {
      schema: {
        ...options.schema,
        params: S.object().prop(
          'id',
          S.string()
            .description(
              'Sample value: d1d4b57d-5c8b-423a-9791-e4007b2e7eb3 (id i.e. patient id),\nWhere to get: id field present in patients collection',
            )
            .minLength(1)
            .required(),
        ),
        description:
          'Delete a patient for an Organization / Facility. It will just mark the patient as Inactive. Physical deletion of reords is not done.',
        summary: 'Delete a patient for an Organization / Facility. ',
      },
    },
    async function deleteSingle(request: FastifyRequest, reply: FastifyReply): Promise<void> {
      try {
        const { id } = request.params as { id: string };
        await request.server.markPatientInactive(id);
        const response = await request.server.deleteRelicPatient(id);
        if (response && response.b2cid) {
          await request.server.deleteB2cPatient(response.b2cid);
        }
      } catch (error) {
        request.log.error(error);
        throw new Error(`error deleting patient : ${error.message}`);
      }
    },
  );

  fastify.get(
    '/chat',
    {
      schema: {
        ...options.schema,
        params: S.object().prop(
          'id',
          S.string()
            .minLength(1)
            .required()
            .description(
              'id can be resource id or ACS id\nSample value: 8:acs:8ca4fbc2-e63c-4283-a5b7-feb83074370e_0000001b-57e2-3b1a-28f4-343a0d000dc\nWhere to get: acsId field present in globalAgents collection',
            ),
        ),
        summary: `Get Patient's default thread or a thread with Patient participant depending on the caller's resource type.`,
        tags: ['Communication Service'],
        description: `Returns Patient's Default Chat thread if caller is Patient. Returns a Chat thread between Practitioner and Patient if caller is Practitioner`,
      },
    },
    async function (req: FastifyRequest, rep: FastifyReply) {
      const { id } = req.params as { id: string };
      const me: IUserIdentity = requestContext.get('whoami');
      const filter: any = {};
      const participants: RelicChatParticipant[] = [];
      //Filter for active threads and return the latest updated thread
      filter.status = 'active';
      filter.sort = 'updateDate';
      filter.order = 'desc';
      participants.push({
        resourceId: me.id,
        resourceType: me.resourceType === 'Patient' ? 'Patient' : 'Practitioner', //This will typically be a practitioner
        id: undefined, // ParticipantACS id will be patched by Communication Plugin
      });
      participants.push({
        resourceId: id,
        resourceType: 'Patient', //Param is a patient since this is a patient route
        id: undefined, // ParticipantACS id will be patched by Communication Plugin
      });
      filter.relicChatParticipants = participants;
      const threadsWithCount: ThreadsWithCount = await fastify.getRelicThreads(filter);
      const threads: Thread[] = threadsWithCount.threads;
      const thread: Thread = threads?.[0];
      if (thread) {
        return thread;
      }
      //Add patient name to the participant request while creating a new thread
      const patientEverything: PatientEverything = await fastify.fetchPatientById(id);
      const relicPatient: RelicPatient = fastify.toRelicPatient(patientEverything.Patient, {
        id: id,
        resourceType: 'Patient',
        organizationId: me.portalIdentity.organizationId,
      });
      participants.find((p) => p.resourceType === 'Patient').displayName = relicPatient.name;
      participants.find((p) => p.resourceType === 'Patient').chatLanguage = relicPatient.primaryLanguage;
      participants.find((p) => p.resourceType === 'Patient').mobilePhone = relicPatient.mobilePhone;
      const organization = req.requestContext.get('organization');
      const newThread: NewThread = {
        endpoint: organization.endpoints.find((e: Endpoint) => e.provider === 'Azure Communication Service').endpoint,
        threadSubject: {
          organizationId: me.portalIdentity.organizationId,
          threadOwner: {
            id: me.id,
            resourceType: me.resourceType,
          },
          patientLanguage: relicPatient.primaryLanguage,
          title: 'Chat with ' + relicPatient.name,
        },
        participants: participants,
        status: 'active',
        inviteVia: 'none',
      };
      return await fastify.createRelicThread(newThread);
    },
  );

  fastify.get(
    '/identities',
    {
      schema: {
        ...options.schema,
        params: S.object().prop('id', S.string().minLength(1).required().description("patient's resource id")),
        summary: `Get Patient's communication identities`,
        tags: ['Communication Service'],
        description: `Returns Patient's communication identities`,
      },
    },
    async function (req: FastifyRequest, rep: FastifyReply) {
      const { id } = req.params as { id: string };
      const relicPatient: RelicPatient = await req.server.ehrService.getPatientById(id);
      const communicationIdentities = relicPatient.communicationIdentities;
      for (let identity of communicationIdentities) {
        const updatedIdentity = await fastify.acs.refreshIdentity(relicPatient, identity);
        identity.secret = updatedIdentity.secret;
      }
      return communicationIdentities;
    },
  );
}
