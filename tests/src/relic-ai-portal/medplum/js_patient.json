{"patients": [{"resourceType": "Patient", "active": true, "name": [{"use": "official", "family": "Playwright", "given": ["Zion"]}], "gender": "male", "birthDate": "1961-01-01", "address": [{"use": "home", "type": "both", "line": ["369, ", "6th St"], "city": "Santa Monica", "state": "USA", "postalCode": "90406"}], "maritalStatus": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/v3-MaritalStatus", "code": "M", "display": "Married"}]}, "managingOrganization": {"reference": "Organization/7760ffcd-64d2-43df-9548-055b257dfaee", "display": "DEV - Sunrise Senior Living"}, "telecom": [{"system": "email", "value": "<EMAIL>", "use": "home"}, {"system": "phone", "value": "(+1)-************", "use": "mobile"}], "communication": [{"language": {"coding": [{"system": "http://hl7.org/fhir/ValueSet/languages", "code": "hi", "display": "English"}]}, "preferred": true}]}, {"resourceType": "Patient", "active": true, "name": [{"use": "official", "family": "Playwright", "given": ["<PERSON><PERSON>"]}], "gender": "male", "birthDate": "01/01/1962", "address": [{"use": "home", "type": "both", "line": ["369, ", "7th St"], "city": "Santa Monica", "state": "USA", "postalCode": "90407"}], "maritalStatus": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/v3-MaritalStatus", "code": "M", "display": "Married"}]}, "managingOrganization": {"reference": "Organization/7760ffcd-64d2-43df-9548-055b257dfaee", "display": "DEV - Sunrise Senior Living"}, "telecom": [{"system": "email", "value": "<EMAIL>", "use": "home"}, {"system": "phone", "value": "(+1)-************", "use": "mobile"}], "communication": [{"language": {"coding": [{"system": "http://hl7.org/fhir/ValueSet/languages", "code": "hi", "display": "English"}]}, "preferred": true}]}, {"resourceType": "Patient", "active": true, "name": [{"use": "official", "family": "Playwright", "given": ["<PERSON><PERSON>"]}], "gender": "male", "birthDate": "1961-01-01", "address": [{"use": "home", "type": "both", "line": ["369, ", "6th St"], "city": "Santa Monica", "state": "USA", "postalCode": "90406"}], "maritalStatus": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/v3-MaritalStatus", "code": "M", "display": "Married"}]}, "managingOrganization": {"reference": "Organization/7760ffcd-64d2-43df-9548-055b257dfaee", "display": "DEV - Sunrise Senior Living"}, "telecom": [{"system": "email", "value": "<EMAIL>", "use": "home"}, {"system": "phone", "value": "(+1)-************", "use": "mobile"}], "communication": [{"language": {"coding": [{"system": "http://hl7.org/fhir/ValueSet/languages", "code": "hi", "display": "English"}]}, "preferred": true}]}, {"resourceType": "Patient", "active": true, "name": [{"use": "official", "family": "Playwright", "given": ["<PERSON><PERSON>"]}], "gender": "male", "birthDate": "1961-01-01", "address": [{"use": "home", "type": "both", "line": ["369, ", "6th St"], "city": "Santa Monica", "state": "USA", "postalCode": "90406"}], "maritalStatus": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/v3-MaritalStatus", "code": "M", "display": "Married"}]}, "managingOrganization": {"reference": "Organization/7760ffcd-64d2-43df-9548-055b257dfaee", "display": "DEV - Sunrise Senior Living"}, "telecom": [{"system": "email", "value": "<EMAIL>", "use": "home"}, {"system": "phone", "value": "(+1)-************", "use": "mobile"}], "communication": [{"language": {"coding": [{"system": "http://hl7.org/fhir/ValueSet/languages", "code": "hi", "display": "English"}]}, "preferred": true}]}], "encounter": {"resourceType": "Encounter", "status": "in-progress", "class": {"system": "http://terminology.hl7.org/CodeSystem/v3-ActCode", "code": "IMP", "display": "inpatient encounter"}, "priority": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/v3-ActPriority", "code": "EL", "display": "elective"}]}, "subject": {"reference": "Patient/1111"}}, "conditions": [{"resourceType": "Condition", "subject": {"reference": "Patient/1111"}, "code": {"coding": [{"display": "Hypertension"}]}}, {"resourceType": "Condition", "subject": {"reference": "Patient/1111"}, "code": {"coding": [{"display": "Diabetes"}]}}], "observations": [{"resourceType": "Observation", "status": "final", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "363808001", "display": "Body weight"}], "text": "Body weight"}, "valueQuantity": {"value": 79, "unit": "kg", "system": "http://unitsofmeasure.org", "code": "kg"}, "subject": {"reference": "Patient/{{patientID}}", "display": "{{patientGivenName}}{{patientFamilyName}}"}}, {"resourceType": "Observation", "status": "final", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "363808006", "display": "Body height"}], "text": "Body height"}, "valueQuantity": {"value": 163, "unit": "cm", "system": "http://unitsofmeasure.org", "code": "cm"}, "subject": {"reference": "Patient/{{patientID}}", "display": "{{patientGivenName}}{{patientFamilyName}}"}}]}