import { INestApplication } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import * as request from 'supertest';
import { AppModule } from './../src/app.module';
import * as eventData from './fixtures/eventRequestData.json';

describe('EventController (e2e)', () => {
  let app: INestApplication;

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  it('POST /event', () => {
    return request(app.getHttpServer()).post('/event').send(eventData).expect('Content-Type', /json/).expect(200);
  }, 30000); // wait up to 30 seconds for the test to complete

  // ... other tests ...

  afterAll(async () => {
    await app.close();
  });
});
