import { Encounter, Patient, Re<PERSON><PERSON>erson, Condition, Observation } from '@medplum/fhirtypes';
import { CommunicationIdentity } from './communication';
import { PccPatient } from './pcc';
import { RelicCommunicationLanguage } from './relicBase';
('use strict');

/**
 * Relic Patient Type for managing Patients
 * This is just a temporary type created quickly from https://app.quicktype.io/
 * TODO: Replace this file with proper types & conversions
 */
export type RelicPatient = {
  id: string;
  resourceType: string;
  organizationId: string;
  active?: boolean;
  name?: string;
  email?: string;
  emailVerified?: boolean;
  mobilePhone?: string;
  homePhone?: string;
  gender?: string;
  height?: string;
  weight?: string;
  birthDate?: string;
  maritalStatus?: string | null;
  link?: RelicLink[];
  communicationLanguage?: RelicCommunicationLanguage[];
  managingOrganization?: ManagingOrganization;
  Condition?: RelicCondition[];
  obfuscationMap?: ObfuscationMap;
  summary?: string | '';
  goal?: string | '';
  expectation?: string | '';
  communicationIdentities?: CommunicationIdentity[];
  primaryLanguage?: RelicCommunicationLanguage;
  bedLocation?: string; //PCC patient bed location
  patientStatus?: 'New' | 'Current' | 'Discharged'; //PCC patient status
  b2cid?: string;
  streetAddress?: string;
  city?: string;
  state?: string;
  pccFacilityName?: string;
  pccPractitionerName?: string;
  pccPatient?: PccPatient;
};

export type Gender = 'male' | 'female' | 'other' | 'unknown';

export type RelicCondition = {
  id?: string;
  resourceType?: string;
  clinicalStatus?: string;
  verificationStatus?: string;
  code?: {
    text: string;
  };
  onsetDateTime?: string;
  abatementDateTime?: string;
  note?: string;
};

export type Code = {
  text: string;
};

export type PatientEverything = RelicPatient & {
  Patient: Patient;
  RelatedPersons?: RelatedPerson[];
  Encounter?: Encounter;
  Conditions?: Condition[];
  Observations?: Observation[];
  PrimaryLanguage?: RelicCommunicationLanguage;
};

export type RelicLink = {
  id?: string;
  resourceType?: string;
  relationship?: {
    system: string;
    code: string;
    display: string;
  };
  use?: string;
  name?: string;
  email?: string;
  mobilePhone?: string;
  homePhone?: string;
  gender?: string;
  birthDate?: string;
};

export type ManagingOrganization = {
  reference?: string;
  display?: string;
};

export type FHIRCommunication = {
  language?: {
    coding?: Array<{
      system?: string;
      code?: string;
      display?: string;
    }>;
  };
  preferred?: boolean;
};

export type ObfuscationMap = {
  name: Obfuscation;
  name_1: Obfuscation;
  name_2: Obfuscation;
  name_3: Obfuscation;
  email: Obfuscation;
};

export type Obfuscation = {
  value: null | string;
  obfuscatedValue: string;
};

export type ContactPointUse = 'home' | 'work' | 'temp' | 'old' | 'mobile';

export type RelicAllergyIntolerance = {
  id: string;
  resourceType: string;
  clinicalStatus: string;
  verificationStatus: string;
  code: Code;
  type?: string;
  category: string;
  onsetDateTime: Date;
  abatementDateTime?: Date;
  note?: string;
  patientID: string;
};

export interface IAllergyIntoleranceFilterVariables {
  q?: string;
  PatientId?: string;
}