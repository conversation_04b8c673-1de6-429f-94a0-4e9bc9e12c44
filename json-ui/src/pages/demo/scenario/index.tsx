import { useLocation } from 'react-router-dom';
import { useOne, useGetIdentity } from '@refinedev/core';
import React, { useMemo, useState, useEffect } from 'react';
import {
  IUser,
  RelicUserProvider,
  CommunicationPanel,
  availableLanguages,
  RelicThreadProvider,
  RelicUserProviderOptions,
  RelicThreadProviderOptions,
  RelicCommunicationLanguage,
} from 'relic-ui';

import { Stack, Drawer } from '@mui/material';
import { Box, useTheme, Typography } from '@mui/material';

import { useRouter } from 'src/routes/hooks';

import { getNodeServicesApi } from 'src/utils/app-config';
import { getProviderServicesApi } from 'src/providers/utils/auth-utils';

import ThreadViewToolbar from 'src/components/_threads/show/threads-toolbar';

const DRAWER_WIDTH = {
  lg: 380,
  md: 320,
  sm: 280,
  xs: '100%',
};

export const DemoChatScenario = () => {
  const router = useRouter();

  const location = useLocation();

  const queryParams = new URLSearchParams(location.search);
  const threadId = queryParams.get('threadId');

  const { data: threadData, isLoading: threadDetailsLoading } = useOne({
    resource: `communication/chat/threads`,
    id: threadId!,
    queryOptions: {
      enabled: !!threadId,
    },
  });

  const threadsDetail = threadData?.data;

  const resident = threadsDetail?.participants?.find(
    (p: any) => p.resourceType === 'Patient',
  );

  const language = useMemo(() => {
    let patientLanguage =
      threadsDetail?.threadSubject?.patientLanguage || 'en-US';
    console.log('patientLanguage', patientLanguage);
    if (patientLanguage === 'en') {
      patientLanguage = 'en-US';
    }
    return availableLanguages.find(
      (item: RelicCommunicationLanguage) => item.code === patientLanguage,
    );
  }, [threadsDetail?.threadSubject?.patientLanguage]);

  const [showThreadDetails, setShowThreadDetails] = React.useState(
    threadId ? true : false,
  );

  const { data: currentPatient } = useGetIdentity<IUser>();

  const handleBack = () => {
    router.back();
  };

  const { data: authUser } = useGetIdentity<IUser>();
  const theme = useTheme();
  const [accessToken, setAccessToken] = useState<string>('');
  const [idToken, setIdToken] = useState<string>('');

  // Update access token when authUser changes
  useEffect(() => {
    if (authUser?.accessToken) {
      setAccessToken(authUser.accessToken);
      setIdToken(authUser?.idToken as string);
    }
  }, [authUser]);

  // RelicUserProviderOptions memoized
  const relicUserProviderOptions: RelicUserProviderOptions = useMemo(
    () => ({
      accessToken,
      idToken,
      serviceUri: getProviderServicesApi(getNodeServicesApi()),
    }),
    [accessToken, idToken],
  );

  // RelicThreadProviderOptions memoized
  const relicThreadProviderOptions: RelicThreadProviderOptions = useMemo(
    () => ({
      displayInEnglish: true,
    }),
    [],
  );

  // Custom styling memoized
  const customMessageThreadStyles = useMemo(
    () => ({
      chatMessageContainer: {
        backgroundColor: theme.palette.background.neutral,
        color: theme.palette.text.primary,
        fontFamily: theme.typography.fontFamily,
        fontSize: `${theme.typography.body2.fontSize}px`,
      },
      myChatMessageContainer: {
        backgroundColor: theme.palette.primary.lighter,
        color: theme.palette.text.primary,
        fontFamily: theme.typography.fontFamily,
        fontSize: `${theme.typography.body2.fontSize}px`,
      },
      chatContainer: {
        maxWidth: '100%',
      },
      citationStyles: {
        tooltip: {
          backgroundColor: '#F7F7F7',
          textColor: 'rgba(0, 0, 0, 0.87)',
          borderColor: '#dadde9',
          hoverBackgroundColor: '#e6e6e6',
          titleColor: '#000000',
          linkColor: 'green',
        },
        link: {
          hoverBackgroundColor: '#c8d4fa',
          textColor: '#202020',
          titleColor: '#000000',
          linkColor: '#000000',
        },
        reference: {
          textColor: '#202020',
          backgroundColor: '#b6b6b6',
          titleColor: '#000000',
        },
      },
    }),
    [theme],
  );

  const customSendBoxStyles = useMemo(
    () => ({
      textField: {
        fontFamily: theme.typography.fontFamily,
        fontSize: `${theme.typography.body2.fontSize}px`,
      },
    }),
    [theme],
  );

  const toolbarProps = useMemo(
    () => ({
      showCallButtons: false,
      showResponseManagement: true,
    }),
    [],
  );

  return (
    <>
      {threadId && (
        <ThreadViewToolbar
          onBack={handleBack}
          onOpenAdd={handleBack}
          title={
            !threadDetailsLoading && (
              <span>
                {resident?.displayName} - {language.display}
              </span>
            )
          }
          showThreadDetails={showThreadDetails}
          onToggleSidePane={() => setShowThreadDetails(!showThreadDetails)}
        />
      )}

      <Stack
        sx={{
          ...(showThreadDetails && {
            width: {
              sm: `calc(100% - ${DRAWER_WIDTH.sm}px)`,
              md: `calc(100% - ${DRAWER_WIDTH.md}px)`,
              lg: `calc(100% - ${DRAWER_WIDTH.lg}px)`,
            },
          }),
        }}
      >
        {currentPatient && (
          <RelicUserProvider {...relicUserProviderOptions}>
            <RelicThreadProvider {...relicThreadProviderOptions}>
              <Stack direction="row" sx={{ flexGrow: 1 }}>
                {accessToken ? (
                  <CommunicationPanel
                    pxAboveCommunicationPanel={156}
                    toolbarProps={toolbarProps}
                    messageThreadStyles={customMessageThreadStyles}
                    sendBoxStyles={customSendBoxStyles}
                  />
                ) : (
                  <Stack sx={{ width: 1, height: 1, overflow: 'hidden' }} />
                )}
              </Stack>
            </RelicThreadProvider>
          </RelicUserProvider>
        )}
      </Stack>
      {threadId && (
        <Drawer
          anchor="right"
          variant="persistent"
          open={showThreadDetails}
          PaperProps={{
            sx: {
              zIndex: 999,
              top: 8 * 18.5,
              boxSizing: 'border-box',
              width: {
                xs: DRAWER_WIDTH.xs,
                sm: DRAWER_WIDTH.sm,
                md: DRAWER_WIDTH.md,
                lg: DRAWER_WIDTH.lg,
              },
            },
          }}
          sx={{
            flexShrink: 0,
            width: {
              xs: DRAWER_WIDTH.xs,
              sm: DRAWER_WIDTH.sm,
              md: DRAWER_WIDTH.md,
              lg: DRAWER_WIDTH.lg,
            },
          }}
        >
          <Box
            sx={{
              p: 1,
              zIndex: 9,
              background: 'background.paper',
            }}
          >
            <Typography variant="h5" gutterBottom>
              AI Compliance Asst.
            </Typography>
            <Typography variant="body1" gutterBottom>
              AI Compliance Asst. is a tool that helps you with regulatory
              compliance queries. It is an AI Assistant that can answer
              questions about compliance requirements for your facility.
            </Typography>
            <Typography variant="body2" gutterBottom>
              Some questions you can ask: 1. What are the requirements for HIPAA
              compliance? 2. What are the requirements for GDPR compliance?
            </Typography>
          </Box>
        </Drawer>
      )}
    </>
  );
};
