import * as React from 'react';
import { useTranslate } from '@refinedev/core';
import { DeleteButtonProps } from '@refinedev/mui';

import Popover from '@mui/material/Popover';
import EditOutlined from '@mui/icons-material/EditOutlined';
import Button, { buttonClasses } from '@mui/material/Button';

import { ReturnType as PopoverProps } from 'src/hooks/use-popover';

import { ConfirmDeleteDialog } from '../../refine-customs/delete-btn';

// ----------------------------------------------------------------------

type DetailsPopoverProps = {
  popover: PopoverProps;
  onOpenEdit: VoidFunction;
  deleteButtonProps?: DeleteButtonProps;
};

export default function AgentDetailsPopover({
  popover,
  onOpenEdit,
  deleteButtonProps,
}: DetailsPopoverProps) {
  const translate = useTranslate();

  return (
    <>
      <Popover
        open={!!popover.open}
        anchorEl={popover.open}
        onClose={popover.onClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        slotProps={{
          paper: {
            sx: {
              mt: 0.5,
              minWidth: 160,
              [`& .${buttonClasses.root}`]: {
                px: 2,
                width: 1,
                display: 'flex',
                justifyContent: 'flex-start',
                fontWeight: 'fontWeightMedium',
              },
            },
          },
        }}
      >
        <Button startIcon={<EditOutlined />} onClick={onOpenEdit}>
          Edit
        </Button>

        <ConfirmDeleteDialog
          deleteButtonProps={{
            ...deleteButtonProps,
            hidden: true,
            confirmTitle: translate('agents.titles.delete'),
            confirmContent: translate('content.confirm.delete'),
          }}
        />
      </Popover>
    </>
  );
}
