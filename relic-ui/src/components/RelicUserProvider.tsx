import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import type { RelicAgent, IUserIdentity, CommunicationIdentity } from '../types';

import Box from '@mui/material/Box';
import Alert from '@mui/material/Alert';
import Button from '@mui/material/Button';
import CircularProgress from '@mui/material/CircularProgress';

interface UserContextType {
  accessToken: string;
  serviceUri: string;
  agentMessengerUri: string | undefined;
  myIdentity: IUserIdentity | undefined;
  aiAssistant: RelicAgent | undefined;
  myDefaultAgent: RelicAgent | undefined;
  refreshMyIdentity: () => void;
  idToken?: string;
  providerError?: string | null;
  updateProviderError: (providerError: string) => void;
  updateProviderLoading: (providerLoading: boolean) => void;
}

const UserContext = createContext<UserContextType>({
  accessToken: '',
  serviceUri: '',
  agentMessengerUri: '',
  myIdentity: undefined,
  aiAssistant: undefined,
  myDefaultAgent: undefined,
  refreshMyIdentity: () => {},
  idToken: '',
  providerError: null,
  updateProviderError: (providerError: string) => {
    console.warn('providerError not implemented in UserContext', providerError);
  },
  updateProviderLoading(providerLoading) {
    console.warn('providerLoading not implemented in UserContext', providerLoading);
  },
});

export interface RelicUserProviderOptions {
  accessToken: string;
  serviceUri: string;
  idToken?: string;
}

export interface RelicUserProviderProps extends RelicUserProviderOptions {
  children: React.ReactNode;
}

const RelicUserProvider: React.FC<RelicUserProviderProps> = ({ children, accessToken, idToken, serviceUri }) => {
  const [myIdentity, setMyIdentity] = useState<IUserIdentity | undefined>(undefined);
  const [aiAssistant, setAiAssistant] = useState<RelicAgent | undefined>(undefined);
  const [myDefaultAgent, setMyDefaultAgent] = useState<RelicAgent | undefined>(undefined);
  const [agentMessengerUri, setAgentMessengerUri] = useState<string | undefined>(undefined);
  const [providerError, setProviderError] = useState<string | null>(null);
  const [providerLoading, setProviderLoading] = useState<boolean>(true);

  const updateProviderError = useCallback((providerError: string): void => {
    if (providerError) {
      setProviderError(providerError);
      setProviderLoading(false);
    }
  }, []);

  const updateProviderLoading = useCallback((providerLoading: boolean): void => {
    setProviderLoading(providerLoading);
  }, []);

  const callWhoAmIEndpoint = async (): Promise<IUserIdentity> => {
    const url = `${serviceUri}/whoami`;
    const headers = {
      accept: 'application/json',
      'x-access-token': accessToken,
      'x-id-token': idToken ?? '',
      'x-organization-id':
        myIdentity?.provider === 'pcc'
          ? myIdentity?.portalIdentity?.companyId
          : myIdentity?.portalIdentity?.organizationId ?? '',
      'x-hostname': window.location.hostname,
    };

    const response = await fetch(url, { headers });
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();
    return data;
  };

  const initializeMyIdentity = async () => {
    if (!accessToken) {
      updateProviderError('No access token found. Please login and retry.');
      return;
    }

    try {
      const userIdentity: IUserIdentity = sessionStorage.getItem('userIdentity')
        ? JSON.parse(sessionStorage.getItem('userIdentity') as string)
        : JSON.parse(localStorage.getItem('userIdentity') as string);
      const whoAmI = userIdentity ? userIdentity : await callWhoAmIEndpoint();
      // To be fixed refresh ACS token (TBD: call communication identity endpoint only if the token is expired)
      setMyIdentity(whoAmI);
      sessionStorage.removeItem('callPanelState');
      sessionStorage.removeItem('defaultAgent');
      sessionStorage.removeItem('aiAssistant');
    } catch (error) {
      console.error('Error while initializing user identity and chat client:', error);
    }
  };

  const refreshMyIdentity = async () => {
    console.log('[RelicUserProvider] refreshMyIdentity called');
    await initializeMyIdentity();
  };

  const fetchAgentMessengerUri = async (): Promise<string> => {
    const url = `${serviceUri}/config/SERVER.AGENTMESSENGER_URL`;
    const headers = {
      accept: 'application/json',
      'x-access-token': accessToken,
      'x-id-token': idToken ?? '',
      'x-organization-id':
        myIdentity?.provider === 'pcc'
          ? myIdentity?.portalIdentity?.companyId
          : myIdentity?.portalIdentity?.organizationId ?? '',
      'x-hostname': window.location.hostname,
    };

    const response = await fetch(url, { method: 'GET', headers });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(`Could not fetch Agent Messenger URL. ${data.message}`);
    }
    return data.value as string;
  };

  const fetchAiAssistant = async (): Promise<RelicAgent> => {
    const url = `${serviceUri}/agents/aiassistant`;
    const headers = {
      'Content-type': 'application/json',
      accept: 'application/json',
      'x-access-token': accessToken,
      'x-id-token': idToken ?? '',
      'x-organization-id':
        myIdentity?.provider === 'pcc'
          ? myIdentity?.portalIdentity?.companyId
          : myIdentity?.portalIdentity?.organizationId ?? '',
      'x-hostname': window.location.hostname,
    };

    const response = await fetch(url, { method: 'GET', headers });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(`Could not fetch AI Assistant. ${data.message}`);
    }
    return data as RelicAgent;
  };

  const fetchMyDefaultAgent = async (): Promise<RelicAgent> => {
    const url = `${serviceUri}/agents/default`;
    const headers = {
      'Content-type': 'application/json',
      accept: 'application/json',
      'x-access-token': accessToken,
      'x-id-token': idToken ?? '',
      'x-organization-id':
        myIdentity?.provider === 'pcc'
          ? myIdentity?.portalIdentity?.companyId
          : myIdentity?.portalIdentity?.organizationId ?? '',
      'x-hostname': window.location.hostname,
    };

    const response = await fetch(url, { method: 'GET', headers });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(`Could not fetch AI Assistant. ${data.message}`);
    }
    return data as RelicAgent;
  };

  const initializeAgents = async () => {
    try {
      const aiAsst: RelicAgent = sessionStorage.getItem('aiAssistant')
        ? JSON.parse(sessionStorage.getItem('aiAssistant') as string)
        : await fetchAiAssistant();
      const myDefaultAgent: RelicAgent = sessionStorage.getItem('defaultAgent')  
        ? JSON.parse(sessionStorage.getItem('defaultAgent') as string)  
        : await fetchMyDefaultAgent();
      setAiAssistant(aiAsst);
      setMyDefaultAgent(myDefaultAgent);
      setAgentMessengerUri(await fetchAgentMessengerUri());
      if (!sessionStorage.getItem('aiAssistant')) sessionStorage.setItem('aiAssistant', JSON.stringify(aiAsst));
      if (!sessionStorage.getItem('defaultAgent')) sessionStorage.setItem('defaultAgent', JSON.stringify(myDefaultAgent));      
    } catch (error) {
      updateProviderError(`Failed to initialize Assistants. ${error}`);
    }
  }

  useEffect(() => {
    if (accessToken) {
      initializeMyIdentity();
    }
  }, [accessToken]);

  useEffect(() => {
    if (myIdentity) {
      initializeAgents();
    }
  }, [myIdentity]);

  return (
    <UserContext.Provider
      value={{
        accessToken,
        serviceUri,
        agentMessengerUri,
        myIdentity,
        aiAssistant,
        myDefaultAgent,
        refreshMyIdentity,
        idToken,
        providerError,
        updateProviderError,
        updateProviderLoading,
      }}
    >
      {providerLoading && (
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            minHeight: '50vh',
          }}
        >
          <CircularProgress color="inherit" />
        </Box>
      )}
      {providerError ? (
        <Box sx={{ width: '100%' }}>
          <Alert
            severity="error"
            action={
              <Button color="inherit" size="small" onClick={() => window.location.reload()}>
                Refresh
              </Button>
            }
          >
            {providerError}
          </Alert>
        </Box>
      ) : (
        children
      )}
    </UserContext.Provider>
  );
};

const useUserContext = () => useContext(UserContext);

export { RelicUserProvider, useUserContext };
