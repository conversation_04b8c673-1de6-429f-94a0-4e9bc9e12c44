import {
  useShow,
  useTranslate,
  IResourceComponentsProps,
} from "@refinedev/core";
import {
  Show,
  MarkdownField,
  TextFieldComponent as TextField,
} from "@refinedev/mui";

import { Stack, Typography } from "@mui/material";

export const DocumentShow: React.FC<IResourceComponentsProps> = () => {
  const translate = useTranslate();
  const { queryResult } = useShow();
  const { data, isLoading } = queryResult;

  const record = data?.data;

  return (
      <Show isLoading={isLoading}>
          <Stack gap={1}>
              <Typography variant="body1" fontWeight="bold">
                  {translate("files.fields.id")}
              </Typography>
              <TextField value={record?.id} />
              <Typography variant="body1" fontWeight="bold">
                  {translate("files.fields.object")}
              </Typography>
              <TextField value={record?.object} />
              <Typography variant="body1" fontWeight="bold">
                  {translate("files.fields.fields")}
              </Typography>
              <TextField value={record?.fields?.name} />
              <Typography variant="body1" fontWeight="bold">
                  {translate("files.fields.url")}
              </Typography>
              <MarkdownField value={record?.url} />
          </Stack>
      </Show>
  );
};
