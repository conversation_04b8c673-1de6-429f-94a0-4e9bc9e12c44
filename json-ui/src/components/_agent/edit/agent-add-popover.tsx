import { useTranslate } from '@refinedev/core';

import Popover from '@mui/material/Popover';
import MenuItem from '@mui/material/MenuItem';

import { ReturnType as PopoverProps } from 'src/hooks/use-popover';

import Iconify from 'src/components/iconify';

type AgentAddPopoverProps = {
  popover: PopoverProps;
  onSamplesAddClick: () => void;
  onTemplatesAddClick: () => void;
};

export default function AgentAddPopover({
  popover,
  onSamplesAddClick,
  onTemplatesAddClick,
}: AgentAddPopoverProps) {
  const translate = useTranslate();

  return (
    <>
      <Popover
        open={!!popover.open}
        anchorEl={popover.open}
        onClose={popover.onClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        slotProps={{
          paper: {
            sx: {
              mt: 0.5,
              minWidth: 160,
            },
          },
        }}
      >
        <MenuItem onClick={onSamplesAddClick}>
          <Iconify width={18} icon={'eva:plus-fill'} sx={{ mr: 1 }} />
          {translate('agents.tabs.samples')}
        </MenuItem>

        <MenuItem onClick={onTemplatesAddClick}>
          <Iconify width={18} icon={'eva:plus-fill'} sx={{ mr: 1 }} />
          {translate('agents.tabs.templates')}
        </MenuItem>
      </Popover>
    </>
  );
}
