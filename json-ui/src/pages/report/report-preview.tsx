import React, { useState, useEffect } from 'react';
import { useOne, useCustom, useGetIdentity } from '@refinedev/core';
import { IUser, RelicPatient, IUserIdentity, RelicDocument } from 'relic-ui';

import { Typography } from '@mui/material';

import { CarePlanReport } from './pcc/care-plan';
import carePlanJson from './pcc/care-plan-data-2.json';

export const ReportPreview: React.FC = () => {
  const [reportUrl, setReportUrl] = useState<string | undefined>(undefined);
  const [reportError, setReportError] = useState<string | undefined>(undefined);
  const { data: currentUser } = useGetIdentity<IUser>();
  const userIdentity: IUserIdentity =
    currentUser?.userIdentity as IUserIdentity;

  //Data Population
  const patientId = '227694';
  const documentId = '0e208587-2b44-436a-9662-ea22507b2145';

  const { data: patientData, isLoading: patientLoading } = useOne({
    resource: 'patients',
    id: patientId,
  });

  const { data: documentData, isLoading: documentLoading } = useOne({
    resource: 'documents',
    id: documentId,
  });

  const { data: reportLabelsData, isLoading: isReportLabelsLoading } =
    useCustom({
      url: `/locales/reports.en-us.json`,
      method: 'get',
    });
  //Data Population end

  const relicPatient =
    (patientData?.data as RelicPatient) ??
    (carePlanJson.relicPatient as RelicPatient);
  const relicDocument =
    (documentData?.data as RelicDocument) ??
    (carePlanJson.relicDocument as unknown as RelicDocument);

  useEffect(() => {
    const fetchUrl = async () => {
      const relicDocumentWithPdf = await CarePlanReport(
        relicPatient,
        relicDocument,
        reportLabelsData?.data?.labels?.careplan,
      );
      setReportUrl(relicDocumentWithPdf.url);
    };
    if (!patientLoading && !documentLoading && !isReportLabelsLoading) {
      fetchUrl().catch(error => {
        setReportError(`Error generating report - ${error.message}`);
      });
    }
  }, [
    documentLoading,
    isReportLabelsLoading,
    patientLoading,
    relicDocument,
    relicPatient,
    reportLabelsData?.data?.labels?.careplan,
    setReportUrl,
    userIdentity.portalIdentity.organizationId,
  ]);

  const iframeCss = {
    width: '100%',
    height: `calc(${window.innerHeight}px - 72px)`,
  };

  return (
    <>
      {reportUrl && (
        <div>
          <iframe
            key={reportUrl}
            title="Report Preview"
            src={reportUrl}
            style={iframeCss}
          />
        </div>
      )}
      {reportError && <Typography>{reportError}</Typography>}
      {!reportUrl && !reportError && <Typography>Loading...</Typography>}
    </>
  );
};
