import fastifyAutoload from '@fastify/autoload';
import { FastifyInstance, FastifyPluginAsync, FastifyPluginOptions, FastifyReply, FastifyRequest } from 'fastify';
import path from 'path';

//TODO: To be fixed. Placeholder for entraProvider
const entraProvider: FastifyPluginAsync = async (fastify: FastifyInstance, opts: FastifyPluginOptions) => {
  //TODO: To be fixed. Implement JWT.
  // const entraJwtConfig: FastifyJWTOptions = {}
  // fastify.register(fastifyJwt, entraJwtConfig);

  async function verifyJwt(req: FastifyRequest, rep: FastifyReply) {
    return true; // Placeholder for jwt verification logic. TBD: To be fixed later.
  }

  async function verifyAccess(req: FastifyRequest, rep: FastifyReply) {
    return true; // Placeholder for access verification logic. TBD: To be fixed later.
  }

  fastify.addHook('onRequest', fastify.auth([verifyJwt, verifyAccess], { relation: 'and' }));

  fastify.log.info('Entra Routes loading...');

  await fastify.register(fastifyAutoload, {
    dir: path.join(__dirname, './entra'),
    options: {
      ...opts,
      prefix: '/api/entra',
    },
    autoHooks: true,
    cascadeHooks: true,
    routeParams: true,
  });
  fastify.log.info('Entra Routes loaded successfully.');
};

export default entraProvider;
