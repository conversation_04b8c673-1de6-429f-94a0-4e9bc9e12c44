import { FastifyInstance, FastifyReply, FastifyRequest, FastifyPluginOptions } from 'fastify'
import config from 'config'

//Types that are not used by other plugins or services
//Keeping them as internal types here
type ServiceHealthStatus = 'RUNNING' | 'NOT_RUNNING'

interface ServiceStatus {
    hostname: string
    status: ServiceHealthStatus
    message: string
}

interface ServiceStatusMap {
    [serviceName: string]: ServiceStatus
}

export default async function (instance: FastifyInstance, opts: FastifyPluginOptions) {
    async function checkServiceHealth(serviceURL: URL): Promise<ServiceStatus> {
        const serviceHost = serviceURL.hostname
        try {
            const resp = await fetch(serviceURL)
            if (!resp.ok) {
                throw new Error(`${serviceHost} returned ${resp.status} ${resp.statusText}`)
            }
            return { hostname: serviceHost, status: 'RUNNING', message: 'service is running' }
        } catch (error: unknown) {
            return {
                hostname: serviceHost,
                status: 'NOT_RUNNING',
                message: (error as Error)?.message ?? `connection failed to ${serviceHost}`
            }
        }
    }

    instance.get(
        '/',
        {
            schema: {
                tags: ['Guest Service'],
                summary: 'Unsecured - Health Check Route',
                description: 'Check health status of node-service and other services. Use this to wake up node-services and agent-messenger. This route is not secured and does not require authentication token.'
            }
        },
        async function (req: FastifyRequest, rep: FastifyReply) {
            const monitoredServices: ServiceStatusMap = {
                'node-services': {
                    hostname: req.hostname,
                    status: 'RUNNING',
                    message: 'service is running'
                }
            }

            const agentMessenger = new URL(config.get('SERVER.AGENTMESSENGER_URL'))
            const agentMessengerStatus = await checkServiceHealth(agentMessenger)
            monitoredServices['agent-messenger'] = agentMessengerStatus

            return monitoredServices
        }
    )
}
