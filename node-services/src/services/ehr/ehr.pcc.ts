import { RelicCondition, RelicPatient } from 'relic-ui';
import { IdentityServiceInterface } from '../identity/identity.interface';
import { requestContext } from '@fastify/request-context';
import { IUser, IUserIdentity } from 'relic-ui';
import { capitalize } from 'lodash';
import { RelicOrganization } from 'relic-ui';
import type {
  PccUserInfo,
  PccFacility,
  PccCondition,
  PccPatientCarePlan,
  PccCarePlanFocus,
  PccCarePlanFocusGoals,
  PccCarePlanFocusInterventions,
  PccPractitioner,
  PccCarePlanFocusResponse,
  IPortalIdentity,
} from 'relic-ui';
import { RelicPractitioner } from 'relic-ui';
import createClient, { Client, Middleware } from 'openapi-fetch';
import { paths } from '@/types/pccOpenapi';
import config from 'config';
import path from 'path';
import { Agent, fetch } from 'undici';
import { readFileSync } from 'fs';
import { FastifyInstance, FastifyPluginOptions } from 'fastify';
import { Collection } from 'mongodb';
import { SignPayloadType } from '@fastify/jwt';
import { EhrServiceInterface } from './ehr.interface';

const comparator = new Intl.Collator(undefined, {
  usage: 'sort',
  caseFirst: 'upper',
  sensitivity: 'base',
  numeric: true,
});

const focusStatusMapping: Record<string, string> = {
  active: 'Active',
  resolved: 'Resolved',
  cancelled: 'Cancelled',
};

const statusMapping: Record<string, string> = {
  active: 'Active',
  closed: 'Closed',
};

const PCC_THREE_LEGGED_RESOURCES: string[] = ['patients', 'care-plans', 'userinfo', 'conditions', 'focuses'];

const pccClient = createClient<paths>({
  baseUrl: path.join(config.get('PCC.API_URL_CERT_BASED'), 'api'),
  headers: {
    'Content-Type': 'application/json',
    Accept: 'application/json',
  },
  requestInitExt: {
    dispatcher: new Agent({
      connect: {
        ca: readFileSync(path.resolve(__dirname, '..', config.get('SERVER.SSL.CA')), `utf-8`),
        cert: readFileSync(path.resolve(__dirname, '..', config.get('SERVER.SSL.CERT')), `utf-8`),
        key: readFileSync(path.resolve(__dirname, '..', config.get('SERVER.SSL.KEY')), `utf-8`),
        rejectUnauthorized: false,
      },
    }),
  },
});

type AuthTokenResponse = { accessToken: string; expiresIn: number; expiresAt: number };

let twoLeggedToken: AuthTokenResponse | null = null;
async function getTwoLeggedToken(): Promise<AuthTokenResponse> {
  if (twoLeggedToken && twoLeggedToken?.expiresAt - 5 * 60 * 1000 >= Date.now()) {
    return twoLeggedToken;
  }
  const token = Buffer.from(config.get('PCC.CLIENT_ID') + ':' + config.get('PCC.CLIENT_SECRET')).toString('base64');
  const params = new URLSearchParams();
  params.append('grant_type', 'client_credentials');
  const response = await fetch(path.join(config.get('PCC.API_URL_CERT_BASED'), 'auth/token'), {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
      Authorization: `Basic ${token}`,
    },
    body: params.toString(),
    dispatcher: new Agent({
      connect: {
        ca: readFileSync(path.resolve(__dirname, '..', config.get('SERVER.SSL.CA')), `utf-8`),
        cert: readFileSync(path.resolve(__dirname, '..', config.get('SERVER.SSL.CERT')), `utf-8`),
        key: readFileSync(path.resolve(__dirname, '..', config.get('SERVER.SSL.KEY')), `utf-8`),
        rejectUnauthorized: false,
      },
    }),
  });
  if (!response.ok) {
    throw this.fastify.httpErrors.getHttpError(response.status as any, response.statusText);
  }
  const data = (await response.json()) as { access_token: string; expires_in: number };
  twoLeggedToken = {
    accessToken: data.access_token,
    expiresIn: data.expires_in,
    expiresAt: Date.now() + data.expires_in * 1000,
  };
  return twoLeggedToken;
}

const pccAuthMiddleware: Middleware = {
  async onRequest({ request }) {
    if (PCC_THREE_LEGGED_RESOURCES.some((resource) => request.url.includes(resource))) {
      const token = requestContext.get('pccAccessToken') || requestContext.get('accessToken');
      if (!token) {
        throw this.fastify.httpErrors.unauthorized(
          `Your account does not have access to this resource - ${request.url}`,
        );
      }
      request.headers.set('Authorization', `Bearer ${token}`);
    } else {
      const token = await getTwoLeggedToken();
      request.headers.set('Authorization', `Bearer ${token.accessToken}`);
    }
    return request;
  },
};

const onErrorResponseMiddleware: Middleware = {
  async onResponse({ response }) {
    if (response.status >= 400) {
      const body = (await response.json()) as {
        errors: Array<{ id: string; status: string; code: string; title: string; detail: string }>;
      };
      throw this.fastify.httpErrors.getHttpError(
        response.status as any,
        body.errors?.[0]?.detail ?? response.statusText,
      );
    }
    return response;
  },
};
pccClient.use(pccAuthMiddleware);
pccClient.use(onErrorResponseMiddleware);

export class PccService implements EhrServiceInterface, IdentityServiceInterface {
  private maxPageSize: number = 200;
  private pccClient: Client<paths>;
  private fastify: FastifyInstance;
  private practitionersCollection: Collection<RelicPractitioner>;
  private pccFacilities: Collection<PccFacility>;

  constructor(fastify: FastifyInstance, options: FastifyPluginOptions) {
    this.fastify = fastify;
    this.pccClient = pccClient;
    this.practitionersCollection = fastify.mongo.reliccare.db.collection<RelicPractitioner>('practitioners');
    this.pccFacilities = fastify.mongo.reliccare.db.collection<PccFacility>('pccFacilities');
  }

  private convertPatientSearchQuery(query: any): any {
    let { _start, _end, _search, patientStatus } = query;
    const pageSize = Math.min(this.maxPageSize, _end - _start);
    const page = Math.floor(_end / pageSize);
    patientStatus = capitalize(patientStatus);
    const filter = {
      page: page,
      pageSize: pageSize,
      patientName: _search ? _search : '',
      patientStatus: patientStatus,
    };

    if (patientStatus === 'Discharged') {
      const dischargeDateTo = new Date();
      const dischargeDateFrom = new Date(dischargeDateTo.toISOString());
      dischargeDateFrom.setDate(dischargeDateFrom.getDate() - 90);
      dischargeDateFrom.setUTCHours(0, 0, 0, 0);
      filter['dischargeDateTo'] = dischargeDateTo.toISOString();
      filter['dischargeDateFrom'] = dischargeDateFrom.toISOString();
    }

    if (!_search) {
      delete filter.patientName;
    }
    return filter;
  }

  private convertConditionSearchQuery(query: any): any {
    let { _start, _end } = query;
    const pageSize = Math.min(this.maxPageSize, _end - _start);
    const page = Math.floor(_end / pageSize);
    const filter = {
      page: page,
      pageSize: pageSize,
    };
    return filter;
  }

  private convertToRelicCondition(input: PccCondition): RelicCondition {
    const c: RelicCondition = {
      id: `${input.conditionId}`,
      resourceType: 'Condition',
      clinicalStatus: capitalize(input.clinicalStatus),
      verificationStatus: '',
      code: {
        text: `${input.icd10Description ?? input.conditionCode?.codings?.[0]?.display!} (${input.icd10})`,
      },
      onsetDateTime: input.onsetDate,
      abatementDateTime: '',
      note: input.comments ?? '',
    };
    return c;
  }

  private convertToRelicConditionArray(input: PccCondition[]): RelicCondition[] {
    const conditions: RelicCondition[] = new Array(input.length);
    for (let i = 0; i < input.length; i++) {
      conditions[i] = this.convertToRelicCondition(input[i]);
    }
    return conditions;
  }

  private convertCarePlanSearchQuery(query: any): any {
    let { _start, _end, _focusStatus, _status } = query;
    const pageSize = Math.min(this.maxPageSize, _end - _start);
    const page = Math.floor(_end / pageSize);
    const mappedFocusStatus = _focusStatus
      ? _focusStatus.split(',').map((status: string) => focusStatusMapping[status.toLowerCase().trim()] || status)
      : 'Active, Resolved, Cancelled';

    const mappedStatus = _status
      ? _status.split(',').map((status: string) => statusMapping[status.toLowerCase().trim()] || status)
      : 'Active, Closed';

    const filter = {
      page: page,
      pageSize: pageSize,
      focusStatus: mappedFocusStatus,
      carePlanStatus: mappedStatus,
    };
    return filter;
  }

  private convertFocusesSearchQuery(query: any): any {
    let { _goalStatus, _interventionStatus } = query;

    const mappedGoalStatus = _goalStatus
      ? _goalStatus.split(',').map((status: string) => focusStatusMapping[status.toLowerCase().trim()] || status)
      : 'Active';

    const mappedInterventionStatus = _interventionStatus
      ? _interventionStatus
          .split(',')
          .map((status: string) => focusStatusMapping[status.toLowerCase().trim()] || status)
      : 'Active';

    const filter = {
      goalStatus: mappedGoalStatus,
      interventionStatus: mappedInterventionStatus,
    };
    // console.log('filter', filter);
    return filter;
  }

  private convertFacilitySearchQuery(query: any): any {
    let { _start, _end } = query;
    const pageSize = Math.min(this.maxPageSize, _end - _start);
    const page = Math.floor(_end / pageSize);
    const filter = {
      page: page,
      pageSize: pageSize,
      includeInactive: false,
      includeHeadOffice: true,
    };
    return filter;
  }

  /**
   * Retrieves a PointClickCare facility by its ID.
   * @param {string} id - The ID of the facility.
   * @returns {Promise<PccFacility>} The facility details.
   */
  public async getFacilityById(id: string): Promise<PccFacility> {
    if (!id) {
      throw this.fastify.httpErrors.badRequest('id is required for PointClickCare facility lookup.');
    }
    let orgUuid: string = '';
    let facId: number = 0;
    const lastHyphenIndex = id.lastIndexOf('-');
    orgUuid = id.substring(0, lastHyphenIndex);
    facId = Number(id.substring(lastHyphenIndex + 1));

    const found: PccFacility = await this.pccFacilities.findOne({ id: id });

    const pccFacility: PccFacility = await this.getPccFacilityById(orgUuid, facId);
    if (!pccFacility) {
      throw this.fastify.httpErrors.notFound(`Facility not found, id:${id}`);
    }
    if (found) {
      pccFacility.id = found.id;
      pccFacility.enabled = found.enabled;
      pccFacility.relicOrganizationId = found.relicOrganizationId;
    } else {
      pccFacility.id = pccFacility.orgUuid + '-' + pccFacility.facId;
      pccFacility.enabled = false;
      pccFacility.relicOrganizationId = '';
    }
    return pccFacility;
  }

  public async searchPatients(query: any): Promise<{ total: number; data: RelicPatient[] }> {
    const profile: IUserIdentity = requestContext.get('whoami');
    const organization: RelicOrganization = requestContext.get('organization');
    const filter = this.convertPatientSearchQuery(query);

    const { data, error, response } = await this.pccClient.GET('/public/preview1/orgs/{orgUuid}/patients', {
      params: {
        path: {
          orgUuid: organization?.pointClickCare?.id,
        },
        query: {
          facId: parseInt(profile.portalIdentity.locationId),
          page: filter.page,
          pageSize: filter.pageSize,
          patientName: filter.patientName,
          patientStatus: filter.patientStatus,
        },
      },
    });
    if (error) {
      throw this.fastify.httpErrors.getHttpError(response.status as any, error as string);
    }
    const result = this.fastify.toRelicPatients(data.data || []);
    return { total: result.length, data: result };
  }

  public async getPatientById(id: string, options?: any): Promise<RelicPatient> {
    const organization: RelicOrganization = requestContext.get('organization');
    const {
      data: pccPatient,
      error: getPatientError,
      response: getPatientResponse,
    } = await this.pccClient.GET('/public/preview1/orgs/{orgUuid}/patients/{patientId}', {
      params: {
        path: {
          orgUuid: organization?.pointClickCare?.id,
          patientId: parseInt(id),
        },
      },
    });
    if (getPatientError) {
      throw this.fastify.httpErrors.getHttpError(getPatientResponse.status as any, getPatientError as string);
    }
    const relicPatient: RelicPatient = await this.fastify.upsertRelicPatient(pccPatient);
    try {
      const pccFacility = await this.getPccFacilityById(organization?.pointClickCare?.id, pccPatient.facId);
      relicPatient.pccFacilityName = pccFacility.facilityName;
      const pccPractitioners = await this.getPractitionersAssignedToPatient(organization?.pointClickCare?.id, id);
      const pccPractitioner =
        pccPractitioners.find((practitioner: PccPractitioner) => practitioner.relation === 'Primary') ||
        pccPractitioners[0];
      relicPatient.pccPractitionerName = pccPractitioner
        ? `${pccPractitioner.firstName} ${pccPractitioner.lastName}`
        : '';
    } catch (error) {
      //Ignore errors which are probably caused by auth issues, as it is not a blocker for the patient to be created
      this.fastify.log.error('Error getting facility or practitioners for patient', id, error);
    }
    return relicPatient;
  }

  public async getPatientConditions(patientId: string, query: any): Promise<{ total: number; data: RelicCondition[] }> {
    const organization: RelicOrganization = requestContext.get('organization');
    const filter = this.convertConditionSearchQuery(query);
    const { data, error, response } = await this.pccClient.GET('/public/preview1/orgs/{orgUuid}/conditions', {
      params: {
        path: {
          orgUuid: organization?.pointClickCare?.id,
        },
        query: {
          patientId: parseInt(patientId),
          page: filter.page,
          pageSize: filter.pageSize,
        },
      },
    });
    if (error) {
      throw this.fastify.httpErrors.getHttpError(response.status as any, error as string);
    }
    const result = this.convertToRelicConditionArray(data.data || []);
    return { total: result.length, data: result };
  }

  public async getPatientCarePlans(
    patientId: string,
    query: any,
  ): Promise<{ total: number; data: PccPatientCarePlan[] }> {
    const organization: RelicOrganization = requestContext.get('organization');
    const filter = this.convertCarePlanSearchQuery(query);
    const { data, error, response } = await this.pccClient.GET('/public/preview1/orgs/{orgUuid}/care-plans', {
      params: {
        path: {
          orgUuid: organization?.pointClickCare?.id,
        },
        query: {
          patientId: parseInt(patientId),
          page: filter.page,
          pageSize: filter.pageSize,
          focusStatus: filter.focusStatus,
          carePlanStatus: filter.carePlanStatus,
        },
      },
    });
    if (error) {
      throw this.fastify.httpErrors.getHttpError(response.status as any, error as string);
    }
    const result = data.data as PccPatientCarePlan[];
    return { total: result.length, data: result };
  }

  public async getCarePlanFocuses(
    focusIds: number[],
    query: any,
  ): Promise<{ total: number; data: PccCarePlanFocus[] }> {
    const organization: RelicOrganization = requestContext.get('organization');
    const filter = this.convertFocusesSearchQuery(query);
    const promises = [];
    for (const focusId of focusIds) {
      promises.push(
        this.pccClient.GET('/public/preview1/orgs/{orgUuid}/focuses/{focusId}', {
          params: {
            path: {
              orgUuid: organization?.pointClickCare?.id,
              focusId: focusId,
            },
            query: {
              goalStatus: filter.goalStatus,
              interventionStatus: filter.interventionStatus,
            },
          },
        }),
      );
    }

    const unsortedFocusArray: PccCarePlanFocusResponse[] = [];
    const results = await Promise.allSettled(promises);
    for (const res of results) {
      if (res.status === 'fulfilled') {
        unsortedFocusArray.push(res.value.data);
      }
    }
    if (unsortedFocusArray.length === 0) {
      return { total: 0, data: [] };
    }
    const sortedArray: PccCarePlanFocusResponse[] = [];
    for (const focusData of unsortedFocusArray) {
      const goals: PccCarePlanFocusGoals[] = focusData.goals;
      const interventions: PccCarePlanFocusInterventions[] = focusData.interventions;
      if (goals) {
        const sortedGoals = goals.sort((a, b) => {
          const initiatedTimeDiff = new Date(a?.initiatedDate).getTime() - new Date(b?.initiatedDate).getTime();
          const revisionTimeDiff = new Date(a?.revisionDate).getTime() - new Date(b?.revisionDate).getTime();
          return initiatedTimeDiff === 0 ? revisionTimeDiff : initiatedTimeDiff;
        });
        focusData.goals = sortedGoals;
      }
      if (interventions) {
        const sortedInterventions = interventions.sort((a, b) => {
          const initiatedTimeDiff = new Date(b?.initiatedDate).getTime() - new Date(a?.initiatedDate).getTime();
          const revisionTimeDiff = new Date(b?.revisionDate).getTime() - new Date(a?.revisionDate).getTime();
          return initiatedTimeDiff === 0 ? revisionTimeDiff : initiatedTimeDiff;
        });
        focusData.interventions = sortedInterventions;
      }
      sortedArray.push(focusData);
    }
    sortedArray.sort((a, b) => new Date(b.initiatedDate).getTime() - new Date(a.initiatedDate).getTime());
    return { total: sortedArray.length, data: sortedArray };
  }

  public async searchFacilities(pccOrgUuids: string[], query: any): Promise<{ total: number; data: PccFacility[] }> {
    let { _start, _end, _sort, _order, _search } = query;
    let facilities: PccFacility[] = [];
    let total = 0;
    for (let i = 0; i < pccOrgUuids.length; i++) {
      const organizationId: string = pccOrgUuids[i];
      let hasMore = true,
        currentOrgTotal = 0,
        page = 1;
      while (hasMore) {
        const searchParams = this.convertFacilitySearchQuery({ _start, _end: page * this.maxPageSize });
        const { data, error, response } = await this.pccClient.GET('/public/preview1/orgs/{orgUuid}/facs', {
          params: {
            path: {
              orgUuid: organizationId,
            },
            query: {
              page: searchParams.page,
              pageSize: this.maxPageSize,
              includeInactive: searchParams.includeInactive,
              includeHeadOffice: searchParams.includeHeadOffice,
            },
          },
        });
        if (error) {
          throw this.fastify.httpErrors.getHttpError(response.status as any, error as string);
        }
        const pccFacilities = data.data as PccFacility[];
        if (pccFacilities?.length > 0) {
          facilities.push(...pccFacilities);
          currentOrgTotal += pccFacilities.length;
        }
        hasMore = data.paging?.hasMore ?? false;
        page++;
      }
      total += currentOrgTotal;
    }
    //perform filter
    _search = _search?.toLowerCase().trim();
    if (_search && facilities.length > 0) {
      facilities = facilities?.filter((f: PccFacility) => {
        return (
          f.facilityName?.toLowerCase().includes(_search) ||
          f.facilityCode?.toLowerCase().includes(_search) ||
          f.state?.toLowerCase().includes(_search) ||
          f.postalCode?.toLowerCase().includes(_search)
        );
      });
      total = facilities.length;
    }
    //perform sorting here
    if (_order && facilities.length > 0) {
      let compareFn =
        _order == 'asc'
          ? (a: any, b: any) => comparator.compare(a?.[_sort] ?? '', b?.[_sort] ?? '')
          : (a: any, b: any) => comparator.compare(b?.[_sort] ?? '', a?.[_sort] ?? '');
      facilities = facilities.sort(compareFn);
    }
    //skip + limit
    facilities = facilities.slice(_start, _end);

    return { total: total, data: facilities };
  }

  /**
   * Retrieves a facility by its ID from the PointClickCare (PCC) system.
   * Todo: This needs to be fixed as facility onboarding is un-necessarily complex.
   *
   * @param pccOrgUuid - The UUID of the PointClickCare organization.
   * @param id - The ID of the facility to retrieve.
   * @returns A promise that resolves to a `PccFacility` object representing the facility.
   * @throws Will throw an error if the `pccOrgUuid` or `id` is empty.
   */
  public async getPccFacilityById(pccOrgUuid: string, id: number): Promise<PccFacility> {
    const { data, error, response } = await this.pccClient.GET('/public/preview1/orgs/{orgUuid}/facs/{facId}', {
      params: {
        path: {
          orgUuid: pccOrgUuid,
          facId: id,
        },
      },
    });
    if (error) {
      throw this.fastify.httpErrors.getHttpError(response.status as any, error as string);
    }
    return data as PccFacility;
  }

  private async getPractitionersAssignedToPatient(pccOrgUuid: string, patientId: string): Promise<PccPractitioner[]> {
    const { data, error, response } = await this.pccClient.GET('/public/preview1/orgs/{orgUuid}/practitioners', {
      params: {
        path: {
          orgUuid: pccOrgUuid,
        },
        query: {
          patientId: parseInt(patientId),
        },
      },
    });
    if (error) {
      throw this.fastify.httpErrors.getHttpError(response.status as any, error as string);
    }
    return data.data as PccPractitioner[];
  }

  /**
   * Retrieves user information from PointClickCare (PCC) based on the provided organization UUID.
   * Todo: This should be a common interface for all EHRs
   *
   * @param {string} pccOrgUuid - The UUID of the PointClickCare organization.
   * @returns {Promise<PccUserInfo>} A promise that resolves to the user information.
   * @throws {HttpError} Throws a bad request error if the organization UUID is not provided.
   */
  private async getMyInfo(pccOrgUuid: string): Promise<PccUserInfo> {
    const { data, error, response } = await this.pccClient.GET('/public/preview1/orgs/{orgUuid}/userinfo', {
      params: {
        path: {
          orgUuid: pccOrgUuid,
        },
      },
    });
    if (error) {
      console.error('Error getting user info', response.url, error);
      throw this.fastify.httpErrors.getHttpError(response.status as any, error as string);
    }
    return data as PccUserInfo;
  }

  public async whoAmI(tokenPayload: SignPayloadType): Promise<IUser> {
    if (!tokenPayload) {
      throw this.fastify.httpErrors.unauthorized('Missing or invalid login. Please login again.');
    }
    const orgId = requestContext.get('organizationId');
    if (!orgId) {
      throw this.fastify.httpErrors.badRequest('Invalid request. Missing OrganizationId.');
    }

    // For PCC, we rely on incoming request context to get org details.
    const myRelicOrg: RelicOrganization = await this.fastify.orgService.getOrganization(orgId);

    const pccOrgUuid: string = myRelicOrg.pointClickCare?.id;
    const myPccProfile: PccUserInfo = await this.getMyInfo(pccOrgUuid);
    const myRelicProfile: RelicPractitioner = await this.practitionersCollection.findOne({ id: myPccProfile.sub });
    if (!myRelicProfile) {
      throw this.fastify.httpErrors.unauthorized('User not found');
    }
    if (!myRelicProfile.enabled) {
      throw this.fastify.httpErrors.unauthorized('Your account has been disabled. Please contact your administrator.');
    }
    const myFacility: PccFacility = await this.getPccFacilityById(pccOrgUuid, myPccProfile.defaultFacId);

    const myPortalIdentity: IPortalIdentity = {
      email: myFacility.emailAddress ?? myPccProfile.username,
      header: true,
      sider: true,
      name: myPccProfile.name,
      mobilePhone: '',
      companyName: myFacility.orgName,
      companyId: myPccProfile.orgUuid,
      clientId: `${myPccProfile.sub}`,
      organizationId: myRelicOrg.id,
      preferredLanguage: myRelicOrg?.fhirStore?.defaultLanguage || {
        code: 'en-US',
        display: 'English (United States)',
      },
      locationId: `${myPccProfile.defaultFacId}`,
    };
    const acsIdentity = await this.fastify.acs.refreshIdentity(
      myRelicProfile,
      myRelicProfile.communicationIdentities?.[0],
    );
    const myUserIdentity: IUserIdentity = {
      id: myRelicProfile.id,
      resourceType: myRelicProfile.resourceType,
      email: myPortalIdentity.email,
      portalIdentity: myPortalIdentity,
      role: myRelicProfile.role,
      communicationIdentities: [acsIdentity],
      provider: 'pcc',
    };

    const me: IUser = {
      id: myUserIdentity.id,
      name: myUserIdentity.portalIdentity.name || myUserIdentity.portalIdentity.email,
      avatar: '',
      organizationId: myUserIdentity.portalIdentity.organizationId,
      userIdentity: myUserIdentity,
      decodedJwtToken: tokenPayload as object,
    };
    return me;
  }

  public async getUser(practitioner: RelicPractitioner): Promise<RelicPractitioner> {
    throw new Error('Method not implemented.'); //TODO: To be fixed. Pending implementation
  }
}
