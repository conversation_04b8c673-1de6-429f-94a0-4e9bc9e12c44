[{"id": "3d928818-5420-4686-a0d6-04222ab23f87", "resourceType": "Organization", "type": "prov", "active": true, "name": "Sunrise Senior Living", "website": "localhost:3000", "location": [{"id": "c3d74003-4fd4-4645-b277-3671eeb3c04f", "resourceType": "Location", "status": "active", "name": "Sunrise Senior Living - Santa Monica", "phone": "+13105551234", "fax": "+17037441601"}], "copilotCompany": {"id": "50114cf4-f3c9-45a7-b9a4-57fdb9e0a9e4", "name": "Sunrise Senior Living", "iconImageUrl": "https://sunriseseniorliving.com/assets/logo.png", "fallbackColor": "blue", "leadInternalUserId": "ffcd8392-64d2-43df-9548-055b257dfaee"}, "fhirStore": {"clientApplication": {"id": "872fe0a8-fda8-49c7-b9ef-0bfadb8d509b", "resourceType": "ClientApplication", "compartment": "Project/51b61d21-e073-4cfa-adbc-e4cd247e1fec"}, "defaultLanguage": {"system": "http://hl7.org/fhir/ValueSet/languages", "code": "en-US", "display": "English - US"}, "provider": "medplum"}, "defaultChatEndpoint": {"url": "https://relic-acs.unitedstates.communication.azure.com/", "provider": "Azure Communication Service"}, "template": {"patientSummary": "My name is {{name}}. I am a {{age}} years old {{sex}} suffering from {{conditions}} admitted at {{facilityName}}. {{goal}}. I expect {{expectation}}.", "welcomeSms": "Hello {{name}}!\nWelcome to RelicCare!\nTo access your account, please visit {{verifyUrl}}.\nThank you for choosing RelicCare.", "welcomeEmail": "<!DOCTYPE html>\n<html>\n    <head>\n        <meta charset=\"utf-8\">\n        <meta http-equiv=\"X-UA-Compatible\" content=\"IE-edge\">\n        <title>Welcome to RelicCare</title>\n    </head>\n\n    <body>\n        <p>Dear {{name}},</p>\n        <p>Welcome to RelicCare! We are thrilled to have you join our community.</p>\n        <p>To access your account, please visit: <a href=\"{{verifyUrl}}\">Verify my Account</a>.</p>\n        <p>Thank you for choosing RelicCare. We look forward to providing you with a seamless and enjoyable experience.</p>\n        <p>Best regards,<br/>\n            RelicCare\n        </p>\n    </body>\n</html>", "agentSummary": "Your name is {{name}}."}, "endpoints": [{"service": ["chat", "voip"], "endpoint": "https://relic-acs.unitedstates.communication.azure.com/", "provider": "Azure Communication Service", "defaultAgents": [{"id": "9b088b6b-6248-4c73-a6d4-73ee8f17c6d0", "type": "Patient Agent"}, {"id": "2dc890ca-4cab-45e4-ae29-04e364d474c3", "type": "Staff Agent"}]}, {"service": ["patient-portal"], "endpoint": "https://sunrise.myrelic.care", "provider": "Relic Care"}]}]