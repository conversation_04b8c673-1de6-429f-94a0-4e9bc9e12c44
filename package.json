{"name": "relic-ai", "version": "1.0.0", "private": true, "homepage": "https://reliccare.com", "packageManager": "pnpm@10.10.0", "scripts": {"build": "turbo run build --only", "dev": "turbo run dev --parallel  --filter=json-ui --filter=relic-ui --filter=agent-messenger --filter=node-services --only", "check-types": "turbo run build --filter=relic-ui... && turbo run check-types --only", "dev:ui": "turbo run dev --parallel --filter=json-ui --filter=relic-ui", "dev:full": "turbo run dev --parallel --filter=json-ui --filter=relic-ui --filter=node-services", "dev:web-crawler": "pnpm i --filter=web-crawler & turbo run dev --parallel --filter=web-crawler", "start": "turbo run start --parallel --only"}, "lint-staged": {"*.{js,jsx,ts,tsx,json,md}": "eslint --fix"}, "devDependencies": {"turbo": "2.5.2", "typescript": "^5.8.3"}, "engines": {"node": "22.0.0", "pnpm": ">=10.0.0"}, "eslintIgnore": ["**/dist", "**/node_modules", "**/build", "**/package-lock.json"], "prettier": {"printWidth": 140, "singleQuote": true, "jsxSingleQuote": true, "trailingComma": "none", "tabWidth": 4, "semi": false, "endOfLine": "auto"}, "babel": {"presets": ["@babel/preset-typescript", ["@babel/preset-env", {"targets": {"node": "current"}}]]}, "dependencies": {"depcheck": "^1.4.7"}}