import React from 'react';
import { IUser } from 'relic-ui';
import { useGetLocale, useSetLocale, useGetIdentity } from '@refinedev/core';
import { HamburgerMenu, RefineThemedLayoutV2HeaderProps } from '@refinedev/mui';

import Stack from '@mui/material/Stack';
import AppBar from '@mui/material/AppBar';
import Avatar from '@mui/material/Avatar';
import Toolbar from '@mui/material/Toolbar';
import Typography from '@mui/material/Typography';

import { Search } from '../search';

// ----------------------------------------------------------------------

export const SimpleHeader: React.FC<RefineThemedLayoutV2HeaderProps> = ({
  sticky = true,
}) => {
  const { data: user } = useGetIdentity<IUser>();

  const locale = useGetLocale();

  if (!user?.userIdentity?.portalIdentity?.header) return <></>;
  else if (window.location.pathname.includes('embedded-call')) return <></>;
  else
    return (
      <AppBar
        position={sticky ? 'sticky' : 'relative'}
        sx={{
          bgcolor: 'background.default',
          boxShadow: theme => theme.customShadows.z1,
        }}
      >
        <Toolbar>
          <HamburgerMenu />
          <Stack
            direction="row"
            width="100%"
            justifyContent="flex-end"
            alignItems="center"
            gap="16px"
          >
            {(user?.avatar || user?.name) && (
              <Stack
                direction="row"
                gap="16px"
                alignItems="center"
                justifyContent="center"
              >
                {user?.name && (
                  <Typography
                    sx={{
                      display: {
                        xs: 'none',
                        sm: 'inline-block',
                      },
                    }}
                    variant="subtitle2"
                  >
                    {user?.name}
                  </Typography>
                )}
                <Avatar src={user?.avatar} alt={user?.name} />
              </Stack>
            )}
          </Stack>
        </Toolbar>
      </AppBar>
    );
};
