import React from 'react';
import { RelicDocument } from 'relic-ui';
import { useDropzone } from 'react-dropzone';
import { useForm, Controller } from 'react-hook-form';
import { useShow, useCreate, useTranslate } from '@refinedev/core';

import LoadingButton from '@mui/lab/LoadingButton';
import DeleteIcon from '@mui/icons-material/Delete';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import DescriptionIcon from '@mui/icons-material/Description';
import InsertDriveFileIcon from '@mui/icons-material/InsertDriveFile';
import {
  Box,
  Stack,
  Alert,
  Button,
  Dialog,
  Select,
  Switch,
  Divider,
  MenuItem,
  TextField,
  InputLabel,
  Typography,
  DialogTitle,
  FormControl,
  DialogActions,
  FormControlLabel,
} from '@mui/material';

import { useParams } from 'src/routes/hooks';

import { uploadToAzureBlobStorage } from 'src/utils/pdf-utils';

const TrainingContentAddDialog = ({
  open,
  onClose,
}: {
  open: boolean;
  onClose: VoidFunction;
}) => {
  const {
    control,
    register,
    handleSubmit,
    reset,
    setError,
    formState: { errors, isSubmitting },
    watch,
    clearErrors,
  } = useForm({
    defaultValues: {
      contentType: 'file', // changed from '' to 'file'
      url: '',
      file: '',
      useReadableContent: true,
      maxUrlsToCrawl: 1,
      crawlEmbeddedLinks: true,
      splitRequests: false,
    },
  });

  const [uploadError, setUploadError] = React.useState<string | null>('');

  const { id } = useParams();

  const { query } = useShow({
    resource: 'trainings/modules',
    id: id,
  });

  const { mutate } = useCreate({
    resource: `trainings/modules/${id}/contents`,
  });

  const [file, setFile] = React.useState<File | null>(null);

  const values = watch();
  const translate = useTranslate();

  const onSubmit = handleSubmit(async data => {
    if (data.contentType === 'file' && !file) {
      clearErrors('file');
      setError('file', {
        type: 'manual',
        message: 'This field is required',
      });
      return;
    }

    if (data.contentType === 'url' && !data.url) {
      clearErrors('url');
      setError('url', {
        type: 'manual',
        message: 'This field is required',
      });
      return;
    }

    try {
      const containerName = query?.data?.data?.storage?.containerName;

      if (!containerName) {
        setUploadError('Training module container not found.');
        return;
      }

      const payloadData: {
        url?: string;
        documentId?: string;
        filename?: string;
        type?: string;
        splitRequests?: boolean;
        options?: {
          maxUrlsToCrawl: number;
          useReadableContent: boolean;
          crawlEmbeddedLinks: boolean;
        };
      } = {
        type: data.contentType === 'url' ? 'url' : data.contentType,
        splitRequests: data.splitRequests,
        options: {
          maxUrlsToCrawl: data.maxUrlsToCrawl,
          useReadableContent: data.useReadableContent,
          crawlEmbeddedLinks: data.crawlEmbeddedLinks,
        },
      };

      if (data.contentType === 'file' && file) {
        const extension = file.name.substring(file.name.lastIndexOf('.') + 1);
        const basename = file.name.substring(0, file.name.lastIndexOf('.'));
        const documentId = `${basename}_${crypto.randomUUID()}.${extension}`;

        const uploadOption = {
          accountName: 'relicstorage',
          containerName: containerName,
        };

        const relicDocument: RelicDocument = {
          organizationId: '',
          language: 'en-us',
          type: file.type,
          url: '',
          filename: file.name,
          documentId,
          header: 0,
          footer: 0,
          status: 'done',
          translationType: 'mono',
        };

        let uploadedDocument: RelicDocument | undefined = undefined;

        try {
          uploadedDocument = await uploadToAzureBlobStorage(
            file,
            relicDocument,
            uploadOption,
          );
        } catch (error) {
          setUploadError(`Failed to upload the document - ${error.message}`);
          return;
        }
        if (!uploadedDocument) {
          setUploadError('Failed to upload the document.');
          return;
        }
        console.log('uploadedContent', uploadedDocument);
        payloadData.documentId = uploadedDocument.documentId;
        payloadData.filename = uploadedDocument.filename;
        payloadData.url = uploadedDocument.url;
        payloadData.type = uploadedDocument.type;
      }

      if (data.contentType === 'url' && data.url) {
        payloadData.url = data.url;
      }

      mutate({
        values: payloadData,
        successNotification: () => ({
          message: translate(`Successfully added Training Content`, {
            resource: 'documents',
          }),
          type: 'success',
        }),
        errorNotification: error => ({
          message: translate('notifications.createError', {
            resource: 'documents',
            statusCode: error?.statusCode,
          }),
          type: 'error',
        }),
      });
      onClose();
    } catch (error) {
      console.error(error);
    }
  });

  const addFile = React.useCallback(
    (acceptedFiles: File[]) => {
      const file = acceptedFiles[0];
      setFile(file);
      clearErrors('file');
    },
    [clearErrors],
  );

  const removeFile = () => {
    setFile(null);
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop: addFile,
    accept: { 'application/pdf': ['.pdf'] },
  });

  return (
    <Dialog
      open={open}
      fullWidth
      maxWidth="sm"
      onClose={onClose}
      aria-labelledby="alert-dialog-title"
      aria-describedby="alert-dialog-description"
    >
      <Stack component="form" onSubmit={onSubmit} autoComplete="off">
        <DialogTitle id="alert-dialog-title">
          {translate('trainings-content.titles.create')}
        </DialogTitle>

        <Divider />

        <Stack spacing={3} sx={{ p: 3 }}>
          <FormControl fullWidth>
            <InputLabel id="content-type-labelId">
              {translate('trainings-content.fields.contentType')}
            </InputLabel>
            <Select
              {...register('contentType', {
                required: 'This field is required',
              })}
              labelId="content-type-labelId"
              label="Content Type"
              value={values.contentType}
            >
              <MenuItem value={'file'}>
                {translate('trainings-content.options.file')}
              </MenuItem>
              <MenuItem value={'url'}>
                {translate('trainings-content.options.url')}
              </MenuItem>
            </Select>
          </FormControl>

          {values.contentType === 'url' && (
            <TextField
              {...register('url', {
                required: 'This field is required',
                pattern: {
                  value: /^https:\/\/[^\s]+$/,
                  message: 'URL must start with https://',
                },
              })}
              error={!!(errors as any)?.url}
              helperText={(errors as any)?.url?.message}
              fullWidth
              type="url"
              label={translate('trainings-content.fields.url')}
              name="url"
            />
          )}
          {values.contentType === 'url' && (
            <Box>
              <Controller
                name="useReadableContent"
                control={control}
                defaultValue={values.useReadableContent}
                render={({ field }) => (
                  <FormControlLabel
                    label={translate(
                      'trainings-content.fields.readable-content',
                    )}
                    labelPlacement="start"
                    control={
                      <Switch
                        {...field}
                        name="useReadableContent"
                        checked={!!field.value}
                      />
                    }
                  />
                )}
              />
            </Box>
          )}
          {values.contentType === 'url' && (
            <TextField
              {...register('maxUrlsToCrawl', {
                required: 'This field is required',
                valueAsNumber: true,
                min: {
                  value: 1,
                  message: 'Minimum value is 1',
                },
                max: {
                  value: 300,
                  message: 'Maximum value is 300',
                },
              })}
              error={!!(errors as any)?.maxUrlsToCrawl}
              helperText={(errors as any)?.maxUrlsToCrawl?.message}
              fullWidth
              type="number"
              inputProps={{ min: 1, max: 300 }}
              label={translate(
                'trainings-content.fields.max-request-per-crawl',
              )}
              name="maxUrlsToCrawl"
            />
          )}
          {values.contentType === 'file' && (
            <>
              {errors.file && (
                <Alert sx={{ my: 2 }} severity="warning" variant="outlined">
                  {(errors as any).file.message}
                </Alert>
              )}
              <Box
                {...getRootProps()}
                sx={{
                  border: '2px dashed',
                  borderColor: isDragActive ? 'primary.main' : 'grey.400',
                  borderRadius: 2,
                  p: 2,
                  mt: 2,
                  cursor: 'pointer',
                  textAlign: 'center',
                  transition: 'border-color 0.3s',
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <input {...getInputProps()} />
                {isDragActive ? (
                  <DescriptionIcon
                    sx={{ fontSize: 60, color: 'primary.main', mb: 2 }}
                  />
                ) : (
                  <CloudUploadIcon
                    sx={{ fontSize: 60, color: 'grey.500', mb: 2 }}
                  />
                )}
                {isDragActive ? (
                  <Typography
                    variant="subtitle1"
                    sx={{ color: 'primary.main' }}
                  >
                    Drop the PDF file here ...
                  </Typography>
                ) : (
                  <Typography
                    variant="subtitle1"
                    sx={{ color: 'text.secondary' }}
                  >
                    Drag 'n' drop a PDF file here, or click to select a PDF file
                  </Typography>
                )}
                {file && (
                  <Box
                    sx={{
                      mt: 2,
                      p: 2,
                      border: 1,
                      borderColor: 'divider',
                      borderRadius: '8px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'space-between',
                      typography: 'body1',
                    }}
                  >
                    <InsertDriveFileIcon sx={{ mr: 1 }} />
                    <Box
                      sx={{
                        flexGrow: 1,
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                      }}
                    >
                      {file.name} - {(file.size / 1024).toFixed(2)} KB
                    </Box>
                    <DeleteIcon
                      sx={{ cursor: 'pointer' }}
                      onClick={removeFile}
                    />
                  </Box>
                )}
              </Box>
            </>
          )}
          {uploadError && (
            <Alert sx={{ my: 2 }} severity="error" variant="outlined">
              {uploadError}
            </Alert>
          )}
        </Stack>
        <Divider />

        <DialogActions>
          <Button
            onClick={() => {
              onClose();
              reset();
            }}
          >
            {translate('buttons.cancel')}
          </Button>

          <LoadingButton
            type="submit"
            variant="contained"
            loading={isSubmitting}
          >
            {translate('buttons.create')}
          </LoadingButton>
        </DialogActions>
      </Stack>
    </Dialog>
  );
};

export default TrainingContentAddDialog;
