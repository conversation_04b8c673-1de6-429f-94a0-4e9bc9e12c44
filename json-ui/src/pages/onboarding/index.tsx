import { useCreate, useTranslate } from '@refinedev/core';
import { useForm } from '@refinedev/react-hook-form';
import { useCallback, useEffect } from 'react';
import { toast } from 'react-toastify';

import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  Stack,
  TextField,
  Typography,
} from '@mui/material';

import { useRouter } from 'src/routes/hooks';

import {
  getActiveLogin,
  MsalInstance,
  setActiveLogin,
  storeUserIdentityAndAccess,
} from 'src/providers/utils';

import LayoutWrapper from './layout-wrapper';
const UserOnboardingComponent = () => {
  const {
    register,
    formState: { errors, isLoading },
    setValue,
    watch,
    handleSubmit,
  } = useForm();

  const values = watch();

  const { mutate: registerWorkAccount } = useCreate({
    resource: 'register',
    mutationOptions: {
      onSuccess: async (data, variables, context) => {
        console.log('Data:', data);
        console.log('Variables:', variables);
        console.log('Context:', context);
        const activeLogin = getActiveLogin();
        await storeUserIdentityAndAccess(activeLogin);
        toast.success(
          translate('onboarding.messages.success', {
            name: `${values.givenName} ${values.surname}`,
          }),
        );
        router.push('/patients');
      },
      onError: error => {
        localStorage.removeItem('entraAuthState');
        router.push('/login');
        console.error('Error:', error);
      },
    },
  });

  const urlParams = useCallback(() => {
    return new URLSearchParams(
      window.location.search || window.location.hash.substring(1),
    );
  }, []);

  const router = useRouter();

  const translate = useTranslate();

  const createAccount = useCallback(() => {
    registerWorkAccount(
      {
        values: values,
      },
      // {
      //   onSuccess: async (data, variables, context) => {
      //     console.log('Data:', data);
      //     console.log('Variables:', variables);
      //     console.log('Context:', context);
      //     const activeLogin = getActiveLogin();
      //     await storeUserIdentityAndAccess(activeLogin);
      //     toast.success(
      //       translate('onboarding.messages.success', {
      //         name: `${values.givenName} ${values.surname}`,
      //       }),
      //     );
      //     router.push('/patients');
      //   },
      //   onError: error => {
      //     localStorage.removeItem('entraAuthState');
      //     router.push('/login');
      //     console.error('Error:', error);
      //   },
      // },
    );
  }, [registerWorkAccount, values]);

  const checkSocialStatus = useCallback(async () => {
    const params = urlParams();
    if (!params.has('code')) {
      // return back to login page
      router.push('/login');
      return;
    }
    let state = params.get('state') as string;
    if (state && state.includes('|')) {
      state = state.split('|')[1];
    }
    const entraAuthState = localStorage.getItem('entraAuthState');
    if (state === entraAuthState) {
      try {
        const entraClient = await MsalInstance.getClient('entra');
        const authResponse = await entraClient.handleRedirectPromise();
        if (authResponse) {
          // Set activeLogin in local storage even though we don't have a whoami ready yet.
          const activeLogin = await MsalInstance.handleAuthResponse(
            authResponse,
            'entra',
          );
          await setActiveLogin(activeLogin);
          const account = authResponse.account;
          console.log('Account:', account);
          const [id, organizationId] = account?.homeAccountId?.split('.') || [];
          console.log('id:', id);
          console.log('organizationId:', organizationId);
          const givenName = account?.name?.split(' ')[0];
          const surname = account?.name?.split(' ')[1];
          const provider = 'entra';
          const email = account?.username;
          setValue('givenName', givenName);
          setValue('surname', surname);
          setValue('email', email);
          setValue('organizationId', organizationId);
          setValue('id', id);
          setValue('provider', provider);
          // createAccount();
          console.log('Authentication successful:', authResponse);
        } else {
          console.error('Authentication failed');
          router.push('/register');
        }
      } catch (error) {
        console.error('Error handling authentication:', error);
        router.push('/register');
      }
    } else {
      // If state doesn't match or no entraAuthState, redirect to signup
      console.log('Invalid state or missing Entra authentication');
      router.push('/register');
    }
  }, [router, setValue, urlParams]);

  const onSubmit = handleSubmit(createAccount);

  useEffect(() => {
    checkSocialStatus();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [urlParams]);

  return (
    <LayoutWrapper>
      <Card sx={{ minWidth: 400 }}>
        <CardContent>
          <Typography variant="h4" color="primary" sx={{ textAlign: 'center' }}>
            {translate('onboarding.title')}
          </Typography>
          <Stack
            spacing={2.5}
            component="form"
            autoComplete="off"
            sx={{ mt: 4 }}
          >
            {/* Add form fields here */}
            <TextField
              {...register('givenName', {
                required: 'This field is required',
              })}
              error={!!errors?.givenName}
              helperText={(errors as any)?.givenName?.message}
              fullWidth
              InputProps={{
                readOnly: true,
              }}
              InputLabelProps={{ shrink: true }}
              type="text"
              label={translate('onboarding.fields.givenName')}
              name="givenName"
            />
            <TextField
              {...register('surname', {
                required: 'This field is required',
              })}
              error={!!errors?.surname}
              helperText={(errors as any)?.surname?.message}
              fullWidth
              InputLabelProps={{ shrink: true }}
              type="text"
              InputProps={{
                readOnly: true,
              }}
              label={translate('onboarding.fields.surname')}
              name="surname"
            />
            <TextField
              {...register('email', {
                required: 'This field is required',
              })}
              error={!!errors?.email}
              helperText={(errors as any)?.email?.message}
              fullWidth
              InputLabelProps={{ shrink: true }}
              type="text"
              InputProps={{
                readOnly: true,
              }}
              label={translate('onboarding.fields.email')}
              name="email"
            />
            <TextField
              {...register('id', {
                required: 'This field is required',
              })}
              error={!!errors?.id}
              helperText={(errors as any)?.id?.message}
              fullWidth
              InputLabelProps={{ shrink: true }}
              type="text"
              InputProps={{
                readOnly: true,
              }}
              name="id"
            />
            <TextField
              {...register('provider', {
                required: 'This field is required',
              })}
              error={!!errors?.provider}
              helperText={(errors as any)?.provider?.message}
              fullWidth
              InputLabelProps={{ shrink: true }}
              type="text"
              InputProps={{
                readOnly: true,
              }}
              name="provider"
            />
            <Button
              variant="contained"
              size="large"
              disabled={isLoading}
              type="submit"
              onClick={onSubmit}
            >
              {translate('onboarding.buttons.submit')}
            </Button>
          </Stack>
        </CardContent>
      </Card>
    </LayoutWrapper>
  );
};

export default UserOnboardingComponent;
