<!DOCTYPE html>
<html>

<head>

  <title>User Authentication</title>

  <meta charset="utf-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="icon" href="https://relic-facility-portal.vercel.app/favicon.ico" />
  <link href="https://ajax.aspnetcdn.com/ajax/bootstrap/3.3.5/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
  <!-- Google Font -->
  <link rel="preconnect" href="https://fonts.googleapis.com" data-preload="true" />
  <link rel="preconnect" href="https://fonts.gstatic.com" data-preload="true" />
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800;900&display=swap" rel="stylesheet"
    data-preload="true" />
  <title>Relic Care</title>
  <style id="relic">
    header {
      margin-left: auto;
      box-sizing: border-box;
      margin-right: auto;
      display: block;
      background-color: #FFFCF8;
    }

    .logo {
      font-size: 24px;
      font-weight: bold;
      color: #333;
    }

    .logo span {
      color: #4CAF50;
    }

    nav {
      display: flex;
      align-items: center;
    }

    nav a,
    .dropbtn {
      margin-left: 20px;
      text-decoration: none;
      color: #333;
    }

    .get-invited {
      background-color: #000;
      color: white;
      padding: 10px 20px;
      border-radius: 10px;
      text-decoration: none;
    }

    main {
      flex-grow: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      height: calc(100vh - 80px);
    }

    main::before {
      width: 100%;
      height: 100%;
      z-index: -1;
      content: '';
      opacity: 0.24;
      position: absolute;
      -webkit-background-size: cover;
      background-size: cover;
      background-repeat: no-repeat;
      -webkit-background-position: center center;
      background-position: center center;
      background-image: url(https://relic-facility-portal.vercel.app/images/overlay_4.jpg);
    }

    .dropdown {
      position: relative;
      display: inline-block;
    }

    .dropdown-content {
      display: none;
      position: absolute;
      background-color: #f9f9f9;
      min-width: 200px;
      border-radius: 5px;
      box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
      z-index: 1;
    }

    .dropdown-content a {
      color: black;
      text-decoration: none;
      margin: 0;
      padding: 10px;
      display: block;
    }

    .dropdown-content a:hover {
      background-color: #f1f1f1;
    }

    .dropdown:hover .dropdown-content {
      display: block;
    }

    .contact {
      margin-left: 10px;
    }

    .logo img {
      height: 50px;
      width: auto;
    }

    .relic-container {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px;
      width: 100%;
      max-width: 1200px;
      margin: auto;
    }

    input[disabled] {
      background-color: #f9f9f9;
      color: #999999;
    }

    input#readOnlyEmail {
      border: 0;
      background: none;
      color: #212B36;
      text-align: center;
      text-indent: 0px !important;
    }

    #attributeVerification .buttons #continue {
      display: none;
    }

    @media (max-width: 1200px) {
      .relic-container {
        max-width: 960px;
      }
    }

    @media (max-width: 992px) {
      .relic-container {
        max-width: 720px;
      }
    }

    @media (max-width: 768px) {
      .relic-container {
        max-width: 540px;
      }
    }

    @media (max-width: 576px) {
      .relic-container {
        max-width: 100%;
      }
    }
  </style>
  <style id="common">
    #AmazonExchange {
      background-image: url(https://relic-facility-portal.vercel.app/templates/src/idp_logos/colored/amazon.svg);
    }

    #AmazonExchange:hover {
      background-image: url(https://relic-facility-portal.vercel.app/templates/src/idp_logos/white/amazon.svg);
    }

    #FacebookExchange {
      background-image: url(https://relic-facility-portal.vercel.app/templates/src/idp_logos/colored/facebook.svg);
    }

    #FacebookExchange:hover {
      background-image: url(https://relic-facility-portal.vercel.app/templates/src/idp_logos/white/facebook.svg);
    }

    #GitHubExchange {
      background-image: url(https://relic-facility-portal.vercel.app/templates/src/idp_logos/colored/github.svg);
    }

    #GitHubExchange:hover {
      background-image: url(https://relic-facility-portal.vercel.app/templates/src/idp_logos/white/github.svg);
    }

    #AppleExchange,
    #AppleManagedExchange {
      background-image: url(https://relic-facility-portal.vercel.app/templates/src/idp_logos/colored/apple.svg);
    }

    #AppleExchange:hover,
    #AppleManagedExchange:hover {
      background-image: url(https://relic-facility-portal.vercel.app/templates/src/idp_logos/white/apple.svg);
    }

    #GoogleExchange {
      background-image: url(https://relic-facility-portal.vercel.app/templates/src/idp_logos/colored/google.svg);
    }

    #GoogleExchange:hover {
      background-image: url(https://relic-facility-portal.vercel.app/templates/src/idp_logos/white/google.svg);
    }

    #LinkedInExchange {
      background-image: url(https://relic-facility-portal.vercel.app/templates/src/idp_logos/colored/linkedin.svg);
    }

    #LinkedInExchange:hover {
      background-image: url(https://relic-facility-portal.vercel.app/templates/src/idp_logos/white/linkedin.svg);
    }

    #MicrosoftAccountExchange {
      background-image: url(https://relic-facility-portal.vercel.app/templates/src/idp_logos/colored/microsoft.svg);
    }

    #MicrosoftAccountExchange:hover {
      background-image: url(https://relic-facility-portal.vercel.app/templates/src/idp_logos/white/microsoft.svg);
    }

    #QQExchange {
      background-image: url(https://relic-facility-portal.vercel.app/templates/src/idp_logos/colored/qq.svg);
    }

    #QQExchange:hover {
      background-image: url(https://relic-facility-portal.vercel.app/templates/src/idp_logos/white/qq.svg);
    }

    #TwitterExchange {
      background-image: url(https://relic-facility-portal.vercel.app/templates/src/idp_logos/colored/twitter.svg);
    }

    #TwitterExchange:hover {
      background-image: url(https://relic-facility-portal.vercel.app/templates/src/idp_logos/white/twitter.svg);
    }

    #WeChatExchange {
      background-image: url(https://relic-facility-portal.vercel.app/templates/src/idp_logos/colored/wechat.svg);
    }

    #WeChatExchange:hover {
      background-image: url(https://relic-facility-portal.vercel.app/templates/src/idp_logos/white/wechat.svg);
    }

    #WeiboExchange {
      background-image: url(https://relic-facility-portal.vercel.app/templates/src/idp_logos/colored/weibo.svg);
    }

    #WeiboExchange:hover {
      background-image: url(https://relic-facility-portal.vercel.app/templates/src/idp_logos/white/weibo.svg);
    }

    .buttons #cancel:before {
      content: url(https://relic-facility-portal.vercel.app/templates/src/images/left-arrow.svg);
    }
  </style>
  <style id="idpselector">
    #SignInWithLogonNameExchange,
    #SignUpWithLogonEmailExchange,
    #SignUpWithLogonNameExchange {
      background-image: url(https://relic-facility-portal.vercel.app/templates/src/idp_logos/colored/local.svg);
    }

    #SignInWithLogonNameExchange:hover,
    #SignUpWithLogonEmailExchange:hover,
    #SignUpWithLogonNameExchange:hover {
      background-image: url(https://relic-facility-portal.vercel.app/templates/src/idp_logos/white/local.svg);
    }

    #SignUpWithLogonEmailExchange.text-link {
      background-image: none;
      background-color: transparent !important;
    }

    #SignUpWithLogonEmailExchange.text-link:hover {
      background-image: none;
      background-color: transparent !important;
    }
  </style>
  <style>
    /* Following are generated styles via gulp. */
    /* inject: css */
    html {
      background: 0 0
    }

    body {
      margin: 0;
      padding: 0;
      height: 100%;
      min-height: 100%;
      flex-direction: column;
      width: 100%;
      font-family: Inter, sans-serif;
      text-align: left;
      font: -apple-system-body;
      color: #212B36
    }

    p {
      margin: 0
    }

    a {
      text-decoration: underline;
      text-decoration-thickness: 1px
    }

    a:focus {
      outline: 0;
      border: 2px solid #000
    }

    input,
    select {
      border: 1px solid #cccccc
    }

    input::placeholder {
      color: #6a6a6a;
      opacity: 1
    }

    img[data-tenant-branding-background=true] {
      width: 100%;
      height: 100%;
      object-fit: cover;
      z-index: 0;
      position: fixed;
      left: 0;
      overflow: hidden;
      top: 0
    }

    div[data-tenant-branding-background-color=true] {
      width: 100%;
      height: 100%;
      position: fixed;
      z-index: 0
    }

    .container {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px;
      text-align: center;
      width: 100%;
      max-width: 1200px;
      margin: auto;
      height: 100vh;
    }

    #api {
      margin: 0 auto
    }

    .heading h1 {
      margin-bottom: 20px
    }

    .col-lg-6 {
      max-width: 470px;
      width: 100%;
      float: none;
      display: inline-block;
      vertical-align: top;
      padding: 0
    }

    .row {
      width: 100%;
      margin: 0;
      display: table-cell;
      vertical-align: middle
    }

    ul {
      padding: 0
    }

    #simplemodal-container .simplemodal-data {
      padding: 5px 8px;
      overflow: hidden;
      background-color: #fff;
      border-radius: 3px;
      font-style: italic
    }

    #LocalAccountSigninEmailExchange,
    #LocalAccountSigninUsernameExchange,
    #SignInWithLogonEmailExchange,
    #SignInWithLogonNameExchange,
    #SignUpWithLogonEmailExchange,
    #SignUpWithLogonNameExchange {
      background-color: #212b36;
      background-image: url(none);
      background-size: 10%;
      background-position-x: 95%;
      background-position-y: 50%;
      background-repeat: no-repeat;
      border-radius: .5rem;
      width: 100%;
      width: 100%;
      font-size: 1em;
      margin-left: auto
    }

    #LocalAccountSigninEmailExchange:hover,
    #LocalAccountSigninUsernameExchange:hover,
    #SignInWithLogonEmailExchange:hover,
    #SignInWithLogonNameExchange:hover,
    #SignUpWithLogonEmailExchange:hover,
    #SignUpWithLogonNameExchange:hover {
      background-color: #919eab !important
    }

    #AmazonExchange:hover {
      background-color: #36474f !important
    }

    #FacebookExchange:hover {
      background-color: #3b5595 !important
    }

    #GitHubExchange:hover {
      background-color: #0f0f0f !important
    }

    #AppleExchange:hover,
    #AppleManagedExchange:hover {
      background-color: #0f0f0f !important
    }

    #GoogleExchange:hover {
      background-color: #c64a29 !important
    }

    #LinkedInExchange:hover {
      background-color: #0274b3 !important
    }

    #MicrosoftAccountExchange:hover {
      background-color: #01a4ef !important
    }

    #QQExchange:hover {
      background-color: #1a97df !important
    }

    #TwitterExchange:hover {
      background-color: #1da1f2 !important
    }

    #WeChatExchange:hover {
      background-color: #4cc522 !important
    }

    #WeiboExchange:hover {
      background-color: #e81123 !important
    }

    .buttons button {
      margin: 0 auto;
      width: 60%;
      height: 50px;
      background-color: #212b36;
      border-radius: .5rem;
      border: none;
      color: #fff;
      font-size: 1em;
      margin-top: 10px
    }

    .buttons button:hover {
      background-color: #919eab;
      border-color: #204d74
    }

    .buttons button[aria-disabled=true] {
      opacity: .3
    }

    .buttons button:not([aria-disabled=true]) {
      opacity: 1
    }

    .buttons #cancel {
      padding: 0;
      margin-left: auto;
      background: 0 0;
      border: 0;
      width: inherit;
      position: absolute;
      background-color: transparent;
      top: 2px;
      left: 15px;
      font-size: 1em;
      color: #505050;
      box-shadow: none;
      text-align: left
    }

    .buttons #cancel:hover {
      color: #000 !important
    }

    .buttons #cancel:before {
      padding: 10px;
      vertical-align: sub;
      zoom: 90%;
      display: inline-block
    }

    #attributeList ul {
      padding: 0
    }

    #attributeList ul li {
      list-style: none;
      margin: 1rem 0
    }

    label {
      font-weight: 600
    }

    #attributeList ul li label {
      display: block;
      text-align: left;
      float: none
    }

    #attributeList ul li.EmailBox label,
    #attributeList ul li.Password label,
    #attributeList ul li.TextBox label {
      display: none !important
    }

    #attributeList ul li label[for=country] {
      float: left;
      display: inline-block !important;
      margin-bottom: .5rem
    }

    #attributeList ul li label[for=cpiminternal_legalCountry] {
      float: none;
      display: block !important;
      text-align: left;
      margin-bottom: .5rem
    }

    #attributeList ul li label[for=cpiminternal_dateOfBirth] {
      float: none;
      text-align: left;
      display: block !important;
      margin-bottom: .5rem
    }

    #attributeList ul li input {
      height: 50px;
      width: 70%;
      border-radius: .5rem;
      text-indent: 20px
    }

    #attributeList ul li input::-webkit-input-placeholder {
      text-indent: 20px
    }

    #attributeList ul li .attrEntry.validate input.invalid,
    #attributeList ul li .attrEntry.validate input:invalid {
      border: 1px solid #b00
    }

    #attributeList ul li .attrEntry .verify {
      margin-top: 2rem;
      padding-bottom: 10px;
      position: relative
    }

    #attributeList ul li .attrEntry .verificationControlContent .buttons {
      margin-top: 2rem;
      padding-bottom: 10px;
      position: relative
    }

    #attributeList ul li .attrEntry .verify .sendButton[id$=ver_but_send] {
      width: 60%;
      height: 50px;
      background-color: #212b36;
      color: #fff;
      border-radius: .5rem
    }

    #attributeList ul li .attrEntry .verificationControlContent .sendCode {
      width: 60%;
      height: 50px;
      background-color: #212b36;
      color: #fff;
      border-radius: .5rem
    }

    #attributeList ul li .attrEntry .verify .sendButton[id$=ver_but_send]:hover {
      background-color: #919eab
    }

    #attributeList ul li .attrEntry .verificationControlContent .sendCode:hover {
      background-color: #919eab
    }

    @media screen and (-ms-high-contrast:active) {

      #attributeList ul li .attrEntry .verify button,
      .buttons #continue {
        border: 1px dashed #fff !important
      }

      #attributeList ul li .attrEntry .verify button:hover,
      .buttons #continue:hover {
        border: 1px solid #fff !important
      }
    }

    #attributeList ul li .attrEntry .verify .verifyButton {
      width: inherit;
      padding: 0 20px;
      height: 50px;
      background-color: #212b36;
      color: #fff;
      margin-right: 5px;
      border-radius: .5rem
    }

    #attributeList ul li .attrEntry .verificationControlContent .verifyCode {
      width: inherit;
      padding: 0 20px;
      height: 50px;
      background-color: #212b36;
      color: #fff;
      margin-right: 5px;
      border-radius: .5rem
    }

    #attributeList ul li .attrEntry .verify .verifyButton:hover {
      background-color: #919eab
    }

    #attributeList ul li .attrEntry .verificationControlContent .verifyCode:hover {
      background-color: #919eab
    }

    #attributeList ul li .attrEntry .verify .sendButton[id$=ver_but_resend] {
      width: inherit;
      padding: 0 20px;
      border: 0;
      font-size: 1em;
      height: 50px;
      right: 0;
      bottom: -38px;
      background-color: #212b36;
      color: #fff;
      border-radius: .5rem
    }

    #attributeList ul li .attrEntry .verificationControlContent .sendNewCode {
      width: inherit;
      padding: 0 20px;
      border: 0;
      font-size: 1em;
      height: 50px;
      right: 0;
      bottom: -38px;
      background-color: #212b36;
      color: #fff;
      border-radius: .5rem
    }

    #attributeList ul li .attrEntry .verify .sendButton[id$=ver_but_resend]:hover {
      background-color: #919eab
    }

    #attributeList ul li .attrEntry .verificationControlContent .sendNewCode:hover {
      background-color: #919eab
    }

    #attributeList ul li .attrEntry .verify .editButton {
      border: none;
      color: #fff;
      background-color: #212b36;
      width: 60%;
      height: 50px;
      border-radius: .5rem
    }

    #attributeList ul li .attrEntry .verificationControlContent .changeClaims {
      border: none;
      color: #fff;
      background-color: #212b36;
      width: 60%;
      height: 50px;
      border-radius: .5rem
    }

    #attributeList ul li .attrEntry .verify .editButton:hover {
      background-color: #919eab
    }

    #attributeList ul li .attrEntry .verificationControlContent .changeClaims:hover {
      background-color: #919eab
    }

    #attributeList ul li .attrEntry .verify #email_ver_input_label {
      display: none !important
    }

    #api .working {
      background: url(data:image/gif;base64,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) no-repeat;
      height: 30px;
      width: auto;
      background-position: center bottom
    }

    #attributeList ul li .attrEntry .itemLevel {
      display: none
    }

    input[type=email],
    input[type=password],
    input[type=text] {
      -webkit-appearance: none
    }

    #attributeList ul li .attrEntry .dropdown_single {
      height: 45px;
      border-radius: .5rem;
      width: 100%;
      padding-left: 15px
    }

    #attributeList ul li .attrEntry div#cpiminternal_dateOfBirth.date {
      display: table;
      width: 100%
    }

    #attributeList ul li .attrEntry div#cpiminternal_dateOfBirth.date select {
      border-radius: .5rem;
      display: inline-block;
      height: 50px;
      padding-left: 15px
    }

    #attributeList ul li .attrEntry div#cpiminternal_dateOfBirth select#cpiminternal_dateOfBirth_day.day {
      margin-right: 3%;
      width: 23%
    }

    #attributeList ul li .attrEntry div#cpiminternal_dateOfBirth select#cpiminternal_dateOfBirth_month.month {
      margin-right: 3%;
      width: 38%
    }

    #attributeList ul li .attrEntry div#cpiminternal_dateOfBirth select#cpiminternal_dateOfBirth_year.year {
      width: 33%
    }

    #attributeList ul li .helpLink {
      display: none
    }

    #attributeVerification .buttons {
      margin-top: 30px
    }

    @media screen and (max-width:100%) {
      .row .panel-default .panel-body {
        margin-top: 40px
      }

      .row .panel-default .panel-body .branding img {
        display: block;
        height: 80px;
        width: auto;
        margin: 0 auto
      }

      .row .panel-default .panel-body .branding h1 {
        display: none
      }

      .row .panel-default .panel-body h3 {
        font-size: 1.5em;
        margin-top: 15px
      }

      #attributeVerification #attributeList ul li input {
        width: 60%;
        height: 40px;
        border-radius: .5rem;
        border: 1px solid #cccccc;
        text-indent: 10px
      }

      #attributeVerification #attributeList ul li input::-webkit-input-placeholder {
        text-indent: 10px
      }

      #attributeVerification #attributeList ul li #email {
        width: 65%;
        float: left;
        position: relative;
        z-index: 2
      }

      #attributeVerification #attributeList ul li #email_ver_but_verify {
        background: 0 0;
        border: 0;
        color: #496ad8;
        font-size: 1em;
        position: relative;
        right: 0;
        top: 60px;
        font-size: 1.1em;
        width: auto;
        z-index: 1;
        height: 20px
      }

      #attributeVerification #attributeList ul li #email_ver_input {
        min-width: 50%;
        width: 60%;
        float: left
      }

      #attributeVerification #attributeList ul li #email_ver_but_resend {
        top: 10px;
        font-size: 1.1em;
        height: 20px
      }

      #attributeVerification #attributeList ul li .attrEntry #email {
        height: 40px
      }

      #attributeVerification #attributeList ul li .attrEntry .verify #email_ver_but_send:after {
        content: " Verify";
        font-size: 1.1em;
        font-style: bold;
        margin-left: 40%
      }

      #attributeVerification #attributeList ul li .attrEntry .verify #email_ver_but_send {
        height: 40px;
        width: auto;
        float: right;
        overflow: hidden;
        text-indent: -200px
      }

      #attributeVerification #attributeList ul li .attrEntry .verify #email_ver_but_edit {
        margin-bottom: 15px;
        border: none;
        color: #fff;
        background-color: #496ad8;
        border-radius: .5rem;
        font-size: 1em;
        box-shadow: 0 0 40px 2px #dae1f7;
        width: 30%;
        height: 40px;
        overflow: hidden;
        padding-top: 6px;
        margin-left: 40%
      }

      #attributeVerification #attributeList ul li .attrEntry .verify:after {
        content: " ";
        display: block;
        clear: both
      }

      #attributeVerification #attributeList #verifying_blurb {
        margin-top: 15px
      }

      #attributeVerification .buttons #continue {
        height: 40px
      }

      #attributeVerification .buttons #cancel {
        font-size: 1em;
        left: 40%
      }
    }

    .panel,
    .panel_layout,
    .panel_layout_row,
    body,
    html {
      height: 100%
    }

    @media only screen and (max-width:600px) {
      img[data-tenant-branding-background=true] {
        display: none
      }

      .container {
        background-color: #fff
      }

      .container .row .panel-default {
        border-radius: 0;
        border: none;
        box-shadow: none
      }
    }

    /* endinject */
  </style>
  <style id="selfasserted">
    /* Following are generated styles via gulp. */
    /* inject: css */
    .self_asserted_container .row .panel-default {
      padding: 40px 30px;
      margin: auto;
      height: 100%;
      background-color: #fff
    }

    .self_asserted_container .row .panel-default .panel-body {
      margin: 0 auto;
      max-width: 380px;
      width: auto;
      padding: 0
    }

    .self_asserted_container .row .panel-default .panel-body img.companyLogo {
      display: block;
      margin: auto;
      height: 70px
    }

    .self_asserted_container .row .panel-default .panel-body .branding h1 {
      font-size: 1.5em;
      color: #505050
    }

    .self_asserted_container .row .panel-default .panel-body h3 {
      font-size: 1.5em;
      color: #505050;
      margin-top: 5px;
      margin-bottom: 30px
    }

    .self_asserted_container .row .panel-default .panel-body .intro {
      display: none;
      text-align: left;
      margin-top: 2rem
    }

    .self_asserted_container .row .panel-default .panel-body #api #attributeVerification .pageLevel {
      color: #b00;
      text-align: left
    }

    .self_asserted_container .row .panel-default .panel-body #api #attributeVerification .error {
      color: #b00;
      text-align: left;
      margin-bottom: 5px
    }

    .errorText {
      border: none;
      padding: 0;
      background: 0 0;
      color: #a61e0c
    }

    .self_asserted_container .row .panel-default .panel-body #api .verifying-modal {
      display: none;
      margin-top: 10px
    }

    #attributeVerification #attributeList ul {
      display: flex;
      flex-direction: column
    }

    #attributeVerification #attributeList ul li input {
      width: 100%
    }

    #attributeVerification #attributeList ul li input:invalid {
      outline: unset
    }

    #attributeVerification #attributeList ul li .helpText {
      display: none;
      color: #a61e0c;
      text-align: justify
    }

    #attributeVerification #attributeList ul li #email_success.verificationSuccessText {
      float: left
    }

    #attributeVerification #attributeList ul li #email_info {
      float: left
    }

    #attributeVerification #attributeList ul li .attrEntry.validate #email_fail_retry {
      float: left
    }

    #attributeVerification #attributeList ul li .attrEntry.validate #email_fail_server {
      float: left;
      text-align: left
    }

    #attributeVerification #attributeList ul li #email_fail_no_retry {
      float: left
    }

    #attributeVerification #attributeList ul li.Readonly div.attrEntry div:first-child {
      text-align: left
    }

    div#email_intro {
      width: 100%;
      float: left;
      text-align: left
    }

    div#email_info {
      text-align: left
    }

    #attributeVerification #attributeList ul li input[type=checkbox],
    #attributeVerification #attributeList ul li input[type=radio] {
      width: 24px !important;
      box-shadow: none;
      height: 24px;
      display: inline-block;
      float: left;
      clear: left;
      margin: .5rem
    }

    #attributeVerification #attributeList ul li input[type=checkbox].invalid {
      border: none
    }

    #attributeList ul li input[type=checkbox]+label {
      float: left;
      display: inline-block !important;
      margin: .8rem 0 0 .8rem;
      font-weight: 400
    }

    #attributeList ul li input[type=checkbox] {
      border: none;
      position: relative;
      width: 24px;
      height: 24px;
      font-size: 20px;
      margin: 0;
      display: inline-block;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      -o-user-select: none;
      user-select: none;
      -webkit-appearance: none;
      -moz-appearance: none;
      appearance: none;
      outline: 0;
      text-indent: 0
    }

    #attributeVerification #attributeList ul li input[type=checkbox]:focus {
      box-shadow: 0 0 0 2px #000;
      border-radius: .2rem
    }

    #attributeList ul li input[type=checkbox]:after {
      content: '';
      position: absolute;
      display: block;
      z-index: 1;
      width: 24px;
      height: 24px;
      border: 1px solid #cccccc;
      border-radius: 2px
    }

    #attributeList ul li input[type=checkbox]:before {
      background: #00f;
      background-size: 10px 8px;
      background-repeat: no-repeat;
      background-position: 3px 4px;
      position: absolute;
      left: 2px;
      z-index: 2;
      opacity: 0;
      width: 100%;
      height: 100%;
      color: #f6ac4f
    }

    #attributeList ul li input[type=checkbox]:checked:after {
      content: '\2713';
      position: absolute;
      box-sizing: border-box;
      padding-left: 4px;
      line-height: 23px;
      color: #fff;
      background-color: #212b36;
      top: 0;
      opacity: 1;
      left: 0;
      border: 1px solid #212b36;
      border-radius: 2px
    }

    #attributeList ul li input[type=radio]+label {
      float: left;
      display: inline-block !important;
      margin: .5rem;
      font-weight: 400
    }

    #attributeList ul li input[type=radio] {
      -webkit-appearance: none;
      appearance: none;
      width: 20px;
      height: 20px;
      border: 1px solid #dedede;
      border-radius: 50%;
      background-color: #fff;
      outline: 0
    }

    #attributeVerification #attributeList ul li input[type=radio]:focus {
      box-shadow: 0 0 0 3px #000
    }

    #attributeList ul li input[type=radio]:checked {
      background-color: #212b36;
      border: 1px solid #212b36
    }

    .attrEntry .verificationInfoText {
      margin-bottom: 5px
    }

    #attributeList ul li .attrEntry select:hover {
      border-color: #888
    }

    #attributeList ul li .attrEntry select option {
      font-weight: 400
    }

    .disclaimer-container {
      text-align: left;
      font-size: 10px
    }

    .disclaimer-container .disclaimer-link {
      display: block;
      width: fit-content
    }

    .disclaimer-container-page-level {
      margin-top: 10px
    }

    #attributeList ul li .buttons.verify label {
      float: left
    }

    .qrCodeControlContent img {
      margin-left: auto;
      margin-right: auto
    }

    .qrCodeInfo {
      text-align: left
    }

    /* endinject */
  </style>
  <style id="phonefactor">
    /* Following are generated styles via gulp. */
    /* inject: css */
    .buttons button:hover {
      background-image: none
    }

    .phonefactor_container .row .panel-default {
      margin: auto;
      height: 100%;
      background-color: #fff;
      min-width: 400px
    }

    .phonefactor_container .row .panel-body {
      margin: 0 auto;
      max-width: 400px;
      width: auto;
      padding: 4.2rem 1rem
    }

    .phonefactor_container .row .panel-body img {
      height: 70px;
      margin: 0 auto
    }

    button#sendCode,
    button#verifyCode,
    button#verifyPhone {
      margin-top: 2rem;
      width: 60%;
      height: 50px
    }

    #api #codeVerification {
      text-align: left;
      margin-top: 1rem
    }

    #api #codeVerification .actionLabel {
      margin-bottom: 1rem;
    }

    #api #codeVerification .actionLabel label {
      margin-bottom: 0px;
    }

    #api .working {
      display: none
    }

    #codeVerification #verificationCode {
      border-radius: .5rem;
      width: 100%;
      height: 50px;
      border: 1px solid #6a6a6a;
      text-indent: 20px;
      outline: 0
    }

    #codeVerification #verificationCode:focus {
      border: 2px solid #000
    }

    #codeVerification #retryCode {
      cursor: pointer;
      color: #337ab7;
      text-decoration: none
    }

    #codeVerification #retryCode:focus,
    #codeVerification #retryCode:hover {
      text-decoration: underline
    }

    #codeVerification .error.itemLevel p {
      color: #d30000
    }

    #api #phonefactor_initial .intro p {
      text-align: left
    }

    #api p {
      text-align: left;
      margin-top: 1rem
    }

    #api .intro p {
      margin-top: 20px
    }

    #api #phoneEntry.phoneEntry {
      margin-top: 10px
    }

    #api #phoneEntry.phoneEntry .input .code {
      text-align: left
    }

    #api #phoneEntry .input .code #countryCode {
      height: 50px;
      border-radius: .5rem;
      width: 100%;
      margin-bottom: 15px;
      padding-left: 15px
    }

    #api #phoneEntry .input .code #countryCode:focus {
      border: 2px solid #000
    }

    #api #phoneEntry .input .error.itemLevel p {
      color: #d30000
    }

    #api #phoneEntry.phoneEntry .input .number {
      text-align: left
    }

    #api #phoneEntry.phoneEntry .input .number #number {
      border-radius: .5rem;
      width: 100%;
      height: 50px;
      border: 1px solid #6a6a6a;
      text-indent: 20px;
      outline: 0
    }

    #api #phoneEntry.phoneEntry .input .number #number:focus {
      border: 2px solid #000
    }

    #api #phonefactor_initial #phoneEntry select#countryCode {
      height: 45px;
      border-radius: .5rem;
      width: 100%;
      margin-bottom: 15px;
      padding-left: 15px;
      border: 1px solid #6a6a6a
    }

    #api #phoneEntry select:hover {
      border-color: #888
    }

    #api #phoneEntry select option {
      font-weight: 400
    }

    #api #phoneEntry input#localNumber {
      border-radius: .5rem;
      width: 100%;
      height: 50px;
      border: 1px solid #6a6a6a;
      text-indent: 20px;
      outline: 0
    }

    #api #phonefactor_initial #phoneEntry input#localNumber:focus {
      border: 2px solid #000
    }

    .phoneNumbers .phoneNumber .displayName,
    .phoneNumbers .phoneNumber .type {
      display: none
    }

    .phoneNumbers .phoneNumber .number,
    .phoneNumbers .phoneNumber label[for="1"] {
      margin-top: 10px
    }

    /* endinject */
  </style>
  <script defer>
    const makeIntroVisible = function (apiElement, introElement) {
      console.log('searching for Phonefactor screen');
      if (apiElement && apiElement.getAttribute("data-name") === "Phonefactor") {
        console.log('found phonefactor');
        introElement.style.display = "block";
        //Stop observing once the element is made visible
        observer.takeRecords();
        observer.disconnect();
      };
    };
    const showContinueButton = function (apiElement, verificationText, continueButton) {
      console.log('searching for SelfAsserted screen');
      if (apiElement && apiElement.getAttribute("data-name") === "SelfAsserted") {
        if (verificationText.style.display === "inline") {
          continueButton.style.display = "inline";
          continueButton.style.marginTop = "-30px";
          //Stop observing once the target element has been modified
          observer.takeRecords();
          observer.disconnect();
        }
      }
    };
    // Callback function to execute when mutations are observed
    const callBack = function (mutationsList, observer) {
      const apiElement = document.getElementById('api');
      const introElement = document.querySelector('.self_asserted_container .row .panel-default .panel-body .intro');
      const verificationText = document.querySelector('#attributeVerification #attributeList ul li.Readonly .attrEntry .verificationSuccessText');
      const continueButton = document.querySelector('#attributeVerification button#continue');
      if (apiElement && introElement) {
        console.log('api & introElement found');
        makeIntroVisible(apiElement, introElement);
      }
      if (verificationText && continueButton) {
        console.log('found verificationText and continueButton');
        showContinueButton(apiElement, verificationText, continueButton);
      }
    };
    const config = { attributes: true, childList: true, subtree: true };
    // Create an observer instance linked to the callback function
    const observer = new MutationObserver(callBack);
    // Start observing the target node for configured mutations
    observer.observe(document, config);
  </script>
</head>

<body>
  <header>
    <div class="relic-container">
      <div class="logo">
        <img src="https://relic-facility-portal.vercel.app/images/relic-care-logo-dark.png" alt="RELIC CARE">
      </div>
      <nav>
        <div class="dropdown">
          <a href="#" class="dropbtn">Relic AI</a>
          <div class="dropdown-content">
            <a target='_blank'
              href="https://www.reliccare.com/ai-for-staff-utilization-in-healthcare-facilities">Optimize
              Staff Utilization</a>
            <a target='_blank'
              href="https://www.reliccare.com/conversational-ai-for-language-services-in-healthcare">Language
              Services</a>
            <a target='_blank' href="https://www.reliccare.com/ai-for-regulatory-compliance-in-healthcare">Regulatory
              Compliance</a>
          </div>
        </div>
        <a target='_blank' href="https://www.reliccare.com/resources">Blogs</a>
        <div class="dropdown">
          <a href="#" class="dropbtn">About</a>
          <div class="dropdown-content">
            <a target='_blank' href="https://www.reliccare.com/about#contact">Contact</a>
          </div>
        </div>
        <a href="https://www.reliccare.com/invitation" class="get-invited">Get Invited</a>
      </nav>
    </div>
  </header>

  <main>
    <div class="container  self_asserted_container " role="presentation">
      <div class="row">
        <div class="col-lg-6" style="max-width: 400px;">
          <div class="panel panel-default" style="border-radius: 8px;">
            <div class="panel-body">
              <div class="branding">
                <h1
                  style="text-align: center; font-size: 24px; margin-bottom: 24px; overflow-wrap: break-word; hyphens: manual; text-overflow: unset; color: #94A72D; font-weight: 600;">
                  Welcome Residents!</h1>
              </div>
              <div id="api" role="main">
              </div>
              <!-- <div id="api" data-name="SelfAsserted" role="main">
                <div class="buttons">
                </div>
                <div class="intro">
                  <p>Please provide the following details.</p>
                </div>
                <form id="attributeVerification" action="JavaScript:void(0);">
                  <div id="passwordEntryMismatch" class="error pageLevel" style="display: none;" aria-hidden="true">The
                    password entry fields do not match. Please enter the same password in both fields and try again.
                  </div>
                  <div id="requiredFieldMissing" class="error pageLevel" style="display: none;" aria-hidden="true">A
                    required field is missing. Please fill out all required fields and try again.</div>
                  <div id="fieldIncorrect" class="error pageLevel" style="display: none;" aria-hidden="true">One or more
                    fields are filled out incorrectly. Please check your entries and try again.</div>
                  <div id="claimVerificationServerError" class="error pageLevel" style="display: none;"
                    aria-hidden="true"></div>
                  <div id="attributeList" class="attr">
                    <ul>
                      <li class="Readonly">
                        <div class="attrEntry">
                          <div>
                            <div class="verificationInfoText" id="readOnlyEmail_intro" style="display: inline;"
                              aria-hidden="false" role="alert" aria-live="polite">We have the following email on record
                              for you. We can send a code via email to authenticate you.</div>
                            <div class="verificationInfoText" id="readOnlyEmail_info" style="display:none"
                              aria-hidden="true" role="alert">Verification code has been sent to your inbox. Please copy
                              it to the input box below.</div>
                            <div class="verificationSuccessText" id="readOnlyEmail_success" style="display:none"
                              aria-hidden="true" role="alert">E-mail address verified. You can now continue.</div>
                            <div class="verificationErrorText error" id="readOnlyEmail_fail_retry" style="display:none"
                              aria-hidden="true" role="alert">That code is incorrect. Please try again.</div>
                            <div class="verificationErrorText error" id="readOnlyEmail_fail_no_retry"
                              style="display:none" aria-hidden="true" role="alert">You've made too many incorrect
                              attempts. Please try again
                              later.</div>
                            <div class="verificationErrorText error" id="readOnlyEmail_fail_throttled"
                              style="display:none" aria-hidden="true" role="alert">There have been too many requests to
                              verify this email address. Please wait a while, then try again.</div>
                            <div class="verificationErrorText error" id="readOnlyEmail_fail_code_expired"
                              style="display:none" aria-hidden="true" role="alert">That code is expired. Please request
                              a
                              new code.</div>
                            <div class="verificationErrorText error" id="readOnlyEmail_fail_server" style="display:none"
                              aria-hidden="true" role="alert">We are having trouble verifying your email address. Please
                              enter a valid email address and try again.</div>
                            <div class="verificationErrorText error" id="readOnlyEmail_incorrect_format"
                              style="display:none" aria-hidden="true" role="alert">Incorrect format.</div>
                          </div><label id="readOnlyEmail_label" for="readOnlyEmail"></label>
                          <div class="error itemLevel" role="alert"></div><input id="readOnlyEmail" class="textInput"
                            type="text" value="p**********@reliccare.com" aria-required="true" disabled=""
                            readonly="readonly" autofocus="autofocus"><a class="helpLink tiny" href="javascript:void(0)"
                            data-help="">What is this?</a>
                          <div class="buttons verify" data-claim_id="readOnlyEmail">
                            <div class="working" id="readOnlyEmail_ver_wait" aria-label="Please wait" aria-hidden="true"
                              style="display: none;"></div><label id="readOnlyEmail_ver_input_label"
                              for="readOnlyEmail_ver_input" aria-hidden="true" style="display: none;">Verification
                              code</label><input class="verifyInput" id="readOnlyEmail_ver_input" type="text"
                              autocomplete="off" placeholder="Verification code" style="display: none;"
                              aria-hidden="true"><button class="sendButton" id="readOnlyEmail_ver_but_send"
                              type="button" aria-label="Send verification code" style="display: inline;"
                              aria-hidden="false">Send
                              verification code</button><button class="verifyButton" id="readOnlyEmail_ver_but_verify"
                              type="button" aria-label="Verify code" style="display: none;" aria-hidden="true">Verify
                              code</button><button class="sendButton" id="readOnlyEmail_ver_but_resend" type="button"
                              aria-label="Send new code" style="display: none;" aria-hidden="true">Send new
                              code</button><button class="defaultButton" id="readOnlyEmail_ver_but_default"
                              type="button" aria-label="Default" style="display: none;">Default</button>
                          </div>
                        </div>
                      </li>
                    </ul>
                  </div>
                  <div class="buttons">
                    <button id="continue" type="submit" form="attributeVerification" aria-disabled="false"
                      aria-label="Continue">Continue</button><button id="cancel" aria-label="Cancel"
                      formnovalidate="">Cancel</button>
                  </div>

                  <div class="verifying-modal">
                    <div id="verifying_blurb"></div>
                  </div>
                </form>
              </div> -->
              <!-- <div id="api" data-name="Phonefactor" role="main">
                <span id="screen_reader_msg" role="alert" aria-label="" aria-hidden="true" style="display:none"></span>
                <div class="buttons">
                  <button id="cancel">Cancel</button>
                </div>
                <div class="intro">
                  <p>We have the following number on record for you. We can send a code via SMS or phone to authenticate
                    you.</p>
                </div>
                <div class="error pageLevel" id="errorMessage" style="display:none" aria-hidden="true">
                  <p role="alert">The verification code you have entered does not match our records. Please try again,
                    or request a new code.</p>
                </div>

                <div class="phoneNumbers" id="phoneNumbers">
                  <div class="phoneNumber">
                    <div class="type">Phone Number</div>
                    <div class="number">XXX-XXX-5943</div>
                  </div>
                </div>

                <div id="codeVerification" style="display:none" aria-hidden="true">
                  <div class="actionLabel">
                    <label for="verificationCode">Enter your verification code below, or</label>
                    <a id="retryCode" tabindex="1">Send a new code</a>
                  </div>
                  <div class="error itemLevel" aria-hidden="true">
                    <p role="alert"></p>
                  </div>
                  <input type="text" role="textbox" id="verificationCode" maxlength="6" autocomplete="false"
                    pattern="\d{6}" title="Please enter the verification code you received" aria-required="true">
                </div>

                <div class="working"></div>

                <div class="buttons">
                  <button id="sendCode" autofocus="">Send Code</button><button id="verifyCode" class="disabled"
                    aria-disabled="true" style="display: none;">Verify Code</button><button id="verifyPhone">Call
                    Me</button><button id="continuePhone" class="disabled" aria-disabled="true"
                    style="display: none;">Continue</button>
                </div>

              </div> -->
            </div>
          </div>
        </div>
      </div>
    </div>
  </main>
  </div>
</body>

</html>