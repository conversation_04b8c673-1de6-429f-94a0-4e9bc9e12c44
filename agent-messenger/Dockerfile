# syntax=docker/dockerfile:1
FROM node:20-slim AS base
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
RUN corepack enable
COPY agent-messenger /services/agent-messenger
COPY relic-ui /services/relic-ui

FROM base AS relic-ui-build
WORKDIR /services/relic-ui
RUN pnpm install
RUN pnpm run build

FROM base AS prod-deps
WORKDIR /services/agent-messenger
RUN --mount=type=cache,id=pnpm,target=/pnpm/store pnpm install --prod --frozen-lockfile

FROM base AS build
COPY --from=relic-ui-build /services/relic-ui /services/relic-ui
WORKDIR /services/agent-messenger
RUN --mount=type=cache,id=pnpm,target=/pnpm/store pnpm install --frozen-lockfile
RUN pnpm run build

FROM base
COPY --from=prod-deps /services/agent-messenger/node_modules /app/node_modules
COPY --from=build /services/agent-messenger/dist /app/dist
COPY agent-messenger/newrelic.js /app/newrelic.js
WORKDIR /app
EXPOSE 3001
CMD [ "node", "-r", "newrelic", "dist/main.js" ]