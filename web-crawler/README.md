## Reliccare Web Crawler (Apify Actor)

This Apify actor is designed to scrape websites using Playwright and Crawlee. 
Features include:

- Extraction of relevant content using [readabilityJS](https://github.com/mozilla/readability)
- Storing extracted content as blob in Azure Storage (relicstorage) for indexing
- Saving each page as a TrainingContent in node-services
- Follow and scrape embedded links up to a specified max depth.


## Getting started

### Run the actor locally

For complete information [see this article](https://docs.apify.com/platform/actors/development#build-actor-locally). To run the actor use the following command:

1. Install apify-cli [see installation guide](https://docs.apify.com/academy/tools/apify-cli#installing)
```bash
npm i -g apify-cli
```

2. Install dependencies.
```bash
cd web-crawler
pnpm install
```

3. Update INPUT.json with valid request input, see `.actor/input_schema.json` and/or `CrawlRequest` type.

4. Run Actor

a. Using apify client
```bash
// install apify client
npm install -g apify-cli@latest

// run actor
cd web-crawler
apify run --purge --input-file=./INPUT.json
```

b. Using pnpm
```
cd web-crawler
pnpm install

// run actor
pnpm dev

```


## Deploy to Apify

### Connect Git repository to Apify

If you've created a Git repository for the project, you can easily connect to Apify:

1. Go to [Actor creation page](https://console.apify.com/actors/new)
2. Click on **Link Git Repository** button
3. To deploy the monorepo using, refer to following links:
  - [Github integration](https://docs.apify.com/platform/integrations/github)
  - [Git source guide](https://docs.apify.com/platform/actors/development/deployment/source-types#git-repository)
  - [Git repository CI guide](https://docs.apify.com/platform/actors/development/deployment/continuous-integration#github-integration)
  - Dockerfile: services/app/web-crawler


## Trigger the actor from node-services

### Setup the crawler's default configuration in `crawlers` mongo collection
Important fields:
- `apify.actorId` : actor's ID in apify platform
- `configuration.isDryRun`: if true, crawler will only perform scraping and crawl. Scraped contents are not saved in storage. Node-services is not notified.
- `configuration.removeElementCssSelector` : css elements to be removed during scraping
- `configuration.maxUrlsToCrawl` : max number of requests (URLs) to be processed
- `configuration.maxDepth`: max depth for crawling
- `configuration.useReadableContent` : if true, the Actor will parse web pages using readabilitJS whenever possible

```
//Sample INPUT.json
{
    "isDryRun": true,
    "crawlStorage": {},
    "relicMessageQueueParams": {},
    "urls": [
        {
            "id": "c30519a6-2e73-41e5-92c5-cf72301e382f",
            "sourceDocumentId": "c30519a6-2e73-41e5-92c5-cf72301e382f",
            "url": "https://www.cdc.gov/early-care-education/php/farm-to-ece/index.html",
            "depth": 0,
            "children": [
                {
                    "url": "https://www.cdc.gov/vaccines-adults/recommended-vaccines/index.html"
                }
            ]
        },

        {
            "id": "9a1a0b31-f01c-4d4c-aea2-525311f57bf0",
            "sourceDocumentId": "9a1a0b31-f01c-4d4c-aea2-525311f57bf0",
            "url": "https://www.cdc.gov/",
            "depth": 0
        }
    ],
    "options": {
        "ignoreIframes": true,
        "removeElementCssSelector": [
            "nav",
            "footer",
            "script",
            "style",
            "noscript",
            "svg",
            "[role=\"alert\"",
            "[role=\"banner\"]",
            "[role=\"dialog\"]",
            "[role=\"alertdialog\"]",
            "[role=\"region\"][aria-label*=\"skip\" i]",
            "[aria-modal=\"true\"]",
            "[class=\"nav-recent-changes\"]",
            "[href=\"/recent-changes\"]",
            "aside",
            "[id=\"supersizeme\"]",
            "header"
        ],
        "maxUrlsToCrawl": 5,
        "maxDepth": 5,
        "useReadableContent": true,
        "crawlEmbeddedLinks": true,
        "relevanceParams": {
            "relevanceApiEndpoint": "https://agent-messenger.calmbay-07fbcdc7.eastus.azurecontainerapps.io/trainings/check-relevance"
        }
    }
}
```

### Send a crawl request from node-services
To trigger the actor from node-services, User needs to send a request to [Create TrainingContent API](https://node-services.calmbay-07fbcdc7.eastus.azurecontainerapps.io/docs/static/index.html#/Training%20Modules/post_api_trainings_modules__id__contents)

## Trigger the actor using APIFY endpoints

### Locate the actor's API endpoints
1. Visit the Actor's Details page in Apify Platform
2. On the upper-right corner of the page, click `API` > `API Endpoints`, a popup will show.
3. See section `Run Actor` for the API.
4. Send a POST request to the endpoint. For the payload, refer to `input_schema.json` and/or `CrawlRequest` type.

### Send a request using `Run Actor` endpoint
Sample CURL requests
```curl
curl --location 'https://api.apify.com/v2/acts/reliccare~reliccare-web-crawler/runs?token=apify_api_token' \
--header 'Content-Type: application/json' \
--data '{
    "isDryRun": true,
    "crawlStorage": {},
    "relicMessageQueueParams": {},
    "urls": [
        {
            "id": "c30519a6-2e73-41e5-92c5-cf72301e382f",
            "sourceDocumentId": "c30519a6-2e73-41e5-92c5-cf72301e382f",
            "url": "https://www.cdc.gov/early-care-education/php/farm-to-ece/index.html",
            "depth": 0,
            "children": [
                {
                    "url": "https://www.cdc.gov/vaccines-adults/recommended-vaccines/index.html"
                }
            ]
        },

        {
            "id": "9a1a0b31-f01c-4d4c-aea2-525311f57bf0",
            "sourceDocumentId": "9a1a0b31-f01c-4d4c-aea2-525311f57bf0",
            "url": "https://www.cdc.gov/",
            "depth": 0
        }
    ],
    "options": {
        "ignoreIframes": true,
        "removeElementCssSelector": [
            "nav",
            "footer",
            "script",
            "style",
            "noscript",
            "svg",
            "[role=\"alert\"",
            "[role=\"banner\"]",
            "[role=\"dialog\"]",
            "[role=\"alertdialog\"]",
            "[role=\"region\"][aria-label*=\"skip\" i]",
            "[aria-modal=\"true\"]",
            "[class=\"nav-recent-changes\"]",
            "[href=\"/recent-changes\"]",
            "aside",
            "[id=\"supersizeme\"]",
            "header"
        ],
        "maxUrlsToCrawl": 5,
        "maxDepth": 5,
        "useReadableContent": true,
        "crawlEmbeddedLinks": true,
        "relevanceParams": {
            "relevanceApiEndpoint": "https://agent-messenger.calmbay-07fbcdc7.eastus.azurecontainerapps.io/trainings/check-relevance"
        }
    }
}'
```

## Documentation References

To learn more about Apify and Actors, take a look at the following resources:

- [Apify SDK for JavaScript documentation](https://docs.apify.com/sdk/js)
- [Apify Platform documentation](https://docs.apify.com/platform)

To learn more about API endpoints
- [Running via Apify API](https://docs.apify.com/platform/actors/running#running-via-apify-api)
- [Run Actor and retrieve data via API](https://docs.apify.com/academy/api/run-actor-and-retrieve-data-via-api)
