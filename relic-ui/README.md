# Relic UI

Relic UI is a package that wraps around @azure/communication-chat to provide a customized chat experience.

## Setup

### Prerequisites
* Node.js 
* pnpm


### Installation


Navigate to the project directory:

```bash
$ cd relic-ui
$ pnpm install
$ pnpm run build    # Compile TypeScript files (This will generate the required JavaScript files in the dist directory)
```

## Usage

(TODO: Add instructions on how to use this package in other projects or any other usage details.)


## Testing

(TODO: Add instructions on how to run tests once you have them.)

## Release

```bash
% pnpm run format
% pnpm run lint
```
