import * as React from 'react';
import {
  SaveButton,
  Create as RefineCreate,
  CreateProps as RefineCreateProps,
} from '@refinedev/mui';

import Button from '@mui/material/Button';

// ----------------------------------------------------------------------

type CreateProps = RefineCreateProps & {
  onClose?: VoidFunction;
};

export const Create: React.FC<CreateProps> = ({ children, onClose, ...other }) => (
  <RefineCreate
    breadcrumb={null}
    goBack={null}
    wrapperProps={{
      sx: {
        borderRadius: 0,
        display: 'flex',
        boxShadow: 'none',
        flexDirection: 'column',
      },
    }}
    contentProps={{
      sx: {
        flex: '1 1 auto',
        overflowY: 'auto',
      },
    }}
    headerProps={{
      sx: {
        pb: 3,
        borderBottom: (theme) => `solid 1px ${theme.palette.divider}`,
        '& .refine-pageHeader-title': {
          typography: 'h6',
        },
      },
    }}
    headerButtonProps={{
      sx: {
        '& a': {
          color: 'inherit',
        },
      },
    }}
    footerButtons={({ saveButtonProps }) => (
      <>
        {onClose && (
          <Button variant="outlined" onClick={onClose}>
            Cancel
          </Button>
        )}
        <SaveButton {...saveButtonProps} />
      </>
    )}
    footerButtonProps={{
      sx: {
        p: 2,
        justifyContent: 'flex-end',
        borderTop: (theme) => `solid 1px ${theme.palette.divider}`,
      },
    }}
    {...other}
  >
    {children}
  </RefineCreate>
);
