import React, { useState } from 'react';
import { Link } from 'react-router-dom';

import {
  Box,
  Menu,
  Button,
  MenuItem,
  Container,
  Typography,
} from '@mui/material';

import { useRouter } from 'src/routes/hooks';

import RelicCareIcon from '/images/relic-care-logo-dark.png';

interface MenuItem {
  label: string;
  href?: string;
  children?: MenuItem[];
}

// JSON data for menu
const menuData: MenuItem[] = [
  {
    label: 'Relic AI',
    children: [
      {
        label: 'Optimize Staff Utilization',
        href: 'https://www.reliccare.com/ai-for-staff-utilization-in-healthcare-facilities',
      },
      {
        label: 'Language Services',
        href: 'https://www.reliccare.com/conversational-ai-for-language-services-in-healthcare',
      },
      {
        label: 'Regulatory Compliance',
        href: 'https://www.reliccare.com/ai-for-regulatory-compliance-in-healthcare',
      },
    ],
  },
  {
    label: 'Blogs',
    href: 'https://www.reliccare.com/resources',
  },
  {
    label: 'About',
    children: [
      { label: 'Contact', href: 'https://www.reliccare.com/about#contact' },
    ],
  },
];

const WebsiteHeader = () => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [currentMenu, setCurrentMenu] = useState<MenuItem[]>([]);

  const handleClose = () => {
    setAnchorEl(null);
    setCurrentMenu([]);
  };

  const handleMouseEnter = (
    event: React.MouseEvent<HTMLDivElement, MouseEvent>,
    children: MenuItem[] = [],
  ) => {
    setAnchorEl(event.currentTarget);
    setCurrentMenu(children);
  };

  const handleMouseLeave = () => {
    setAnchorEl(null);
    setCurrentMenu([]);
  };

  const handleMenuClick = (href?: string) => {
    if (href) {
      window.location.href = href;
      handleClose();
    }
    return;
  };

  return (
    <Container maxWidth="md" sx={{ p: 1 }}>
      <Box sx={{ justifyContent: 'space-between', display: 'flex' }}>
        <Typography
          variant="h6"
          sx={{ fontWeight: 'bold' }}
          component={Link}
          to={'https://www.reliccare.com'}
        >
          <Box
            component="img"
            src={RelicCareIcon}
            alt="logo"
            sx={{ width: '250px', height: 'auto' }}
          />
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          {menuData.map((item, index) => (
            <React.Fragment key={index}>
              <Box
                key={index}
                onMouseEnter={event => {
                  if (item.children) {
                    handleMouseEnter(event, item.children);
                  }
                }}
                onClick={event => {
                  if (item.children && item.children.length > 0) {
                    handleMouseEnter(event, item.children);
                  } else {
                    handleMenuClick(item.href);
                  }
                }}
                sx={{
                  marginRight: 4,
                  textTransform: 'none',
                  cursor: 'pointer',
                }}
              >
                <Typography
                  sx={{
                    cursor: 'pointer',
                  }}
                >
                  {item.label}
                </Typography>
              </Box>
            </React.Fragment>
          ))}

          <Menu
            anchorEl={anchorEl}
            open={Boolean(anchorEl)}
            onClose={handleClose}
            MenuListProps={{
              onMouseLeave: handleMouseLeave,
            }}
          >
            {currentMenu.length > 0 &&
              currentMenu.map((subItem, subIndex) => (
                <MenuItem
                  key={subIndex}
                  onClick={() => handleMenuClick(subItem.href)}
                >
                  {subItem.label}
                </MenuItem>
              ))}
          </Menu>

          <Button
            onClick={() => {
              handleMenuClick('https://www.reliccare.com/invitation');
            }}
            variant="contained"
            sx={{
              backgroundColor: '#000',
              borderRadius: '10px',
              padding: '8px 16px',
              textTransform: 'none',
              '&:hover': {
                backgroundColor: '#333',
              },
            }}
          >
            Get Invited
          </Button>
        </Box>
      </Box>
    </Container>
  );
};

export default WebsiteHeader;
