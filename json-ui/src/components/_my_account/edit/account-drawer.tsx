import { useTranslate } from '@refinedev/core';
import { useAutocomplete } from '@refinedev/mui';
import { FieldValues, UseFormHandleSubmit } from 'react-hook-form';
import { IUser } from 'relic-ui';

import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import CircularProgress from '@mui/material/CircularProgress';
import Drawer from '@mui/material/Drawer';
import IconButton from '@mui/material/IconButton';

import Iconify from 'src/components/iconify';
import { Edit } from 'src/components/refine-customs/edit';

import AccountDetailsView from './account-details-view';

const DRAWER_WIDTH = {
  lg: 380,
  md: 320,
  sm: 280,
  xs: '100%',
};

interface AccountDrawerProps {
  showDrawer: boolean;
  setShowEdit: React.Dispatch<React.SetStateAction<boolean>>;
  onFinish: (values: FieldValues) => Promise<void>;
  saveButtonProps: {
    disabled: boolean;
    onClick: (e: React.BaseSyntheticEvent) => void;
  };
  handleSubmit: UseFormHandleSubmit<FieldValues>;
  showEdit: boolean;
  currentUser: IUser;
  isLoading: boolean;
  popoverDetails: any;
  renderEditForm: JSX.Element;
}

const AccountDrawer: React.FC<AccountDrawerProps> = ({
  showDrawer,
  setShowEdit,
  onFinish,
  saveButtonProps,
  handleSubmit,
  showEdit,
  currentUser,
  isLoading,
  popoverDetails,
  renderEditForm,
}) => {
  const translate = useTranslate();

  const onSubmit = async (values: FieldValues) => {
    await onFinish(values);
    setShowEdit(false);
  };

  const organizationId =
    currentUser?.userIdentity?.portalIdentity?.organizationId;

  const { autocompleteProps: organizationAutocompleteProps } = useAutocomplete({
    resource: 'organizations',
    defaultValue: organizationId,
  });

  if (!currentUser) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Drawer
      anchor="right"
      variant="persistent"
      open={showDrawer}
      PaperProps={{
        sx: {
          top: 8 * 18.5,
          boxSizing: 'border-box',
          width: {
            xs: DRAWER_WIDTH.xs,
            sm: DRAWER_WIDTH.sm,
            md: DRAWER_WIDTH.md,
            lg: DRAWER_WIDTH.lg,
          },
          zIndex: 999,
        },
      }}
      sx={{
        flexShrink: 0,
        width: {
          xs: DRAWER_WIDTH.xs,
          sm: DRAWER_WIDTH.sm,
          md: DRAWER_WIDTH.md,
          lg: DRAWER_WIDTH.lg,
        },
      }}
    >
      <Edit
        saveButtonProps={{
          ...saveButtonProps,
          onClick: handleSubmit(onSubmit),
        }}
        wrapperProps={{
          sx: {
            flexGrow: 1,
            borderRadius: 0,
            boxShadow: 'none',
            overflowY: 'auto',
            mb: `${8 * 18.5}px`,
          },
        }}
        headerButtonProps={{
          sx: { display: 'none' },
        }}
        headerProps={{
          title: translate('my-account.title'),
          action: (
            <>
              {showEdit ? (
                <Button
                  variant="outlined"
                  startIcon={<Iconify icon={'eva:close-fill'} />}
                  onClick={() => setShowEdit(false)}
                >
                  {translate('buttons.cancel')}
                </Button>
              ) : (
                <IconButton onClick={popoverDetails.onOpen}>
                  <Iconify icon="eva:more-vertical-fill" />
                </IconButton>
              )}
            </>
          ),
        }}
        footerButtonProps={{
          ...(!showEdit && {
            sx: { display: 'none' },
          }),
        }}
      >
        {!isLoading ? (
          <AccountDetailsView
            currentUser={currentUser}
            showEdit={showEdit}
            organizationAutocompleteProps={organizationAutocompleteProps}
          />
        ) : (
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              minHeight: '50vh',
            }}
          >
            <CircularProgress color="inherit" />
          </Box>
        )}
        {showEdit && !isLoading && renderEditForm}
      </Edit>
    </Drawer>
  );
};

export default AccountDrawer;
