import { agentType } from 'relic-ui';
import { List } from '@refinedev/mui';
import React, { useMemo, useEffect } from 'react';
import {
  useList,
  CrudFilter,
  useOnError,
  useTranslate,
  useGetIdentity,
} from '@refinedev/core';
import type {
  IUser,
  RelicPatient,
  IUserIdentity,
  RelicOrganization,
  RelicChatParticipant,
} from 'relic-ui';

import { Box, useMediaQuery } from '@mui/material';
import GroupAddIcon from '@mui/icons-material/GroupAdd';
import AssignmentIndIcon from '@mui/icons-material/AssignmentInd';
import {
  GridColDef,
  DataGridPro,
  gridClasses,
  DataGridProProps,
} from '@mui/x-data-grid-pro';

import { useRouter } from 'src/routes/hooks';

import { useSearch } from 'src/hooks/use-search';

import { NewButton } from 'src/components/refine-customs/new-btn';
import { DataGridToolbar } from 'src/components/list/data-grid-toolbar';

interface ThreadListProps extends DataGridProProps {
  currentOrganization?: RelicOrganization;
  currentPatient?: RelicPatient;
  currentIdentity?: IUserIdentity;
  setFilters: ((filters: CrudFilter[]) => void) &
    ((setter: (prevFilters: CrudFilter[]) => CrudFilter[]) => void);
  filters: CrudFilter[];
  title?: string;
}

export default function ListThreads({
  currentOrganization,
  currentPatient,
  currentIdentity,
  setFilters,
  title,
  filters,
  ...dataGridProps
}: ThreadListProps) {
  const translate = useTranslate();

  const { globalSearch } = useSearch();

  const { mutate: onError } = useOnError();

  const [viewButtonEl, setViewButtonEl] =
    React.useState<HTMLButtonElement | null>(null);

  const isSmallScreen = useMediaQuery('(max-width:600px)');

  const router = useRouter();

  const { data: currentUser } = useGetIdentity<IUser>();

  const resourceType = currentUser?.userIdentity?.resourceType;

  const { data: relatedOrgs, isLoading: organizationLoading } = useList({
    resource: 'organizations',
    queryOptions: {
      queryKey: ['organizations'],
    },
  });

  useEffect(() => {
    try {
      const filter: CrudFilter[] = filters;
      if (globalSearch.length > 0) {
        filter.push({
          field: 'search',
          value: globalSearch,
          operator: 'contains',
        });
      }
      setFilters(filter);
    } catch (error) {
      onError(error);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [globalSearch, currentUser]);

  const columns = React.useMemo<GridColDef[]>(
    () => [
      {
        field: 'ai-participant',
        headerName: translate('threads.fields.ai-participant'),
        flex: 1,
        renderCell: ({ row }) => (
          <Box component="span">
            {row.participants?.find(
              (p: RelicChatParticipant) => p?.type === 'Patient Agent',
            )?.displayName ||
              row.participants?.find(
                (p: RelicChatParticipant) => p?.type === 'Staff Agent',
              )?.displayName ||
              row.participants?.find(
                (p: RelicChatParticipant) => p?.type === 'System Agent',
              )?.displayName}
          </Box>
        ),
      },
      {
        field: 'participants',
        headerName: translate('threads.fields.participant'),
        flex: 1,
        sortable: false,
        renderCell: ({ row }) => {
          const patientParticipant: RelicChatParticipant =
            row.participants?.find(
              (p: RelicChatParticipant) => p?.resourceType === 'Patient',
            );
          const staffParticipant: RelicChatParticipant = row.participants?.find(
            (p: RelicChatParticipant) =>
              p?.resourceType === 'Practitioner' && !p?.type && !p?.role,
          );

          const displayIcon =
            resourceType === 'Practitioner' && patientParticipant ? (
              <GroupAddIcon fontSize="small" />
            ) : (
              <AssignmentIndIcon fontSize="small" />
            );
          const displayName =
            resourceType === 'Practitioner' && patientParticipant
              ? patientParticipant?.displayName
              : staffParticipant?.displayName;

          return (
            <Box
              component="span"
              sx={{
                display: 'flex',
                gap: 1,
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              {displayIcon}
              {displayName}
            </Box>
          );
        },
      },
      {
        field: 'threadSubject.title',
        headerName: translate('threads.fields.title'),
        flex: 1,
        sortable: false,
        renderCell: ({ row }) => {
          return <Box component="span">{row.threadSubject?.title}</Box>;
        },
      },
      {
        field: 'threadSubject.patientLanguage',
        headerName: translate('threads.fields.language'),
        flex: 1,
        sortable: false,
        renderCell: ({ row }) => {
          return (
            <Box component="span">
              {row.threadSubject?.patientLanguage
                ? row.threadSubject.patientLanguage?.display
                : 'NA'}
            </Box>
          );
        },
      },
      {
        field: 'threadSubject.organizationId',
        headerName: translate('threads.fields.organization'),
        flex: 1,
        sortable: false,
        renderCell: ({ row }) => (
          <Box component="span">
            {organizationLoading ? (
              <>Loading...</>
            ) : (
              relatedOrgs?.data?.find(
                (org: any) => org.id === row.threadSubject?.organizationId,
              )?.name || ''
            )}
          </Box>
        ),
      },
      // {
      //   field: 'inviteVia',
      //   headerName: translate('threads.fields.invited-via'),
      //   minWidth: 200,
      //   sortable: false,
      //   renderCell: ({ value }) => (
      //     <Box component="span">{value ?? 'none'}</Box>
      //   ),
      // },
      // {
      //   field: 'status',
      //   headerName: translate('threads.fields.status'),
      //   minWidth: 200,
      //   sortable: false,
      //   renderCell: ({ value }) => (
      //     <Label
      //       variant="soft"
      //       color={value === 'active' ? 'success' : 'error'}
      //     >
      //       {value}
      //     </Label>
      //   ),
      // },
    ],
    [organizationLoading, relatedOrgs?.data, resourceType, translate],
  );

  const displayColumns = useMemo(() => {
    let filteredColumns = columns;

    if (isSmallScreen) {
      filteredColumns = columns.filter(
        col => col.field === 'ai-participant' || col.field === 'participants',
      );
    }
    if (currentOrganization && currentOrganization.id) {
      filteredColumns = filteredColumns.filter(
        col => col.field !== 'threadSubject.organizationId',
      );
    }

    if (currentPatient && currentPatient.id) {
      filteredColumns = filteredColumns.filter(
        col => col.field !== 'participants',
      );
    }

    return filteredColumns;
  }, [columns, currentOrganization, currentPatient, isSmallScreen]);

  const handleOnCellClick = React.useCallback(
    (params: any) => {
      if (params.field === 'actions') return;

      const { row } = params;
      const { threadSubject, threadId } = row;

      const title = threadSubject?.title;

      router.push(
        `/threads/show/${threadId}?name=${title}&threadId=${threadId}`,
      );
    },
    [router],
  );

  const Toolbar = React.useCallback(
    () => (
      <DataGridToolbar
        title={title ?? translate('threads.threads')}
        exportButton={false}
        actions={
          <>
            <NewButton
              href={'/threads/create'}
              label={translate('buttons.create')}
            />
          </>
        }
      />
    ),
    [title, translate],
  );

  function renderGrid() {
    return (
      <Box
        sx={{
          height:
            currentOrganization?.id || currentPatient?.id || currentIdentity?.id
              ? 'calc(100vh - 215px)'
              : '100%',
        }}
      >
        <DataGridPro
          {...dataGridProps}
          getRowId={row => row._id}
          pagination
          sx={{
            '& .MuiDataGrid-cell': {
              display: 'flex',
              alignSelf: 'center',
              height: '100%',
              border: 'none',
            },
          }}
          disableColumnMenu
          columns={displayColumns}
          localeText={{ toolbarColumns: 'View' }}
          onCellClick={handleOnCellClick}
          slots={
            (currentOrganization && currentOrganization.id) ||
            (currentPatient && currentPatient.id) ||
            (currentIdentity && currentIdentity.id)
              ? undefined
              : {
                  toolbar: Toolbar,
                }
          }
          slotProps={{
            panel: {
              anchorEl: viewButtonEl,
            },
            toolbar: {
              onClick: (e: React.MouseEvent) => {
                e.stopPropagation();
                setViewButtonEl(null);
              },
            },
          }}
        />
      </Box>
    );
  }

  function renderGridTransHeader() {
    return (
      <List
        headerProps={{
          sx: {
            display: 'none',
          },
        }}
        contentProps={{
          sx: {
            p: '0 !important',
          },
        }}
        breadcrumb={null}
        wrapperProps={{
          sx: {
            boxShadow: 'none',
            background: 'transparent',
            [`& .${gridClasses.root}`]: {
              [`& .${gridClasses.cell}`]: {
                py: 1,
              },
              [`& .${gridClasses.columnHeader}`]: {
                bgcolor: 'transparent',
              },
              [`& .${gridClasses.columnHeaders}`]: {
                bgcolor: 'transparent',
                borderBottomStyle: 'dashed',
              },
            },
          },
        }}
      >
        {renderGrid()}
      </List>
    );
  }

  return (
    <>
      {currentOrganization?.id || currentPatient?.id || currentIdentity?.id
        ? renderGridTransHeader()
        : renderGrid()}
    </>
  );
}
