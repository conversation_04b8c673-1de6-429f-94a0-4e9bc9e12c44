//default timeout secs
export const DEFAULT_TIMEOUT_SECS: number = 60 * 10;

//excluded URLs for text scraping
const EXCLUDED_URLS = [
    //zip files
    'zip', 'rar', '7z', 'tar', 'tar.gz', 'tgz', 'gz', 'tar.bz2', 'tbz', 'bz2', 'xz', 'iso', 'zipx',
    //executable
    'exe', 'bat', 'cmd', 'msi', 'vbs', 'ps1', 'sh', 'app', 'run', 'bin', 'command', 'jar',
    //audio, video
    'mp3', 'wav', 'aac', 'flac', 'ogg', 'm4a', 'wma', 'aiff', 'alac',
    'mp4', 'avi', 'mov', 'mkv', 'wmv', 'flv', 'webm', 'm4v', 'mpeg', '3gp',
    //image
    'jpg', 'jpeg', 'png', 'gif', 'bmp', 'tif', 'tiff', 'webp', 'svg', 'eps', 'psd', 'ai', 'raw', 'cr2', 'nef'
];

const SUPPORTED_FILE_EXTS = ['pdf', 'docx', 'doc', 'txt', 'json', 'xml', 'md', 'yaml', 'yml'];

export const EXCLUDED_URL_REGEX = new RegExp(`\.(${EXCLUDED_URLS.join('|')})$`, 'i');
export const SUPPORTED_FILE_EXTS_REGEX = new RegExp(`\.(${SUPPORTED_FILE_EXTS.join('|')})$`, 'i');