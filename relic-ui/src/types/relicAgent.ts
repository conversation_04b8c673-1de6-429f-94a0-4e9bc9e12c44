import { RelicPractitioner } from './relicPractitioner';
import { Role } from './relicBase';
import { Type, Static } from '@sinclair/typebox';

export const PatientAgentType: string = 'Patient Agent';
export const StaffAgentType: string = 'Staff Agent';
export const SystemAgentType: string = 'System Agent';
export const AgentTypes: string[] = [PatientAgentType, StaffAgentType, SystemAgentType];

export type AzureAssistantSetup = {
  systemPrompt: string;
  fewShotExamples: FewShotExample[];
  chatParameters: ChatParameters;
  command: string;
  agentSummary: string;
  topic: string;
};

export type ChatParameters = {
  maxResponseLength: number;
  temperature: number;
  topProbabilities: number;
  stopSequences?: string[];
  pastMessagesToInclude: number;
  frequencyPenalty: number;
  presencePenalty: number;
};

export type FewShotExample = {
  chatbotResponse: string;
  userInput: string;
};

export type PublicData = {
  name: string;
  role: string;
  bio: string;
  activeSince: Date;
  chats: number;
  messages: number;
  avatarUrl: string;
  coverUrl: string;
};

export type RelicAssistantSetup = {
  greetingTemplates: GreetingTemplate[];
  kbLinked: string[]; //cb1-732: To be fixed or investigated for side-effects in frontend and backend
  kbPromptTemplate: string;
};

export type GreetingTemplate = {
  event: string;
  greetingTemplate: string;
};

export type RelicAgent = Omit<RelicPractitioner, 'role'> & {
  version:             string;
  type:                'Patient Agent' | 'Staff Agent' | 'System Agent';
  active:              boolean;
  azureAssistantSetup: AzureAssistantSetup;
  relicAssistantSetup: RelicAssistantSetup;
  env:                 string;
  publicData:          PublicData;
  perspective?:        string; //for AI Agent perspective
  role:                Role; //Role of the agent, not practitioner.
};

export const RelicAgentQuerySchema = Type.Object({
  active: Type.Optional(Type.Union([
    Type.Literal('both'),
    Type.Literal('true'),
    Type.Literal('false')
  ], { default: 'true' })),
  organizationId: Type.Optional(Type.String()),
  type: Type.Optional(Type.Union([
    Type.Literal(PatientAgentType),
    Type.Literal(StaffAgentType),
    Type.Literal(SystemAgentType)
  ])),
  _sort: Type.Optional(Type.Union([
    Type.Literal('name'),
    Type.Literal('role'),
    Type.Literal('type')
  ], { default: 'name' })),
  _order: Type.Optional(Type.Union([
    Type.Literal('asc'),
    Type.Literal('desc')
  ], { default: 'asc' })),
  _start: Type.Optional(Type.Number({ default: 0 })),
  _end: Type.Optional(Type.Number({ default: 25 })),
  _search: Type.Optional(Type.String({ maxLength: 255 }))
});

export type RelicAgentQuery = Static<typeof RelicAgentQuerySchema>;