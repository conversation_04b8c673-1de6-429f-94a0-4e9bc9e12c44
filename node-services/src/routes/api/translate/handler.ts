//Language codes supported by Deepl
const deeplSupportedSourceLangCodes: string[] = [
    'bg', 'cs', 'da', 'de', 'el', 'en', 'es', 'et', 'fi', 'fr',
    'hu', 'id', 'it', 'ja', 'ko', 'lt', 'lv', 'nb', 'nl', 'pl',
    'pt', 'ro', 'ru', 'sk', 'sl', 'sv', 'tr', 'uk', 'zh'
];

const deeplSupportedTargetLangCodes: string[] = [
    'bg', 'cs', 'da', 'de', 'el', 'en-gb', 'en-us', 'es', 'et', 'fi', 'fr',
    'hu', 'id', 'it', 'ja', 'ko', 'lt', 'lv', 'nb', 'nl', 'pl',
    'pt-br', 'pt-pt', 'ro', 'ru', 'sk', 'sl', 'sv', 'tr', 'uk', 'zh'
];

async function translateText(request: any, reply: any): Promise<void> {
    const { text, source_lang, target_lang } = request.body;
    let targetLanguage: string;

    let sourceLanguage: string | undefined = source_lang;
    if (!source_lang) {
        const response = await request.server.detectLanguage(text);
        if (!response) {
            throw 'Language detection failed';
        }
        sourceLanguage = response[0]?.language;
    }
    if (sourceLanguage === 'zh_chs' || sourceLanguage === 'zh_cht') {  // DeepL supports 'zh' only instead of 'zh_chs' and 'zh_cht'
        sourceLanguage = 'zh';
    }

    if (target_lang) {
        targetLanguage = target_lang.toLowerCase().split('-')[0]; // 'EN-US' -> 'en', 'en' -> 'en'
    } else {
        targetLanguage = sourceLanguage;
    }

    const translatedText: string = await performTranslation(request, text, sourceLanguage, targetLanguage);
    reply.send({ languageCode: sourceLanguage, translation: translatedText });
}

async function isLanguageSupportedByDeepL(sourceLanguage: string, targetLanguage: string): Promise<boolean> {
    return deeplSupportedSourceLangCodes.includes(sourceLanguage) && deeplSupportedTargetLangCodes.includes(targetLanguage);
}

async function performTranslation(request: any, text: string, sourceLanguage: string, targetLanguage: string): Promise<string> {
    const sourceLang: string = sourceLanguage.toLowerCase();
    const targetLang: string = targetLanguage.toLowerCase();
    let translatedText: string;
    //Translation using Deepl
    if (await isLanguageSupportedByDeepL(sourceLang, targetLang)) {
        const response = await request.server.translateWithDeepL(text, sourceLang, targetLang);
        translatedText = response[0]?.text;
    } else {
        //Translation using Azure
        const response = await request.server.translateWithAzure(text, sourceLang, targetLang);
        translatedText = response[0]?.translations[0]?.text;
    }
    return translatedText;
}

export default { translateText };