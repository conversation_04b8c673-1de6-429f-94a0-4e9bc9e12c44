import { Type, Static } from '@sinclair/typebox';
import { ProfileResource } from '@medplum/core';
import { CommunicationIdentitySchema, CommunicationIdentity } from './communication';
import { IdentityProviderSchema, RelicCommunicationLanguageSchema } from './relicBase';

export const IPortalIdentitySchema = Type.Object({
  email: Type.String(),
  name: Type.Optional(Type.String()),
  mobilePhone: Type.Optional(Type.String()),
  header: Type.Optional(Type.Boolean()),
  sider: Type.Optional(Type.Boolean()),
  companyId: Type.String(),
  companyName: Type.Optional(Type.String()),
  clientId: Type.String(),
  channelId: Type.Optional(Type.String()),
  organizationId: Type.Optional(Type.String()),
  locationId: Type.Optional(Type.String()),
  preferredLanguage: Type.Optional(RelicCommunicationLanguageSchema),
});

export type IPortalIdentity = Static<typeof IPortalIdentitySchema>;

export const IRoleSchema = Type.Object({
  name: Type.String(),
  permissions: Type.Optional(Type.Array(Type.String())),
});

export type IRole = Static<typeof IRoleSchema>;

export const IUserIdentitySchema = Type.Object({
  id: Type.String(),
  resourceType: Type.String(),
  email: Type.Optional(Type.String()),
  profile: Type.Optional(Type.Any()),
  portalIdentity: IPortalIdentitySchema,
  role: IRoleSchema,
  communicationIdentities: Type.Array(CommunicationIdentitySchema),
  provider: Type.Optional(IdentityProviderSchema),
});

type IUserIdentitySchemaType = Static<typeof IUserIdentitySchema>;

export type IUserIdentity = Omit<IUserIdentitySchemaType, 'profile' | 'communicationIdentities'> & {
  profile?: ProfileResource;
  communicationIdentities: CommunicationIdentity[];
}

export type IUser = {
  id: string;
  name: string;
  avatar: string;
  userIdentity?: IUserIdentity;
  accessToken?: string;
  idToken?: string;
  organizationId?: string;
  decodedJwtToken?: object;
};

export const AdminRole: IRole = { name: 'admin' };
export const MemberRole: IRole = { name: 'member', permissions: ['my-facility', 'staff', 'json-ui', 'patients'] };

export type IChannel = {
  id: string;
  object: string;
  createdAt: string;
  updatedAt: string;
  membershipType: string;
  membershipEntityId: string;
  memberIds: string[];
};
