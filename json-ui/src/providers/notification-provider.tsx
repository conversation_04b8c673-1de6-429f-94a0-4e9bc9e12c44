import React from 'react';
import { toast } from 'react-toastify';
import { NotificationProvider } from '@refinedev/core';

import { UndoableNotification } from 'src/components/refine-customs/UndoableNotification';

export const notificationProvider: NotificationProvider = {
  open: ({
    key,
    message,
    description,
    type,
    undoableTimeout,
    cancelMutation,
  }) => {
    const displayMessage =
      type === 'error' && description ? `${message} ${description}` : message;
    if (type === 'progress') {
      if (toast.isActive(key as number | string)) {
        toast.update(key as number | string, {
          progress: undoableTimeout && (undoableTimeout / 10) * 2,
          render: (
            <UndoableNotification
              message={displayMessage}
              cancelMutation={cancelMutation}
            />
          ),
          type: 'default',
        });
      } else {
        toast(
          <UndoableNotification
            message={displayMessage}
            cancelMutation={cancelMutation}
          />,
          {
            toastId: key,
            updateId: key,
            closeOnClick: false,
            closeButton: false,
            autoClose: false,
            progress: undoableTimeout && (undoableTimeout / 10) * 2,
          },
        );
      }
    } else {
      if (toast.isActive(key as number | string)) {
        toast.update(key as number | string, {
          render: displayMessage,
          closeButton: true,
          autoClose: 5000,
          type,
        });
      } else {
        toast(displayMessage, {
          toastId: key,
          type,
          autoClose: 5000,
        });
      }
    }
  },
  close: key => toast.dismiss(key),
};
