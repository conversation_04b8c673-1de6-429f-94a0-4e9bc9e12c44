import React from 'react';
import { useDataGrid } from '@refinedev/mui';

import { Box, Card } from '@mui/material';
import { GridColDef } from '@mui/x-data-grid-pro';

import ListTrainings from 'src/components/_trainings/list/trainings-list';

export const TrainingList = () => {
  const {
    dataGridProps: ListTrainingsProps,
    filters,
    setFilters,
  } = useDataGrid();

  const trainingsColumns = React.useMemo<GridColDef[]>(() => [], []);

  return (
    <Card>
      <Box sx={{ height: 'calc(100vh - 96px)', width: '100%' }}>
        <ListTrainings
          {...ListTrainingsProps}
          columns={trainingsColumns}
          filters={filters}
          setFilters={setFilters}
        />
      </Box>
    </Card>
  );
};
