{"name": "relic-ui", "version": "0.0.1", "description": "", "type": "module", "main": "dist/relic-ui.cjs.js", "module": "dist/relic-ui.es.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./src/types/index.ts", "import": "./dist/relic-ui.es.js", "require": "./dist/relic-ui.cjs.js"}, "./schema": {"types": "./src/types/index.ts", "import": "./dist/relic-ui.schema.es.js", "require": "./dist/relic-ui.schema.cjs"}}, "files": ["dist"], "packageManager": "pnpm@10.10.0", "scripts": {"type-check": "tsc --noEmit", "format": "prettier --write \"src/**/*.{ts,tsx}\"", "build": "rm -rf dist && vite build", "dev": "rm -rf dist && vite build --watch", "watch": "rm -rf dist && vite build --watch", "check-types": "tsc --noEmit && pnpm circular", "test": "echo \"Error: no test specified\" && exit 1", "lint": "eslint .", "lint:fix": "eslint . --fix", "circular": "npx madge --circular --extensions ts,tsx,js,json,yml,yaml,md --ts-config tsconfig.json src"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@azure/abort-controller": "^2.1.2", "@azure/communication-calling": "^1.28.2", "@azure/communication-calling-effects": "^1.0.1", "@azure/communication-chat": "^1.3.2", "@azure/communication-common": "^2.3.1", "@azure/communication-react": "^1.26.0", "@crawlee/playwright": "^3.11.5", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.0", "@fluentui/react": "^8.112.3", "@fluentui/react-components": "^9.40.0", "@fluentui/react-icons": "^2.0.232", "@medplum/core": "^4.0.0", "@medplum/fhirtypes": "^4.0.0", "@mui/icons-material": "^6.0.0", "@mui/material": "^6.0.0", "@rjsf/core": "^5.14.1", "@rjsf/mui": "^5.19.4", "@rjsf/validator-ajv8": "^5.14.1", "@sinclair/typebox": "^0.34.33", "@types/lodash": "^4.14.202", "@types/react": "^18.2.28", "@types/react-dom": "^18.2.13", "@vitejs/plugin-react": "^4.3.0", "antd": "^5.11.1", "date-fns": "^3.0.6", "fluent-json-schema": "^4.2.1", "fuse.js": "^7.0.0", "handlebars": "^4.7.8", "lodash": "^4.17.21", "mui-tel-input": "3.2.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.30.0", "react-imask": "^7.1.3", "react-markdown": "^9.0.1", "rehype-raw": "^7.0.0", "rehype-react": "^8.0.0", "remark-gfm": "^4.0.0", "scheduler": "0.23.0", "typescript": "^5.2.2"}, "devDependencies": {"@microsoft/microsoft-graph-types": "^2.40.0", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "eslint": "^9.25.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "madge": "^8.0.0", "mongodb": "^6.16.0", "openapi-typescript": "^7.4.4", "prettier": "3.2.5", "vite": "^6.3.0", "vite-plugin-dts": "^4.5.0", "vite-tsconfig-paths": "^4.3.2"}}