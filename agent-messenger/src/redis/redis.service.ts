import { Injectable } from '@nestjs/common';
import Redis from 'ioredis';

@Injectable()
export class RedisService {
  private readonly redis: Redis;

  constructor() {
    this.redis = new Redis({
      host: 'agent-messenger.redis.cache.windows.net',
      port: 6380,
      password: 'pqw0sxMMC6BqkNWNIVT8XBY3orTgyK2kyAzCaM2djg8=',
      tls: {
        servername: 'agent-messenger.redis.cache.windows.net',
      },
      connectionName: 'agent-messenger-redis',
    });
  }

  async setString(key: string, value: string, ttl?: number): Promise<void> {
    if (ttl) {
      await this.redis.set(key, value, 'EX', ttl);
    } else {
      await this.redis.set(key, value);
    }
  }
  async getString(key: string): Promise<string | null> {
    const value = await this.redis.get(key);
    return value;
  }

  async setObject(key: string, object: any, ttl?: number): Promise<void> {
    try {
      const stringValue = JSON.stringify(object);
      if (ttl) {
        await this.redis.set(key, stringValue, 'EX', ttl);
      } else {
        await this.redis.set(key, stringValue);
      }
    } catch (error) {
      throw new Error(`Error setting Redis key: ${key}`);
    }
  }

  async getObject(key: string): Promise<any> {
    try {
      const value = await this.redis.get(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      throw new Error(`Error getting Redis key: ${key}`);
    }
  }

  async delObject(key: string): Promise<void> {
    try {
      await this.redis.del(key);
    } catch (error) {
      throw new Error(`Error deleting Redis key: ${key}`);
    }
  }
}
