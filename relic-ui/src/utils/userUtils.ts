import type { IUserIdentity } from '../types';

export function extractDisplayNameFromUserIdentity(userIdentity: IUserIdentity): string | undefined {
  if (userIdentity) {
    const displayName: string | undefined =
      userIdentity.portalIdentity && userIdentity.portalIdentity.name
        ? userIdentity.portalIdentity.name
        : userIdentity.communicationIdentities[0].displayName;
    return displayName;
  } else {
    return undefined;
  }
}
