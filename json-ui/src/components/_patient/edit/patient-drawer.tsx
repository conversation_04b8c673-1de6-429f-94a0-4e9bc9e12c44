import { CreateResponse, UpdateResponse, useTranslate } from '@refinedev/core';
import * as React from 'react';
import { FieldValues, UseFormHandleSubmit } from 'react-hook-form';
import type { RelicPatient } from 'relic-ui';

import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import CircularProgress from '@mui/material/CircularProgress';
import Drawer from '@mui/material/Drawer';
import IconButton from '@mui/material/IconButton';

import { ReturnType } from 'src/hooks/use-popover';

import { formatToYYYYMMDD } from 'src/utils/date';

import Iconify from 'src/components/iconify';
import { Edit } from 'src/components/refine-customs/edit';
import PatientDetailsViewOnly from './patient-details-view';

const DRAWER_WIDTH = {
  lg: 380,
  md: 320,
  sm: 280,
  xs: '100%',
};

interface PatientDrawerProps {
  showPatient: boolean;
  setShowEdit: React.Dispatch<React.SetStateAction<boolean>>;
  onFinish: (
    values: FieldValues,
  ) => Promise<
    void | UpdateResponse<RelicPatient> | CreateResponse<RelicPatient>
  >;
  saveButtonProps: {
    disabled: boolean;
    onClick: (e: React.BaseSyntheticEvent) => void;
  };
  handleSubmit: UseFormHandleSubmit<FieldValues>;
  currentIdentityProvider: string;
  showEdit: boolean;
  currentPatient: RelicPatient;
  isPatientLoaded: boolean;
  queryResult: any;
  popoverDetails: ReturnType;
  renderEditForm: JSX.Element;
}

const PatientDrawer: React.FC<PatientDrawerProps> = ({
  showPatient,
  setShowEdit,
  onFinish,
  saveButtonProps,
  handleSubmit,
  currentIdentityProvider,
  showEdit,
  currentPatient,
  isPatientLoaded,
  queryResult,
  popoverDetails,
  renderEditForm,
}) => {
  const translate = useTranslate();

  const onSubmit = async (values: FieldValues) => {
    const { birthDate } = values;
    let formattedBirthdate = birthDate;
    if (birthDate instanceof Date) {
      formattedBirthdate = formatToYYYYMMDD(birthDate);
    }
    await onFinish({ ...values, birthDate: formattedBirthdate });
    setShowEdit(false);
  };

  return (
    <Drawer
      anchor="right"
      variant="persistent"
      open={showPatient}
      PaperProps={{
        sx: {
          zIndex: 999,
          top: 8 * 18.5,
          boxSizing: 'border-box',
          width: {
            xs: DRAWER_WIDTH.xs,
            sm: DRAWER_WIDTH.sm,
            md: DRAWER_WIDTH.md,
            lg: DRAWER_WIDTH.lg,
          },
        },
      }}
      sx={{
        flexShrink: 0,
        width: {
          xs: DRAWER_WIDTH.xs,
          sm: DRAWER_WIDTH.sm,
          md: DRAWER_WIDTH.md,
          lg: DRAWER_WIDTH.lg,
        },
      }}
    >
      <Edit
        saveButtonProps={{
          ...saveButtonProps,
          onClick: handleSubmit(onSubmit),
        }}
        wrapperProps={{
          sx: {
            flexGrow: 1,
            borderRadius: 0,
            boxShadow: 'none',
            overflowY: 'auto',
            mb: `${8 * 18.5}px`,
          },
        }}
        headerButtonProps={{
          sx: { display: 'none' },
        }}
        headerProps={{
          title: translate('patients.titles.details'),
          action:
            currentIdentityProvider !== 'pcc' ? (
              <>
                {showEdit ? (
                  <Button
                    variant="outlined"
                    startIcon={<Iconify icon={'eva:close-fill'} />}
                    onClick={() => setShowEdit(false)}
                  >
                    Cancel
                  </Button>
                ) : (
                  <IconButton onClick={popoverDetails.onOpen}>
                    <Iconify icon="eva:more-vertical-fill" />
                  </IconButton>
                )}
              </>
            ) : null,
        }}
        footerButtonProps={{
          ...(!showEdit && {
            sx: { display: 'none' },
          }),
        }}
        deleteButtonProps={{
          hidden: true,
          confirmTitle: translate('patients.titles.delete'),
          confirmContent: translate('content.confirm.delete'),
        }}
      >
        {isPatientLoaded ? (
          <PatientDetailsViewOnly
            currentPatient={currentPatient}
            showEdit={showEdit}
          />
        ) : (
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              minHeight: '50vh',
            }}
          >
            <CircularProgress color="inherit" />
          </Box>
        )}
        {showEdit && !queryResult?.isLoading && renderEditForm}
      </Edit>
    </Drawer>
  );
};

export default PatientDrawer;
