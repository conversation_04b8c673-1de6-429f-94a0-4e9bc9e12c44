import { RelicSearchQuery } from "@/types/request";
import { TrainingContent, TrainingModule } from "relic-ui";
import { FastifyReply, FastifyRequest } from "fastify";
import { RelicCrawlerOptions } from "relic-ui";

async function createTrainingModule(req: FastifyRequest, rep: FastifyReply) {
    const trainingModuleRequest: TrainingModule = req.body as TrainingModule;
    return await (req.server as any).createTrainingModule(trainingModuleRequest);
};

async function searchTrainingModules(req: FastifyRequest, rep: FastifyReply) {
    const { total, data } = await (req.server as any).searchTrainingModules(req.query);
    rep.header('x-total-count', total);
    return data;
};

async function getTrainingModuleDetails(req: FastifyRequest, rep: FastifyReply) {
    (req.query as RelicSearchQuery).id = req.params['id'];
    return await (req.server as any).getTrainingModuleDetails(req.query);
};

async function updateTrainingModuleDetails(req: FastifyRequest, rep: FastifyReply) {
    (req.query as RelicSearchQuery).id = req.params['id'];
    return await (req.server as any).updateTrainingModuleDetails(req.query, req.body);
};

async function deleteTrainingModule(req: FastifyRequest, rep: FastifyReply) {
    (req.query as RelicSearchQuery).id = req.params['id'];
    return await (req.server as any).deleteTrainingModule(req.query);
};

async function addTrainingContent(req: FastifyRequest, rep: FastifyReply) {
    const data: TrainingContent = req.body as TrainingContent;
    if (!['url', 'folder'].includes(data.type)) {
        const { documentId, filename, type } = data;
        if (!(documentId && filename && type)) {
            throw req.server.httpErrors.badRequest('required fields missing: [type, filename, documentId]');
        }
        data.url = data.url?.split('?').at(0);
    }
    if (data.type === 'folder' && data.sitemap?.length == 0) {
        throw req.server.httpErrors.badRequest('Sitemap is required when type is folder.');
    }
    return await (req.server as any).addTrainingContent(req.params['id'], data);
};

async function getTrainingContentDetails(req: FastifyRequest, rep: FastifyReply) {
    (req.query as RelicSearchQuery).id = req.params['contentId'];
    (req.query as RelicSearchQuery).foreignFields = [{ key: 'moduleId', value: req.params['id'] }];
    return await (req.server as any).getTrainingContentDetails(req.query);
};

async function deleteTrainingContent(req: FastifyRequest, rep: FastifyReply) {
    (req.query as RelicSearchQuery).id = req.params['contentId'];
    (req.query as RelicSearchQuery).foreignFields = [{ key: 'moduleId', value: req.params['id'] }];
    return await (req.server as any).deleteTrainingContent(req.params['id'], req.query);
};

async function searchTrainingContent(req: FastifyRequest, rep: FastifyReply) {
    const { total, data } = await (req.server as any).searchTrainingContent(req.params['id'], req.query);
    rep.header('x-total-count', total);
    return data;
};

async function updateTrainingContentDetails(req: FastifyRequest, rep: FastifyReply) {
    throw new Error('not supported');
}

//draft
async function retryaddTrainingContent(req: FastifyRequest, rep: FastifyReply) {
    return await (req.server as any).retryaddTrainingContent(req.params['id'], req.params['contentId']);
}

async function estimateTrainingContent(req: FastifyRequest, rep: FastifyReply) {
    const { url, options } = req.body as { url: string, options: RelicCrawlerOptions };
    const moduleId = req.params['id'];
    return await (req.server as any).estimateTrainingContent(moduleId, url, options);
}

async function scrapeTrainingContent(req: FastifyRequest, rep: FastifyReply) {
    const data = req.body as TrainingContent & { options?: RelicCrawlerOptions };
    data.moduleId = req.params['id'];
    data.id = req.params['contentId'];
    return await (req.server as any).scrapeWithSitemap(data);
}

const trainingModulesHandler = {
    createTrainingModule,
    searchTrainingModules,
    getTrainingModuleDetails,
    updateTrainingModuleDetails,
    deleteTrainingModule,
    addTrainingContent,
    getTrainingContentDetails,
    deleteTrainingContent,
    searchTrainingContent,
    updateTrainingContentDetails,
    retryaddTrainingContent,
    estimateTrainingContent,
    scrapeTrainingContent
}
export default trainingModulesHandler;