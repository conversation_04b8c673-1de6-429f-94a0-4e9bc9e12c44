import { ConnectionOptions, DefaultJobOptions, Job, Queue, Worker } from "bullmq";
import { RelicMessageQueueParams } from "relic-ui";
import config from 'config';
import IORedis from 'ioredis';

export class RelicMessageQueue extends Queue {
    private queueParams: RelicMessageQueueParams;
    private static redisConnection: IORedis;
    private static redisConnectionOptions: ConnectionOptions;

    /**
     * Initialize the redis connection to be used by all queue instances.
     */
    private static initialize() {
        if(RelicMessageQueue.redisConnection) {
            return;
        }
        RelicMessageQueue.redisConnectionOptions = {
            port: config.get('REDIS.PORT'),
            host: config.get('REDIS.HOST'),
            password: config.get('REDIS.PASSWORD'),
            tls: {
                servername: config.get('REDIS.TLS_SERVERNAME'),
            },
            connectTimeout: 20000,
            maxRetriesPerRequest: null,
            retryStrategy: function (times: number) {
                return Math.max(Math.min(Math.exp(times), 20000), 1000);
            }
        };

        RelicMessageQueue.redisConnection = new IORedis({
            ...RelicMessageQueue.redisConnectionOptions,
        });
        RelicMessageQueue.redisConnection.on('error', (error) => {
            console.error(error, { message: 'Redis connection error.' });
        });
        RelicMessageQueue.redisConnection.on('connect', () => {
            console.info({ message: 'Redis connection established.' });
        });
        
    }

    static getRedisConnection() {
        RelicMessageQueue.initialize();
        return RelicMessageQueue.redisConnection;
    }
    /**
     * @param queueName - Name of the queue
     * @param moduleId - ID of the module (optional, defaults to empty string)
     * @param organizationId - ID of the organization (optional, defaults to empty string)
     * @param createdBy - Object containing id and resourceType of the createdBy (optional, defaults to empty object)
     */
    constructor({ queueName, moduleId, organizationId, createdBy }: { queueName: string, moduleId?: string, organizationId?: string, createdBy?: { id: string, resourceType: string } }) {
        RelicMessageQueue.initialize();
        const defaultJobOptions: DefaultJobOptions = {
            removeOnComplete: true,
            removeOnFail: true,
            attempts: 3,
            backoff: { type: 'exponential', delay: 1000 }
        };
        super(queueName, {
            connection: RelicMessageQueue.redisConnection,
            defaultJobOptions: defaultJobOptions
        });

        this.queueParams = {
            moduleId: moduleId,
            organizationId: organizationId,
            queueName: queueName,
            jobName: queueName,
            createdBy: createdBy,
            port: (RelicMessageQueue.redisConnectionOptions as any).port,
            host: (RelicMessageQueue.redisConnectionOptions as any).host, 
            password: (RelicMessageQueue.redisConnectionOptions as any).password,
            tlsServername: (RelicMessageQueue.redisConnectionOptions as any).tls.servername,
            maxRetriesPerRequest: (RelicMessageQueue.redisConnectionOptions as any).maxRetriesPerRequest,
        }

        this.on('error', (error) => {
            console.error(error, { queue: this.name, message: 'Queue error.' });
        });
    }

    /**
     * Helper method to create a queue by job type.
     * @param workerType 
     * @returns 
     */
    static CreateQueueForWorker(workerType: 'DOCUMENT_TRANSLATOR' | 'TRAINING_CONTENT_WRITER' | 'TRAINING_CONTENT_ESTIMATOR'){
        const jobConfig = (config.get("REDIS.MESSAGE_QUEUES") as Array<{ WORKER_TYPE: string, QUEUE: string, CONCURRENCY: number }>).find((job: any) => job.WORKER_TYPE == workerType);
        return new RelicMessageQueue({ queueName: jobConfig.QUEUE });
    }

    getQueueParams() {
        return this.queueParams;
    }

    /**
     * Attach worker to queue.
     * Worker will handle job requests
     * @param queueName 
     */
    attachWorkerToQueue(workerFunc: (job: Job) => Promise<void>) {
        const worker = new Worker(this.name, workerFunc, {
            connection: this.opts.connection,
            autorun: true,
            concurrency: 1,
        });
        worker.on('ready', () => {
            console.info({ queue: this.name, message: 'Worker is ready to accept jobs.' });
        });
        worker.on('active', (job) => {
            console.info({ queue: job.queueName, jobId: job.id, jobName: job.name, message: 'Job received.' });
        });
        worker.on('completed', (job) => {
            console.info({ queue: job.queueName, jobId: job.id, jobName: job.name, message: 'Job completed.' });
        });
        worker.on('failed', (job, error) => {
            console.error(error, { queue: job.queueName, jobId: job.id, jobName: job.name, message: 'Job failed.' });
        });
        worker.on('error', (error) => {
            console.error(error, { queue: this.name, message: 'Worker error.' });
        });
    }

}