import { RelicPractitioner, RelicPatient, RelicAgent, CommunicationIdentity } from 'relic-ui'

export interface CommunicationServiceInterface {
  createIdentity(resource: RelicPractitioner | RelicPatient | RelicAgent): Promise<CommunicationIdentity>;
  refreshIdentity(
    resource: RelicPatient | RelicPractitioner | RelicAgent,
    identity: CommunicationIdentity,
  ): Promise<CommunicationIdentity>;
}
