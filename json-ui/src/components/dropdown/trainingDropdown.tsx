import React from 'react';
import { useAutocomplete } from '@refinedev/mui';
import { Control, Controller } from 'react-hook-form';

import TextField from '@mui/material/TextField';
import Autocomplete from '@mui/material/Autocomplete';

interface TrainingDropdownProps {
  control: Control<any>;
  name: string;
  label: string;
  value: any | null;
  error: boolean;
  helperText: string;
  onChange?: (value: any | null) => void;
  disabled?: boolean;
}

export const TrainingDropdown: React.FC<TrainingDropdownProps> = ({
  control,
  name,
  label,
  value,
  error,
  helperText,
  onChange,
  disabled,
}) => {
  const { autocompleteProps: trainingAutocompleteProps } = useAutocomplete({
    resource: 'trainings/',
  });

  return (
    <Controller
      control={control}
      name={name}
      defaultValue={value}
      rules={{ required: 'This field is required' }}
      render={({ field }) => {
        const { onChange: fieldOnChange, value: fieldValue } = field;
        return (
          <Autocomplete
            loading={trainingAutocompleteProps.loading}
            options={trainingAutocompleteProps.options}
            onInputChange={trainingAutocompleteProps.onInputChange}
            disabled={disabled}
            autoHighlight
            getOptionLabel={(option: any) =>
              trainingAutocompleteProps.options?.find(
                org => org.id === option.id,
              )?.name ?? ''
            }
            isOptionEqualToValue={(option, value) => {
              return (
                value === undefined ||
                option?.indexName?.toString() ===
                  (value?.indexName ?? value).toString()
              );
            }}
            onChange={(_, newValue) => {
              const selectedName = newValue?.indexName || null;
              fieldOnChange(selectedName);
              if (onChange) {
                onChange(newValue);
              }
            }}
            renderInput={params => (
              <TextField
                {...params}
                disabled={disabled}
                label={label}
                variant="outlined"
                error={error}
                helperText={helperText}
              />
            )}
            value={
              trainingAutocompleteProps.options?.find(
                option => option.indexName === fieldValue,
              ) || null
            }
          />
        );
      }}
    />
  );
};
