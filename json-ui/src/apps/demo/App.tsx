import { useTranslation } from 'react-i18next';
import 'react-toastify/dist/ReactToastify.css';
import { ToastContainer } from 'react-toastify';
import dataProvider from '@refinedev/simple-rest';
import { Route, Routes, Outlet } from 'react-router-dom';
import { Refine, CanAccess, Authenticated } from '@refinedev/core';
import { ErrorComponent, RefineSnackbarProvider } from '@refinedev/mui';
import routerBindings, {
  CatchAllNavigate,
  NavigateToResource,
  DocumentTitleHandler,
  UnsavedChangesNotifier,
} from '@refinedev/react-router-v6';

import HomeIcon from '@mui/icons-material/Home';
import ForumIcon from '@mui/icons-material/Forum';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';

import { getNodeServicesApi } from 'src/utils/app-config';

import ThemeProvider from 'src/theme';
import { Header } from 'src/components';
import { SearchProvider } from 'src/providers';
import CheckThread from 'src/pages/demo/check';
import { relicDataProvider } from 'src/providers';
import { DemoB2CLogin } from 'src/pages/demo/login';
import { Sandbox, EmbeddedCall } from 'src/pages/chat';
import { DemoChatScenario } from 'src/pages/demo/scenario';
import { authProviderLobby as authProvider } from 'src/providers';
import { SecuredHome, UnsecuredHome } from 'src/pages/demo/home';
import { notificationProvider } from 'src/providers/notification-provider';
import { accessControlProvider } from 'src/providers/access-control-provider';
import {
  SecuredRegistration,
  UnsecuredRegistration,
} from 'src/pages/demo/register';

import { ThemedLayoutV2 } from 'src/components/layout';
import { ThemedTitleV2 } from 'src/components/layout/title';
import { DocumentTitle } from 'src/components/_documentTitle';
import { SimpleHeader } from 'src/components/header/simpleHeader';
import UnAuthorized from 'src/components/refine-customs/unauthorized';
import { OrganizationIcon } from 'src/components/icon/OrganizationIcon';

const demoProviderUrl = `${getNodeServicesApi()}/api`;

function App() {
  const { t, i18n } = useTranslation();

  const i18nProvider = {
    translate: (key: string, params: object) => t(key, params),
    changeLocale: (lang: string) => i18n.changeLanguage(lang),
    getLocale: () => i18n.language,
  };

  return (
    <ThemeProvider>
      <RefineSnackbarProvider>
        <Refine
          notificationProvider={notificationProvider}
          routerProvider={routerBindings}
          authProvider={authProvider}
          accessControlProvider={accessControlProvider}
          i18nProvider={i18nProvider}
          dataProvider={{
            default: relicDataProvider,
            json_server: dataProvider('https://relic-json-server.vercel.app'),
            demo_provider: dataProvider(demoProviderUrl),
          }}
          resources={[
            {
              name: 'home',
              meta: {
                label: 'Demo Home',
                icon: <HomeIcon />,
              },
              list: '/home',
            },
            {
              name: 'sandbox',
              meta: {
                label: 'Demo Scenario',
                icon: <ForumIcon />,
              },
              list: '/check',
            },
          ]}
          options={{
            syncWithLocation: true,
            useNewQueryKeys: true,
            projectId: 'uvSWGo-A848GS-kqC6S9',
            reactQuery: {
              clientConfig: {
                defaultOptions: {
                  queries: {
                    staleTime: 5 * 60 * 1000,
                    retry: false,
                  },
                },
              },
            },
          }}
        >
          <LocalizationProvider dateAdapter={AdapterDateFns}>
            <SearchProvider>
              <Routes>
                <Route
                  element={
                    <Authenticated
                      key="authenticated-inner"
                      fallback={<CatchAllNavigate to="/demo-home" />}
                    >
                      <ThemedLayoutV2
                        Title={({ collapsed }) => (
                          <ThemedTitleV2
                            // collapsed is a boolean value that indicates whether the <Sidebar> is collapsed or not
                            collapsed={collapsed}
                            icon={<OrganizationIcon />}
                            text="Relic AI Demo"
                          />
                        )}
                        Header={() => <SimpleHeader sticky />}
                      >
                        <CanAccess fallback={<UnAuthorized />}>
                          <Outlet />
                        </CanAccess>
                      </ThemedLayoutV2>
                    </Authenticated>
                  }
                >
                  <Route
                    index
                    element={<NavigateToResource resource="home" />}
                  />
                  <Route path="home">
                    <Route index element={<SecuredHome />} />
                  </Route>
                  <Route path="register">
                    <Route index element={<SecuredRegistration />} />
                  </Route>
                  <Route path="check">
                    <Route index element={<CheckThread />} />
                  </Route>
                  <Route path="scenario">
                    <Route index element={<DemoChatScenario />} />
                  </Route>
                  <Route path="embedded-call">
                    <Route index element={<EmbeddedCall />} />
                  </Route>

                  <Route path="*" element={<ErrorComponent />} />
                </Route>
                <Route
                  element={
                    <Authenticated
                      key="authenticated-outer"
                      fallback={<Outlet />}
                    >
                      <NavigateToResource />
                    </Authenticated>
                  }
                >
                  <Route path="login" element={<DemoB2CLogin />} />
                </Route>
                <Route path="demo-home">
                  <Route index element={<UnsecuredHome />} />
                </Route>
                <Route path="demo-register">
                  <Route index element={<UnsecuredRegistration />} />
                </Route>
                <Route path="*" element={<ErrorComponent />} />
              </Routes>
            </SearchProvider>
          </LocalizationProvider>

          <UnsavedChangesNotifier />
          <DocumentTitleHandler handler={DocumentTitle} />
          <ToastContainer />
        </Refine>
      </RefineSnackbarProvider>
    </ThemeProvider>
  );
}

export default App;
