{"resourceType": "AccessPolicy", "name": "StaffAdmin", "resource": [{"resource": "my-facility", "canAccess": [{"name": "my-facility", "allowed": true}, {"name": "patients", "allowed": true}, {"name": "documents", "allowed": true}, {"name": "sandbox", "allowed": true}]}, {"resource": "my-organization", "canAccess": [{"name": "my-organization", "allowed": true}, {"name": "agents", "allowed": true}, {"name": "training-modules", "allowed": true}, {"name": "staff", "allowed": true}, {"name": "organizations", "allowed": true}]}, {"resource": "patient", "canRead": true, "canUpdate": true, "canDelete": true, "canCreate": true, "canList": true}, {"resource": "documents", "canRead": true, "canUpdate": true, "canDelete": true, "canCreate": true, "canList": true}, {"resource": "sandbox", "allowed": true, "canRead": true, "canList": true}, {"resource": "my-conversations", "canRead": true, "canUpdate": true, "canDelete": true, "canCreate": true, "canList": true}, {"resource": "staff", "canRead": true, "canUpdate": true, "canDelete": true, "canCreate": true, "canList": true}, {"resource": "agents", "canRead": true, "canUpdate": true, "canDelete": true, "canCreate": true, "canList": true}, {"resource": "training-modules", "canRead": true, "canUpdate": true, "canDelete": true, "canCreate": true, "canList": true}, {"resource": "my-account", "canRead": true, "canUpdate": true, "canDelete": true, "canCreate": true, "canList": true}, {"resource": "organizations", "canRead": true, "canUpdate": true, "canDelete": true, "canCreate": true, "canList": true}, {"resource": "live-data", "canAccess": [{"name": "live-data", "allowed": true}, {"name": "facilities", "allowed": true}, {"name": "trainings", "allowed": true}, {"name": "agents", "allowed": true}, {"name": "threads", "allowed": true}]}, {"resource": "facilities", "canRead": true, "canUpdate": true, "canDelete": true, "canCreate": true, "canList": true}, {"resource": "trainings", "canRead": true, "canUpdate": true, "canDelete": true, "canCreate": true, "canList": true}, {"resource": "threads", "canRead": true, "canUpdate": true, "canDelete": true, "canCreate": true, "canList": true}, {"resource": "json-ui", "canAccess": [{"name": "json-ui", "allowed": true}, {"name": "conditions", "allowed": true}, {"name": "allergyintolerances", "allowed": true}, {"name": "locations", "allowed": true}, {"name": "report-content", "allowed": true}, {"name": "report-layout", "allowed": true}]}, {"resource": "conditions", "canRead": true, "canUpdate": true, "canDelete": true, "canCreate": true, "canList": true}, {"resource": "allergyintolerances", "canRead": true, "canUpdate": true, "canDelete": true, "canCreate": true, "canList": true}, {"resource": "locations", "canRead": true, "canUpdate": true, "canDelete": true, "canCreate": true, "canList": true}, {"resource": "report-content", "canRead": true, "canUpdate": true, "canDelete": true, "canCreate": true, "canList": true}, {"resource": "report-layout", "canRead": true, "canUpdate": true, "canDelete": true, "canCreate": true, "canList": true}]}