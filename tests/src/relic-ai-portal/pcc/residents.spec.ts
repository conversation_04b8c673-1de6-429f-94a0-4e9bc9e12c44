import { test, expect } from '@playwright/test';
import { common, getPCCPatient, loginToRelicaiPcc, loginToPcc } from '../../../common'

test.describe('PCC-Search Existing Resident', () => {
    test.beforeEach(loginToRelicaiPcc);
    test('PCC-Search Existing Resident', async ({ page }) => {
        await expect(page.locator('div').filter({ hasText: /^registerednurse$/ })).toBeVisible()
        await page.waitForURL(common.patientListPage)
        await page.waitForSelector('.MuiDataGrid-row--firstVisible', { state: 'visible' })
        await page.getByPlaceholder('Search...').click()
        await page.getByPlaceholder('Search...').fill('Bibi')
        await Promise.all([page.waitForURL(common.patientDetailsPage), page.getByRole('gridcell', { name: '<PERSON>, <PERSON><PERSON> (2466)' }).click()])
    })
})

test.describe.serial('PCC-Admit and Delete New Resident', async () => {
    const jsonPatient = await getPCCPatient(0)
    const firstName = jsonPatient.firstName
    const surname = jsonPatient.surname
    const dob = jsonPatient.dob
    const gender = jsonPatient.gender
    const ssn = jsonPatient.ssn

    test('PCC-Portal-Add New Resident', async ({ page }) => {
        await loginToPcc({ page });
        await expect(page.locator('#pageHeader')).toContainText('Today for uiaccess')
        await page.getByRole('link', { name: 'Admin' }).click()
        await page.getByRole('link', { name: 'Admin' }).hover()

        const [popup] = await Promise.all([
            page.waitForEvent('popup'), // Wait for the popup to be created
            page.getByRole('link', { name: 'New Resident' }).click() // Click the admin menu item
        ])

        await popup.waitForLoadState('domcontentloaded')
        const createResidentPopup = await popup
        await expect(createResidentPopup.locator('body')).toContainText('New Resident Entry')

        await createResidentPopup.getByLabel('First Name:').fill(`${firstName}`)
        await createResidentPopup.getByLabel('Surname:').fill(`${surname}`)
        await createResidentPopup.locator('#ESOLdateofbirth_dummy').fill(`${dob}`)
        await createResidentPopup.getByLabel('Gender:').selectOption(`${gender}`)
        await createResidentPopup.getByLabel('Social Security #:').fill(`${ssn}`)
        await createResidentPopup.getByRole('button', { name: 'Search' }).click()
        await createResidentPopup.getByRole('button', { name: 'Continue' }).click()

        await expect(page.getByText('Resident Detail - New Resident')).toBeVisible()
        await page.getByLabel('Social Security #:').fill(`${ssn}`)
        await page.getByRole('button', { name: 'Save' }).first().click()
    })

    test('PCC-Search New Resident', async ({ page }) => {
        await loginToRelicaiPcc({ page });
        await expect(page.getByRole('banner')).toContainText('registerednurse')
        await page.waitForURL(common.patientListPage)
        await page.waitForSelector('.MuiDataGrid-row--firstVisible', { state: 'visible' })
        await page.getByRole('button', { name: 'New' }).click()
        await page.waitForSelector('.MuiDataGrid-row--firstVisible', { state: 'visible' })
        await Promise.all([
          page.getByPlaceholder('Search...').fill(`${surname}`), 
        ])
        await expect(page.getByRole('gridcell', { name: `${surname}, ${firstName}` })).toBeVisible()
    })

    test('PCC-Portal-Delete New Resident', async ({ page }) => {
        console.log(`edit adt ${surname}, ${firstName} (`)
        console.log(`************************${surname} ${firstName}**************************************`)

        await loginToPcc({ page });
        await expect(page.locator('#pageHeader')).toContainText('Today for uiaccess')
        await page.getByRole('link', { name: 'Admin' }).click()
        await page.locator('html').click()
        await page.getByRole('link', { name: 'New', exact: true }).click()
        await page.getByRole('link', { name: 'P', exact: true }).first().click()

        await page
            .getByRole('row', { name: `edit adt ${surname}, ${firstName} (` })
            .getByRole('link')
            .first()
            .click()
        page.on('dialog', async (dialog) => {
            // Ensure it's a confirmation dialog (not an alert or prompt)
            expect(dialog.type()).toBe('confirm')
            // Accept the dialog (this simulates clicking "OK")
            await dialog.accept()
        })

        // Click the delete button by its ID
        await page.click('#idDeleteBtn')
    })
})

test.describe('PCC-Other Resident Tests', () => {
    test.beforeEach(loginToRelicaiPcc)
    test('PCC-Search Residents-Deletion Search', async ({ page }) => {
        await expect(page.getByRole('banner')).toContainText('registerednurse')
        await page.getByPlaceholder('Search...').click()
        await page.getByPlaceholder('Search...').fill('Adam')
        await page.getByPlaceholder('Search...').press('Enter')
        await page.getByPlaceholder('Search...').fill('')
        await page.getByRole('heading', { name: 'Residents' }).click()
    })

    test('PCC-Resident List-Success', async ({ page }) => {
        await expect(page.getByRole('banner')).toContainText('registerednurse')
        await page.getByRole('heading', { name: 'Residents' }).click()
        await page.getByLabel('Go to next page').click()
        await page.getByLabel('Go to next page').click()
    })

    test('PCC-Resident List-Sort by Name (Page 1)', async ({ page }) => {
        await expect(page.getByRole('banner')).toContainText('registerednurse')
        await page.getByRole('heading', { name: 'Residents' }).click()
        await page.getByLabel('Go to next page').click()
        await page.getByLabel('Go to next page').click()
        await page.getByLabel('Go to next page').click()
        await page.getByLabel('Go to next page').click()
    })

    test('PCC-Resident List-Sort by Name (Page 2)', async ({ page }) => {
        await expect(page.getByRole('banner')).toContainText('registerednurse')
        await page.getByRole('heading', { name: 'Residents' }).click()
        await page.getByLabel('25').click()
        await page.getByRole('option', { name: '100' }).click()
        await page.getByLabel('Go to next page').click()
        await page.getByLabel('Go to next page').click()
    })

    test('PCC-Resident Details-DOB and Age is visible.', async ({ page }) => {
        await expect(page.getByRole('banner')).toContainText('registerednurse')
        await page.getByRole('gridcell', { name: 'Abreu khan, Sharii' }).click()
        await expect(page.getByText('Date of Birth')).toBeVisible()
        await expect(page.getByText('(71 years old)')).toBeVisible()
        await expect(page.locator('form')).toContainText('11/26/1953')
    })

    test('PCC-Resident Details-Phone is mandatory', async ({ page }) => {
        await expect(page.getByRole('banner')).toContainText('registerednurse')
        await page.getByRole('gridcell', { name: 'Abreu khan, Sharii' }).click()
        await expect(page.getByText('Mobile Phone')).toBeVisible()
        await expect(page.locator('form')).toContainText('Mobile PhoneUS+1')
    })

    test('PCC-Resident Condition List', async ({ page }) => {
        await expect(page.getByRole('banner')).toContainText('registerednurse')
        await page.getByRole('gridcell', { name: 'Abreu khan, Sharii (7557)' }).click()
        await page.getByRole('tab', { name: 'Conditions' }).click()
        await expect(page.getByRole('gridcell', { name: 'CORONARY ATHEROSCLEROSIS DUE' })).toBeVisible()
    })

    test('PCC-Interpretor Call-Phone number validation', async ({ page }) => {
        await page.waitForURL(common.patientListPage)
        await page.waitForSelector('.MuiDataGrid-row--firstVisible', { state: 'visible' })
        await page.getByPlaceholder('Search...').click()
        await page.getByPlaceholder('Search...').fill('Abreu')
        await Promise.all([
            page.getByRole('gridcell', { name: 'Abreu khan, Sharii' }).click(),
            expect(page.locator('textarea#sendbox')).toBeVisible()
        ])
        await page.getByLabel('Voice').click()
        await expect(page.getByLabel('Voice Conversation')).toContainText('Voice Conversation using Phone')
        await page.getByTestId('PhoneIcon').click()
        await page.getByLabel('Phone Number').fill('**********')
        await expect(page.getByRole('button', { name: 'Start Phone Call' })).toBeVisible()
    })

    test('PCC-Interpretor Call-Primary Language validation', async ({ page }) => {
        await expect(page.getByRole('banner')).toContainText('registerednurse')
        await page.getByRole('gridcell', { name: 'Abreu khan, Sharii' }).click()
        await page.getByLabel('Voice').click()
        await expect(page.locator('#language-autocomplete-label')).toBeVisible()
    })
})
