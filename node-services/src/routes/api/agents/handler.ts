import { RelicAgent } from "relic-ui";

export async function getRelicAgents(req:any, resp:any){
    const service = req.server;
    return await service.searchRelicAgents(req.filter);
}

export async function getRelicAgentById(req:any, resp:any){
    const service = req.server;
    const id:string = req.params.id as string;
    return await service.getRelicAgent(id, req.filter);
}

export async function createRelicAgent(req:any, resp:any){
    const service = req.server;
    const resource:RelicAgent = req.body as RelicAgent;
    return await service.createRelicAgent(resource);
}

export async function updateRelicAgent(req:any, resp:any){
    const service = req.server;
    const agentId:string = req.params.id as string;
    const resource:RelicAgent = req.body as RelicAgent;
    return await service.updateRelicAgent(agentId, resource);
}

export async function deactivateRelicAgent(req:any, resp:any){
    const service = req.server;
    const agentId:string = req.params.id as string;
    return await service.deactivateRelicAgent(agentId);
}