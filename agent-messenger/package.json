{"name": "agent-messenger", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "packageManager": "pnpm@10.10.0", "scripts": {"build": "nest build", "agent-messenger:build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "dev": "dotenv -- cross-var wait-on http-get://%RELIC_NODE_SERVICE_HOST%:%RELIC_NODE_SERVICE_PORT% ../relic-ui/dist/relic-ui.es.js && nest start --preserveWatchOutput --watch", "start": "dotenv -- cross-var wait-on http-get://%RELIC_NODE_SERVICE_HOST%:%RELIC_NODE_SERVICE_PORT% && node dist/main", "start:debug": "nest start --debug --watch", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "check-types": "tsc --project tsconfig.build.json && pnpm circular", "check-types:test": "tsc --project tsconfig.test.json", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "circular": "madge --circular --extensions ts,tsx,js,json,yml,yaml,md --ts-config tsconfig.json src"}, "dependencies": {"@azure/communication-call-automation": "^1.4.0-beta.1", "@azure/communication-chat": "^1.4.0", "@azure/communication-common": "^2.3.0", "@azure/communication-identity": "^1.3.0", "@azure/core-auth": "^1.6.0", "@azure/search-documents": "^12.1.0", "@medplum/core": "^4.0.0", "@nestjs/axios": "^4.0.0", "@nestjs/common": "^11.0.0", "@nestjs/config": "^4.0.0", "@nestjs/core": "^11.0.0", "@nestjs/platform-express": "^11.0.0", "dayjs": "^1.11.10", "ioredis": "^5.3.2", "lodash": "^4.17.21", "nestjs-pino": "^4.0.0", "newrelic": "^12.0.0", "openai": "^4.85.4", "pino-pretty": "^13.0.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "tiktoken": "^1.0.10", "uuid": "^11.0.0"}, "devDependencies": {"@azure/eventgrid": "^5.1.1", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.0", "@types/express": "^5.0.0", "@types/jest": "^29.5.12", "@types/lodash": "^4.17.12", "@types/node": "^22.0.0", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "cross-var": "^1.1.0", "dotenv-cli": "^8.0.0", "eslint": "^9.0.0", "eslint-config-prettier": "^10.0.0", "eslint-plugin-prettier": "^5.1.3", "jest": "^29.5.0", "madge": "^8.0.0", "prettier": "^3.2.5", "relic-ui": "workspace:relic-ui", "source-map-support": "^0.5.21", "supertest": "^7.1.1", "ts-jest": "^29.1.2", "ts-loader": "^9.5.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.3.3", "wait-on": "^8.0.1"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}