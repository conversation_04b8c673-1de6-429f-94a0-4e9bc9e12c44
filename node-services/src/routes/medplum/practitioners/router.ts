import { FastifyInstance, FastifyPluginOptions, FastifyReply, FastifyRequest } from 'fastify';
import { RelicIdParam } from 'relic-ui';
import { relicRequire } from '../../../utils/common-js';
const { RelicIdParamSchema } = relicRequire('relic-ui/schema');

interface PractitionerIdParams {
  Params: RelicIdParam;
}

export default async function (fastify: FastifyInstance, opts: FastifyPluginOptions) {
  fastify.get(
    '/:id',
    {
      schema: {
        ...opts.schema,
        params: RelicIdParamSchema,
        summary: `Get a specific practitioner by Id. Id can be EHR Id, Relic Care Id, email or mobile number.`,
        tags: ['Practitioner Service'],
        description: `Fetches a specific practitioner from the database using the provided ID.`,
      },
    },
    async function getPractitioner(req: FastifyRequest<PractitionerIdParams>, rep: FastifyReply) {
      const practitionerId = req.params.id;
      const practitioner = await fastify.getPractitioner(practitionerId);
      if (!practitioner) {
        throw fastify.httpErrors.notFound(`Practitioner not found, id: ${practitionerId}`);
      }
      return practitioner;
    },
  );
}
