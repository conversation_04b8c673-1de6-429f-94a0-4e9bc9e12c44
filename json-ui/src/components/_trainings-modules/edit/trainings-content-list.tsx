import { C<PERSON>Filter, useOnError, useTranslate } from '@refinedev/core';
import { DateField } from '@refinedev/mui';
import React, { useEffect, useMemo, useState } from 'react';

import { DataObject, Folder } from '@mui/icons-material';
import DescriptionIcon from '@mui/icons-material/Description';
import LinkIcon from '@mui/icons-material/Link';
import PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';
import { Box, Typography } from '@mui/material';
import {
  DataGridPro,
  DataGridProProps,
  GridColDef,
  GridGroupNode,
  useGridApiRef,
} from '@mui/x-data-grid-pro';

import { useSearch } from 'src/hooks/use-search';

import { useParams } from 'src/routes/hooks';
import TrainingContentCrawlDialog from './training-content-crawl-dialog';
import TrainingContentViewDetails from './training-content-view';

interface ListTrainingsContentProps extends DataGridProProps {
  setFilters: ((filters: CrudFilter[]) => void) &
    ((setter: (prevFilters: CrudFilter[]) => CrudFilter[]) => void);
  filters: CrudFilter[];
  refetchTrainingContent: VoidFunction;
}

/**
 * The maximum depth allowed for nested children in the training content structure.
 * This constant defines how many levels of nesting are supported in the hierarchy.
 * @constant {number} CHILDREN_MAX_DEPTH
 */
const CHILDREN_MAX_DEPTH = 6;

const ListTrainingsContent = ({
  setFilters,
  refetchTrainingContent,
  filters,
  ...dataGridProps
}: ListTrainingsContentProps) => {
  const translate = useTranslate();
  const apiRef = useGridApiRef();
  const { id: moduleId } = useParams();

  const { globalSearch } = useSearch();
  const { mutate: onError } = useOnError();

  const [showViewDialog, setShowViewDialog] = useState(false);
  const [showCrawlDialog, setShowCrawlDialog] = useState(false);
  const [selectedTrainingContent, setSelectedTrainingContent] = useState<any>();
  const [expandedRowIds, setExpandedRowIds] = useState<Set<string>>(new Set());
  const [manuallyCollapsedRowIds, setManuallyCollapsedRowIds] = useState<
    Set<string>
  >(new Set());
  const [viewButtonEl, setViewButtonEl] =
    React.useState<HTMLButtonElement | null>(null);

  const columns = React.useMemo<GridColDef[]>(
    () => [
      {
        field: 'type',
        headerName: translate('trainings-content.fields.contentType'),
        flex: 1,
      },

      {
        field: 'status',
        flex: 1,
        headerName: translate('trainings-content.fields.status'),
        renderCell: function render({ value }) {
          let color;
          let text;
          switch (value) {
            case 'pending':
              color = '#FFA500'; // Orange for Pending Translation
              text = 'Pending';
              break;
            case 'failed':
              color = '#FF4C4C'; // Soft Red for Failed Translation
              text = 'Failed';
              break;
            case 'done':
              color = '#4CAF50'; // Green for Completed Translation
              text = 'Completed';
              break;
            case 'inprogress':
              color = '#1E90FF'; // Blue for In Progress
              text = 'In Progress';
              break;
            case 'sitemap_ready':
              color = '#32CD32'; // Lime Green for Sitemap Ready
              text = 'Sitemap Ready';
              break;
            case 'estimating_sitemap':
              color = '#DAA520'; // Goldenrod for Estimating Sitemap (darker gold)
              text = 'Estimating Sitemap';
              break;
            default:
              color = '#A9A9A9'; // Light Gray for Unknown Status
              text = value?.toUpperCase() || 'Unknown';
          }
          return (
            <Typography variant="body2" sx={{ color: color }}>
              {text}
            </Typography>
          );
        },
      },
      {
        field: 'updateDate',
        headerName: translate('trainings-content.fields.last_update'),
        flex: 1,
        sortable: false,
        renderCell: function render({ value, row }) {
          return (
            <DateField
              sx={{ display: 'inline' }}
              value={row.lastProcessingDate ?? value}
              format="MMMM DD YYYY, h:mm a"
            />
          );
        },
      },
    ],
    [translate],
  );

  const handleSetShowViewDialog = () => {
    setShowViewDialog(true);
  };

  const handleCloseViewDialog = () => {
    setShowViewDialog(false);
  };

  const handleSetShowCrawlDialog = () => {
    setShowCrawlDialog(true);
  };

  const handleCloseCrawlDialog = () => {
    setShowCrawlDialog(false);
    refetchTrainingContent();
  };

  const handleRefetchTrainingContent = React.useCallback(() => {
    refetchTrainingContent();
  }, [refetchTrainingContent]);

  function expandGroup(node: GridGroupNode) {
    const nodeId = node.id as string;
    // Don't expand if node was manually collapsed by the user
    if (manuallyCollapsedRowIds.has(nodeId)) {
      return false;
    }
    return expandedRowIds.has(nodeId);
  }

  const getFileIcon = (type: string | undefined) => {
    if (!type) return <LinkIcon fontSize="small" />;
    switch (type.toLowerCase()) {
      case 'url':
        return <LinkIcon fontSize="small" />;
      case 'folder':
        return <Folder fontSize="small" />;
      case 'application/pdf':
        return <PictureAsPdfIcon fontSize="small" />;
      case 'application/json':
        return <DataObject fontSize="small" />;
      case 'application/msword':
      case 'application/vnd.ms-excel.sheet.macroEnabled.12':
      case 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
      case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
      case 'text/csv':
      case 'text/plain':
        return <DescriptionIcon fontSize="small" />;
      default:
        return <DescriptionIcon fontSize="small" />;
    }
  };

  // Build nested structure for tree data up to 5 levels deep
  const buildNestedDocuments = (rows: any[]) => {
    const documentMap = new Map();

    // Initialize the map with all rows
    rows.forEach(row => {
      documentMap.set(row.id, { ...row, children: [], level: 0 });
    });

    // First pass: identify direct parent-child relationships
    const childToParentMap = new Map();
    rows.forEach(row => {
      if (row.sourceDocumentId && row.sourceDocumentId !== row.id) {
        childToParentMap.set(row.id, row.sourceDocumentId);
      }
    });

    // Second pass: build the full hierarchy path for each node
    // and determine the nesting level
    rows.forEach(row => {
      const node = documentMap.get(row.id);
      if (!node) return;

      let currentId = row.id;
      let parentId = childToParentMap.get(currentId);
      let level = 0;
      const hierarchyPath = [currentId];

      // Traverse up the hierarchy to find the complete path and level
      while (parentId && level < 5 && parentId !== currentId) {
        hierarchyPath.unshift(parentId);
        currentId = parentId;
        parentId = childToParentMap.get(currentId);
        level++;
      }

      // Update the node with its level in the hierarchy
      node.level = level;
      node.hierarchyPath = hierarchyPath;
    });

    // Third pass: build the nested structure respecting the 5-level limit
    rows.forEach(row => {
      if (row.sourceDocumentId && row.sourceDocumentId !== row.id) {
        const parent = documentMap.get(row.sourceDocumentId);
        const child = documentMap.get(row.id);

        if (parent && child && child.level <= CHILDREN_MAX_DEPTH) {
          // Only add children if we haven't reached the maximum nesting level
          parent.children.push(child);
        }
      }
    });

    // Return only top-level documents (those with no parent or self-referencing parent ID)
    return Array.from(documentMap.values()).filter(
      row => row.sourceDocumentId === row.id || !row.sourceDocumentId,
    );
  };

  // Get top-level documents from the server response
  const topLevelDocuments = useMemo(() => {
    // Get all rows from the dataGridProps
    const allRows = Array.isArray(dataGridProps.rows)
      ? [...dataGridProps.rows]
      : [];

    // Filter top-level documents (those with sourceDocumentId === id or no sourceDocumentId)
    return allRows.filter(
      row => row.sourceDocumentId === row.id || !row.sourceDocumentId,
    );
  }, [dataGridProps.rows]);

  const handleEditRow = React.useCallback(
    (row: any) => {
      // Only expand the minimum necessary parent nodes to make the selected content visible
      // but don't alter any manually collapsed nodes
      if (
        row.hierarchyPath &&
        Array.isArray(row.hierarchyPath) &&
        row.hierarchyPath.length > 1
      ) {
        // Only add the direct parent to expanded set (skip the last item which is the row itself)
        const parentId = row.hierarchyPath[row.hierarchyPath.length - 2];

        // Only expand if not manually collapsed
        if (parentId && !manuallyCollapsedRowIds.has(parentId)) {
          setExpandedRowIds(prev => {
            const newExpanded = new Set(prev);
            newExpanded.add(parentId);
            return newExpanded;
          });
        }
      }
      // Handle sourceDocumentId case (fallback)
      else if (row?.sourceDocumentId && row.sourceDocumentId !== row.id) {
        // Only expand if not manually collapsed
        if (!manuallyCollapsedRowIds.has(row.sourceDocumentId)) {
          setExpandedRowIds(prev => {
            const newExpanded = new Set(prev);
            newExpanded.add(row.sourceDocumentId);
            return newExpanded;
          });
        }
      }

      handleSetShowViewDialog();
      setSelectedTrainingContent(row);
    },
    [setExpandedRowIds, setSelectedTrainingContent, manuallyCollapsedRowIds],
  );

  const handleOnCellClick = React.useCallback(
    (params: any) => {
      if (params.row.status === 'sitemap_ready') {
        // For sitemap_ready status, only open the crawl dialog
        setSelectedTrainingContent(params.row);
        handleSetShowCrawlDialog();
      } else {
        // For all other statuses, open the view details dialog
        handleEditRow(params.row);
      }
    },
    [handleEditRow, handleSetShowCrawlDialog],
  );

  useEffect(() => {
    try {
      if (globalSearch.length > 0) {
        setFilters([
          {
            field: 'search',
            value: globalSearch.length > 0 ? globalSearch : '',
            operator: 'contains',
          },
        ]);
      } else {
        setFilters([]);
      }
    } catch (error) {
      onError(error);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [globalSearch]);

  // Function to flatten a nested structure with all its children
  const flattenNestedStructure = (items: any[]): any[] => {
    const result: any[] = [];

    const processItem = (item: any) => {
      result.push(item);
      if (item.children && item.children.length > 0) {
        item.children.forEach(processItem);
      }
    };

    items.forEach(processItem);
    return result;
  };

  // Get all documents for display, including all children
  const displayDocuments = useMemo(() => {
    if (!dataGridProps.rows) return [];

    // Convert readonly array to regular array for buildNestedDocuments
    const rowsArray = Array.isArray(dataGridProps.rows)
      ? [...dataGridProps.rows]
      : [];

    // Use buildNestedDocuments to create a hierarchical structure
    const nestedDocuments = buildNestedDocuments(rowsArray);

    // Flatten the hierarchical structure for the DataGrid
    return flattenNestedStructure(nestedDocuments);
  }, [dataGridProps.rows]);

  const displayColumns = React.useMemo(() => {
    const filteredColumns = columns;

    return filteredColumns;
  }, [columns]);

  function renderGrid() {
    return (
      <>
        <DataGridPro
          {...dataGridProps}
          apiRef={apiRef}
          filterMode="server"
          getRowId={row => row?.id}
          isGroupExpandedByDefault={expandGroup}
          rows={displayDocuments}
          disableColumnMenu
          columns={displayColumns}
          pagination
          paginationMode="server"
          sx={{
            '& .MuiDataGrid-cell': {
              display: 'flex',
              alignSelf: 'center',
              height: '100%',
              border: 'none',
            },
          }}
          onCellClick={handleOnCellClick}
          onRowClick={params => {
            // Toggle expansion state when a row with children is clicked
            const row = params.row;
            if (row && row.children && row.children.length > 0) {
              const id = params.id.toString();

              // Toggle expanded state
              setExpandedRowIds(prev => {
                const newExpanded = new Set(prev);

                if (newExpanded.has(id)) {
                  // If already expanded, collapse it
                  newExpanded.delete(id);

                  // Remember this was manually collapsed by the user
                  setManuallyCollapsedRowIds(prev => {
                    const newSet = new Set(prev);
                    newSet.add(id);
                    return newSet;
                  });
                } else {
                  // If collapsed, expand it
                  newExpanded.add(id);

                  // Remove from manually collapsed set
                  setManuallyCollapsedRowIds(prev => {
                    const newSet = new Set(prev);
                    newSet.delete(id);
                    return newSet;
                  });
                }

                return newExpanded;
              });
            }
          }}
          localeText={{ toolbarColumns: 'View' }}
          slotProps={{
            panel: {
              anchorEl: viewButtonEl,
            },
            toolbar: {
              onClick: (e: React.MouseEvent) => {
                e.stopPropagation();
                setViewButtonEl(null);
              },
            },
          }}
          treeData
          // getTreeDataPath={row => {
          //   return [row?.sourceDocumentId?.toString(), row?.id?.toString()];
          // }}
          getTreeDataPath={row => {
            // If the row has a pre-calculated hierarchy path, use it
            if (row.hierarchyPath) {
              return row.hierarchyPath;
            }

            // Otherwise, build the path from parent-child hierarchy
            const path = [];
            let current = row;
            let depth = 0;
            const maxDepth = 5; // Maximum depth to prevent infinite loops

            while (current && depth < maxDepth) {
              path.unshift(current.id);

              // Find the parent document
              if (
                current.sourceDocumentId &&
                current.sourceDocumentId !== current.id
              ) {
                // Look in all rows, not just paginated ones, to ensure we find all parents
                const parent = dataGridProps.rows?.find(
                  doc => doc.id === current.sourceDocumentId,
                );

                if (parent) {
                  current = parent;
                } else {
                  break; // Parent not found
                }
              } else {
                break; // No parent or self-referencing parent
              }

              depth++;
            }

            return path;
          }}
          groupingColDef={{
            headerName: translate('trainings-content.fields.content'),
            flex: 2,
            valueGetter: (_, row) => {
              return (
                <>
                  {getFileIcon(row.type)} {row.filename || row.title || '-'}
                </>
              );
            },
          }}
        />

        {selectedTrainingContent && (
          <>
            <TrainingContentViewDetails
              showDialog={showViewDialog}
              sourceTrainingContent={dataGridProps.rows?.find(doc => {
                return (
                  doc.id === selectedTrainingContent.sourceDocumentId &&
                  doc.id !== selectedTrainingContent.id
                );
              })}
              handleCloseDialog={handleCloseViewDialog}
              selectedTrainingContent={selectedTrainingContent}
              refetchTrainingContent={handleRefetchTrainingContent}
            />

            <TrainingContentCrawlDialog
              open={showCrawlDialog}
              onClose={handleCloseCrawlDialog}
              moduleId={moduleId || selectedTrainingContent?.moduleId}
              contentId={selectedTrainingContent?.id}
              onSuccess={handleRefetchTrainingContent}
              initUrl={selectedTrainingContent?.url}
            />
          </>
        )}
      </>
    );
  }

  return <Box>{renderGrid()}</Box>;
};

const MemorisedListTrainingsContent = React.memo(ListTrainingsContent);

export default MemorisedListTrainingsContent;
