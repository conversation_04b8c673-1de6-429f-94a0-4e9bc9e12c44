import { requestContext } from '@fastify/request-context';
import { FastifyInstance, FastifyRequest, FastifyReply, FastifyPluginOptions } from 'fastify';
import { RelicOrganization, RelicPractitioner } from 'relic-ui';

export default async function (fastify: FastifyInstance, options: FastifyPluginOptions) {
  // By the time this hook is called, we know that there is a valid Jwt presented by the user obtained via entra or equivalent api.
  // Trying to mimic setOrganization in Authentication Hook but we may not have an organization yet.
  // In such a scenario, we modify the request body to create a new organization.
  fastify.addHook('preValidation', async (request: FastifyRequest, reply: FastifyReply) => {
    const { email, organizationId, provider } = request.body as RelicPractitioner;
    const relicCareOrg: RelicOrganization = await fastify.orgService.getRelicCareOrganization();
    requestContext.set('organization', relicCareOrg);
    const domain = (email as string).split('@').at(-1);
    const filter: unknown[] = [{ website: domain }];
    if (organizationId) {
      filter.push({ 'externalIdentifier.id': organizationId });
    }
    let registeredOrgs: RelicOrganization[] = await request.server.orgService.getOrganizations({ $or: filter });
    if (registeredOrgs.length == 0) {
      //Create a base business organization using just the domain name.
      const newOrganization: RelicOrganization = {
        id: undefined,
        resourceType: 'Organization',
        type: 'bus',
        name: domain!,
        website: domain,
        externalIdentifier: {
          id: organizationId!,
          provider: provider,
        },
      };
      //Temporarily set the request body to the new organization.
      const registrationRequest: RelicPractitioner = request.body as RelicPractitioner;
      const registeredOrg = await fastify.orgService.createOrganization(newOrganization);
      requestContext.set('organization', registeredOrg);
      request.headers['x-organization-id'] = registeredOrg.id;
      //Restore the registration request to the original request body.
      request.body = {
        ...registrationRequest,
        organizationId: registeredOrg.id,
      };
    } else {
      const registeredOrg = registeredOrgs[0];
      if (registeredOrg) {
        const registrationRequest: RelicPractitioner = request.body as RelicPractitioner;
        request.body = {
          ...registrationRequest,
          organizationId: registeredOrg.id,
        };
        requestContext.set('organization', registeredOrg);
        request.headers['x-organization-id'] = registeredOrg.id;
      }
    }
  });
}
