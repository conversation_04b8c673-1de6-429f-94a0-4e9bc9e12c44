import { FastifyInstance, FastifyReply, FastifyRequest } from "fastify";
import handler from "./handlers";
import { S } from "fluent-json-schema";
import { RelicSearchQuery } from "@/types/request";
import { IUserIdentity } from "relic-ui";
import { TrainingContent, TrainingModule } from "relic-ui";
import config from "config";
import { requestContext } from "@fastify/request-context";

export default async function (instance: FastifyInstance, options: any) {

    instance.addHook('preHandler', async function (req: FastifyRequest, rep: FastifyReply) {
        const whoami = requestContext.get('whoami' as never) as IUserIdentity;
        if (whoami.role.name !== 'admin') {
            (req.query as RelicSearchQuery).organizationId = whoami.portalIdentity.organizationId;
        }
    });

    //preserialization hook for GET training content
    async function attachAzureSASTokenHook(req: any, rep: any, payload: any) {
        const module = await (req.server as any).getTrainingModuleDetails({ id: req.params['id'] }) as TrainingModule;
        const _attachTokenToDocuments = async function (contents: TrainingContent[]) {
            if (contents.length == 0 || !contents[0].id) { //possible error object
                return payload;
            }

            const token: string = await req.server.generateAzureStorageSASToken({
                containerName: module.storage.containerName,
                permissions: 'r',
                accountName: config.get('AZURE.TRAINING_STORAGE_ACCOUNT_NAME') as string
            });

            contents.forEach(content => {
                if (content.type !== 'url' && content.url.length > 0) {
                    content.url = [content.url?.split('?')?.[0], '?', token].join('').trim();
                }
                if (content.jsonContentUrl?.length > 0) {
                    content.jsonContentUrl = [content.jsonContentUrl?.split('?')?.[0], '?', token].join('').trim();
                }
            })
            return contents;
        }

        if (rep.statusCode == 200) {
            if (payload?.id) {
                const result = await _attachTokenToDocuments([payload]);
                return result?.[0];
            } else {
                return await _attachTokenToDocuments(payload);
            }
        }
        return payload;
    }

    instance.post('/', {
        schema: {
            ...options.schema,
            tags: ['Training Module Service'],
            body: S.object()
                .prop('organizationId', S.string().description('(optional) if empty, logged-in user`s organization is used'))
                .prop('name', S.string().required().description('training module name'))
                .prop('description', S.string().description('short description of the training module'))
                .prop('role', S.object())
                .prop('crawler', S.object()
                    .prop('id', S.string().format('uuid').required().description('crawler id'))
                    .prop('configuration', S.object().description('crawler custom configuration for this training module, see type CrawlerConfiguration')
                        .prop('maxRequestsPerCrawl', S.number().minimum(1).maximum(300).default(100).description('requests handled by the crawler'))
                    ))
        }
    }, handler.createTrainingModule);

    instance.get('/', {
        schema: {
            ...options.schema,
            tags: ['Training Module Service'],
            summary: 'Search training modules',
            description: 'Obtain list of training modules based on query. API returns array of Training Modules.',
            querystring: S.object()
                .prop('_search', S.string().maxLength(255).description('search training module against fields [name, description, containerName]'))
                .prop('_order', S.enum(['asc', 'desc']).default('desc'))
                .prop('_sort', S.enum(['name', 'updateDate', 'storage']).default('updateDate'))
                .prop('_start', S.number().default(0).minimum(0))
                .prop('_end', S.number().default(25).minimum(1))
        },
        preHandler: async function (req: FastifyRequest, rep: FastifyReply) {
            const sort = (req.query as RelicSearchQuery)._sort;
            (req.query as RelicSearchQuery)._sort = sort == 'storage' ? 'storage.containerName' : sort;
        }
    }, handler.searchTrainingModules);

    instance.get('/:id', {
        schema: {
            ...options.schema,
            tags: ['Training Module Service'],
            summary: 'Get training module by ID.',
            description: 'Obtain training module details specified by ID. See db collection trainingModules > id (field).',
            params: S.object()
                .prop('id', S.string().format('uuid').required().description('training module ID'))
        }
    }, handler.getTrainingModuleDetails);

    instance.patch('/:id', {
        schema: {
            ...options.schema,
            tags: ['Training Module Service'],
            params: S.object()
                .prop('id', S.string().format('uuid').required().description('training module ID')),
        }
    }, handler.updateTrainingModuleDetails);

    instance.delete('/:id', {
        schema: {
            ...options.schema,
            tags: ['Training Module Service'],
            params: S.object()
                .prop('id', S.string().format('uuid').required().description('training module ID'))
        }
    }, handler.deleteTrainingModule);

    instance.post('/:id/contents', {
        schema: {
            ...options.schema,
            tags: ['Training Module Service'],
            description: 'add a training content to a module',
            params: S.object()
                .prop('id', S.string().format('uuid').required().description('training module id, see db collection trainingModules')),
            body: S.object()
                .prop('description', S.string().description('content description'))
                .prop('url', S.string().required().format('url').description(`if file, Azure Storage blob URL. if url, url to crawl`))
                .prop('filename', S.string().description('(for files, then required) filename to be used for display'))
                .prop('documentId', S.string().description('(for files, then required) Azure Storage blob filename or id'))
                .prop('type', S.string().required().description('file type ie: application/json, application/pdf, url. For type=folder, url is ignored and sitemap is required'))
                .prop('sitemap', S.array().description(`see URLLike`).items(S.object()
                    .prop('url', S.string().required().format('url').description(`External URL to crawl`))
                ))
                .prop('options', S.object().description('custom crawler configuration, see RelicCrawlerOptions')
                    .prop('maxUrlsToCrawl', S.number().minimum(1).maximum(300).default(100).description('maximum number of urls to crawl'))
                    .prop('maxDepth', S.number().minimum(0).default(5).description('maximum depth of the crawl'))
                    .prop('useReadableContent', S.boolean().default(true).description('use readable content'))
                    .prop('crawlEmbeddedLinks', S.boolean().default(false).description('crawl embedded links'))
                )
        },
        preSerialization: attachAzureSASTokenHook,
    }, handler.addTrainingContent);

    instance.get('/:id/contents', {
        schema: {
            ...options.schema,
            tags: ['Training Module Service'],
            description: 'search training contents under a module',
            params: S.object()
                .prop('id', S.string().format('uuid').required().description('training module id, see db collection trainingModules')),
            querystring: S.object()
                .prop('_search', S.string().maxLength(255).description('search training contents against fields [filename]'))
                .prop('_order', S.enum(['asc', 'desc']).default('desc'))
                .prop('_sort', S.enum(['filename', 'updateDate', 'type', 'status']).default('updateDate'))
                .prop('_start', S.number().default(0).minimum(0))
                .prop('_end', S.number().default(25).minimum(1))
        },
        preSerialization: attachAzureSASTokenHook,
    }, handler.searchTrainingContent);

    instance.get('/:id/contents/:contentId', {
        schema: {
            ...options.schema,
            tags: ['Training Module Service'],
            description: 'retrieve training content details',
            params: S.object()
                .prop('id', S.string().format('uuid').required().description('training module id, see db collection trainingModules'))
                .prop('contentId', S.string().format('uuid').required().description('training content id, see db collection trainingContents'))
        },
        preSerialization: attachAzureSASTokenHook,
    }, handler.getTrainingContentDetails);

    instance.patch('/:id/contents/:contentId', {
        schema: {
            ...options.schema,
            tags: ['Training Module Service'],
            description: 'update training content details',
            params: S.object()
                .prop('id', S.string().format('uuid').required().description('training module id, see db collection trainingModules'))
                .prop('contentId', S.string().format('uuid').required().description('training content id, see db collection trainingContents'))
        }
    }, handler.updateTrainingContentDetails);

    instance.delete('/:id/contents/:contentId', {
        schema: {
            ...options.schema,
            tags: ['Training Module Service'],
            description: 'delete training content',
            params: S.object()
                .prop('id', S.string().format('uuid').required().description('training module id, see db collection trainingModules'))
                .prop('contentId', S.string().format('uuid').required().description('training content id, see db collection trainingContents'))
        }
    }, handler.deleteTrainingContent);


    instance.post('/:id/contents/estimate', {
        schema: {
            ...options.schema,
            tags: ['Training Module Service'],
            description: '(for URL only) test content request to obtain URL count estimate',
            params: S.object()
                .prop('id', S.string().format('uuid').required().description('training module id, see db collection trainingModules')),
            body: S.object()
                .prop('url', S.string().required().format('url').description(`External URL to crawl`))
                .prop('options', S.object().description('custom crawler configuration, see RelicCrawlerOptions')
                    .prop('maxUrlsToCrawl', S.number().minimum(1).maximum(300).default(100).description('maximum number of urls to crawl'))
                    .prop('maxDepth', S.number().minimum(0).default(5).description('maximum depth of the crawl'))
                    .prop('useReadableContent', S.boolean().default(true).description('use readable content'))
                    .prop('crawlEmbeddedLinks', S.boolean().default(true).description('crawl embedded links'))
                )
        }
    }, handler.estimateTrainingContent);

    instance.post('/:id/contents/:contentId/scrape', {
        schema: {
            ...options.schema,
            tags: ['Training Module Service'],
            description: 'Perform web scraping using sitemap',
            params: S.object()
                .prop('id', S.string().format('uuid').required().description('training module id, see db collection trainingModules'))
                .prop('contentId', S.string().format('uuid').required().description('training content id, see db collection trainingContents')),
            body: S.object()
                .prop('sitemap', S.array().description(`see URLLike`).required().minItems(1).items(S.object()
                    .prop('url', S.string().required().format('url').description(`External URL to crawl`))
                ))
                .prop('options', S.object().description('custom crawler configuration, see RelicCrawlerOptions')
                    .prop('maxUrlsToCrawl', S.number().minimum(1).maximum(300).default(100).description('maximum number of urls to crawl'))
                    .prop('maxDepth', S.number().minimum(0).default(5).description('maximum depth of the crawl'))
                    .prop('useReadableContent', S.boolean().default(true).description('use readable content'))
                    .prop('crawlEmbeddedLinks', S.boolean().default(false).description('crawl embedded links'))
                )
        }
    }, handler.scrapeTrainingContent);
}
