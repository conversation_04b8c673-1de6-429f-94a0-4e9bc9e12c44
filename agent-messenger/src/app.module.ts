import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { HttpModule } from '@nestjs/axios';
import { LoggerModule } from 'nestjs-pino';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { EventController, CallEventController } from './event/event.controller';
import { ChatService } from './chat/chat.service';
import { CallService } from './call/call.service';
import { ChatClientManager } from './chat/chat-client-manager.service';
import { NodeService } from './node/node.service';
import { AgentService } from './agent/agent.service';
import { RedisService } from './redis/redis.service';
import { TrainingsController } from './trainings/trainings.controller';
import { ThreadSpeechService } from './thread/thread.speech';
import { ThreadService } from './thread/thread.service';

@Module({
  imports: [
    LoggerModule.forRoot({
      pinoHttp: {
        autoLogging: false,
        transport: {
          target: 'pino-pretty',
          options: {
            colorize: true,
          },
        },
        serializers: {
          req: (req) => ({
            id: req.id,
            method: req.method,
            url: req.url,
          }),
        },
      },
    }),
    ConfigModule.forRoot(),
    HttpModule,
  ],
  controllers: [AppController, EventController, CallEventController, TrainingsController],
  providers: [
    NodeService,
    AppService,
    ChatService,
    CallService,
    ChatClientManager,
    AgentService,
    RedisService,
    ThreadSpeechService,
    ThreadService,
  ],
})
export class AppModule { }

