import React from 'react';
import { RelicPatient } from 'relic-ui';
import { useAutocomplete } from '@refinedev/mui';
import { Control, Controller } from 'react-hook-form';

import TextField from '@mui/material/TextField';
import Autocomplete from '@mui/material/Autocomplete';

interface PatientDropdownProps {
  control: Control<any>;
  name: string;
  label: string;
  value: string | null;
  error: boolean;
  helperText: string;
  onChange?: (value: RelicPatient | null) => void;
  disabled?: boolean;
}

export const PatientDropdown: React.FC<PatientDropdownProps> = ({
  control,
  name,
  label,
  value,
  error,
  helperText,
  onChange,
  disabled,
}) => {
  const { autocompleteProps: patientAutocompleteProps } = useAutocomplete({
    resource: 'demo/organization/patients?_start=0&_end=1&active=true',
    dataProviderName: 'demo_provider',
  });

  return (
    <Controller
      control={control}
      name={name}
      defaultValue={value}
      rules={{ required: 'This field is required' }}
      render={({ field }) => {
        const { onChange: fieldOnChange, value: fieldValue } = field;
        return (
          <Autocomplete
            loading={patientAutocompleteProps.loading}
            options={patientAutocompleteProps.options}
            onInputChange={patientAutocompleteProps.onInputChange}
            disabled={disabled}
            autoHighlight
            getOptionLabel={(option: RelicPatient) => option.name || ''}
            isOptionEqualToValue={(option, value) => {
              return (
                value === undefined ||
                option?.id?.toString() === (value?.id ?? value).toString()
              );
            }}
            onChange={(_, newValue) => {
              fieldOnChange(newValue && newValue.id ? newValue.id : null);
              if (onChange) {
                onChange(newValue ? newValue : null);
              }
            }}
            renderInput={params => (
              <TextField
                {...params}
                disabled={disabled}
                label={label}
                variant="outlined"
                margin="normal"
                error={error}
                helperText={helperText}
                required
              />
            )}
            value={
              patientAutocompleteProps.options?.find(
                org => org.id === fieldValue,
              ) || null
            }
          />
        );
      }}
    />
  );
};
