import { RelicDocument } from './document';
import { Role } from './relicBase';
import { StorageLocation } from './storage';
import { URLLike } from './crawler';
// refers to trainingmodules under a training
export type AssignedTrainingModule = TrainingModule & {
  indexerName: string;
  parsingMode: 'json';

  /**
   * not saved in DB, except when deletion is inprogress,
   * then status is temporarily set as 'inprogress_delete'
   * until the indexer and its indexed-documents are compeletely deleted from the index (training)
   *
   * inprogress_delete: (reliccare only), temporary status saved in DB until module deletion is complete
   *
   * inprogress: indexer is running (indexerexecutionstatus), not saved in DB
   *
   * success: indexer last run succeeded (indexerexecutionstatus), not saved in DB
   *
   * reset: indexer was reset (indexerexecutionstatus), not saved in DB
   *
   *
   * see https://learn.microsoft.com/en-us/javascript/api/@azure/search-documents/indexerexecutionstatus?view=azure-node-latest
   */
  indexerStatus?: 'inprogress_delete' | 'transientfailure' | 'success' | 'inprogress' | 'reset';
  indexerLastUpdateDate?: Date; //not saved in db
};

export type Training = {
  id: string;
  organizationId: string;
  name: string;
  description?: string;
  indexName: string; //system-assigned
  skillsetName: string;
  schedule?: {
    interval: string; //default to PT24H (every 24 hours)
    startTime: Date; //UTC
  };
  modules: Array<AssignedTrainingModule>;
  createdBy?: {
    id: string;
    resourceType: string;
  };
  createDate?: Date;
  updateDate?: Date; //latest assignedModule indexerLastUpdateDate
};

export type TrainingModule = {
  id: string;
  organizationId: string;
  name: string;
  description?: string;
  subject?: string;
  intent?: string;
  storage: StorageLocation;
  role?: Role;
  createdBy?: {
    id: string;
    resourceType: string;
  };
  createDate?: Date;
  updatedBy?: {
    id: string;
    resourceType: string;
  };
  updateDate?: Date;
};

export type TrainingContent = Omit<RelicDocument, 'status'> & {
  moduleId: string;
  subject?: string;
  intent?: string;
  //for requestType=file, extract texts and upload JSON file
  jsonContentUrl?: string;
  lastProcessingDate?: Date;
  sitemap?: Array<URLLike>;
  title?: string;
  /**
   * pending: initial status
   * inprogress: crawler is running the scraping + crawling process
   * estimating_sitemap: url-estimator is running to estimate the number of URLs to crawl
   * sitemap_ready: url-estimator has finished and sitemap is ready
   * sitemap_failed: url-estimator has failed
   * done: training content is ready (terminal status)
   * failed: training content failed (terminal status)
   *
   * Sample status transitions:
   * 1. pending -> inprogress -> done (normal crawling request)
   * 2. pending -> estimating_sitemap -> sitemap_ready (estimate request)
   * 3. pending -> estimating_sitemap -> sitemap_ready -> inprogress -> done (estimate + crawl request)
   * 4. pending -> estimating_sitemap -> sitemap_failed
   */
  status: 'pending' | 'estimating_sitemap' | 'sitemap_ready' | 'sitemap_failed' | 'inprogress' | 'failed' | 'done';
};
