{"type": "object", "properties": {"name": {"type": "string"}, "identifier": {"type": "string"}, "type": {"type": "object", "properties": {"definition": {"type": "string"}, "code": {"type": "string"}, "display": {"type": "string"}}}, "phone": {"type": "string"}, "fax": {"type": "string"}, "redirectUri": {"type": "string"}, "description": {"type": "string"}, "address": {"type": "object", "properties": {"use": {"type": "string", "enum": ["home", "work", "temp", "old", "billing"]}, "type": {"type": "string", "enum": ["postal", "physical", "both"]}, "line": {"type": "array", "items": {"type": "string"}}, "city": {"type": "string"}, "district": {"type": "string"}, "state": {"type": "string"}, "postalCode": {"type": "string"}, "country": {"type": "string"}}, "required": ["use", "type", "line", "city", "district", "state", "postalCode", "country"]}, "fhirStore": {"type": "object", "properties": {"defaultLanguage": {"type": "object", "properties": {"system": {"type": "string"}, "code": {"type": "string"}, "display": {"type": "string"}}, "required": ["system", "code", "display"]}, "clientApplication": {"oneOf": [{"type": "object", "properties": {"id": {"type": "string"}, "compartment": {"type": "string"}}, "required": ["id", "compartment"], "additionalProperties": false}, {"type": "null"}]}}}}, "required": ["name"]}