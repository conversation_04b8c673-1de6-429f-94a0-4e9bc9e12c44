import * as React from 'react';
import { RelicDocument } from 'relic-ui';
import { useDropzone } from 'react-dropzone';
import { useParams } from 'react-router-dom';
import { Controller } from 'react-hook-form';
import { IUser, IUserIdentity } from 'relic-ui';
import { useForm } from '@refinedev/react-hook-form';
import { useCreate, useTranslate, useGetIdentity } from '@refinedev/core';

import Box from '@mui/material/Box';
import Alert from '@mui/material/Alert';
import Dialog from '@mui/material/Dialog';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import DeleteIcon from '@mui/icons-material/Delete';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import DescriptionIcon from '@mui/icons-material/Description';
import InsertDriveFileIcon from '@mui/icons-material/InsertDriveFile';

import { uploadToAzureBlobStorage } from 'src/utils/pdf-utils';

import { Create } from 'src/components/refine-customs/create';

type Props = {
  open: boolean;
  onClose: VoidFunction;
};

const CreateDocument = ({ open, onClose }: Props) => {
  const translate = useTranslate();

  const { mutate } = useCreate();

  const { id: patientId } = useParams<{ id?: string }>();

  const [file, setFile] = React.useState<File | null>(null);

  const [uploadError, setUploadError] = React.useState<string | null>('');
  const { data: currentUser } = useGetIdentity<IUser>();

  const currentIdentity: IUserIdentity =
    currentUser?.userIdentity as IUserIdentity;

  const {
    saveButtonProps,
    formState: { errors },
    refineCore: { formLoading },
    handleSubmit,
    control,
    setError,
    clearErrors,
  } = useForm({
    defaultValues: {
      header: '0',
      footer: '0',
      file: null,
    },
  });

  const addFile = React.useCallback(
    (acceptedFiles: File[]) => {
      const file = acceptedFiles[0];
      setFile(file);
      clearErrors('file');
    },
    [clearErrors],
  );

  const removeFile = () => {
    setFile(null);
  };

  const onSubmit = async (data: any) => {
    if (!file) {
      setError('file', {
        type: 'manual',
        message: 'Please select a file to upload.',
      });
      return;
    }

    try {
      const organizationId = currentIdentity?.portalIdentity?.organizationId;
      if (!organizationId) {
        setUploadError('Organization ID is missing.');
        return;
      }

      const relicDocument: RelicDocument = {
        url: '',
        documentId: file.name,
        filename: file.name,
        type: file.type,
        organizationId,
        status: 'done',
        translationType: 'mono',
        language: 'EN-US',
        header: data.header,
        footer: data.footer,
        ...(patientId && { patientId }),
      };
      const uploadOption = {
        accountName: 'facility',
        containerName: organizationId,
      };

      const uploadedRelicDocument = await uploadToAzureBlobStorage(
        file,
        relicDocument,
        uploadOption,
      );
      if (!uploadedRelicDocument.url || !uploadedRelicDocument.documentId) {
        setUploadError('Failed to upload the document.');
        return;
      }

      mutate({
        resource: 'documents',
        values: uploadedRelicDocument,
        successNotification: () => ({
          message: translate(
            `Successfully added ${uploadedRelicDocument.filename}`,
            {
              resource: 'documents',
            },
          ),
          type: 'success',
        }),
        errorNotification: error => ({
          message: translate('notifications.createError', {
            resource: 'documents',
            statusCode: error?.statusCode,
          }),
          type: 'error',
        }),
      });

      onClose();
    } catch (error) {
      console.error('Upload failed:', error);
      setUploadError(`Error uploading document: ${error.message}`);
    }
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop: addFile,
    accept: { 'application/pdf': ['.pdf'] },
  });

  return (
    <Dialog
      fullWidth
      maxWidth="sm"
      open={open}
      onClose={onClose}
      aria-labelledby="alert-dialog-title"
      aria-describedby="alert-dialog-description"
    >
      <Create
        isLoading={formLoading}
        saveButtonProps={{
          ...saveButtonProps,
          onClick: handleSubmit(onSubmit),
        }}
        onClose={onClose}
        title={translate('documents.newDocument')}
      >
        {errors.file && (
          <Alert sx={{ my: 2 }} severity="warning" variant="outlined">
            {(errors as any).file.message}
          </Alert>
        )}
        <Box
          {...getRootProps()}
          sx={{
            border: '2px dashed',
            borderColor: isDragActive ? 'primary.main' : 'grey.400',
            borderRadius: 2,
            p: 2,
            mt: 2,
            cursor: 'pointer',
            textAlign: 'center',
            transition: 'border-color 0.3s',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <input {...getInputProps()} />
          {isDragActive ? (
            <DescriptionIcon
              sx={{ fontSize: 60, color: 'primary.main', mb: 2 }}
            />
          ) : (
            <CloudUploadIcon sx={{ fontSize: 60, color: 'grey.500', mb: 2 }} />
          )}
          {isDragActive ? (
            <Typography variant="subtitle1" sx={{ color: 'primary.main' }}>
              Drop the PDF file here ...
            </Typography>
          ) : (
            <Typography variant="subtitle1" sx={{ color: 'text.secondary' }}>
              Drag 'n' drop a PDF file here, or click to select a PDF file
            </Typography>
          )}
        </Box>

        {file && (
          <Box
            sx={{
              mt: 2,
              p: 2,
              border: 1,
              borderColor: 'divider',
              borderRadius: '8px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              typography: 'body1',
            }}
          >
            <InsertDriveFileIcon sx={{ mr: 1 }} />
            <Box
              sx={{ flexGrow: 1, overflow: 'hidden', textOverflow: 'ellipsis' }}
            >
              {file.name} - {(file.size / 1024).toFixed(2)} KB
            </Box>
            <DeleteIcon sx={{ cursor: 'pointer' }} onClick={removeFile} />
          </Box>
        )}
        <Controller
          name="header"
          control={control}
          rules={{ required: 'Header is required.' }}
          render={({ field }) => (
            <TextField
              {...field}
              label="Document Header"
              variant="outlined"
              fullWidth
              margin="normal"
              helperText={
                (errors as any).header
                  ? (errors as any).header.message
                  : 'How many lines from top are header?'
              }
              error={!!errors.header}
            />
          )}
        />
        <Controller
          name="footer"
          control={control}
          rules={{ required: 'Footer is required.' }}
          render={({ field }) => (
            <TextField
              {...field}
              label="Document Footer"
              variant="outlined"
              fullWidth
              margin="normal"
              helperText={
                (errors as any).footer
                  ? (errors as any).footer.message
                  : 'How many lines from bottom are footer?'
              }
              error={!!errors.footer}
            />
          )}
        />
        {uploadError && (
          <Typography color="error" variant="caption" sx={{ mt: 2 }}>
            {uploadError}
          </Typography>
        )}
      </Create>
    </Dialog>
  );
};

export default CreateDocument;
