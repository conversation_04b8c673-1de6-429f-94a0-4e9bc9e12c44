import { useGetIdentity, useOne } from '@refinedev/core';
import { useEffect, useMemo, useState } from 'react';
import { useLocation } from 'react-router-dom';
// relic-ui
import type {
  IUser,
  RelicAgent,
  RelicChatParticipant,
  RelicThreadProviderOptions,
  RelicUserProviderOptions,
  Thread,
} from 'relic-ui';
import {
  CommunicationPanel,
  RelicThreadProvider,
  RelicUserProvider,
} from 'relic-ui';

// @mui
import { Drawer, useTheme } from '@mui/material';
import Stack from '@mui/material/Stack';

import { useRouter } from 'src/routes/hooks';

import { useResponsive } from 'src/hooks/use-responsive';

import { getProviderServicesApi } from 'src/providers/utils/auth-utils';
import { getNodeServicesApi } from 'src/utils/app-config';

import ThreadViewToolbar from 'src/components/_threads/show/threads-toolbar';
import { AgentDetailsPopover, AgentDrawer } from 'src/components/_agent';
import { usePopover } from 'src/hooks/use-popover';

const DRAWER_WIDTH = {
  lg: 380,
  md: 320,
  sm: 280,
  xs: '100%',
};

interface ThreadsViewProps {
  threadId?: string;
}

// ----------------------------------------------------------------------
export const ThreadsView: React.FC<ThreadsViewProps> = ({ threadId }) => {
  const router = useRouter();
  const [showThreadDetails, setShowThreadDetails] = useState(false);
  const [accessToken, setAccessToken] = useState<string>('');
  const [idToken, setIdToken] = useState<string>('');

  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const currentThreadId = threadId || queryParams.get('threadId');

  const { data: authUser } = useGetIdentity<IUser>();
  const theme = useTheme();
  const upMd = useResponsive('up', 'md');
  const popoverDetails = usePopover();
  useEffect(() => {
    if (authUser?.accessToken) {
      setAccessToken(authUser.accessToken);
      setIdToken(authUser?.idToken as string);
    }
  }, [authUser]);

  const {
    data: threadData,
    isLoading: isLoadingThread,
    error: threadError,
  } = useOne({
    resource: `communication/chat/threads`,
    id: currentThreadId as string,
    queryOptions: {
      enabled: !!currentThreadId,
    },
  });

  // const { threadData, isLoading: isLoadingThread } = useThreadData(
  //   currentThreadId,
  //   accessToken,
  //   authUser,
  // );

  const threadsDetail = threadData?.data as Thread | undefined;
  const [showEdit, setShowEdit] = useState(false);

  const currentAgentParticipant = useMemo(() => {
    // If no participants available, return undefined
    if (!threadsDetail?.participants?.length) return undefined;

    // Priority 1: Look for Patient Agent
    const patientAgent = threadsDetail.participants.find(
      (p: RelicChatParticipant) => p.type === 'Patient Agent',
    );
    if (patientAgent) return patientAgent;

    // Priority 2: Look for Staff Agent
    const staffAgent = threadsDetail.participants.find(
      (p: RelicChatParticipant) => p.type === 'Staff Agent',
    );
    if (staffAgent) return staffAgent;

    // Priority 3: Fall back to System Agent (guaranteed to exist)
    return threadsDetail.participants.find(
      (p: RelicChatParticipant) => p.type === 'System Agent',
    );
  }, [threadsDetail?.participants]);

  const {
    data: currentAgentData,
    isLoading: isLoadingCurrentAgent,
    error: currentAgentError,
  } = useOne<RelicAgent>({
    resource: `agents`,
    id: currentAgentParticipant?.resourceId || '',
    queryOptions: {
      enabled: !!currentAgentParticipant?.resourceId,
    },
  });

  const agentData: RelicAgent = currentAgentData?.data as RelicAgent;

  // RelicUserProviderOptions memoized
  const relicUserProviderOptions: RelicUserProviderOptions = useMemo(
    () => ({
      accessToken,
      idToken,
      serviceUri: getProviderServicesApi(getNodeServicesApi()),
    }),
    [accessToken, idToken],
  );

  // RelicThreadProviderOptions memoized
  const relicThreadProviderOptions: RelicThreadProviderOptions = useMemo(
    () => ({
      displayInEnglish: true,
    }),
    [],
  );

  // Custom styling memoized
  const customMessageThreadStyles = useMemo(
    () => ({
      chatMessageContainer: {
        backgroundColor: theme.palette.background.neutral,
        color: theme.palette.text.primary,
        fontFamily: theme.typography.fontFamily,
        fontSize: `${theme.typography.body2.fontSize}px`,
      },
      myChatMessageContainer: {
        backgroundColor: theme.palette.primary.lighter,
        color: theme.palette.text.primary,
        fontFamily: theme.typography.fontFamily,
        fontSize: `${theme.typography.body2.fontSize}px`,
      },
      chatContainer: {
        maxWidth: '100%',
      },
      citationStyles: {
        tooltip: {
          backgroundColor: '#F7F7F7',
          textColor: 'rgba(0, 0, 0, 0.87)',
          borderColor: '#dadde9',
          hoverBackgroundColor: '#e6e6e6',
          titleColor: '#000000',
          linkColor: 'green',
        },
        link: {
          hoverBackgroundColor: '#c8d4fa',
          textColor: '#202020',
          titleColor: '#000000',
          linkColor: '#000000',
        },
        reference: {
          textColor: '#202020',
          backgroundColor: '#b6b6b6',
          titleColor: '#000000',
        },
      },
    }),
    [theme],
  );

  const customSendBoxStyles = useMemo(
    () => ({
      textField: {
        fontFamily: theme.typography.fontFamily,
        fontSize: `${theme.typography.body2.fontSize}px`,
      },
    }),
    [theme],
  );

  const toolbarProps = useMemo(
    () => ({
      showResponseManagement: true,
    }),
    [],
  );

  const renderCommunicationPanel = useMemo(
    () => (
      <CommunicationPanel
        pxAboveCommunicationPanel={156}
        toolbarProps={toolbarProps}
        messageThreadStyles={customMessageThreadStyles}
        sendBoxStyles={customSendBoxStyles}
      />
    ),
    [toolbarProps, customMessageThreadStyles, customSendBoxStyles],
  );

  // Add loading state handling
  // if (isLoadingThread || isLoadingCurrentAgent) {
  //   return (
  //     <Stack
  //       sx={{ width: 1, height: '62vh' }}
  //       alignItems="center"
  //       justifyContent="center"
  //     >
  //       <CircularProgress color="inherit" />
  //     </Stack>
  //   );
  // }

  // Ensure we have required data
  // if (!threadsDetail && currentThreadId) {
  //   return null;
  // }

  return (
    <>
      <ThreadViewToolbar
        onBack={router.back}
        onOpenAdd={router.back}
        title={threadsDetail?.threadSubject?.title || 'New Thread'}
        showThreadDetails={showThreadDetails}
        onToggleSidePane={
          currentThreadId
            ? () => setShowThreadDetails(!showThreadDetails)
            : null
        }
      />

      <AgentDetailsPopover
        popover={popoverDetails}
        onOpenEdit={() => {
          setShowEdit(true);
          popoverDetails.onClose();
        }}
      />
      <Stack
        sx={{
          height: '100%',
          ...(showThreadDetails && {
            width: {
              sm: `calc(100% - ${DRAWER_WIDTH.sm}px)`,
              md: `calc(100% - ${DRAWER_WIDTH.md}px)`,
              lg: `calc(100% - ${DRAWER_WIDTH.lg}px)`,
            },
          }),
        }}
      >
        {authUser && accessToken ? (
          <RelicUserProvider {...relicUserProviderOptions}>
            <RelicThreadProvider {...relicThreadProviderOptions}>
              <Stack direction="row" sx={{ flexGrow: 1 }}>
                {renderCommunicationPanel}
              </Stack>
            </RelicThreadProvider>
          </RelicUserProvider>
        ) : null}

        <AgentDrawer
          popoverDetails={popoverDetails}
          showAgent={showThreadDetails}
          showEdit={showEdit}
          setShowEdit={setShowEdit}
          agentData={agentData}
          loading={isLoadingCurrentAgent}
        />
      </Stack>
    </>
  );
};
