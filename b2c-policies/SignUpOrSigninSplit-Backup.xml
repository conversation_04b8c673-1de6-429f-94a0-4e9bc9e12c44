<TrustFrameworkPolicy xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns:xsd="http://www.w3.org/2001/XMLSchema"
    xmlns="http://schemas.microsoft.com/online/cpim/schemas/2013/06" PolicySchemaVersion="*******" TenantId="patientauth.onmicrosoft.com" PolicyId="B2C_1A_Demo_SignUp_SignIn_SplitEmailVerificationAndSignUp" PublicPolicyUri="http://patientauth.onmicrosoft.com/B2C_1A_Demo_SignUp_SignIn_SplitEmailVerificationAndSignUp">

    <BasePolicy>
        <TenantId>patientauth.onmicrosoft.com</TenantId>
        <PolicyId>B2C_1A_TrustFrameworkExtensions</PolicyId>
    </BasePolicy>

    <BuildingBlocks>
        <ClaimsSchema>
            <!-- Read only email address to present to the user-->
            <ClaimType Id="readonlyEmail">
                <DisplayName>E-mail Address</DisplayName>
                <DataType>string</DataType>
                <UserInputType>Readonly</UserInputType>
            </ClaimType>
        </ClaimsSchema>
        <ClaimsTransformations>
            <ClaimsTransformation Id="CreateReadonlyEmailClaim" TransformationMethod="FormatStringClaim">
                <InputClaims>
                    <InputClaim ClaimTypeReferenceId="email" TransformationClaimType="inputClaim" />
                </InputClaims>
                <InputParameters>
                    <InputParameter Id="stringFormat" DataType="string" Value="{0}" />
                </InputParameters>
                <OutputClaims>
                    <OutputClaim ClaimTypeReferenceId="readonlyEmail" TransformationClaimType="outputClaim" />
                </OutputClaims>
            </ClaimsTransformation>
        </ClaimsTransformations>

        <ContentDefinitions>
            <ContentDefinition Id="api.localaccount.emailVerification">
                <LoadUri>~/tenant/templates/AzureBlue/selfAsserted.cshtml</LoadUri>
                <RecoveryUri>~/common/default_page_error.html</RecoveryUri>
                <DataUri>urn:com:microsoft:aad:b2c:elements:contract:selfasserted:2.1.8</DataUri>
                <Metadata>
                    <Item Key="DisplayName">Collect information from user page</Item>
                </Metadata>
                <LocalizedResourcesReferences MergeBehavior="Prepend">
                    <LocalizedResourcesReference Language="en" LocalizedResourcesReferenceId="api.localaccount.emailVerification.en" />
                </LocalizedResourcesReferences>
            </ContentDefinition>
        </ContentDefinitions>

        <Localization Enabled="true">
            <SupportedLanguages DefaultLanguage="en" MergeBehavior="ReplaceAll">
                <SupportedLanguage>en</SupportedLanguage>
            </SupportedLanguages>
            <LocalizedResources Id="api.localaccount.emailVerification.en">
                <LocalizedStrings>
                    <LocalizedString ElementType="UxElement" StringId="button_continue">Continue</LocalizedString>
                </LocalizedStrings>
            </LocalizedResources>
        </Localization>
    </BuildingBlocks>

    <ClaimsProviders>
        <ClaimsProvider>
            <DisplayName>Email Verification</DisplayName>
            <TechnicalProfiles>
                <!--Email verification only-->
                <TechnicalProfile Id="EmailVerification">
                    <DisplayName>Initiate Email Address Verification For Local Account</DisplayName>
                    <Protocol Name="Proprietary" Handler="Web.TPEngine.Providers.SelfAssertedAttributeProvider, Web.TPEngine, Version=*******, Culture=neutral, PublicKeyToken=null" />
                    <Metadata>
                        <Item Key="ContentDefinitionReferenceId">api.localaccount.emailVerification</Item>
                        <Item Key="language.button_continue">Continue</Item>
                    </Metadata>
                    <OutputClaims>
                        <OutputClaim ClaimTypeReferenceId="email" PartnerClaimType="Verified.Email" Required="true" />
                    </OutputClaims>
                </TechnicalProfile>
            </TechnicalProfiles>
        </ClaimsProvider>
        <ClaimsProvider>
            <DisplayName>Local Account</DisplayName>
            <TechnicalProfiles>
                <!--Sign-up self-asserted technical profile without Email verification-->
                <TechnicalProfile Id="LocalAccountSignUpWithReadOnlyEmail">
                    <DisplayName>Email signup</DisplayName>
                    <Protocol Name="Proprietary" Handler="Web.TPEngine.Providers.SelfAssertedAttributeProvider, Web.TPEngine, Version=*******, Culture=neutral, PublicKeyToken=null" />
                    <Metadata>
                        <Item Key="IpAddressClaimReferenceId">IpAddress</Item>
                        <Item Key="ContentDefinitionReferenceId">api.localaccountsignup</Item>
                        <Item Key="language.button_continue">Create</Item>
                        <!-- Remove sign-up email verification -->
                        <Item Key="EnforceEmailVerification">False</Item>
                    </Metadata>
                    <InputClaimsTransformations>
                        <InputClaimsTransformation ReferenceId="CreateReadonlyEmailClaim" />
                    </InputClaimsTransformations>
                    <InputClaims>
                        <!--Sample: Set input the ReadOnlyEmail claim type to prefilled the email address-->
                        <InputClaim ClaimTypeReferenceId="readOnlyEmail" />
                    </InputClaims>
                    <OutputClaims>
                        <OutputClaim ClaimTypeReferenceId="objectId" />
                        <!-- Sample: Display the ReadOnlyEmail claim type (instead of email claim type)-->
                        <OutputClaim ClaimTypeReferenceId="readOnlyEmail" Required="true" />
                        <OutputClaim ClaimTypeReferenceId="newPassword" Required="true" />
                        <OutputClaim ClaimTypeReferenceId="reenterPassword" Required="true" />
                        <OutputClaim ClaimTypeReferenceId="executed-SelfAsserted-Input" DefaultValue="true" />
                        <OutputClaim ClaimTypeReferenceId="authenticationSource" />
                        <OutputClaim ClaimTypeReferenceId="newUser" />
                        <!-- Optional claims, to be collected from the user -->
                        <OutputClaim ClaimTypeReferenceId="displayName" />
                        <OutputClaim ClaimTypeReferenceId="givenName" />
                        <OutputClaim ClaimTypeReferenceId="surName" />
                    </OutputClaims>
                    <ValidationTechnicalProfiles>
                        <ValidationTechnicalProfile ReferenceId="AAD-UserWriteUsingLogonEmail" />
                    </ValidationTechnicalProfiles>
                    <!-- Sample: Disable session management for sign-up page -->
                    <UseTechnicalProfileForSessionManagement ReferenceId="SM-Noop" />
                </TechnicalProfile>
            </TechnicalProfiles>
        </ClaimsProvider>
    </ClaimsProviders>
    <UserJourneys>
        <UserJourney Id="SignUpOrSignIn_Custom">
            <OrchestrationSteps>
                <OrchestrationStep Order="1" Type="CombinedSignInAndSignUp" ContentDefinitionReferenceId="api.signuporsignin">
                    <ClaimsProviderSelections>
                        <ClaimsProviderSelection ValidationClaimsExchangeId="LocalAccountSigninEmailExchange" />
                    </ClaimsProviderSelections>
                    <ClaimsExchanges>
                        <ClaimsExchange Id="LocalAccountSigninEmailExchange" TechnicalProfileReferenceId="SelfAsserted-LocalAccountSignin-Email" />
                    </ClaimsExchanges>
                </OrchestrationStep>

                <!-- Check if the user has selected to sign in using one of the social providers -->
                <OrchestrationStep Order="2" Type="ClaimsExchange">
                    <Preconditions>
                        <Precondition Type="ClaimsExist" ExecuteActionsIf="true">
                            <Value>objectId</Value>
                            <Action>SkipThisOrchestrationStep</Action>
                        </Precondition>
                    </Preconditions>
                    <ClaimsExchanges>
                        <ClaimsExchange Id="SignUpWithLogonEmailExchange_EmailVerification" TechnicalProfileReferenceId="EmailVerification" />
                    </ClaimsExchanges>
                </OrchestrationStep>
                <OrchestrationStep Order="3" Type="ClaimsExchange">
                    <Preconditions>
                        <Precondition Type="ClaimsExist" ExecuteActionsIf="true">
                            <Value>objectId</Value>
                            <Action>SkipThisOrchestrationStep</Action>
                        </Precondition>
                    </Preconditions>
                    <ClaimsExchanges>
                        <ClaimsExchange Id="SignUpWithLogonEmailExchange_WithReadOnlyEmail" TechnicalProfileReferenceId="LocalAccountSignUpWithReadOnlyEmail" />
                    </ClaimsExchanges>
                </OrchestrationStep>
                <OrchestrationStep Order="4" Type="ClaimsExchange">
                    <ClaimsExchanges>
                        <ClaimsExchange Id="AADUserReadWithObjectId" TechnicalProfileReferenceId="AAD-UserReadUsingObjectId" />
                    </ClaimsExchanges>
                </OrchestrationStep>
                <OrchestrationStep Order="5" Type="SendClaims" CpimIssuerTechnicalProfileReferenceId="JwtIssuer" />
            </OrchestrationSteps>
        </UserJourney>
    </UserJourneys>

    <RelyingParty>
        <DefaultUserJourney ReferenceId="SignUpOrSignIn_Custom" />
        <TechnicalProfile Id="PolicyProfile">
            <DisplayName>PolicyProfile</DisplayName>
            <Protocol Name="OpenIdConnect" />
            <OutputClaims>
                <OutputClaim ClaimTypeReferenceId="displayName" />
                <OutputClaim ClaimTypeReferenceId="givenName" />
                <OutputClaim ClaimTypeReferenceId="surname" />
                <OutputClaim ClaimTypeReferenceId="email" />
                <OutputClaim ClaimTypeReferenceId="objectId" PartnerClaimType="sub"/>
                <OutputClaim ClaimTypeReferenceId="identityProvider" DefaultValue="" />
                <OutputClaim ClaimTypeReferenceId="tenantId" AlwaysUseDefaultValue="true" DefaultValue="{Policy:TenantObjectId}" />
            </OutputClaims>
            <SubjectNamingInfo ClaimType="sub" />
        </TechnicalProfile>
    </RelyingParty>
</TrustFrameworkPolicy>