import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  HttpError,
  IResourceComponentsProps,
  useGetIdentity,
  useOne,
  useTranslate,
} from '@refinedev/core';
import { useDataGrid } from '@refinedev/mui';
import { useForm } from '@refinedev/react-hook-form';
import * as React from 'react';
import {
  useLocation,
  useNavigate,
  useParams,
  useSearchParams,
} from 'react-router-dom';
import {
  IUser,
  RelicAllergyIntolerance,
  RelicChatParticipant,
  RelicCondition,
  RelicDocument,
  RelicOrganization,
  RelicPatient,
} from 'relic-ui';

import { Input } from '@mui/material';
import Box from '@mui/material/Box';
import MenuItem from '@mui/material/MenuItem';
import Stack from '@mui/material/Stack';
import TextField from '@mui/material/TextField';
import { GridColDef } from '@mui/x-data-grid-pro';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';

import { useRouter } from 'src/routes/hooks';

import { usePopover } from 'src/hooks/use-popover';
import { useResponsive } from 'src/hooks/use-responsive';

import { ListDocuments } from 'src/components/_document';
import {
  AddPopover,
  ChatView,
  ConditionAllergiesList,
  PatientDetailsPopover,
  PatientDrawer,
  PatientTabs,
  PatientToolbar,
} from 'src/components/_patient/edit';
import ChatViewWrapper from 'src/components/_patient/edit/patient-chat-view-wrapper';
import ListThreads from 'src/components/_threads/list/threads-list';
import { LanguageDropdown } from 'src/components/dropdown/languageDropdown';
import PhoneInput from 'src/components/phone-input';
import { ConfirmDeleteDialog } from 'src/components/refine-customs/delete-btn';
// ----------------------------------------------------------------------

type IListFilterVariables = {
  q?: string;
  PatientId?: string;
};

// ----------------------------------------------------------------------

const TABS = [
  { value: 'chat', label: 'chat.interpreterAi' },
  { value: 'threads', label: 'threads.threads' },
  { value: 'documents', label: 'documents.documents' },
  { value: 'conditions', label: 'conditions.conditions' },
  { value: 'allergies', label: 'allergyintolerances.allergyintolerances' },
];

// eslint-disable-next-line react-refresh/only-export-components
export const DRAWER_WIDTH = {
  lg: 380,
  md: 320,
  sm: 280,
  xs: '100%',
};

// ----------------------------------------------------------------------

export const PatientEdit: React.FC<IResourceComponentsProps> = () => {
  const router = useRouter();

  const [searchParams, setSearchParams] = useSearchParams();

  const navigate = useNavigate();

  const location = useLocation();

  const queryParams = new URLSearchParams(location.search);

  const translate = useTranslate();

  const popoverDetails = usePopover();

  const popoverAdd = usePopover();

  const upMd = useResponsive('up', 'md');

  const [openCondition, setOpenCondition] = React.useState(false);

  const [openAllergie, setOpenAllergie] = React.useState(false);

  const [openDocument, setOpenDocument] = React.useState(false);

  const [openThread, setOpenThread] = React.useState(false);

  const [showEdit, setShowEdit] = React.useState(false);

  const [showPatient, setShowPatient] = React.useState(true);

  const [accessToken, setAccessToken] = React.useState<string>('');

  const [idToken, setIdToken] = React.useState<string>('');

  const { id: patientId } = useParams<{ id?: string }>();

  const initialTab = queryParams.get('tab') || 'chat';

  const [currentTab, setCurrentTab] = React.useState(
    TABS.find(tab => tab.value === initialTab)?.value,
  );

  const { data: currentUser } = useGetIdentity<IUser>();

  const currentIdentityProvider: string = React.useMemo(() => {
    const identityProvider: string = (currentUser?.userIdentity
      ?.provider as string)
      ? (currentUser?.userIdentity?.provider as string)
      : 'medplum';
    return identityProvider;
  }, [currentUser]);

  const {
    watch,
    control,
    register,
    setValue,
    saveButtonProps,
    formState: { errors },
    refineCore: { queryResult, onFinish, formLoading },
    handleSubmit,
  } = useForm<RelicPatient>({
    refineCoreProps: {
      redirect: 'edit',
    },
  });

  const values = watch();

  // Type for the new parameters to update
  const updateQueryParams = React.useCallback(
    (newParams: Record<string, string>): void => {
      const updatedParams = new URLSearchParams(searchParams);

      // Merge existing and new parameters
      Object.entries(newParams).forEach(([key, value]) =>
        updatedParams.set(key, value),
      );
      setSearchParams(updatedParams);
    },
    [searchParams, setSearchParams],
  );

  const handleChangeTab = React.useCallback(
    (event: React.SyntheticEvent, newValue: string) => {
      updateQueryParams({ tab: newValue });
      setCurrentTab(newValue);
    },
    [updateQueryParams],
  );

  const handleBack = () => {
    router.push('/patients');
  };

  let thisPatient = null;
  let isPatientLoaded = false;

  if (queryResult && 'data' in queryResult && 'isSuccess' in queryResult) {
    ({ data: thisPatient, isSuccess: isPatientLoaded } = queryResult);
  }
  const currentPatient = thisPatient?.data as RelicPatient;

  const [_, setPatientData] = React.useState(currentPatient);

  React.useEffect(() => {
    if (upMd) {
      setShowPatient(true);
    } else {
      setShowPatient(false);
    }
  }, [upMd]);

  React.useEffect(() => {
    if (currentUser?.accessToken) {
      setAccessToken(currentUser.accessToken);
      setIdToken(currentUser?.idToken as string);
    }
  }, [currentUser]);

  const [patientOrganization, setPatientOrganization] = React.useState<
    RelicOrganization | undefined
  >(undefined);

  const { data: organization, isLoading: orgLoading } = useOne({
    resource: 'organizations',
    id: currentPatient?.organizationId,
    queryOptions: {
      enabled: !!currentPatient?.organizationId,
    },
  });

  React.useEffect(() => {
    if (organization?.data) {
      setPatientOrganization(organization.data as RelicOrganization);
    }
  }, [organization]);

  const { dataGridProps: conditionGridProps } = useDataGrid<
    RelicCondition,
    HttpError,
    IListFilterVariables
  >({
    resource: `patients/${currentPatient?.id}/conditions`,
    queryOptions: {
      enabled: isPatientLoaded && currentTab === 'conditions',
      cacheTime: 0,
      staleTime: 0,
    },
    syncWithLocation: false,
  });

  const conditionColumns = React.useMemo<GridColDef<RelicCondition>[]>(
    () => [
      {
        field: 'code',
        headerName: translate('conditions.fields.code'),
        minWidth: 300,
        flex: 1,
        renderCell: function render({ value }) {
          if (value) {
            return <Box component={'span'}>{value?.text}</Box>;
          } else {
            return <></>;
          }
        },
      },
      {
        field: 'note',
        flex: 1,
        headerName: translate('conditions.fields.note'),
        minWidth: 100,
      },
      {
        field: 'clinicalStatus',
        flex: 1,
        headerName: translate('conditions.fields.clinicalStatus'),
        maxWidth: 100,
      },
      {
        field: 'actions',
        headerName: translate('table.actions'),
        sortable: false,
        disableColumnMenu: true,
        renderCell: ({ row }) => (
          <ConfirmDeleteDialog
            rowId={row.id}
            hideText
            deleteButtonProps={{
              confirmTitle: translate('conditions.titles.delete'),
              confirmContent: translate('content.confirm.delete'),
              successNotification: () => ({
                message: translate('notifications.deleteSuccess', {
                  resource: 'condition',
                }),
                description: 'Successful',
                type: 'success',
              }),
              errorNotification: () => ({
                message: translate('notifications.deleteError', {
                  resource: 'condition',
                }),
                description: 'Error',
                type: 'error',
              }),
              resource: `patients/${currentPatient?.id}/conditions`,
              recordItemId: row.id,
            }}
          />
        ),
        align: 'right',
        headerAlign: 'right',
        minWidth: 80,
      },
    ],
    [translate, currentPatient],
  );

  const conditionDisplayColumns = React.useMemo(() => {
    if (currentIdentityProvider === 'pcc') {
      return conditionColumns.filter(item => item.field !== 'actions');
    }
    return conditionColumns;
  }, [currentIdentityProvider, conditionColumns]);

  const { dataGridProps: allergyIntoleranceGridProps } = useDataGrid<
    RelicAllergyIntolerance,
    HttpError,
    IListFilterVariables
  >({
    resource: 'allergyintolerances',
    permanentFilter: [
      {
        field: 'patientId',
        operator: 'eq',
        value: currentPatient?.id,
      },
    ],
    queryOptions: {
      enabled: isPatientLoaded && currentTab === 'allergies',
      cacheTime: 0,
      staleTime: 0,
    },
    syncWithLocation: false,
  });

  const allergyIntoleranceColumns = React.useMemo<
    GridColDef<RelicAllergyIntolerance>[]
  >(
    () => [
      {
        field: 'code',
        headerName: translate('allergyintolerances.fields.code'),
        minWidth: 150,
        flex: 1,
        renderCell: function render({ value }) {
          return <Box component={'span'}>{value.text}</Box>;
        },
      },
      {
        field: 'note',
        flex: 1,
        headerName: translate('allergyintolerances.fields.note'),
        minWidth: 100,
      },
      {
        field: 'clinicalStatus',
        flex: 1,
        headerName: translate('allergyintolerances.fields.clinicalStatus'),
        maxWidth: 100,
      },
      {
        field: 'actions',
        headerName: translate('table.actions'),
        sortable: false,
        disableColumnMenu: true,
        renderCell: ({ row }) => (
          <ConfirmDeleteDialog
            rowId={row.id}
            hideText
            deleteButtonProps={{
              confirmTitle: translate('allergyintolerances.titles.delete'),
              confirmContent: translate('content.confirm.delete'),
            }}
          />
        ),
        align: 'right',
        headerAlign: 'right',
        minWidth: 80,
      },
    ],
    [translate],
  );

  const {
    dataGridProps: listDocumentProps,
    setFilters: setListDocumentFilters,
    tableQuery: listDocumentsQuery,
  } = useDataGrid<RelicDocument, HttpError, IListFilterVariables>({
    resource: 'documents',
    filters: {
      mode: 'server', // Assuming server-side filtering
      permanent: [
        ...(patientId
          ? [{ field: 'patientId', operator: 'eq' as const, value: patientId }]
          : []),
      ],
    },
    queryOptions: {
      enabled: isPatientLoaded && currentTab === 'documents',
      cacheTime: 0,
      staleTime: 0,
    },
    syncWithLocation: false,
  });

  const refetchDocuments = React.useCallback(() => {
    listDocumentsQuery.refetch();
  }, [listDocumentsQuery]);

  const documentColumns = React.useMemo<GridColDef[]>(() => [], []);

  const initialFilters: CrudFilter[] = React.useMemo(() => {
    const participant: RelicChatParticipant = {
      id: {
        communicationUserId: currentPatient?.communicationIdentities?.[0]
          ?.userId as string,
      },
      resourceId: currentPatient?.id,
      resourceType: 'Patient',
    };
    const patientThreadFilter: CrudFilter = {
      field: 'participant',
      value: encodeURIComponent(JSON.stringify(participant)),
      operator: 'eq',
    };
    return [patientThreadFilter];
  }, [currentPatient]);

  const {
    dataGridProps: threadGridProps,
    filters,
    setFilters: setThreadGridFilters,
  } = useDataGrid({
    resource: 'communication/chat/threads',
    sorters: {
      initial: [
        {
          field: 'updateDate',
          order: 'desc',
        },
      ],
    },
    filters: {
      mode: 'server', // Assuming server-side filtering
      permanent: initialFilters,
    },
    queryOptions: {
      enabled: isPatientLoaded && currentTab === 'threads',
    },
    syncWithLocation: false,
  });

  const threadColumns = React.useMemo<GridColDef[]>(() => [], []);

  const renderEditForm = (
    <Stack
      spacing={3}
      component="form"
      autoComplete="off"
      sx={{
        flexDirection: 'column',
        display: showEdit ? 'flex' : 'none',
      }}
    >
      <TextField
        {...register('name', {
          required: 'This field is required',
        })}
        fullWidth
        type="text"
        name="name"
        InputLabelProps={{ shrink: true }}
        label={translate('patients.fields.name')}
        error={!!(errors as any)?.name}
        helperText={(errors as any)?.name?.message}
        disabled
      />

      <DatePicker
        {...register('birthDate', {
          //required: 'This field is required',
        })}
        format="dd/MM/yyyy"
        label={translate('patients.fields.birthDate')}
        onChange={newValue => setValue('birthDate', newValue)}
        value={values.birthDate ? new Date(values.birthDate) : null}
        disabled
        slotProps={{
          textField: {
            fullWidth: true,
            error: !!errors?.birthDate,
            helperText: (errors as any)?.birthDate?.message,
            InputLabelProps: { shrink: true },
          },
        }}
      />

      <TextField
        {...register('gender', {
          //required: 'This field is required',
        })}
        select
        fullWidth
        name="gender"
        value={values.gender || ''}
        InputLabelProps={{ shrink: true }}
        label={translate('patients.fields.gender')}
        error={!!errors?.gender}
        helperText={(errors as any)?.gender?.message}
        disabled
        SelectProps={{
          displayEmpty: true,
          sx: {
            ...(!values.gender && {
              color: 'text.disabled',
            }),
          },
        }}
      >
        <MenuItem value="" sx={{ color: 'text.disabled', display: 'none' }}>
          {translate('selects.empty')}
        </MenuItem>

        {[
          { value: 'Male', label: 'selects.gender.male' },
          { value: 'Female', label: 'selects.gender.female' },
          { value: 'Other', label: 'selects.gender.other' },
          { value: 'Unknown', label: 'selects.gender.incognito' },
        ].map(option => (
          <MenuItem key={option.value} value={option.value}>
            {translate(option.label)}
          </MenuItem>
        ))}
      </TextField>

      <LanguageDropdown
        control={control}
        name="primaryLanguage"
        label={translate('patients.fields.primaryLanguage')}
        value={values.primaryLanguage ?? currentPatient?.primaryLanguage}
        error={!!(errors as any)?.primaryLanguage}
        helperText={(errors as any)?.primaryLanguage?.message}
      />

      <Input
        sx={{ display: 'none' }}
        type="hidden"
        {...register('maritalStatus')}
        value={currentPatient?.maritalStatus}
      />

      <TextField
        {...register('email', {
          ...(currentIdentityProvider !== 'pcc' && {
            required: 'This field is required',
          }),
          pattern: {
            value: /\S+@\S+\.\S+/,
            message: 'Entered value does not match email format',
          },
        })}
        fullWidth
        type="email"
        name="email"
        InputLabelProps={{ shrink: true }}
        label={translate('patients.fields.email')}
        error={!!(errors as any)?.email}
        helperText={(errors as any)?.email?.message}
      />

      <PhoneInput
        control={control}
        name="mobilePhone"
        label={translate('patients.fields.mobilePhone')}
        value={values.mobilePhone ?? currentPatient?.mobilePhone}
      />

      <PhoneInput
        control={control}
        name="homePhone"
        label={translate('patients.fields.homePhone')}
        value={values.homePhone ?? currentPatient?.homePhone}
      />

      <Input
        sx={{ display: 'none' }}
        type="hidden"
        {...register('link')}
        value={currentPatient?.link}
      />

      <Input
        sx={{ display: 'none' }}
        type="hidden"
        {...register('organizationId')}
        value={currentPatient?.organizationId}
      />

      <Input
        sx={{ display: 'none' }}
        type="hidden"
        {...register('active')}
        value={currentPatient?.active}
      />
    </Stack>
  );

  return (
    <>
      <AddPopover
        popover={popoverAdd}
        openDocument={openDocument}
        currentPatient={currentPatient}
        currentOrganization={patientOrganization}
        onOpenDocument={() => {
          popoverAdd.onClose();
          setOpenDocument(true);
        }}
        onCloseDocument={() => {
          setOpenDocument(false);
          queryParams.set('tab', 'documents');
          navigate(`${location.pathname}?${queryParams.toString()}`);
        }}
        openCondition={openCondition}
        onOpenCondition={() => {
          popoverAdd.onClose();
          setOpenCondition(true);
        }}
        addConditionProps={{
          resource: `patients/${currentPatient?.id}/conditions`,
        }}
        onCloseCondition={() => setOpenCondition(false)}
        openAllergie={openAllergie}
        onOpenAllergie={() => {
          popoverAdd.onClose();
          setOpenAllergie(true);
        }}
        onCloseAllergie={() => setOpenAllergie(false)}
        openThread={openThread}
        onOpenThread={() => {
          popoverAdd.onClose();
          setOpenThread(true);
        }}
        onCloseThread={() => setOpenThread(false)}
      />

      <PatientDetailsPopover
        popover={popoverDetails}
        onOpenEdit={() => {
          setShowEdit(true);
          popoverDetails.onClose();
        }}
        deleteButtonProps={{
          onSuccess: () => {
            currentPatient.active = false;
            setPatientData(currentPatient);
          },
        }}
      />

      <PatientToolbar
        onBack={handleBack}
        showPatient={showPatient}
        onOpenAdd={popoverAdd.onOpen}
        patientName={currentPatient?.name}
        onTogglePatient={() => setShowPatient(!showPatient)}
      />

      <Stack
        sx={{
          ...(showPatient && {
            width: {
              sm: `calc(100% - ${DRAWER_WIDTH.sm}px)`,
              md: `calc(100% - ${DRAWER_WIDTH.md}px)`,
              lg: `calc(100% - ${DRAWER_WIDTH.lg}px)`,
            },
          }),
        }}
      >
        <PatientTabs
          tabOptions={TABS}
          currentTab={currentTab as string}
          onChangeTab={handleChangeTab}
        />

        {currentPatient && (
          <ChatViewWrapper
            currentPatient={currentPatient}
            accessToken={accessToken}
            idToken={idToken}
          >
            {currentPatient && currentTab === 'chat' && (
              <>
                <ChatView accessToken={accessToken} />
              </>
            )}
          </ChatViewWrapper>
        )}

        {currentTab === 'documents' && (
          <ListDocuments
            {...listDocumentProps}
            loading={listDocumentsQuery.isFetching}
            refetchDocuments={refetchDocuments}
            columns={documentColumns}
            setFilters={setListDocumentFilters}
            patient={currentPatient}
          />
        )}

        {currentTab === 'conditions' && (
          <ConditionAllergiesList
            {...conditionGridProps}
            columns={conditionDisplayColumns}
          />
        )}

        {currentTab === 'allergies' && (
          <ConditionAllergiesList
            {...allergyIntoleranceGridProps}
            columns={allergyIntoleranceColumns}
          />
        )}

        {currentTab === 'threads' && (
          <ListThreads
            {...threadGridProps}
            filters={filters}
            setFilters={setThreadGridFilters}
            columns={threadColumns}
            currentPatient={currentPatient}
          />
        )}
      </Stack>

      <PatientDrawer
        showPatient={showPatient}
        setShowEdit={setShowEdit}
        onFinish={onFinish}
        saveButtonProps={saveButtonProps}
        handleSubmit={handleSubmit}
        currentIdentityProvider={currentIdentityProvider}
        showEdit={showEdit}
        currentPatient={currentPatient}
        isPatientLoaded={isPatientLoaded}
        queryResult={queryResult}
        popoverDetails={popoverDetails}
        renderEditForm={renderEditForm}
      />
    </>
  );
};
