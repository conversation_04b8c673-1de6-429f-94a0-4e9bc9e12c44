import { BaseRecord, useTranslate } from '@refinedev/core';
import React, { useEffect } from 'react';
import { FieldValues, UseFormSetValue } from 'react-hook-form';

import { Box } from '@mui/material';
import { DataGridPro, GridColDef } from '@mui/x-data-grid-pro';

import { Templates } from 'src/types/agent-types';
import AgentDeleteConfirmDialog from './agent-delete-confirm-dialog';
import AgentsRowActionsPopover from './agents-row-actions-popover';

interface Props {
  agentData: BaseRecord | undefined;
  saveButtonProps: any;
  setValue: UseFormSetValue<FieldValues>;
  setShowAddDialog: React.Dispatch<React.SetStateAction<boolean>>;
  templatesRowData: Templates[];
  setTemplatesRowData: React.Dispatch<React.SetStateAction<Templates[]>>;
  templatesSelectedRow: Templates | undefined;
  setTemplatesSelectedRow: React.Dispatch<
    React.SetStateAction<Templates | undefined>
  >;
  setFormValue: UseFormSetValue<FieldValues>;
}

const AgentTemplatesTab = ({
  agentData,
  saveButtonProps,
  setValue,
  setShowAddDialog,
  templatesRowData,
  setTemplatesRowData,
  templatesSelectedRow,
  setTemplatesSelectedRow,
  setFormValue,
}: Props) => {
  const translate = useTranslate();

  const [openDel, setOpenDel] = React.useState(false);

  const handleOpenDel = () => {
    setOpenDel(true);
  };

  const handleCloseDel = () => {
    setOpenDel(false);
  };

  const columns: GridColDef[] = [
    {
      field: 'id',
      headerName: '#',
      flex: 0.5,
      sortable: true,
    },
    {
      field: 'event',
      headerName: 'Event',
      flex: 4,
      sortable: true,
    },
    {
      field: 'greetingTemplate',
      headerName: 'Template',
      flex: 4,
      sortable: true,
    },
    {
      field: 'actions',
      headerName: translate('table.actions'),
      flex: 1,
      renderCell: function render({ row }) {
        return (
          <>
            <AgentsRowActionsPopover
              onEdit={() => handleEditRow(row)}
              onDelete={() => handleDeleteRow(row)}
            />
          </>
        );
      },
    },
  ];

  useEffect(() => {
    const rows = agentData?.relicAssistantSetup?.greetingTemplates?.map(
      (template: Templates, index: number) => {
        return {
          id: index + 1,
          event: template.event,
          greetingTemplate: template.greetingTemplate,
        };
      },
    );
    setTemplatesRowData(rows || []);
  }, [agentData, setTemplatesRowData]);

  type Inputs = {
    event: string;
    greetingTemplate: string;
  };

  const handleOnCellClick = React.useCallback((params: any) => {
    if (params.field !== 'actions') {
      handleEditRow(params.row as Templates);
    }
  }, []);

  const handleEditRow = (row: Templates) => {
    setTemplatesSelectedRow(undefined);
    setShowAddDialog(true);
    setTemplatesSelectedRow(row);
    setFormValue('event', row.event);
    setFormValue('greetingTemplate', row.greetingTemplate);
  };

  const handleDeleteRow = (row: Templates) => {
    setTemplatesSelectedRow(undefined);
    setTemplatesSelectedRow(row);
    handleOpenDel();
  };

  const handleOnDeleteConfirm = () => {
    const index = templatesRowData.findIndex(
      r => r.id === templatesSelectedRow?.id,
    );
    const newRows = [...templatesRowData];
    newRows.splice(index, 1);
    setTemplatesRowData(newRows);
    setValue(
      'relicAssistantSetup.greetingTemplates',
      newRows.map(({ event, greetingTemplate }) => ({
        event,
        greetingTemplate,
      })),
    );
    saveButtonProps.onClick();
  };

  return (
    <Box sx={{ height: '100%' }}>
      <DataGridPro
        rows={templatesRowData || []}
        columns={columns}
        headerFilters={false}
        disableColumnMenu
        getRowHeight={() => 'auto'}
        onCellClick={handleOnCellClick}
        pagination
        isRowSelectable={() => false}
        isCellEditable={() => false}
        sx={{
          '& .MuiDataGrid-columnHeader': {
            background: 'transparent',
            borderBottom: '1px solid rgb(229, 229, 229)',
          },
          '& .MuiDataGrid-cell': {
            borderBottom: '1px solid rgb(242, 242, 242)',
          },
        }}
      />

      <AgentDeleteConfirmDialog
        handleCloseDel={handleCloseDel}
        openDel={openDel}
        handleOnDeleteConfirm={handleOnDeleteConfirm}
      />
    </Box>
  );
};

export default AgentTemplatesTab;
