import { S } from "fluent-json-schema";
import { FastifyInstance } from "fastify";
import config from 'config';

const configRoutes = (encapsulatedRouter: FastifyInstance, options: any, next: any) => {
    encapsulatedRouter.get("/:id", {
        schema: {
            description: "Retrieve configuration value by ID",
            params: S.object()
                .prop('id', S.string().description('Configuration ID, e.g., BITLY.URL')),
            tags: ["Configuration Service"],
            summary: "Get configuration value by ID"
        }
    }, async (req, rep) => {
        let { id } = req.params as unknown as { id: string };
        if (
            req.headers['host']?.includes('localhost') ||
            req.headers['origin']?.includes('localhost') ||
            req.headers['referer']?.includes('localhost')
        ) {
            switch (id) {
                case 'SERVER.AGENTMESSENGER_URL':
                    id = 'SERVER.LOCALHOST_AGENTMESSENGER_URL'
                    break
                default:
                    break
            }
        }
        try {
            const configValue = config.get(id);
            rep.send({ value: configValue });
        } catch (error) {
            rep.status(404).send({ error: `Configuration ID ${id} not found` });
        }
    });

    next();
};

export default configRoutes;
