// Increase memory limit
process.env.NODE_OPTIONS = '--max-old-space-size=8192';

import { RelicWebCrawler } from './relic-web-crawler/crawler.js';
import { CrawlRequest } from 'relic-ui';
import path from 'node:path';
import { existsSync, mkdirSync, readFileSync } from 'node:fs';

const main = async () => {
    let crawler: RelicWebCrawler;
    let exitCode = 0;
    try {
        const inputFilePath = path.resolve(process.env.INPUT_FILE || './INPUT.json');
        if (!existsSync(inputFilePath)) {
            throw Error('Input file not found. Make sure ./INPUT.json exists or provide file path using environment variable INPUT_FILE.');
        }
        console.info(`Reading requests from input file: ${inputFilePath}`);
        const data = readFileSync(inputFilePath, 'utf-8');
        const request: CrawlRequest = JSON.parse(data);
        request.isEstimatorRun = true;

        const storageDir = path.join(process.cwd(), 'storage', 'key_value_stores', 'default');
        mkdirSync(storageDir, { recursive: true });
        crawler = await RelicWebCrawler.create(request);
        const stats = await crawler.run();
        console.info(`Crawler finished with stats: ${JSON.stringify(stats)}`);
    } catch (err) {
        console.error('Error running crawler', err);
        exitCode = 1;
    } finally {
        if (crawler) {
            await crawler.cleanup();
        }
        process.exit(exitCode);
    }
};

main();