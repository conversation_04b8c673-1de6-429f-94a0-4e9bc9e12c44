import {
  useApiUrl,
  useCustom,
  useCustomMutation,
  useTranslate,
} from '@refinedev/core';
import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';

import {
  Alert,
  Box,
  CircularProgress,
  Dialog,
  Stack,
  TextField,
  Typography,
} from '@mui/material';

import { Create } from 'src/components/refine-customs/create';
import {
  FlatNode,
  flattenTreeFromChildrenToRoot,
} from 'src/utils/training-utils';

const MAX_URLS_THRESHOLD = 100;

type TrainingContentCrawlDialogProps = {
  open: boolean;
  onClose: VoidFunction;
  moduleId: string;
  contentId: string;
  onSuccess?: VoidFunction;
  // Kept for future use if needed
  initUrl?: string;
};

const TrainingContentCrawlDialog = ({
  open,
  onClose,
  moduleId,
  contentId,
  onSuccess,
}: TrainingContentCrawlDialogProps) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pageLinks, setPageLinks] = useState<
    { url: string; selected: boolean }[]
  >([]);
  // Removed urlsExceedThreshold state as we're validating on submit

  const apiUrl = useApiUrl();
  const translate = useTranslate();

  const {
    data: sitemapData,
    isLoading: fetchSitemapLoading,
    refetch: refetchSitemap,
    isFetching: isFetchingSitemap,
  } = useCustom({
    url: `trainings/modules/${moduleId}/contents/${contentId}`,
    method: 'get',
  });

  const { mutateAsync: crawlContent, isLoading: crawlLoading } =
    useCustomMutation();

  const {
    register,
    formState: { errors, isSubmitting },
    handleSubmit,
    reset,
    setValue,
  } = useForm({
    defaultValues: {
      title: '',
      urls: '',
    },
  });

  // Reset state and fetch data when dialog opens
  useEffect(() => {
    if (open && contentId) {
      // Clear form and state
      setLoading(true);
      setError(null);
      setPageLinks([]);
      reset({ title: '', urls: '' });
      refetchSitemap();
    }
  }, [open, contentId, reset, refetchSitemap]);

  // Process sitemap data when it's loaded
  useEffect(() => {
    if (open && sitemapData && !fetchSitemapLoading) {
      processSitemapData();
    } else if (open && !sitemapData && !fetchSitemapLoading) {
      // If we're not loading but have no data, show an error
      setError('No data available. Please try again.');
      setLoading(false);
    } else if (open && fetchSitemapLoading) {
      // Ensure loading state is set when fetching
      setLoading(true);
    }
  }, [sitemapData, fetchSitemapLoading, open]);

  // We no longer need to check URLs on every change
  // The validation will happen only on form submission

  const processSitemapData = (data = sitemapData) => {
    try {
      setLoading(true);
      setError(null);

      console.log('Processing sitemap data:', data);

      // Ensure we have valid data
      if (!data || !data.data) {
        throw new Error('Invalid data structure received');
      }

      if (data.data && data.data.sitemap) {
        // The function will handle the sitemap property appropriately
        const links = flattenTreeFromChildrenToRoot(data.data);

        if (links && links.length > 0) {
          // Filter out any links without URLs
          const validLinks = links.filter(link => link && link.url);

          const newPageLinks = validLinks.map((link: FlatNode) => ({
            url: link.url,
            selected: true,
          }));

          setPageLinks(newPageLinks);

          const title = newPageLinks[0]?.url || '';
          setValue('title', title);

          // Set URLs in the form
          const urlsText = newPageLinks
            .filter(link => link.selected)
            .map(link => link.url)
            .join('\n');
          setValue('urls', urlsText);
        } else {
          console.error('No links found after flattening');
          setError('No links found in the sitemap.');
        }
      } else {
        console.error('No sitemap data available in the response');
        setError('No sitemap data available.');
      }
    } catch (err) {
      console.error('Error processing sitemap:', err);
      if (err instanceof Error) {
        setError(err.message);
      } else if (typeof err === 'string') {
        setError(err);
      } else {
        setError('An unexpected error occurred while processing sitemap data.');
      }
    } finally {
      setLoading(false);
    }
  };

  // Just update the form value directly without validation
  const handleUrlsChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    // Simply update the form value
    setValue('urls', event.target.value);
  };

  const onSubmit = async (data: { title?: string; urls?: string }) => {
    try {
      setError(null);

      // Get URLs from the form
      const urlList = (data.urls || '')
        .split('\n')
        .filter(url => url.trim() !== '');

      if (urlList.length === 0) {
        setError('Please select at least one URL to crawl');
        return;
      }

      if (urlList.length > MAX_URLS_THRESHOLD) {
        setError(
          `Number of URLs exceeds the maximum limit of ${MAX_URLS_THRESHOLD}`,
        );
        return;
      }

      // Validate that all URLs are from the original list
      const originalUrls = pageLinks.map(link => link.url);
      const validUrls = urlList.filter(url => originalUrls.includes(url));

      if (validUrls.length < urlList.length) {
        setError(
          'Invalid URLs detected. Please only use URLs from the original sitemap list. New URLs cannot be added.',
        );
        return;
      }

      // Use the validated URLs
      const urlsToSubmit = validUrls;

      // Format URLs as objects for API
      const formattedUrls = urlsToSubmit.map(url => ({ url: url }));

      // Call the crawl endpoint
      await crawlContent({
        url: `${apiUrl}/trainings/modules/${moduleId}/contents/${contentId}/scrape`,
        method: 'post',
        values: {
          title: data.title || '',
          sitemap: formattedUrls, // Use the properly formatted sitemap
          options: {
            maxUrlsToCrawl: 100,
            maxDepth: 5,
            useReadableContent: true,
            crawlEmbeddedLinks: false,
          },
        },
      });

      // Call success callback if provided
      onSuccess?.();

      handleClose();
    } catch (err) {
      if (err instanceof Error) {
        setError(err.message);
      } else if (typeof err === 'string') {
        setError(err);
      } else {
        setError('An unexpected error occurred while starting the crawl.');
      }
    }
  };

  const handleClose = () => {
    // Reset form and state before closing
    setLoading(false);
    setError(null);
    setPageLinks([]);
    reset({ title: '', urls: '' });
    onClose();
  };

  return (
    <Dialog
      fullWidth
      maxWidth="md"
      open={open}
      onClose={handleClose}
      TransitionProps={{
        onExited: () => {
          // Reset everything when the dialog has fully closed
          setLoading(false);
          setError(null);
          setPageLinks([]);
          reset({ title: '', urls: '' });
        },
      }}
      aria-labelledby="alert-dialog-title"
      aria-describedby="alert-dialog-description"
    >
      <Create
        isLoading={isSubmitting || crawlLoading}
        title={translate('trainings-content.titles.crawl-content')}
        onClose={handleClose}
        saveButtonProps={{
          onClick: handleSubmit(onSubmit),
          disabled: isSubmitting || crawlLoading,
          children: translate('buttons.crawl'),
        }}
      >
        <Stack spacing={2.5} component="form" autoComplete="off">
          {fetchSitemapLoading || loading || isFetchingSitemap ? (
            <>
              <Box sx={{ display: 'flex', justifyContent: 'center', py: 5 }}>
                <CircularProgress />
              </Box>
            </>
          ) : (
            <>
              {error && <Alert severity="error">{error}</Alert>}

              <TextField
                {...register('title', {
                  required: 'Title is required',
                })}
                fullWidth
                label={translate('trainings-content.fields.title')}
                error={!!errors.title}
                helperText={errors.title?.message as string}
                disabled={fetchSitemapLoading || loading}
              />

              <Box>
                <Box
                  sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    mb: 1,
                  }}
                >
                  <Typography variant="subtitle1">
                    {translate('trainings-content.fields.web-pages')}
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Typography variant="caption">
                      {`Max ${MAX_URLS_THRESHOLD} URLs allowed`}
                    </Typography>
                  </Box>
                </Box>

                <TextField
                  {...register('urls')}
                  fullWidth
                  multiline
                  rows={10}
                  onChange={handleUrlsChange}
                  placeholder="One URL per line"
                  disabled={fetchSitemapLoading || loading}
                  helperText={translate(
                    'trainings-content.fields.urls-helper-text',
                  )}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      fontFamily: 'monospace',
                    },
                  }}
                />
              </Box>
            </>
          )}
        </Stack>
      </Create>
    </Dialog>
  );
};

export default TrainingContentCrawlDialog;
