'use strict';

import { S } from 'fluent-json-schema';
import translateHandler from './handler';

/**
 * Patient Request Router
 * @param {any} encapsulatedRouter - Fastify instance router.
 * @param {any} options - Fastify options as needed
 * @param {any} next - Next callback
 */
export default function (encapsulatedRouter: any, options: any, next: any) {
    encapsulatedRouter.post("/", {
        schema: {
            description: `Text translate API\n
            source_lang can be "en", "fr", "de" etc\n
            target_lang can be "en", "fr", "de" etc\n
            smaple text : "Hello world"`,
            tags: ['Translate Services'],
            summary: 'Get a translated text',
            body: S.object()
                .prop('source_lang', S.string().description('language code eg. en, de, ru'))
                .prop('target_lang', S.string().required().description('language code eg. en, de, ru'))
                .prop('text', S.array().items(S.string()))
        },
    },
        translateHandler.translateText
    ),
        next();
}