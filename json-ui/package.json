{"name": "json-ui", "version": "0.1.0", "private": true, "type": "module", "packageManager": "pnpm@10.10.0", "dependencies": {"@azure/communication-react": "^1.26.0", "@azure/msal-browser": "^3.23.0", "@azure/storage-blob": "^12.17.0", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@fileforge/react-print": "^0.1.150", "@fluentui/react": "^8.117.5", "@iconify/react": "^4.1.1", "@mui/icons-material": "^6.0.0", "@mui/lab": "^6.0.0-beta.14", "@mui/material": "^6.0.0", "@mui/x-data-grid-pro": "^7.3.2", "@mui/x-date-pickers": "^7.0.0", "@mui/x-license": "^7.2.0", "@refinedev/cli": "^2.16.28", "@refinedev/core": "^4.49.0", "@refinedev/inferencer": "^5.0.3", "@refinedev/mui": "^6.0.0", "@refinedev/react-hook-form": "^4.8.16", "@refinedev/react-router-v6": "^4.5.7", "@refinedev/simple-rest": "^5.0.4", "@refinedev/ui-types": "^1.22.5", "axios": "^1.6.8", "axios-auth-refresh": "^3.3.6", "date-fns": "^2.30.0", "i18next": "^20.1.0", "i18next-browser-languagedetector": "^6.1.1", "i18next-http-backend": "^2.5.0", "lodash.merge": "^4.6.2", "mui-tel-input": "3.2.2", "react": "^18.2.0", "react-dom": "^18.0.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.30.0", "react-i18next": "^11.8.11", "react-imask": "^7.1.3", "react-router-dom": "^6.8.1", "react-toastify": "^10.0.5", "relic-ui": "workspace:relic-ui"}, "devDependencies": {"@microsoft/microsoft-graph-types": "^2.40.0", "@plugin-web-update-notification/vite": "^1.7.1", "@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.1.1", "@testing-library/user-event": "^14.1.1", "@types/jest": "^29.2.4", "@types/lodash.merge": "^4.6.9", "@types/node": "^18.16.2", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "@vitejs/plugin-react": "^4.0.0", "cross-var": "^1.1.0", "dotenv-cli": "^7.4.2", "eslint": "^8.0.0", "eslint-config-prettier": "^9.1.0", "eslint-config-react-app": "^7.0.1", "eslint-plugin-jsx-a11y": "^6.9.0", "eslint-plugin-perfectionist": "^4.0.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.34.4", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.3.4", "graphql-tag": "^2.12.6", "husky": "^9.0.11", "lint-staged": "^15.2.7", "madge": "^8.0.0", "prettier": "^3.3.3", "scheduler": "0.23.0", "typescript": "^4.7.4", "vite": "^6.3.0", "vite-plugin-mkcert": "^1.17.8", "vite-plugin-mpa": "^1.2.0", "vite-plugin-static-copy": "^2.0.0", "wait-on": "^8.0.1"}, "scripts": {"dev": "dotenv -e .env.local -- cross-var wait-on http-get://%VITE_NODE_SERVICES_HOST%:%VITE_NODE_SERVICES_PORT% ../relic-ui/dist/relic-ui.es.js && vite dev --force true", "build": "vite build", "check-types": "tsc --noEmit && pnpm circular", "preview": "vite preview", "start": "dotenv -e .env.local -- cross-var wait-on http-get://%VITE_NODE_SERVICES_HOST%:%VITE_NODE_SERVICES_PORT% && vite preview", "refine": "refine", "lint": "eslint . --ext .ts,.tsx", "format": "prettier --write .", "circular": "npx madge --circular --extensions ts,tsx,js,json,yml,yaml,md --ts-config tsconfig.json src"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "refine": {"projectId": "uvSWGo-A848GS-kqC6S9"}}