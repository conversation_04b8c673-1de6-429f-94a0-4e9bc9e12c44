import tap from "tap";
import { fastify } from "../index";
import { MedplumClient, MedplumClientOptions, EmailPasswordLoginRequest, ProfileResource } from '@medplum/core';
import { JSDOM } from 'jsdom';

// Create a new JSDOM instance
const { window } = new JSDOM();
// Mock global objects
global.window = window as unknown as Window & typeof globalThis;
global.document = window.document;
global.Element = window.Element;
const sessionStorageMock: Storage = {
  store: {} as Record<string, string>,
  getItem: function (key: string) {
    return this.store[key] || null;
  },
  setItem: function (key: string, value: string) {
    this.store[key] = value.toString();
  },
  removeItem: function (key: string) {
    delete this.store[key];
  },
  clear: function () {
    this.store = {};
  },
  get length() {
    return Object.keys(this.store).length;
  },
  key: function (index: number) {
    return Object.keys(this.store)[index] || null;
  },
};

global.sessionStorage = sessionStorageMock;

// Test Data for Organization Testing.
const practitionerEmail: string = "<EMAIL>";
const practitionerPwd: string = "Z1sZ9N3F8tcycDDK";

const todayHumanDate: string = new Date().toISOString();

const orgUnitTestName: string = `Unit Test Organization - ${todayHumanDate}`;
const orgUnitTestWebsite: string = "https://relic-unit-test-only.com";
const orgPhone: string = "(+1)-************";
const orgFaxNum: string = "(+1)-************";
const orgType: string = "prov";
const orgLanguageSystem: string = "http://hl7.org/fhir/ValueSet/languages";
const orgLanguageCode: string = "my";
const orgLanguageDisplay: string = "Burmese";
const orgDisplayType: string = "Healthcare Provider";
const orgUpdatedWebsite: string = "https://node-unit-test-updated.org/";
const pointClickCare: string = "pointclickcareId";
const orgPhoneUpdated: string = "(+1)-************";
const orgFaxNumUpdated: string = "(+1)-************";

let accessToken: any;
let createdOrganizationId = '';
const medplumOptions: MedplumClientOptions = {
  verbose: true,
};
const medplum: MedplumClient = new MedplumClient(medplumOptions);

const loginToMedplum = async (
  medplum: MedplumClient,
  email: string,
  password: string
): Promise<ProfileResource> => {
  const loginRequest: EmailPasswordLoginRequest = {
    email,
    password,
  };
  const loginResponse = await medplum.startLogin(loginRequest);
  const userProfile = await medplum.processCode(loginResponse.code as string);
  accessToken = medplum.getActiveLogin()?.accessToken;
  return userProfile;
};

tap.teardown(async () => {
  await fastify.close();
  process.exit(0);
}); //after running all tests
tap.before(async () => { //run before running all tests
  await loginToMedplum(medplum, practitionerEmail, practitionerPwd);
});

tap.test("requests the health check up, should return success", async (t: Tap.Test) => {
  const response = await fastify.inject({
    method: "GET",
    url: `/`,
    headers: ({
      'x-access-token': accessToken
    }),
  });

  t.equal(response.statusCode, 200);
  t.equal(response.body, 'Node Services');
});

tap.test("create Organization with POST `/api/organizations/`, should return success", async (t) => {
  const response = await fastify.inject({
    method: "POST",
    url: `/api/organizations/`,
    payload: {
      "name": orgUnitTestName,
      "website": orgUnitTestWebsite,
      "phone": orgPhone,
      "fax": orgFaxNum,
      "type": orgType,
      "fhirStore": {
        "defaultLanguage": {
          "system": orgLanguageSystem,
          "code": orgLanguageCode,
          "display": orgLanguageDisplay
        }
      },
      "resourceType": "Organization",
      "active": true,
      "typeDisplay": orgDisplayType
    },
    headers: ({
      'Content-Type': 'application/json',
      'x-access-token': accessToken,
    }),
  });

  t.equal(response.statusCode, 201);//status=201, as it will create a new resouces in the server
  const responseBody = JSON.parse(response.body);
  createdOrganizationId = responseBody.id;
  t.ok(responseBody.id);
  t.ok(responseBody.active);
  t.ok(responseBody.endpoints);
  t.ok(responseBody.template.patientSummary);
  t.ok(responseBody.template.welcomeSms);
  t.ok(responseBody.template.welcomeEmail);
  t.ok(responseBody.template.agentSummary);
  t.equal(responseBody.resourceType, 'Organization');
  t.equal(responseBody.type, 'prov');
  t.equal(responseBody.name, orgUnitTestName);
  t.equal(responseBody.website, orgUnitTestWebsite);
  t.equal(responseBody.fhirStore.clientApplication.resourceType, 'ClientApplication');
  t.equal(responseBody.fhirStore.defaultLanguage.system, orgLanguageSystem);
  t.equal(responseBody.fhirStore.defaultLanguage.code, orgLanguageCode);
  t.equal(responseBody.fhirStore.defaultLanguage.display, orgLanguageDisplay);
  // Find the indexes for specific services
  const chatEndpointIndex = responseBody.endpoints.findIndex(endpoint => endpoint.service.includes('chat'));
  const voipEndpointIndex = responseBody.endpoints.findIndex(endpoint => endpoint.service.includes('voip'));
  const patientPortalEndpointIndex = responseBody.endpoints.findIndex(endpoint => endpoint.service.includes('patient-portal'));

  // Check if the endpoints contain the services correctly
  t.ok(chatEndpointIndex !== -1, 'Chat service is present');
  t.ok(voipEndpointIndex !== -1, 'Voip service is present');
  t.ok(patientPortalEndpointIndex !== -1, 'Patient Portal service is present');

  // Check the details of the default agents
  const agentTypes = ['Patient Agent', 'Staff Agent'];
  if (chatEndpointIndex !== -1) {
    const defaultAgents = responseBody.endpoints[chatEndpointIndex].defaultAgents;
    agentTypes.forEach(agentType => {
      const agentIndex = defaultAgents.findIndex(agent => agent.type === agentType);
      t.ok(agentIndex !== -1, `${agentType} is present in default agents`);
    });
  }
});

tap.test("get organization details with GET `/api/organizations/{id}`, should return success", async (t) => {
  const response = await fastify.inject({
    method: "GET",
    url: `/api/organizations/${createdOrganizationId}`,
    headers: ({
      'x-access-token': accessToken,
    }),
  });

  t.equal(response.statusCode, 200);
  const responseBody = JSON.parse(response.body);
  t.equal(responseBody.resourceType, 'Organization');
  t.equal(responseBody.id, createdOrganizationId);
  t.ok(responseBody.active);
  t.equal(responseBody.name, orgUnitTestName);
  t.equal(responseBody.website, orgUnitTestWebsite);
  t.equal(responseBody.phone, orgPhone);
  t.equal(responseBody.fax, orgFaxNum);
  t.equal(responseBody.fhirStore.clientApplication.resourceType, 'ClientApplication');
  t.equal(responseBody.fhirStore.defaultLanguage.system, orgLanguageSystem);
  t.equal(responseBody.fhirStore.defaultLanguage.code, orgLanguageCode);
  t.equal(responseBody.fhirStore.defaultLanguage.display, orgLanguageDisplay);
});

tap.test("update organization's fhirStore details with PATCH `/api/organizations/{id}`, should return success", async (t) => {
  const response = await fastify.inject({
    method: "PATCH",
    url: `/api/organizations/${createdOrganizationId}`,
    payload: {
      "website": orgUnitTestWebsite,
      "fhirStore": {
        "defaultLanguage": {
          "system": "http://hl7.org/fhir/ValueSet/languages",
          "code": "en-US",
          "display": "English (United States)"
        },
      }
    },
    headers: ({
      'x-access-token': accessToken,
    }),
  });

  t.equal(response.statusCode, 200);
  t.equal(response.statusMessage, 'OK');
  const responseBody = JSON.parse(response.body);
  t.equal(responseBody.id, createdOrganizationId);
  t.equal(responseBody.active, true);
  t.equal(responseBody.resourceType, 'Organization');
  t.equal(responseBody.fhirStore.defaultLanguage.system, 'http://hl7.org/fhir/ValueSet/languages');
  t.equal(responseBody.fhirStore.defaultLanguage.code, 'en-US');
  t.equal(responseBody.fhirStore.defaultLanguage.display, 'English (United States)');
});

tap.test("update organization's phone details with PATCH `/api/organizations/{id}`, should return success", async (t) => {
  const response = await fastify.inject({
    method: "PATCH",
    url: `/api/organizations/${createdOrganizationId}`,
    payload: {
      "website": orgUpdatedWebsite,
      "phone": orgPhoneUpdated,
    },
    headers: ({
      'x-access-token': accessToken,
    }),
  });

  t.equal(response.statusCode, 200);
  t.equal(response.statusMessage, 'OK');
  const responseBody = JSON.parse(response.body);
  t.equal(responseBody.id, createdOrganizationId);
  t.equal(responseBody.resourceType, 'Organization');
  t.equal(responseBody.website, orgUpdatedWebsite);
  t.equal(responseBody.phone, orgPhoneUpdated);
});

tap.test("update organization's pointClickCare id with PATCH `/api/organizations/{id}`, should return success", async (t) => {
  const response = await fastify.inject({
    method: "PATCH",
    url: `/api/organizations/${createdOrganizationId}`,
    payload: {
      "website": orgUpdatedWebsite,
      "pointClickCare": {
        "id": pointClickCare
      }
    },
    headers: ({
      'x-access-token': accessToken,
    }),
  });

  t.equal(response.statusCode, 200);
  t.equal(response.statusMessage, 'OK');
  const responseBody = JSON.parse(response.body);
  t.equal(responseBody.id, createdOrganizationId);
  t.equal(responseBody.resourceType, 'Organization');
  t.equal(responseBody.website, orgUpdatedWebsite);
  t.equal(responseBody.pointClickCare.id, pointClickCare);
});

tap.test("update organization's fax details with PATCH `/api/organizations/{id}`, should return success", async (t) => {
  const response = await fastify.inject({
    method: "PATCH",
    url: `/api/organizations/${createdOrganizationId}`,
    payload: {
      "website": orgUpdatedWebsite,
      "fax": orgFaxNumUpdated,
    },
    headers: ({
      'x-access-token': accessToken,
    }),
  });

  t.equal(response.statusCode, 200);
  t.equal(response.statusMessage, 'OK');
  const responseBody = JSON.parse(response.body);
  t.equal(responseBody.id, createdOrganizationId);
  t.equal(responseBody.resourceType, 'Organization');
  t.equal(responseBody.website, orgUpdatedWebsite);
  t.equal(responseBody.fax, orgFaxNumUpdated);
});

tap.test("update organization with missing required field 'website' using PATCH `/api/organizations/{id}`, should return failure", async (t) => {
  const response = await fastify.inject({
    method: "PATCH",
    url: `/api/organizations/${createdOrganizationId}`,
    payload: {
      "fax": orgFaxNumUpdated,
    },
    headers: ({
      'x-access-token': accessToken,
    }),
  });

  t.equal(response.statusCode, 400);
  const responseBody = JSON.parse(response.body);
  t.equal(responseBody.code, 'FST_ERR_VALIDATION');
  t.equal(responseBody.error, 'Bad Request');
  t.equal(responseBody.message, "body must have required property 'website'");
});

tap.test("get organization details after PATCH request with GET `/api/organizations/{id}`, should return success", async (t) => {
  const uniqueQueryParam = `nocache=${new Date().getTime()}`;
  const response = await fastify.inject({
    method: "GET",
    url: `/api/organizations/${createdOrganizationId}?${uniqueQueryParam}`,
    headers: ({
      'x-access-token': accessToken,
    }),
  });

  t.equal(response.statusCode, 200);
  const responseBody = JSON.parse(response.body);
  t.equal(responseBody.resourceType, 'Organization');
  t.equal(responseBody.id, createdOrganizationId);
  t.ok(responseBody.active);
  t.ok(responseBody.endpoints);
  t.ok(responseBody.template);
  t.equal(responseBody.name, orgUnitTestName);
  t.equal(responseBody.website, orgUpdatedWebsite);
  t.equal(responseBody.type, 'prov');
  t.equal(responseBody.fax, orgFaxNumUpdated);
  t.equal(responseBody.phone, orgPhoneUpdated);
  t.equal(responseBody.fhirStore.clientApplication.resourceType, 'ClientApplication');
  t.equal(responseBody.fhirStore.defaultLanguage.system, 'http://hl7.org/fhir/ValueSet/languages');
  t.equal(responseBody.fhirStore.defaultLanguage.code, 'en-US');
  t.equal(responseBody.fhirStore.defaultLanguage.display, 'English (United States)');
});

tap.test("get all organization details with GET `/api/organizations?_end=25&_order=desc&_sort=name&_start=0`, should return success", async (t) => {
  const response = await fastify.inject({
    method: "GET",
    url: `/api/organizations?_end=50&_order=desc&_start=0&_search=${orgUnitTestName}`,
    headers: ({
      'x-access-token': accessToken,
    }),
  });
  t.equal(response.statusCode, 200);
  t.equal(response.statusMessage, 'OK');
  const responseBody = JSON.parse(response.body);
  t.equal(responseBody[0].name, orgUnitTestName);
  t.equal(responseBody[0].website, orgUpdatedWebsite);
  t.equal(responseBody[0].resourceType, 'Organization');
  t.equal(responseBody[0].active, true);
});

tap.test("get organization with search by name with GET `/api/organizations?_end=25&_order=asc&_sort=name&_start=0&_search=organizationName`, should return success", async (t) => {
  const response = await fastify.inject({
    method: "GET",
    url: `/api/organizations?_end=25&_order=asc&_sort=name&_start=0&_search=${orgUnitTestName}`,
    headers: ({
      'x-access-token': accessToken,
    }),
  });
  t.equal(response.statusCode, 200);
  t.equal(response.statusMessage, 'OK');
  const responseBody = JSON.parse(response.body);
  t.equal(responseBody[0].name, orgUnitTestName);
  t.equal(responseBody[0].website, orgUpdatedWebsite);
  t.equal(responseBody[0].resourceType, 'Organization');
  t.equal(responseBody[0].active, true);
});

tap.test("delete organization details with DELETE `/api/organizations/{id}`, should return success", async (t) => {
  const response = await fastify.inject({
    method: "DELETE",
    url: `/api/organizations/${createdOrganizationId}`,
    headers: ({
      'x-access-token': accessToken,
    }),
  });

  t.equal(response.statusCode, 200);
  t.equal(response.statusMessage, 'OK');
  const responseBody = JSON.parse(response.body);
  t.equal(responseBody.acknowledged, true);
  t.ok(responseBody.deletedCount >= 0);
});

tap.test("get organization details after DELETE `/api/organizations/{id}`, should return failure", async (t) => {
  const uniqueQueryParam = `nocache=${new Date().getTime()}`;
  const response = await fastify.inject({
    method: "GET",
    url: `/api/organizations/${createdOrganizationId}?${uniqueQueryParam}`,
    headers: ({
      'x-access-token': accessToken,
    }),
  });

  t.equal(response.statusCode, 404);
  const responseBody = JSON.parse(response.body);
  t.equal(responseBody.statusCode, 404);
  t.equal(responseBody.error, "Not Found");
  t.equal(responseBody.message, `NotFoundError: organization not found`);
});

tap.test("create Organization without passing organization name with POST `/api/organizations/`, should return failure", async (t) => {
  const response = await fastify.inject({
    method: "POST",
    url: `/api/organizations/`,
    payload: {
      "website": orgUnitTestWebsite,
      "phone": orgPhone,
      "fax": orgFaxNum,
      "type": orgType,
      "fhirStore": {
        "defaultLanguage": {
          "system": orgLanguageSystem,
          "code": orgLanguageCode,
          "display": orgLanguageDisplay
        }
      },
      "resourceType": "Organization",
      "active": true,
      "typeDisplay": orgDisplayType
    },
    headers: ({
      'Content-Type': 'application/json',
      'x-access-token': accessToken,
    }),
  });

  t.equal(response.statusCode, 400);
  const responseBody = JSON.parse(response.body);
  t.equal(responseBody.code, "FST_ERR_VALIDATION");
  t.equal(responseBody.error, "Bad Request");
  t.equal(responseBody.message, "body must have required property 'name'");
});