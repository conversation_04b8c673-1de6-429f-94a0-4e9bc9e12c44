import { useTranslate } from '@refinedev/core';
import React from 'react';
import {
  Control,
  Controller,
  FieldErrors,
  FieldValues,
  SubmitHandler,
  UseFormReset,
  UseFormSetValue,
} from 'react-hook-form';

import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  TextField,
} from '@mui/material';
import { Samples } from 'src/types/agent-types';

interface Props {
  samplesSelectedRow: Samples | undefined;
  samplesRowData: Samples[];
  saveButtonProps: any;
  showAddDialog: boolean;
  setSamplesRowData: React.Dispatch<React.SetStateAction<Samples[]>>;
  setShowAddDialog: React.Dispatch<React.SetStateAction<boolean>>;
  setSamplesSelectedRow: React.Dispatch<
    React.SetStateAction<Samples | undefined>
  >;
  setValue: UseFormSetValue<FieldValues>;
  formErrors: FieldErrors<FieldValues>;
  formReset: UseFormReset<FieldValues>;
  handleSubmit: any;
  formControl: Control<FieldValues, object>;
}

const AgentSamplesAddDialog = ({
  samplesSelectedRow,
  samplesRowData,
  saveButtonProps,
  showAddDialog,
  setSamplesRowData,
  setShowAddDialog,
  setSamplesSelectedRow,
  setValue,
  formErrors,
  formReset,
  handleSubmit,
  formControl,
}: Props) => {
  const translate = useTranslate();
  const onSubmit: SubmitHandler<Samples> = (data: Samples) => {
    let newSamplesRowData;
    if (samplesSelectedRow) {
      const index = samplesRowData.findIndex(
        row => row.id === samplesSelectedRow?.id,
      );
      newSamplesRowData = [...samplesRowData];
      newSamplesRowData[index] = {
        ...samplesSelectedRow,
        ...data,
      };
    } else {
      newSamplesRowData = [
        ...samplesRowData,
        {
          id: samplesRowData.length + 1,
          chatbotResponse: data.chatbotResponse,
          userInput: data.userInput,
        },
      ];
    }
    setSamplesRowData(newSamplesRowData);
    setSamplesSelectedRow(undefined);
    setShowAddDialog(false);
    formReset();

    setValue(
      'azureAssistantSetup.fewShotExamples',
      newSamplesRowData.map(({ chatbotResponse, userInput }) => ({
        chatbotResponse,
        userInput,
      })),
    );

    saveButtonProps.onClick();
  };

  const handleClose = () => {
    setShowAddDialog(false);
    setSamplesSelectedRow(undefined);
    formReset();
  };

  return (
    <Dialog
      open={showAddDialog}
      fullWidth
      maxWidth="sm"
      onClose={handleClose}
      aria-labelledby="alert-dialog-title"
      aria-describedby="alert-dialog-description"
    >
      <DialogTitle id="alert-dialog-title">
        {samplesSelectedRow
          ? translate('agents.editSamples')
          : translate('agents.createSamples')}
      </DialogTitle>
      <Divider />
      <DialogContent>
        <Box my={3}>
          <Controller
            name="userInput"
            control={formControl}
            rules={{ required: true }}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                type="text"
                name="userInput"
                InputLabelProps={{ shrink: true }}
                label={translate('agents.fields.userInput')}
                error={!!(formErrors as any)?.userInput}
                multiline
                maxRows={4}
                minRows={4}
                required
                helperText={(formErrors as any)?.userInput?.message}
              />
            )}
          />
        </Box>
        <Box my={3}>
          <Controller
            name="chatbotResponse"
            control={formControl}
            rules={{ required: true }}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                type="text"
                name="chatbotResponse"
                multiline
                maxRows={4}
                minRows={4}
                required
                InputLabelProps={{ shrink: true }}
                label={translate('agents.fields.chatbotResponse')}
                error={!!(formErrors as any)?.chatbotResponse}
                helperText={(formErrors as any)?.chatbotResponse?.message}
              />
            )}
          />
        </Box>
      </DialogContent>
      <Divider />
      <DialogActions>
        <Button onClick={handleClose}>{translate('buttons.cancel')}</Button>
        <Button
          variant="contained"
          onClick={() => {
            handleSubmit(onSubmit)();
          }}
        >
          {translate('buttons.save')}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default AgentSamplesAddDialog;
