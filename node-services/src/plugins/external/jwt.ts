import config from 'config';
import fastifyJwt, { FastifyJWTOptions } from '@fastify/jwt';

export const autoConfig: FastifyJWTOptions = {
  secret: config.get('PCC.CLIENT_ID') + ':' + config.get('PCC.CLIENT_SECRET'),
  verify: {
    complete: true,
  },
  decode: {
    complete: true,
  },
};

/**
 * JWT utils for Fastify
 *
 * @see {@link https://github.com/fastify/fastify-jwt}
 */
export default fastifyJwt;
