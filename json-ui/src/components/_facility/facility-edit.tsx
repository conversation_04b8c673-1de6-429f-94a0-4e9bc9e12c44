import { useTranslate } from '@refinedev/core';
import { useForm } from '@refinedev/react-hook-form';
import { Controller, FieldValues } from 'react-hook-form';

import Dialog from '@mui/material/Dialog';
import Typography from '@mui/material/Typography';
import { Checkbox, FormControlLabel } from '@mui/material';

import { Edit } from 'src/components/refine-customs/edit';

const EditFacility = ({ open, onClose, facilityDetails }: any) => {
  const translate = useTranslate();
  const {
    control,
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    refineCore: { onFinish, formLoading },
  } = useForm<FieldValues>({
    defaultValues: facilityDetails,
    refineCoreProps: {
      resource: 'facilities',
      id: facilityDetails.id,
      action: 'edit',
      redirect: false,
    },
  });

  const onSubmit = async (values: FieldValues) => {
    await onFinish(values);
    onClose();
  };

  const renderField = (labelKey: string, value: any) => (
    <div>
      <Typography
        component={'span'}
        variant="subtitle2"
        sx={{
          fontSize: 12,
          color: 'text.secondary',
          display: 'block',
          mb: 0.2,
        }}
      >
        {translate(labelKey)}
      </Typography>
      <Typography component={'span'} variant="body2">
        {value || '-'}
      </Typography>
    </div>
  );

  return (
    <Dialog
      fullWidth
      maxWidth="sm"
      open={open}
      onClose={onClose}
      aria-labelledby="alert-dialog-title"
      aria-describedby="alert-dialog-description"
    >
      <Edit
        resource="documents"
        isLoading={formLoading}
        saveButtonProps={{
          onClick: handleSubmit(onSubmit),
          disabled: isSubmitting,
        }}
        onClose={onClose}
        title={translate('facilities.titles.edit')}
        deleteButtonProps={{ hidden: true }}
      >
        {renderField(
          'facilities.fields.facilityCode',
          facilityDetails.facilityCode,
        )}
        {renderField(
          'facilities.fields.facilityName',
          facilityDetails.facilityName,
        )}
        {renderField(
          'facilities.fields.postalCode',
          facilityDetails.postalCode,
        )}
        {renderField('facilities.fields.state', facilityDetails.state)}
        <Controller
          control={control}
          name="enabled"
          defaultValue={null as any}
          render={({ field }) => (
            <FormControlLabel
              label={translate('facilities.fields.enabled')}
              control={
                <Checkbox
                  {...field}
                  checked={field.value}
                  onChange={event => {
                    field.onChange(event.target.checked);
                  }}
                />
              }
            />
          )}
        />
      </Edit>
    </Dialog>
  );
};

export default EditFacility;
