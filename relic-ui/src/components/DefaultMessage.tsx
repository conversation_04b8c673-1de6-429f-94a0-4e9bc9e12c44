import React from 'react';
import { useThreadContext } from './RelicThreadProvider';
import ChatMessageRenderer from './ChatMessageRenderer';

type DefaultMessageProps = {
  defaultOnRender: (props: any) => JSX.Element; // eslint-disable-line @typescript-eslint/no-explicit-any
  messageProps: any; // eslint-disable-line @typescript-eslint/no-explicit-any
};

const DefaultMessage: React.FC<DefaultMessageProps> = ({ defaultOnRender, messageProps }) => {
  const { displayInEnglish } = useThreadContext();

  // Handle translated content
  let content = messageProps.message.content;
  let propsToUse = messageProps;

  if (messageProps.message.metadata?.messageIntl && !displayInEnglish) {
    const messageIntl = JSON.parse(messageProps.message.metadata.messageIntl);
    content = messageIntl.content;
    propsToUse = {
      ...messageProps,
      message: {
        ...messageProps.message,
        content: messageIntl.content,
      },
    };
  }

  // or if it's a string that needs to be processed
  const isReactElement = React.isValidElement(content);

  // Prepare message props for rendering
  const wrappedMessageProps = {
    ...propsToUse,
    message: {
      ...propsToUse.message,
      content: isReactElement ? content : <ChatMessageRenderer content={content} />,
    },
  };

  // Provide default rendering with the appropriate content
  return <div>{defaultOnRender(wrappedMessageProps)}</div>;
};

export default DefaultMessage;
