/* eslint-disable @typescript-eslint/no-unused-vars */
import { Test, TestingModule } from '@nestjs/testing';
import { ChatService } from './chat.service';
import { ChatClientManager } from './chat-client-manager.service';
import { ChatClient } from '@azure/communication-chat';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';

jest.mock('@azure/openai', () => {
  return {
    OpenAIClient: jest.fn().mockImplementation(() => {
      return {
        getChatCompletions: jest.fn().mockResolvedValue({
          choices: [{ message: { content: 'Mocked message from ChatGPT' } }],
        }),
      };
    }),
    AzureKeyCredential: jest.fn().mockImplementation(() => {
      return {};
    }),
  };
});

const mockAllAgentsData = [
  {
    _id: '123',
    acsId: 'agent1_acsId',
    agentName: 'Agent One',
    acsToken: {
      token: 'xxx',
      expiresOn: new Date('2024-12-31'),
    },
    version: '1.0.0',
    type: 'Counselor',
    status: 'Active',
    role: {
      'practitioner-role': 'Counselor',
      'display-role': 'Senior Counselor',
    },
    azureAssistantSetup: {
      systemPrompt: 'Welcome to Azure Assistant!',
      fewShotExamples: [
        {
          chatbotResponse: 'Sure, I can help with that!',
          userInput: 'Can you assist me with my issue?',
        },
      ],
      chatParameters: {
        maxResponseLength: 300,
        temperature: 0.7,
        topProbabilities: 0.8,
        stopSequences: null,
        pastMessagesToInclude: 5,
        frequencyPenalty: 0.5,
        presencePenalty: 0.6,
      },
      command: 'Execute Task',
      agentSummary: 'Experienced counselor agent',
      topic: 'Counseling',
    },
    relicAssistantSetup: {
      greetingTemplates: [
        {
          event: 'New Chat',
          greetingTemplate: 'Hello! How can I assist you today?',
        },
      ],
      kbLinked: [],
      kbPromptTemplate: 'How can I assist?',
    },
    env: 'production',
    acsEndpoint: 'https://api.agentone.com',
    publicData: {
      name: 'Agent One',
      role: 'Senior Counselor',
      bio: 'Experienced counselor with expertise in mental health.',
      activeSince: new Date('2022-01-01'),
      chats: 1200,
      messages: 30000,
      avatarUrl: 'https://example.com/avatar/agent1.jpg',
      coverUrl: 'https://example.com/cover/agent1.jpg',
    },
  },
  {
    _id: '456',
    acsId: 'agent2_acsId',
    agentName: 'Agent Two',
    acsToken: {
      token: 'yyy',
      expiresOn: new Date('2024-12-31'),
    },
    version: '2.0.0',
    type: 'Support',
    status: 'Active',
    role: {
      'practitioner-role': 'Support Agent',
      'display-role': 'Technical Support',
    },
    azureAssistantSetup: {
      systemPrompt: 'Welcome to Azure Support!',
      fewShotExamples: [
        {
          chatbotResponse: 'I can help you with that tech issue.',
          userInput: 'My computer is slow.',
        },
      ],
      chatParameters: {
        maxResponseLength: 300,
        temperature: 0.7,
        topProbabilities: 0.8,
        stopSequences: null,
        pastMessagesToInclude: 5,
        frequencyPenalty: 0.5,
        presencePenalty: 0.6,
      },
      command: 'Resolve Issue',
      agentSummary: 'Tech support agent with rapid problem-solving skills',
      topic: 'Technical Support',
    },
    relicAssistantSetup: {
      greetingTemplates: [
        {
          event: 'New Chat',
          greetingTemplate: 'Hi, I am here to help with your technical issues!',
        },
      ],
      kbLinked: [],
      kbPromptTemplate: 'What issue are you facing today?',
    },
    env: 'production',
    acsEndpoint: 'https://api.agenttwo.com',
    publicData: {
      name: 'Agent Two',
      role: 'Technical Support',
      bio: 'Skilled in resolving complex technical problems.',
      activeSince: new Date('2023-01-01'),
      chats: 800,
      messages: 15000,
      avatarUrl: 'https://example.com/avatar/agent2.jpg',
      coverUrl: 'https://example.com/cover/agent2.jpg',
    },
  },
];

const mockAgentConfigData = mockAllAgentsData[0];

const mockPatientData = {
  id: '685556e6-3ea6-449e-8230-695469e61e64',
  resourceType: 'Patient',
  organizationId: '7760ffcd-64d2-43df-9548-055b257dfaee',
  active: true,
  name: 'Juan Carlos',
  email: '<EMAIL>',
  emailVerified: true,
  mobilePhone: '************',
  homePhone: '************',
  gender: 'Male',
  birthDate: '1949-02-20',
  maritalStatus: 'Married',
  link: [
    {
      id: '4617e09d-a615-4ee6-b98f-f7681c8f84f8',
      resourceType: 'RelatedPerson',
      relationship: {
        system: 'http://terminology.hl7.org/CodeSystem/v3-RoleCode',
        code: 'WIFE',
        display: 'wife',
      },
      use: 'usual',
      name: 'Mary Carlos',
      email: '<EMAIL>',
      mobilePhone: '***********',
      homePhone: '',
      gender: 'female',
      birthDate: '1962-07-09',
    },
  ],
  communicationLanguage: [
    {
      system: 'http://hl7.org/fhir/ValueSet/languages',
      code: 'en-US',
      display: 'English - US',
      preferred: false,
    },
  ],
  managingOrganization: {
    reference: 'Organization/7760ffcd-64d2-43df-9548-055b257dfaee',
    display: 'Sunrise Senior Living - Santa Monica',
  },
  Condition: [
    {
      id: '221d688e-96c1-4943-81f2-b402a69b37c1',
      resourceType: 'Condition',
      clinicalStatus: '',
      verificationStatus: '',
      code: {
        text: 'History of hypertension (situation)',
      },
      onsetDateTime: '2023-10-03',
      abatementDateTime: '',
      note: '',
    },
    {
      id: '5d4dfc3d-e52a-44f8-b357-c6967a254dd0',
      resourceType: 'Condition',
      clinicalStatus: '',
      verificationStatus: '',
      code: {
        text: 'Chronic Obstructive Pulmonary Disease',
      },
      onsetDateTime: '2023-09-28',
      abatementDateTime: '',
      note: '',
    },
    {
      id: 'ea359f81-8c03-4ed5-bcf7-d26d39071369',
      resourceType: 'Condition',
      clinicalStatus: '',
      verificationStatus: '',
      code: {
        text: 'Diabetes',
      },
      onsetDateTime: '2023-09-28',
      abatementDateTime: '',
      note: '',
    },
  ],
  Location: {
    reference: 'Location/112f4240-3f61-42e1-996e-0a550cdb94dd',
    display: 'Sunrise Senior Living - Santa Monica',
  },
  summary:
    'Juan Carlos is a 75 years old Male suffering from History of hypertension (situation), Chronic Obstructive Pulmonary Disease, Diabetes, and under treatment at Sunrise Senior Living - Santa Monica as inpatient.',
  goal: 'My goal is to recover from my illness as soon as possible, get discharged and be home with my loved ones. I am expecting dignified senior care with love, respect and compassion from you.',
};

describe('ChatService', () => {
  let service: ChatService;
  let mockSendMessage: jest.Mock;
  let mockChatClientManager: jest.Mocked<ChatClientManager>;
  let mockHttpService: jest.Mocked<HttpService>;
  let mockConfigService: jest.Mocked<ConfigService>;

  beforeEach(async () => {
    mockSendMessage = jest.fn();

    const mockAsyncIterable = {
      [Symbol.asyncIterator]() {
        const messages = [
          // TODO: fix message format
          {
            id: 'message0',
            type: 'topicUpdated',
            content: { topic: '{"organizationId":"12345"}' },
          },
          { id: 'message1', content: { message: 'Mock message 1' } },
          { id: 'message2', content: { message: 'Mock message 2' } },
        ];
        let index = 0;
        return {
          next() {
            if (index < messages.length) {
              return Promise.resolve({ value: messages[index++], done: false });
            } else {
              return Promise.resolve({ done: true });
            }
          },
        };
      },
    };

    mockChatClientManager = {
      getChatClientForUser: jest.fn().mockImplementation(() => ({
        getChatThreadClient: jest.fn().mockReturnValue({
          sendMessage: mockSendMessage,
          listMessages: jest.fn().mockReturnValue(mockAsyncIterable),
        }),
      })),
    } as any;

    mockHttpService = {
      get: jest.fn().mockImplementation(() => ({
        toPromise: () =>
          Promise.resolve({
            data: 'mock-data',
            status: 200,
            statusText: 'OK',
            headers: {},
            config: {},
          }),
      })),
    } as any;

    mockConfigService = {
      get: jest.fn().mockImplementation((key: string) => {
        return 'mock-token';
      }),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ChatService,
        { provide: ChatClientManager, useValue: mockChatClientManager },
        { provide: ConfigService, useValue: mockConfigService },
        { provide: HttpService, useValue: mockHttpService },
      ],
    }).compile();

    service = module.get<ChatService>(ChatService);

    jest.spyOn(service, 'getAllAgents').mockResolvedValue(mockAllAgentsData);
    jest.spyOn(service, 'getAgent').mockResolvedValue(mockAgentConfigData);
    jest.spyOn(service, 'getPatientData').mockResolvedValue(mockPatientData);

    await service.onModuleInit();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('processChatMessageReceivedInThread', () => {
    it('should call sendMessage with the correct message content', async () => {
      // from https://learn.microsoft.com/en-us/azure/event-grid/communication-services-chat-events#microsoftcommunicationchatmessagereceivedinthread-event
      const mockData = {
        composeTime: '2021-02-19T00:25:58.927Z',
        messageBody: 'Welcome to Azure Communication Services',
        messageId: '1613694358927',
        metadata: {
          type: 'chat',
          description: 'A map of data associated with the message',
        },
        senderDisplayName: 'Test from Agent Messenger',
        senderCommunicationIdentifier: {
          rawId: '8:acs:109f0644-b956-4cd9-87b1-71024f6e2f44_00000008-578d-7caf-07fd-084822001724',
          communicationUser: {
            id: '8:acs:109f0644-b956-4cd9-87b1-71024f6e2f44_00000008-578d-7caf-07fd-084822001724',
          },
        },
        transactionId: 'oh+LGB2dUUadMcTAdRWQxQ.*******.1827536918.1.7',
        threadId: '19:6e5d6ca1d75044a49a36a7965ec4a906@thread.v2',
        type: 'Text',
        version: 1613694358927,
      };

      await service.processChatMessageReceivedInThread(mockData);

      expect(mockSendMessage).toHaveBeenCalledWith(
        { content: 'Mocked message from ChatGPT' },
        {
          senderDisplayName: 'Agent One',
          metadata: {
            type: 'chat',
          },
          type: 'text', // 'text' is the default
        },
      );
    });
  });
});
