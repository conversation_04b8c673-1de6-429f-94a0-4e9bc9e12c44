{
    "$schema": "https://turbo.build/schema.json",
    "tasks": {
      "build": {
        "dependsOn": ["^build"],
        "outputs": ["dist/**"]
      },
      "check-types": {
        "dependsOn": ["^check-types"]
      },
      "dev": {
        "cache": false,
        "dependsOn": [
        "^dev"
        ]
      },
      "node-services#dev": {
        "cache": false,
        "dependsOn": [], // dependecies are defined in the package.json through wait on
        "persistent": true
      },
      "json-ui#dev": {
        "cache": false,
        "dependsOn": [], // dependecies are defined in the package.json through wait on
        "persistent": true
      },
      "agent-messenger#dev": {
        "cache": false,
        "dependsOn": [], // dependecies are defined in the package.json through wait on
        "persistent": true
      },
      "start": {
        "dependsOn": ["build", "^start"],
        "persistent": true
      }
    }
  }