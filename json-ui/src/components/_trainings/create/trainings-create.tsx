import { FieldValues } from 'react-hook-form';
import { useForm } from '@refinedev/react-hook-form';
import { useCreate, useTranslate } from '@refinedev/core';

import { Stack, Dialog, TextField } from '@mui/material';

import { useRouter } from 'src/routes/hooks';

import { Create } from 'src/components/refine-customs/create';
import { OrganizationDropdown } from 'src/components/dropdown/organizationDropdown';

type Props = {
  open: boolean;
  onClose: VoidFunction;
  organizationId?: string;
};
const CreateTraining = ({ open, onClose, organizationId }: Props) => {
  const translate = useTranslate();
  const router = useRouter();
  const { mutate, isLoading: createTrainingLoading } = useCreate({
    resource: 'trainings',
    mutationOptions: {
      onSuccess: data => {
        const trainingId = data?.data?.id;
        onClose();
        router.push(`/trainings/edit/${trainingId}`);
      },
    },
  });
  const {
    watch,
    control,
    handleSubmit,
    saveButtonProps,
    register,
    formState: { errors },
    refineCore: { formLoading },
  } = useForm();

  const onSubmit = (values: FieldValues) => {
    try {
      const payload = {
        name: values.name,
        organizationId: values.organizationId,
      };
      mutate({
        values: payload,
      });
    } catch (error) {
      console.error(error);
    }
  };

  const values = watch();

  return (
    <Dialog
      fullWidth
      maxWidth="sm"
      open={open}
      onClose={onClose}
      aria-labelledby="alert-dialog-title"
      aria-describedby="alert-dialog-description"
    >
      <Create
        isLoading={formLoading || createTrainingLoading}
        saveButtonProps={{
          ...saveButtonProps,
          disabled: !values.name || !values.organizationId,
          onClick: handleSubmit(onSubmit),
        }}
        title={translate('trainings.titles.create')}
      >
        <Stack component="form" spacing={2.5} autoComplete="off">
          <OrganizationDropdown
            control={control}
            name="organizationId"
            label={translate('locations.fields.organizationId')}
            error={!!(errors as any)?.organizationId}
            helperText={(errors as any)?.organizationId?.message}
            value={organizationId ?? null}
          />
          <TextField
            {...register('name', {
              required: 'This field is required',
            })}
            error={!!(errors as any)?.name}
            helperText={(errors as any)?.name?.message}
            margin="normal"
            fullWidth
            InputLabelProps={{ shrink: true }}
            type="text"
            label={translate('trainings.fields.name')}
            name="name"
          />
        </Stack>
      </Create>
    </Dialog>
  );
};

export default CreateTraining;
