# URL to the access policy API
VITE_NODE_SERVICES_HOST=prod-services.jollyforest-c5124478.westus.azurecontainerapps.io

VITE_NODE_SERVICES_PORT=443

# Support Link
VITE_SUPPORT_LINK=https://portals.docsie.io/reliccare/ws/relic-ai-user-manual/conversational-ai-multi-lingual/deployment_J3thpDIFs5Zbl4EsU/?doc=/how-to-get-support/

# Privacy Policy Link
VITE_PRIVACY_LINK=https://portals.docsie.io/reliccare/ws/relic-ai-user-manual/conversational-ai-multi-lingual/deployment_J3thpDIFs5Zbl4EsU/?doc=/privacy-statement/

# MUI Licence
VITE_MUI_LICENSE=8a9b6eb7f80198b143b1cf166a9582d3Tz04OTY0OSxFPTE3NDYzMzAyNzUwMDAsUz1wcm8sTE09c3Vic2NyaXB0aW9uLEtWPTI=

# PCC Client ID
VITE_PCC_CLIENT_ID=HIbxDEL5xPrsdjgnqyg8tlv6yWC8SyU2

# Medplum Client ID
VITE_MEDPLUM_CLIENT_ID=872fe0a8-fda8-49c7-b9ef-0bfadb8d509b

# Medplum Client ID for localhost
VITE_MEDPLUM_LOCALHOST_CLIENT_ID=0194828e-9dd1-7049-9ded-865b2e7163f4