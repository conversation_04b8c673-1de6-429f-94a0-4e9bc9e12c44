{"resourceType": "AccessPolicy", "name": "<PERSON><PERSON><PERSON><PERSON>", "resource": [{"resource": "patient-portal", "canAccess": [{"name": "facility-menu", "allowed": true}, {"name": "aidioligist-menu", "allowed": true}, {"name": "chat-menu", "allowed": true}, {"name": "sandbox-menu", "allowed": true}, {"name": "sandbox-menu", "allowed": true}, {"name": "account-menu", "allowed": true}]}, {"resource": "Patient", "constraint": "patientId", "alias": [{"target": "patient-portal", "resource": "profile"}], "canRead": true, "canUpdate": true, "canDelete": false, "canCreate": false, "canList": false}, {"resource": "RelicAgent", "alias": [{"target": "my-facility", "resource": "agent"}], "canRead": true, "canUpdate": false, "canDelete": false, "canCreate": false, "canList": true}]}