import React, { useMemo, useEffect } from 'react';
import { use<PERSON><PERSON>, <PERSON>rud<PERSON><PERSON><PERSON>, useOnError, useTranslate } from '@refinedev/core';

import { Box } from '@mui/material';
import {
  GridColDef,
  DataGridPro,
  DataGridProProps,
} from '@mui/x-data-grid-pro';

import { useRouter } from 'src/routes/hooks';

import { useSearch } from 'src/hooks/use-search';

import { NewButton } from 'src/components/refine-customs/new-btn';
import { DataGridToolbar } from 'src/components/list/data-grid-toolbar';

interface ListTrainingsModuleProps extends DataGridProProps {
  setFilters: ((filters: CrudFilter[]) => void) &
    ((setter: (prevFilters: CrudFilter[]) => CrudFilter[]) => void);
  filters: CrudFilter[];
}

const ListTrainingsModule = ({
  setFilters,
  filters,
  ...dataGridProps
}: ListTrainingsModuleProps) => {
  const translate = useTranslate();
  const router = useRouter();

  const { globalSearch } = useSearch();

  const { mutate: onError } = useOnError();

  useEffect(() => {
    try {
      if (globalSearch.length > 0) {
        setFilters([
          {
            field: 'search',
            value: globalSearch.length > 0 ? globalSearch : '',
            operator: 'contains',
          },
        ]);
      } else {
        setFilters([]);
      }
    } catch (error) {
      onError(error);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [globalSearch]);

  const { data: relatedOrgs, isLoading: organizationIsLoading } = useMany({
    resource: 'organizations',
    ids: dataGridProps?.rows?.map((item: any) => item?.organizationId) ?? [],
    queryOptions: {
      enabled: !!dataGridProps?.rows,
    },
  });

  const columns = React.useMemo<GridColDef[]>(
    () => [
      {
        field: 'name',
        headerName: translate('trainings-modules.fields.name'),
        minWidth: 400,
      },
      {
        field: 'storage',
        headerName: translate('trainings-modules.fields.storage'),
        minWidth: 500,
        renderCell: function render({ value }) {
          return <>{value?.containerName}</>;
        },
      },
      {
        field: 'organizationId',
        flex: 1,
        headerName: translate('trainings-modules.fields.organizationId'),
        minWidth: 400,
        renderCell: function render({ value }) {
          return organizationIsLoading ? (
            <>Loading...</>
          ) : (
            relatedOrgs?.data?.find((item: any) => item.id === value)?.name
          );
        },
        sortable: false,
      },
    ],
    [organizationIsLoading, relatedOrgs?.data, translate],
  );

  const [viewButtonEl, setViewButtonEl] =
    React.useState<HTMLButtonElement | null>(null);

  const displayColumns = useMemo(() => {
    const filteredColumns = columns;

    return filteredColumns;
  }, [columns]);

  const Toolbar = React.useCallback(
    () => (
      <DataGridToolbar
        title={translate('trainings-modules.label')}
        exportButton={false}
        setViewButtonEl={setViewButtonEl}
        actions={
          <>
            <NewButton
              href={'/trainings/modules/create'}
              label={translate('buttons.create')}
            />
          </>
        }
      />
    ),
    [translate],
  );

  const handleOnCellClick = React.useCallback(
    (params: any) => {
      if (params.field !== 'actions') {
        router.push(
          `/trainings/modules/edit/${params.id}?name=${params?.row?.name}`,
        );
      }
    },
    [router],
  );

  function renderGrid() {
    return (
      <>
        <DataGridPro
          {...dataGridProps}
          getRowId={row => row?.id}
          disableColumnMenu
          columns={displayColumns}
          pagination
          sx={{
            '& .MuiDataGrid-cell': {
              display: 'flex',
              alignSelf: 'center',
              height: '100%',
              border: 'none',
            },
          }}
          onCellClick={handleOnCellClick}
          localeText={{ toolbarColumns: 'View' }}
          slots={{
            toolbar: Toolbar,
          }}
          slotProps={{
            panel: {
              anchorEl: viewButtonEl,
            },
            toolbar: {
              onClick: (e: React.MouseEvent) => {
                e.stopPropagation();
                setViewButtonEl(null);
              },
            },
          }}
        />
      </>
    );
  }

  return <Box>{renderGrid()}</Box>;
};

const MemorisedListTrainingsModule = React.memo(ListTrainingsModule);
export default MemorisedListTrainingsModule;
