import { useRef, useEffect } from 'react';
import { useLocation, useSearchParams } from 'react-router-dom';

import { useSearch } from 'src/hooks/use-search';

/**
 * A Router component that clears search state when the pathname changes or when
 * the tab query parameter changes to a different value.
 * This component wraps around other components and ensures the search state is reset
 * during navigation between different routes.
 *
 * @component
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child elements to be rendered within the router
 *
 * @example
 * ```tsx
 * <SearchAwareRouter>
 *   <App />
 * </SearchAwareRouter>
 * ```
 */

export const SearchAwareRouter: React.FC<{ children: React.ReactNode }> = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const location = useLocation();
  const [searchParams] = useSearchParams();
  const { clearSearch } = useSearch();
  const previousTab = useRef<string | null>(null);
  const currentTab = searchParams.get('tab');

  useEffect(() => {
    if (currentTab !== previousTab.current) {
      clearSearch();
    }
    previousTab.current = currentTab;
  }, [location.pathname, currentTab, clearSearch]);

  return <>{children}</>;
};
