import React from 'react';
import { Control, Controller } from 'react-hook-form';

import TextField from '@mui/material/TextField';
import Autocomplete from '@mui/material/Autocomplete';

import facilityType from '../../data/facilityType.json';

interface OrgTypeDropdownProps {
  control: Control<any>;
  name: string;
  label: string;
  value: string | null;
  error: boolean;
  helperText: string;
  onChange?: (value: string | null) => void;
  disabled?: boolean;
}

export const OrgTypeDropdown: React.FC<OrgTypeDropdownProps> = ({
  control,
  name,
  label,
  value,
  error,
  helperText,
  onChange,
  disabled,
}) => {
  const initialValue = value
    ? facilityType.find(type => type.code === value) || null
    : null;

  return (
    <Controller
      control={control}
      name={name}
      defaultValue={initialValue}
      rules={{ required: 'This field is required' }}
      render={({ field }) => {
        const { onChange: fieldOnChange, value: fieldValue } = field;

        return (
          <Autocomplete
            options={facilityType}
            disabled={disabled}
            getOptionLabel={option => option?.display || ''}
            isOptionEqualToValue={(option, value) => {
              if (!value || !option) return false;
              return option.code === value.code;
            }}
            onChange={(_, newValue) => {
              fieldOnChange(newValue);
              if (onChange) {
                onChange(newValue ? newValue.code : null);
              }
            }}
            renderInput={params => (
              <TextField
                {...params}
                disabled={disabled}
                label={label}
                variant="outlined"
                error={error}
                helperText={helperText}
                required
              />
            )}
            value={
              typeof fieldValue === 'string'
                ? facilityType.find(type => type.code === fieldValue) || null
                : fieldValue
            }
          />
        );
      }}
    />
  );
};
