import React from 'react';
import { useDataGrid } from '@refinedev/mui';
import { IUser, RelicDocument, IUserIdentity } from 'relic-ui';
import {
  CrudFilter,
  useGetIdentity,
  IResourceComponentsProps,
} from '@refinedev/core';

import { Box, Card } from '@mui/material';
import { GridColDef } from '@mui/x-data-grid-pro';

import { ListDocuments } from 'src/components/_document';

export const DocumentList: React.FC<IResourceComponentsProps> = () => {
  const { data: currentUser } = useGetIdentity<IUser>();

  const currentIdentity: IUserIdentity =
    currentUser?.userIdentity as IUserIdentity;

  const currentRole: string = currentIdentity?.role?.name
    ? currentIdentity?.role?.name
    : 'member';

  const initialFilters: CrudFilter[] = React.useMemo(() => {
    const filter: CrudFilter = {
      field: 'organizationId',
      value: currentIdentity?.portalIdentity?.organizationId,
      operator: 'eq',
    };
    return currentRole === 'admin' ? [] : [filter];
  }, [currentIdentity?.portalIdentity?.organizationId, currentRole]);

  const {
    dataGridProps: listDocumentProps,
    setFilters,
    tableQuery: listDocumentsQuery,
  } = useDataGrid<RelicDocument>({
    resource: 'documents',
    sorters: {
      initial: [
        {
          field: 'createDate',
          order: 'desc',
        },
      ],
    },
    filters: {
      permanent: initialFilters,
    },
    pagination: { pageSize: 10 },
  });

  const documentColumns = React.useMemo<GridColDef[]>(() => [], []);

  const refetchDocuments = React.useCallback(() => {
    listDocumentsQuery.refetch();
  }, [listDocumentsQuery]);

  return (
    <Card>
      <Box sx={{ width: '100%' }}>
        <ListDocuments
          {...listDocumentProps}
          loading={listDocumentsQuery.isFetching}
          refetchDocuments={refetchDocuments}
          pageSizeOptions={[10]}
          pagination
          columns={documentColumns}
          setFilters={setFilters}
        />
      </Box>
    </Card>
  );
};
