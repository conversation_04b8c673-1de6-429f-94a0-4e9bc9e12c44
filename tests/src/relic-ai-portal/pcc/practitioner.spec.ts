import { test, expect } from '@playwright/test';
import { common } from '../../../common';

test('PCC-Login-Success', async ({ page }) => {
  await page.goto(common.facilityPortal);
  await page.waitForURL(common.facilityPortal);
  await page.getByRole('link', { name: 'PointClickCare' }).click();
  await page.getByPlaceholder('example.johnsmith').fill(common.pccLoginCredentials.username);
  await page.getByPlaceholder('****').fill(common.pccLoginCredentials.password);
  await page.getByRole('button', { name: 'Sign in' }).click();
  await expect(page.getByRole('link', { name: 'Relic AI Portal' })).toBeVisible();
});

test('PCC-Login-Failure', async ({ page }) => {
  await page.goto(common.facilityPortal);
  await page.waitForURL(common.facilityPortal);
  await page.getByRole('link', { name: '<PERSON><PERSON>lick<PERSON>are' }).click();
  await page.getByPlaceholder('example.johnsmith').fill(common.pccLoginCredentials.username);
  await page.getByPlaceholder('****').fill('WRONG_PWD');
  await page.getByRole('button', { name: 'Sign in' }).click();
  await expect(page.getByText('There was an error logging')).toBeVisible();
});