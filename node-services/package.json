{"name": "node-services", "version": "1.0.0", "description": "", "main": "index.js", "packageManager": "pnpm@10.10.0", "scripts": {"watch": "tsc -w --preserveWatchOutput", "dev": "wait-on ../relic-ui/dist/relic-ui.es.js && ts-node-dev --respawn --transpile-only index.ts", "dev:start": "nodemon --watch dist --ext js --exec node ./dist/index.js", "start": "node -r newrelic ./dist/index.js", "build": "tsc && cp -R ./certs ./dist/ && cp -R ./src/public ./dist/src/", "check-types": "tsc --noEmit && pnpm circular", "test": "find ./test -type f -name '*.ts' ! -name '*pcc*.ts' ! -name 'training*.ts' | xargs tap --node-arg=--require=ts-node/register --timeout=60000 --no-check-coverage", "test:pcc": "find ./test -type f -name '*pcc*.ts' | xargs tap --node-arg=--require=ts-node/register --timeout=60000 --no-check-coverage", "test:watch": "find ./test -type f -name '*.ts' ! -name '*pcc*.ts' | xargs tap --node-arg=--require=ts-node/register --timeout=60000 --no-check-coverage --watch", "test:watch:pcc": "find ./test -type f -name '*pcc*.ts' | xargs tap --node-arg=--require=ts-node/register --timeout=60000 --no-check-coverage --watch", "circular": "madge --circular --extensions ts,tsx,js,json,yml,yaml,md --ts-config tsconfig.json src"}, "nodemonConfig": {"ignore": ["./downloads/*", "./storage/*"]}, "keywords": ["fastify starter"], "author": "reliccare", "license": "MIT", "dependencies": {"@azure/communication-chat": "^1.4.0", "@azure/communication-common": "^2.3.0", "@azure/communication-email": "^1.0.0", "@azure/communication-identity": "^1.2.0", "@azure/communication-sms": "^1.1.0", "@azure/core-auth": "^1.5.0", "@azure/identity": "^4.1.0", "@azure/msal-node": "^3.5.3", "@azure/search-documents": "^12.1.0", "@azure/storage-blob": "^12.17.0", "@crawlee/playwright": "^3.13.3", "@faker-js/faker": "^9.0.0", "@fastify/auth": "^5.0.0", "@fastify/autoload": "^6.0.0", "@fastify/cors": "^11.0.1", "@fastify/env": "^5.0.2", "@fastify/helmet": "^13.0.1", "@fastify/http-proxy": "^11.1.2", "@fastify/jwt": "^9.0.0", "@fastify/merge-json-schemas": "^0.2.1", "@fastify/mongodb": "^9.0.2", "@fastify/rate-limit": "^10.2.2", "@fastify/redis": "^7.0.0", "@fastify/request-context": "^6.0.0", "@fastify/sensible": "^6.0.0", "@fastify/static": "^8.0.0", "@fastify/swagger": "^9.0.0", "@fastify/swagger-ui": "^5.0.0", "@fastify/type-provider-json-schema-to-ts": "^5.0.0", "@fastify/under-pressure": "^9.0.3", "@medplum/core": "^4.0.0", "@medplum/fhirtypes": "^4.0.0", "@microsoft/microsoft-graph-client": "^3.0.7", "@pdftron/pdfnet-node": "11.5.0", "@types/html-to-text": "^9.0.4", "api": "^6.1.1", "apify": "^3.2.6", "apify-client": "^2.10.0", "axios": "^1.5.1", "bullmq": "^5.12.3", "close-with-grace": "^2.2.0", "config": "^3.3.9", "crawlee": "3.13.3", "date-fns": "^4.0.0", "deepl-node": "^1.11.0", "desm": "^1.3.0", "dotenv": "^16.3.1", "fastify": "^5.3.2", "fastify-axios": "^1.2.6", "fastify-cli": "^7.4.0", "fastify-plugin": "^5.0.0", "fastify-tsconfig": "^3.0.0", "fluent-json-schema": "^6.0.0", "generate-password": "^1.7.1", "get-jwks": "^11.0.1", "google-libphonenumber": "^3.2.34", "handlebars": "^4.7.8", "html-to-text": "^9.0.5", "install": "^0.13.0", "ioredis": "^5.4.1", "jsonwebtoken": "^9.0.2", "jwks-rsa": "^3.1.0", "lodash": "^4.17.21", "mongodb": "^6.16.0", "newrelic": "^12.0.0", "officeparser": "^5.0.0", "openapi-fetch": "^0.14.0", "pdfkit": "^0.17.0", "pino-pretty": "^13.0.0", "playwright": "*", "playwright-chromium": "^1.50.1", "playwright-extra": "^4.3.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "relic-ui": "workspace:relic-ui", "tar": "^7.1.0", "undici": "^7.9.0", "uuid": "^11.0.0", "validator": "^13.12.0"}, "devDependencies": {"@microsoft/microsoft-graph-types": "^2.40.0", "@tapjs/run": "^4.0.0", "@types/config": "^3.3.1", "@types/google-libphonenumber": "^7.4.30", "@types/lodash": "^4.14.199", "@types/node": "^22.0.0", "@types/uuid": "^10.0.0", "concurrently": "^9.1.2", "fastify-tsconfig": "^3.0.0", "form-auto-content": "^3.2.0", "madge": "^8.0.0", "puppeteer": "^24.0.0", "tap": "^21.0.0", "ts-node-dev": "^2.0.0", "tsx": "^4.6.2", "typescript": "^5.2.2", "wait-on": "^8.0.1"}}