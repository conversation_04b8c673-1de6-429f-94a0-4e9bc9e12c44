import { FastifyInstance, FastifyPluginAsync, FastifyPluginOptions } from 'fastify';
import { OrganizationService } from '../../services/organization/organization.service';
import userPlugin from './userPlugin';
import { AccessPolicy } from 'relic-ui';
import { requestContext } from '@fastify/request-context';

declare module 'fastify' {
  interface FastifyInstance {
    orgService: OrganizationService;
    setMyOrganization(organizationId: string): Promise<void>;
    getMyAccessPolicy(): Promise<AccessPolicy>;
    
  }
}

const tenantPlugin: FastifyPluginAsync = async (fastify: FastifyInstance, options: FastifyPluginOptions) => {

  // relicLanguages decorator for Languages collection
  fastify.decorate('relicLanguages', function () {
    return fastify.mongo.reliccare.db.collection('languages');
  });
  fastify.log.info('relicLanguages decorator successful.');

  // orgService decorator for OrganizationService
  if (!fastify.hasDecorator('orgService')) {
    fastify.decorate('orgService', new OrganizationService(fastify, options));
  }
  fastify.log.info('orgService decorator successful.');

  fastify.decorate('setMyOrganization', async function (organizationId: string): Promise<void> {
    if (!organizationId) {
      throw fastify.httpErrors.badRequest('Organization id is mandatory.');
    }
    const relicOrganization = await fastify.orgService.getOrganization(organizationId);
    requestContext.set('organization', relicOrganization);
  });
    
  fastify.register(userPlugin, options);

};

export default tenantPlugin;
