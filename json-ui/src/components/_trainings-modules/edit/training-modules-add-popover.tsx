import { useTranslate } from '@refinedev/core';
import { useState } from 'react';

import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import MenuItem from '@mui/material/MenuItem';
import Popover from '@mui/material/Popover';

import { ReturnType as PopoverProps } from 'src/hooks/use-popover';

import Iconify from 'src/components/iconify';

import TrainingViaBrowsingDialog from './training-via-browsing-dialog';
import TrainingViaFileDialog from './training-via-file-dialog';
import TrainingViaWebPagesDialog from './training-via-web-pages-dialog';

type TrainingModulesAddPopoverProps = {
  popover: PopoverProps;
  refetchTrainingContent: VoidFunction;
};

export default function TrainingModulesAddPopover({
  popover,
  refetchTrainingContent,
}: TrainingModulesAddPopoverProps) {
  const translate = useTranslate();
  const [showFileDialog, setShowFileDialog] = useState(false);
  const [showBrowsingDialog, setShowBrowsingDialog] = useState(false);
  const [showWebPagesDialog, setShowWebPagesDialog] = useState(false);

  const handleFileDialogOpen = () => {
    setShowFileDialog(true);
    popover.onClose();
  };

  const handleBrowsingDialogOpen = () => {
    setShowBrowsingDialog(true);
    popover.onClose();
  };

  const handleWebPagesDialogOpen = () => {
    setShowWebPagesDialog(true);
    popover.onClose();
  };

  return (
    <>
      <Popover
        open={!!popover.open}
        anchorEl={popover.open}
        onClose={popover.onClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        slotProps={{
          paper: {
            sx: {
              mt: 0.5,
              minWidth: 220,
            },
          },
        }}
      >
        <MenuItem onClick={handleFileDialogOpen}>
          <ListItemIcon>
            <Iconify width={18} icon={'eva:file-fill'} />
          </ListItemIcon>
          <ListItemText primary={translate('trainings-content.add-via-file')} />
        </MenuItem>

        <MenuItem onClick={handleBrowsingDialogOpen}>
          <ListItemIcon>
            <Iconify width={18} icon={'eva:globe-fill'} />
          </ListItemIcon>
          <ListItemText
            primary={translate('trainings-content.add-via-browsing')}
          />
        </MenuItem>

        <MenuItem onClick={handleWebPagesDialogOpen}>
          <ListItemIcon>
            <Iconify width={18} icon={'eva:list-fill'} />
          </ListItemIcon>
          <ListItemText
            primary={translate('trainings-content.add-via-web-pages')}
          />
        </MenuItem>
      </Popover>

      <TrainingViaFileDialog
        refetchTrainingContent={refetchTrainingContent}
        open={showFileDialog}
        onClose={() => setShowFileDialog(false)}
      />

      <TrainingViaBrowsingDialog
        refetchTrainingContent={refetchTrainingContent}
        open={showBrowsingDialog}
        onClose={() => setShowBrowsingDialog(false)}
      />

      <TrainingViaWebPagesDialog
        refetchTrainingContent={refetchTrainingContent}
        open={showWebPagesDialog}
        onClose={() => setShowWebPagesDialog(false)}
      />
    </>
  );
}
