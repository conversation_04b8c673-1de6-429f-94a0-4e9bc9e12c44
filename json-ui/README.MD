# Json-Ui

Json-Ui project is designed to work in parallel with [json-server](https://github.com/reliccare/services/tree/main/json-server) project. Json-Ui is for rapid UI prototyping using designed REST interfaces.

## Getting Started with Refine

[**Refine**](https://refine.dev) is a React-based framework for building data-intensive applications. Refine offers a number of out-of-the box functionality for rapid user interface development, without compromising extreme customizability.

At Relic Care, we would be using Refine to 
- <b>Auto-generate any UI</b> that has basic CRUD screens. After auto-generation, these screens can be modified as per requirements. Refine offers a headless architecture & hence full customizations on the user-interface are possible.
- <b>Create quick playgrounds</b> for back-end REST services. This would allow back-end developers to test/verify their system behavior for any issues that are raised. This playground can be customized over time for specific REST end points (also called as [refine resources](https://refine.dev/docs/tutorial/understanding-resources/index/#what-is-resource)).
- <b>Develop our front end</b> using [Refine Core API](https://refine.dev/docs/api-reference/core/) so we can follow refine's standard core APIs as a best practice for front-end development. If we need to deviate from refine core APIs for front end development then it should require a discussion and approval from Technical Leadership.

This [refine](https://refine.dev) project was generated using [refine cli](https://refine.dev/docs/tutorial/getting-started/mui/create-project/#launch-the-refine-cli-setup)

```bash
    npm create refine-app@latest
```
## Available Scripts

Scripts mentioned below are available 
### Running the development server.

```bash
    pnpm dev
```

### Building for production.

```bash
    pnpm build
```

### Running the production server.

```bash
    pnpm start
```

## Learn More

To learn more about **refine**, please check out the [Documentation](https://refine.dev/docs)

- **REST Data Provider** [Docs](https://refine.dev/docs/core/providers/data-provider/#overview)
- **Material UI** [Docs](https://refine.dev/docs/ui-frameworks/mui/tutorial/)
- **Inferencer** [Docs](https://refine.dev/docs/packages/documentation/inferencer)
- **React Router** [Docs](https://refine.dev/docs/core/providers/router-provider/)
- **Custom Auth Provider** [Docs](https://refine.dev/docs/core/providers/auth-provider/)
- **i18n** [Docs](https://refine.dev/docs/core/providers/i18n-provider/)

## License

MIT
