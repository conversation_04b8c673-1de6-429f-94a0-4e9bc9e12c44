import { test, expect } from './test.setup';
import { common, getPCCPatient } from '../../../common';

test('PCC-View-CarePlan-Success', async ({ page }) => {
  await expect(page.getByRole('banner')).toContainText('registerednurse');
  await expect(page.getByRole('heading', { name: 'Residents' })).toBeVisible();
  Promise.all([
    await page.getByPlaceholder('Search...').click(),
    await page.waitForSelector('.MuiDataGrid-row--firstVisible', { state: 'visible' })
  ])
  await page.getByPlaceholder('Search...').fill('Held');
  await page.getByPlaceholder('Search...').press('Enter'),
  await expect(page.getByRole('gridcell', { name: 'Held, Rhoda (2174)' })).toBeVisible();
  await page.getByRole('gridcell', { name: '<PERSON>, <PERSON>ho<PERSON> (2174)' }).click();
  await expect(page.getByRole('heading', { name: '<PERSON>, <PERSON>ho<PERSON> (2174)' })).toBeVisible();
  await page.getByRole('tab', { name: 'Documents' }).click();
  await page.getByText('CarePlan-185449.pdf').click(),
  await page.getByTestId('refine-delete-button').click();
  Promise.all([
    await page.getByRole('button', { name: 'Delete' }).click(),
    await expect(page.getByText('Successfully Deleted CarePlan')).toBeVisible(),
  ])
  await page.reload();
  await page.waitForSelector('.MuiDataGrid-row--firstVisible', { state: 'visible' });
  await page.getByText('CarePlan-185449.pdf', { exact: true }).click();
  await expect(page.getByText('Preparing CarePlan-185449.pdf')).toBeVisible();
  await expect(page.getByRole('dialog').locator('span').filter({ hasText: 'CarePlan-185449.pdf' })).toBeVisible();
  await page.getByRole('button', { name: 'Cancel' }).click();
}); 

test('PCC-Translate_Care-Plan-Success', async ({ page }) => {
  await expect(page.getByRole('banner')).toContainText('registerednurse');
  await expect(page.getByRole('heading', { name: 'Residents' })).toBeVisible();
  Promise.all([
    await page.getByPlaceholder('Search...').click(),
    await page.waitForSelector('.MuiDataGrid-row--firstVisible', { state: 'visible' })
  ])
  await page.getByPlaceholder('Search...').fill('Held');
  await page.getByPlaceholder('Search...').press('Enter');
  await expect(page.getByRole('gridcell', { name: 'Held, Rhoda (2174)' })).toBeVisible();
  await page.getByRole('gridcell', { name: 'Held, Rhoda (2174)' }).click();
  await expect(page.getByRole('heading', { name: 'Held, Rhoda (2174)' })).toBeVisible();
  await page.getByRole('tab', { name: 'Documents' }).click();
  await page.getByText('CarePlan-185449.pdf').click();
  await page.getByTestId('refine-delete-button').click();
  Promise.all([
    await page.getByRole('button', { name: 'Delete' }).click(),
    await expect(page.getByText('Successfully Deleted CarePlan')).toBeVisible(),
  ])
  await page.reload();
  await page.waitForSelector('.MuiDataGrid-row--firstVisible', { state: 'visible' });
  await page.getByText('CarePlan-185449.pdf', { exact: true }).click();
  await expect(page.getByText('Preparing CarePlan-185449.pdf')).toBeVisible();
  await expect(page.getByRole('dialog').locator('span').filter({ hasText: 'CarePlan-185449.pdf' })).toBeVisible();
  await page.getByRole('button', { name: 'Cancel' }).click();
  await page.getByText('CarePlan-185449.pdf').click();
  await page.getByLabel('Translate To *').click();
  await page.getByRole('combobox', { name: 'Translate To' }).fill('Spanish');
  await page.getByRole('option', { name: 'Spanish' }).click();
  await page.getByRole('button', { name: 'Translate' }).click();
  await expect(page.getByText('Successfully started')).toBeVisible();
  await page.getByText('CarePlan-185449-orig-fmt-ES.').click();
  await expect(page.getByText('Preparing CarePlan-185449-')).toBeVisible();
  await expect(page.getByRole('link', { name: 'CarePlan-185449-orig-fmt-ES.' })).toBeVisible();
  await page.getByRole('button', { name: 'Done' }).click();
}); 
