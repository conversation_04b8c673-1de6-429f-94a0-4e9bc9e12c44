import { useMemo } from 'react';
import { CommunicationPanel } from 'relic-ui';

import Stack from '@mui/material/Stack';
import { useTheme } from '@mui/material';

interface ChatViewProps {
  accessToken: string;
}

/**
 * ChatView React Component
 *
 * This component renders a chat interface with custom styling for message threads and send box.
 * It uses the Material-UI theme for styling and memoizes the styles for performance optimization.
 *
 * @param {Object} props - The props for the ChatView component.
 * @param {string} props.accessToken - The access token used to authenticate the chat session.
 *
 * @returns {JSX.Element} The rendered ChatView component.
 */
const ChatView: React.FC<ChatViewProps> = ({
  accessToken,
}: {
  accessToken: string;
}): JSX.Element => {
  const theme = useTheme();

  // Custom styling memoized
  const customMessageThreadStyles = useMemo(
    () => ({
      chatMessageContainer: {
        backgroundColor: theme.palette.background.neutral,
        color: theme.palette.text.primary,
        fontFamily: theme.typography.fontFamily,
        fontSize: `${theme.typography.body2.fontSize}px`,
      },
      myChatMessageContainer: {
        backgroundColor: theme.palette.primary.lighter,
        color: theme.palette.text.primary,
        fontFamily: theme.typography.fontFamily,
        fontSize: `${theme.typography.body2.fontSize}px`,
      },
      chatContainer: {
        maxWidth: '100%',
      },
    }),
    [theme],
  );

  const customSendBoxStyles = useMemo(
    () => ({
      textField: {
        fontFamily: theme.typography.fontFamily,
        fontSize: `${theme.typography.body2.fontSize}px`,
      },
    }),
    [theme],
  );

  const toolbarProps = useMemo(
    () => ({
      showResponseManagement: true,
    }),
    [],
  );

  return (
    <Stack direction="row" sx={{ flexGrow: 1 }}>
      {accessToken ? (
        <CommunicationPanel
          pxAboveCommunicationPanel={220}
          toolbarProps={toolbarProps}
          messageThreadStyles={customMessageThreadStyles}
          sendBoxStyles={customSendBoxStyles}
        />
      ) : (
        <Stack sx={{ width: 1, height: 1, overflow: 'hidden' }} />
      )}
    </Stack>
  );
};

export default ChatView;
