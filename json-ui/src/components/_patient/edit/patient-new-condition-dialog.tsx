import { useTranslate, useCreate, MetaQuery } from '@refinedev/core';
import { useForm } from '@refinedev/react-hook-form';

import Stack from '@mui/material/Stack';
import Dialog from '@mui/material/Dialog';
import Button from '@mui/material/Button';
import Divider from '@mui/material/Divider';
import TextField from '@mui/material/TextField';
import LoadingButton from '@mui/lab/LoadingButton';
import DialogTitle from '@mui/material/DialogTitle';
import DialogActions from '@mui/material/DialogActions';
import { MenuItem } from '@mui/material';

// ----------------------------------------------------------------------

type Props = {
  open: boolean;
  onClose: VoidFunction;
} & AddNewConditionProps;

export type AddNewConditionProps = {
  meta?: MetaQuery;
  dataProviderName?: string;
  resource?: string;
}

export default function PatientNewConditionDialog({ open, onClose, meta, dataProviderName, resource}: Props ) {
  const translate = useTranslate();
  const {mutate} = useCreate();

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
    watch
  } = useForm({
    defaultValues: {
      code: '',
      note: '',
      status: '',
    },
  });

  
  const values = watch();

  const onSubmit = handleSubmit(async (data) => {
    try {
      await new Promise((resolve) => setTimeout(resolve, 1000));
      reset();
      onClose();
      if(dataProviderName && resource){
        mutate({
          resource: resource,
          dataProviderName, 
          values: {
            ...data,
            code: {text: data?.code}
          },
          meta: meta,
          successNotification: (data, values, resource) => {
              return {
                message: translate('notifications.createSuccess', {resource: 'condition'}),
                description: "Successful",
                type: "success"
              }
          },
          errorNotification:(error, values, resource) => { 
              return {
                message: translate('notifications.createError',{resource: 'condition', statusCode: error?.statusCode}),
                description: error?.message,
                type: 'error'
              }
          }
        })
      }
    } catch (error) {
      console.error(error);
    }
  });

  return (
    <>
      <Dialog
        fullWidth
        maxWidth="xs"
        open={open}
        onClose={onClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <Stack component="form" onSubmit={onSubmit} autoComplete="off">
          <DialogTitle id="alert-dialog-title"> {translate('conditions.titles.create')}</DialogTitle>

          <Divider />

          <Stack spacing={3} sx={{ p: 3 }}>
            <TextField
              {...register('code', {
                required: 'This field is required',
              })}
              error={!!(errors as any)?.code}
              helperText={(errors as any)?.code?.message}
              fullWidth
              type="text"
              label={translate('conditions.fields.code')}
              name="code"
            />

            <TextField
              {...register('note', {
                required: 'This field is required',
              })}
              error={!!(errors as any)?.note}
              helperText={(errors as any)?.note?.message}
              fullWidth
              multiline
              minRows={4}
              maxRows={8}
              type="text"
              label={translate('conditions.fields.note')}
              name="note"
            />
            <TextField
              {...register('status', {
                required: 'This field is required',
              })}
              select
              fullWidth
              name="status"
              value={values.status || ''}
              InputLabelProps={{ shrink: true }}
              label={translate('conditions.fields.clinicalStatus')}
              helperText={(errors as any)?.status?.message}
              SelectProps={{
                displayEmpty: true,
                sx: {
                  ...(!values.status && {
                    color: 'text.disabled',
                  }),
                },
              }}
            >
            <MenuItem value="" sx={{ color: 'text.disabled', display: 'none' }}>
              {translate('selects.empty')}
            </MenuItem>
            {[
              { value: 'active', label: 'selects.clinicalStatus.active' },
              { value: 'recurrence', label: 'selects.clinicalStatus.recurrence' },
              { value: 'relapse', label: 'selects.clinicalStatus.relapse' },
              { value: 'inactive', label: 'selects.clinicalStatus.inactive' },
              { value: 'remission', label: 'selects.clinicalStatus.remission' },
              { value: 'resolved', label: 'selects.clinicalStatus.resolved' },
              ].map((option) => (
              <MenuItem key={option.value} value={option.value}>
                {translate(option.label)}
              </MenuItem>
            ))}</TextField>
          </Stack>

          <Divider />

          <DialogActions>
            <Button
              onClick={() => {
                onClose();
                reset();
              }}
            >
              {translate('buttons.cancel')}
            </Button>

            <LoadingButton type="submit" variant="contained" loading={isSubmitting}>
              {translate('buttons.create')}
            </LoadingButton>
          </DialogActions>
        </Stack>
      </Dialog>
    </>
  );
}
