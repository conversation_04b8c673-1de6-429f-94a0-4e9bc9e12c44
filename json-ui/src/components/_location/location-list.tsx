import { CrudFilter, useMany, useOnError, useTranslate } from '@refinedev/core';
import { List } from '@refinedev/mui';
import React, { useEffect, useMemo } from 'react';
import { RelicOrganization } from 'relic-ui';

import { Box, useMediaQuery } from '@mui/material';
import {
  DataGridPro,
  DataGridProProps,
  GridCellParams,
  gridClasses,
  GridColDef,
} from '@mui/x-data-grid-pro';

import { useRouter } from 'src/routes/hooks';

import { useSearch } from 'src/hooks/use-search';

import Label from 'src/components/label';
import { RowActionsPopover } from 'src/components/list';
import { DataGridToolbar } from 'src/components/list/data-grid-toolbar';
import { PhoneInputView } from 'src/components/phone-input';
import { NewButton } from 'src/components/refine-customs/new-btn';

type SetFilterBehavior = 'merge' | 'replace';
interface LocationListProps extends DataGridProProps {
  currentOrganizationsData?: RelicOrganization;
  setFilters: ((filters: CrudFilter[], behavior?: SetFilterBehavior) => void) &
    ((setter: (prevFilters: CrudFilter[]) => CrudFilter[]) => void);
}
export default function ListLocations({
  currentOrganizationsData,
  setFilters,
  ...dataGridProps
}: LocationListProps) {
  const translate = useTranslate();

  const router = useRouter();

  const { globalSearch } = useSearch();

  const { mutate: onError } = useOnError();

  const isSmallScreen = useMediaQuery('(max-width:600px)');

  const [viewButtonEl, setViewButtonEl] =
    React.useState<HTMLButtonElement | null>(null);

  const { data: organizationData, isLoading: organizationIsLoading } = useMany({
    resource: 'organizations',
    ids: dataGridProps?.rows?.map((item: any) => item?.organizationId) ?? [],
    queryOptions: {
      enabled: !!dataGridProps?.rows,
    },
  });

  const columns = React.useMemo<GridColDef[]>(
    () => [
      {
        field: 'name',
        flex: 1,
        headerName: translate('locations.fields.name'),
        minWidth: 300,
      },

      {
        field: 'phone',
        flex: 1,
        headerName: translate('locations.fields.phone'),
        minWidth: 200,
        renderCell: function render({ value }) {
          return (
            <Box
              sx={{
                marginTop: '2rem',
              }}
            >
              <PhoneInputView value={value} label="" />
            </Box>
          );
        },
      },
      {
        field: 'fax',
        flex: 1,
        headerName: translate('locations.fields.fax'),
        minWidth: 200,
        renderCell: function render({ value }) {
          return (
            <Box
              sx={{
                marginTop: '2rem',
              }}
            >
              <PhoneInputView value={value} label="" />
            </Box>
          );
        },
      },
      {
        field: 'organizationId',
        flex: 1,
        headerAlign: 'left',
        headerName: translate('locations.fields.organizationId'),
        minWidth: 200,
        renderCell: function render({ value }) {
          return organizationIsLoading ? (
            <>Loading...</>
          ) : (
            organizationData?.data?.find(item => item.id === value)?.name
          );
        },
      },
      {
        field: 'status',
        flex: 1,
        headerName: translate('locations.fields.status'),
        minWidth: 200,
        renderCell: function render({ value }) {
          return (
            <Label variant="soft" color={value ? 'success' : 'error'}>
              {translate(value ? 'status.enabled' : 'status.disabled')}
            </Label>
          );
        },
      },
      {
        field: 'actions',
        headerName: translate('table.actions'),
        sortable: false,
        renderCell: function render({ row }) {
          return (
            <>
              <RowActionsPopover
                rowId={row.id}
                deleteButtonProps={{
                  confirmTitle: translate('location.titles.delete'),
                  confirmContent: `Are you sure you want to delete ${row.name}?`,
                  resource: 'locations',
                  recordItemId: row.id,
                }}
              />
            </>
          );
        },
        align: 'center',
        headerAlign: 'center',
        minWidth: 80,
      },
    ],
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [translate, organizationData?.data],
  );

  const handleOnCellClick = React.useCallback(
    (params: GridCellParams) => {
      if (params.field !== 'actions') {
        router.push(`/locations/edit/${params.id}`);
      }
    },
    [router],
  );

  const Toolbar = React.useCallback(
    () => (
      <DataGridToolbar
        setViewButtonEl={setViewButtonEl}
        title={translate('locations.locations')}
        actions={
          <NewButton
            href={'/locations/create'}
            label={translate('buttons.create')}
          />
        }
      />
    ),
    [translate],
  );

  const displayColumns = useMemo(() => {
    if (isSmallScreen) {
      return columns.filter(
        col => col.field === 'organizationId' || col.field === 'actions',
      );
    }

    if (currentOrganizationsData && currentOrganizationsData.id) {
      return columns.filter(col => col.field !== 'organizationId');
    }

    return columns;
  }, [columns, currentOrganizationsData, isSmallScreen]);

  // Render the grid
  function renderGrid() {
    return (
      <>
        <DataGridPro
          {...dataGridProps}
          pagination
          sx={{
            '& .MuiDataGrid-cell': {
              display: 'flex',
              alignSelf: 'center',
              height: '100%',
              border: 'none',
            },
          }}
          disableColumnMenu
          columns={displayColumns}
          localeText={{ toolbarColumns: 'View' }}
          onCellClick={handleOnCellClick}
          slots={
            currentOrganizationsData
              ? undefined
              : {
                  toolbar: Toolbar,
                }
          }
          slotProps={{
            panel: {
              anchorEl: viewButtonEl,
            },
            toolbar: {
              onClick: (e: React.MouseEvent) => {
                e.stopPropagation();
                setViewButtonEl(null);
              },
            },
          }}
        />
      </>
    );
  }

  // Render the grid for location Tab
  function renderGridTransHeader() {
    return (
      <List
        headerProps={{
          sx: {
            display: 'none',
          },
        }}
        contentProps={{
          sx: {
            p: '0 !important',
          },
        }}
        breadcrumb={null}
        wrapperProps={{
          sx: {
            boxShadow: 'none',
            background: 'transparent',
            [`& .${gridClasses.root}`]: {
              [`& .${gridClasses.cell}`]: {
                py: 1,
              },
              [`& .${gridClasses.columnHeader}`]: {
                bgcolor: 'transparent',
              },
              [`& .${gridClasses.columnHeaders}`]: {
                bgcolor: 'transparent',
                borderBottomStyle: 'dashed',
              },
            },
          },
        }}
      >
        {renderGrid()}
      </List>
    );
  }

  useEffect(() => {
    try {
      if (globalSearch.length > 0) {
        setFilters([
          {
            field: 'search',
            value: globalSearch.length > 0 ? globalSearch : '',
            operator: 'contains',
          },
        ]);
      } else {
        setFilters([]);
      }
    } catch (error) {
      onError(error);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [globalSearch]);

  return (
    <>
      {currentOrganizationsData && currentOrganizationsData.id
        ? renderGridTransHeader()
        : renderGrid()}
    </>
  );
}
