import { useCreate, useShow, useTranslate } from '@refinedev/core';
import React from 'react';
import { useDropzone } from 'react-dropzone';
import { useParams } from 'react-router-dom';
import { RelicDocument } from 'relic-ui';

import DeleteIcon from '@mui/icons-material/Delete';
import DescriptionIcon from '@mui/icons-material/Description';
import InsertDriveFileIcon from '@mui/icons-material/InsertDriveFile';
import { Alert, Box, Button, Dialog, Stack, Typography } from '@mui/material';

import { useForm } from 'react-hook-form';
import { Create } from 'src/components/refine-customs/create';
import { uploadToAzureBlobStorage } from 'src/utils/pdf-utils';

const TrainingViaFileDialog = ({
  open,
  onClose,
  refetchTrainingContent,
}: {
  open: boolean;
  onClose: VoidFunction;
  refetchTrainingContent: VoidFunction;
}) => {
  const {
    handleSubmit,
    reset,
    setError,
    formState: { errors, isSubmitting },
    clearErrors,
  } = useForm({
    defaultValues: {
      file: '',
    },
  });

  const [uploadError, setUploadError] = React.useState<string | null>('');
  const [files, setFiles] = React.useState<File[]>([]);

  const { id } = useParams();

  const { query } = useShow({
    resource: 'trainings/modules',
    id: id,
  });

  const { mutate, isLoading } = useCreate({
    resource: `trainings/modules/${id}/contents`,
    invalidates: ['list'],
  });

  const translate = useTranslate();

  const onSubmit = async () => {
    if (files.length === 0) {
      setError('file', {
        type: 'manual',
        message: 'Please select at least one file',
      });
      return;
    }

    try {
      const containerName = query?.data?.data?.storage?.containerName;

      if (!containerName) {
        setUploadError('Training module container not found.');
        return;
      }

      const uploadPromises = files.map(async file => {
        const extension = file.name.substring(file.name.lastIndexOf('.') + 1);
        const basename = file.name.substring(0, file.name.lastIndexOf('.'));
        const documentId = `${basename}_${crypto.randomUUID()}.${extension}`;

        const uploadOption = {
          accountName: 'relicstorage',
          containerName: containerName,
        };

        const relicDocument: RelicDocument = {
          organizationId: '',
          language: 'en-us',
          type: file.type,
          url: '',
          filename: file.name,
          documentId,
          header: 0,
          footer: 0,
          status: 'done',
          translationType: 'mono',
        };

        const uploadedDocument = await uploadToAzureBlobStorage(
          file,
          relicDocument,
          uploadOption,
        );

        if (!uploadedDocument) {
          throw new Error('Failed to upload the document.');
        }

        const payloadData = {
          url: uploadedDocument.url,
          documentId: uploadedDocument.documentId,
          filename: uploadedDocument.filename,
          type: uploadedDocument.type,
        };

        return mutate(
          {
            values: payloadData,
            successNotification: () => ({
              message: `Successfully added Training Content: ${uploadedDocument.filename}`,
              type: 'success',
            }),
            errorNotification: (error: any) => ({
              message: `Error adding Training Content: ${error.message}`,
              type: 'error',
            }),
          },
          {
            onSuccess: () => {
              refetchTrainingContent();
              console.log(
                `Successfully added Training Content: ${uploadedDocument.filename}`,
              );
            },
            onError: (error: any) => {
              console.error(`Error adding Training Content: ${error.message}`);
            },
          },
        );
      });

      try {
        await Promise.all(uploadPromises);
      } catch (error) {
        setUploadError(`Failed to upload documents: ${error.message}`);
        return;
      }

      onClose();
    } catch (error) {
      setUploadError(`Failed to upload the document - ${error.message}`);
      console.error(error);
    }
  };

  const addFile = React.useCallback(
    (acceptedFiles: File[]) => {
      setFiles(prevFiles => [...prevFiles, ...acceptedFiles]);
      clearErrors('file');
    },
    [clearErrors],
  );

  const removeFile = (fileToRemove: File) => {
    setFiles(prevFiles => prevFiles.filter(file => file !== fileToRemove));
  };

  const removeAllFiles = () => {
    setFiles([]);
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop: addFile,
    accept: { 'application/pdf': ['.pdf'] },
    multiple: true,
  });

  const handleClose = () => {
    reset();
    setFiles([]);
    setUploadError(null);
    onClose();
  };

  return (
    <Dialog
      fullWidth
      maxWidth="sm"
      open={open}
      onClose={handleClose}
      aria-labelledby="alert-dialog-title"
      aria-describedby="alert-dialog-description"
    >
      <Create
        isLoading={isSubmitting || isLoading}
        title={translate('trainings-content.titles.add-via-file')}
        onClose={handleClose}
        saveButtonProps={{
          onClick: handleSubmit(onSubmit),
          disabled: isSubmitting || isLoading,
          loading: isSubmitting || isLoading,
        }}
      >
        <Typography sx={{ typography: 'subtitle3', mb: 2 }}>
          {translate('trainings-content.subtitles.add-via-file')}
        </Typography>
        <Stack spacing={2.5} component="form" autoComplete="off">
          {uploadError && <Alert severity="error">{uploadError}</Alert>}

          <Box
            {...getRootProps()}
            sx={{
              p: 5,
              outline: 'none',
              borderRadius: 1,
              cursor: 'pointer',
              overflow: 'hidden',
              position: 'relative',
              bgcolor: theme =>
                theme.palette.mode === 'light' ? 'grey.100' : 'grey.800',
              border: theme => `1px dashed ${theme.palette.divider}`,
              '&:hover': {
                opacity: 0.72,
              },
              ...(isDragActive && {
                opacity: 0.72,
              }),
              ...(files.length > 0 && {
                padding: '5% 0',
              }),
            }}
          >
            <input {...getInputProps()} />

            {files.length > 0 ? (
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                  alignItems: 'center',
                  textAlign: 'center',
                  width: '100%',
                }}
              >
                <Typography variant="subtitle1" sx={{ mb: 2 }}>
                  {`${files.length} file${files.length > 1 ? 's' : ''} selected`}
                </Typography>

                <Box
                  sx={{
                    maxHeight: 200,
                    overflow: 'auto',
                    width: '100%',
                    mb: 2,
                  }}
                >
                  {files.map((file, index) => (
                    <Box
                      key={`${file.name}-${index}`}
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        p: 1,
                        borderBottom: theme =>
                          index < files.length - 1
                            ? `1px solid ${theme.palette.divider}`
                            : 'none',
                      }}
                    >
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <DescriptionIcon
                          sx={{ mr: 1, color: 'text.secondary' }}
                        />
                        <Typography
                          variant="body2"
                          noWrap
                          sx={{ maxWidth: 200 }}
                        >
                          {file.name}
                        </Typography>
                      </Box>
                      <Button
                        size="small"
                        color="error"
                        onClick={e => {
                          e.stopPropagation();
                          removeFile(file);
                        }}
                        sx={{ minWidth: 'auto' }}
                      >
                        <DeleteIcon fontSize="small" />
                      </Button>
                    </Box>
                  ))}
                </Box>

                <Button
                  size="small"
                  color="error"
                  onClick={e => {
                    e.stopPropagation();
                    removeAllFiles();
                  }}
                  startIcon={<DeleteIcon />}
                >
                  {translate('buttons.remove-all')}
                </Button>
              </Box>
            ) : (
              <Stack
                spacing={1}
                alignItems="center"
                justifyContent="center"
                direction={{ xs: 'column', md: 'row' }}
                sx={{ width: 1 }}
              >
                <Stack
                  alignItems="center"
                  justifyContent="center"
                  sx={{
                    width: 40,
                    height: 40,
                    borderRadius: '50%',
                    backgroundColor: theme =>
                      theme.palette.mode === 'light'
                        ? 'primary.main'
                        : 'primary.dark',
                  }}
                >
                  <InsertDriveFileIcon
                    sx={{ color: 'common.white', width: 24, height: 24 }}
                  />
                </Stack>
                <Box sx={{ p: 1, ml: { md: 2 } }}>
                  <Typography variant="h6">
                    {translate('trainings-content.fields.drop-or-select-files')}
                  </Typography>

                  <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                    {translate('trainings-content.fields.drop-files-here')}
                  </Typography>
                </Box>
              </Stack>
            )}
          </Box>

          {(errors as any)?.file && (
            <Alert severity="error">{(errors as any)?.file?.message}</Alert>
          )}
        </Stack>
      </Create>
    </Dialog>
  );
};

export default TrainingViaFileDialog;
