import { Type, Static } from '@sinclair/typebox';

// RelicCommunicationLanguage
export const RelicCommunicationLanguageSchema = Type.Object({
  system: Type.Optional(Type.String()),
  code: Type.String(),
  display: Type.String(),
  preferred: Type.Optional(Type.Boolean()),
});
export type RelicCommunicationLanguage = Static<typeof RelicCommunicationLanguageSchema>;

// Role
export const RoleSchema = Type.Object({
  'practitioner-role': Type.String(),
  'display-role': Type.String(),
});
export type Role = Static<typeof RoleSchema>;

// Secret
export const SecretSchema = Type.Object({
  token: Type.String(),
  expiresOn: Type.String({ format: 'date-time' }),
});
export type Secret = Omit<Static<typeof SecretSchema>, 'expiresOn'> & { expiresOn: Date };

// IdentityProvider
export const IdentityProviderSchema = Type.Union([
  Type.Literal('medplum'),
  Type.Literal('pcc'),
  Type.Literal('msgraph'), //TODO: To be fixed. Replace with entra-external
  Type.Literal('entra'),
  Type.Literal('entra-external'),
]);
export type IdentityProvider = Static<typeof IdentityProviderSchema>;

// Communication Services Provider
export const CommunicationProviderSchema = Type.Union([
  Type.Literal('Azure Communication Service'),
  Type.Literal('acs'),
]);
export type CommunicationProvider = Static<typeof CommunicationProviderSchema>;

// User Schema
export const RelicUserSchema = Type.Object({
  id: Type.String(),
  resourceType: Type.String(),
});
export type RelicUser = Static<typeof RelicUserSchema>;

// HTTP Header schema for Authenticated Requests
const HttpHeaderSchema = Type.Object({
  'x-access-token': Type.String(),
  'x-id-token': Type.Optional(Type.String()),
  'x-organization-id': Type.String(),
});

// getList - Query schema
const QuerySchema = Type.Object({
  _start: Type.Optional(Type.String()),
  _end: Type.Optional(Type.String()),
  _sort: Type.Optional(Type.String()), // comma-separated string for multiple fields
  _order: Type.Optional(Type.Union([Type.Literal('asc'), Type.Literal('desc')])), // comma-separated string for multiple fields
  _search: Type.Optional(Type.String()),
});

// getList - Filter schema
const FilterSchema = Type.Record(Type.String(), Type.String());

// getOne, getMany - Id schema
const IdSchema = Type.Object({
  id: Type.String(), // comma-separated string for multiple IDs
});

// Header schema and type
export const RelicHeaderSchema = Type.Intersect([HttpHeaderSchema]);
export type RelicHeader = Static<typeof RelicHeaderSchema>;

// Id Param schema and type
export const RelicIdParamSchema = Type.Intersect([IdSchema]);
export type RelicIdParam = Static<typeof RelicIdParamSchema>;

// List schema and type
export const ListQuerySchema = Type.Intersect([QuerySchema]); //Removed FilterSchema as it causes issues with fastify-swagger.

// Get schema and type
export const GetQuerySchema = Type.Intersect([IdSchema]);
export type GetQuery = Static<typeof GetQuerySchema>;
