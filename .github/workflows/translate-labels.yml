name: Localize strings

on:
  push:
    branches: 
      - cb1-738
    
jobs:
  translate:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2

      # https://github.com/marketplace/actions/machine-translator for more details
      - name: Machine Translator
        id: translator
        uses: IEvangelist/resource-translator@v2.2.1
        with:
          # The source locale (for example, 'en') used to create the glob pattern
          # for which resource (**/*.en.resx) files to use as input
          sourceLocale: 'en-us'
          # The Azure Cognitive Services translator resource subscription key
          subscriptionKey: ${{ secrets.AZURE_TRANSLATOR_SUBSCRIPTION_KEY }}
          # The Azure Cognitive Services translator resource endpoint.
          endpoint: https://api.cognitive.microsofttranslator.com/
          # (Optional) The Azure Cognitive Services translator resource region.
          # This is optional when using a global translator resource.
          region: eastus
          # (Optional) Locales to translate to, otherwise all possible locales
          # are targeted. Requires double quotes.
          toLocales: '["ar","hy","bg","my","yue","en","fil","fr","hi","it","ja","ko","zh","fa","ru","es","th","vi"]'

      - name: create-pull-request
        uses: peter-evans/create-pull-request@v3.4.1
        if: ${{ steps.translator.outputs.has-new-translations }} == 'true'
        with:
          title: '${{ steps.translator.outputs.summary-title }}'
          commit-message: '${{ steps.translator.outputs.summary-message }}'