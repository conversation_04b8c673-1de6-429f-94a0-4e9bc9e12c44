import Stack from '@mui/material/Stack';
import Tooltip from '@mui/material/Tooltip';
import { GridColumnHeaderParams } from '@mui/x-data-grid-pro';

import Iconify from '../iconify';

// ----------------------------------------------------------------------

type Props = {
  title: string;
  params: GridColumnHeaderParams;
};

export function HeaderFieldTooltip({ title, params }: Props) {
  return (
    <Tooltip title={title}>
      <Stack
        direction={'row'}
        alignItems='center'
        sx={{
          color: 'text.secondary',
          fontWeight: 'fontWeightSemiBold',
        }}
      >
        {params.colDef.headerName}{' '}
        <Iconify
          width={16}
          icon={'eva:question-mark-circle-outline'}
          sx={{ ml: 1 }}
        />
      </Stack>
    </Tooltip>
  );
}
