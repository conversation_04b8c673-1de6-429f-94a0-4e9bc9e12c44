import tap from "tap";
import { fastify } from "../index";
import { MedplumClient, MedplumClientOptions, EmailPasswordLoginRequest, ProfileResource } from '@medplum/core';
import { JSDOM } from 'jsdom';

const { window } = new JSDOM();
global.window = window as unknown as Window & typeof globalThis;
global.document = window.document;
global.document = window.document;

const sessionStorageMock: Storage = {
    store: {} as Record<string, string>,
    getItem: function (key: string) {
        return this.store[key] || null;
    },
    setItem: function (key: string, value: string) {
        this.store[key] = value.toString();
    },
    removeItem: function (key: string) {
        delete this.store[key];
    },
    clear: function () {
        this.store = {};
    },
    get length() {
        return Object.keys(this.store).length;
    },
    key: function (index: number) {
        return Object.keys(this.store)[index] || null;
    },
};

global.sessionStorage = sessionStorageMock;

const trainingsApiEndpoint: string = '/api/trainings';
const practitionerEmail: string = '<EMAIL>';
const practitionerPwd: string = 'Z1sZ9N3F8tcycDDK';
let accessToken: any;

const trainingsName: string = `test-training-${Date.now()}`;
let trainingsId: string = '';

const medplumOptions: MedplumClientOptions = {
    verbose: true,
};
const medplum: MedplumClient = new MedplumClient(medplumOptions);
const loginToMedplum = async (
    medplum: MedplumClient,
    email: string,
    password: string
): Promise<ProfileResource> => {
    const loginRequest: EmailPasswordLoginRequest = {
        email,
        password,
    };
    const loginResponse = await medplum.startLogin(loginRequest);
    const userProfile = await medplum.processCode(loginResponse.code as string);
    accessToken = medplum.getActiveLogin()?.accessToken.toString();
    return userProfile;
};

tap.teardown(async () => {
    await fastify.close();
    process.exit(0);
});

tap.before(async () => {
    await loginToMedplum(medplum, practitionerEmail, practitionerPwd);
});

tap.test("create training with POST /api/trainings, should return success", async (t: Tap.Test) => {
    const response = await fastify.inject({
        method: "POST",
        url: trainingsApiEndpoint,
        payload: {
            "name": trainingsName,
            "description": "unit-test training",
            "modules": [],
            "schedule": {
                "interval": "PT24H"
            }
        },
        headers: ({
            'x-access-token': accessToken
        }),
    });

    t.equal(response.statusCode, 200);
    const responseBody = JSON.parse(response.body);
    t.ok(responseBody.id, 'training id is null/undefined');
    trainingsId = responseBody.id;
    t.equal(responseBody.name, trainingsName, 'training name mismatch');
    t.ok(responseBody.indexName, 'indexName is null/undefined');
    t.ok(responseBody.skillsetName, 'indexName is null/undefined');
    t.ok(responseBody.modules, 'training modules list is empty');
    t.ok(responseBody.modules.length > 0, 'training modules list is empty');
});

tap.test("get training details with GET /api/trainings/:id, should return success", async (t: Tap.Test) => {
    t.ok(trainingsId, 'training not yet created');
    const response = await fastify.inject({
        method: "GET",
        url: `${trainingsApiEndpoint}/${trainingsId}`,
        headers: ({
            'x-access-token': accessToken
        }),
    });

    t.equal(response.statusCode, 200);
    const responseBody = JSON.parse(response.body);
    t.equal(responseBody.id, trainingsId, 'training id mismatch');
    t.equal(responseBody.name, trainingsName, 'training name mismatch');
    t.ok(responseBody.indexName, 'indexName is null/undefined');
    t.ok(responseBody.skillsetName, 'indexName is null/undefined');
    t.ok(responseBody.modules, 'training modules list is empty');
    t.ok(responseBody.modules.length > 0, 'training modules list is empty');
});

tap.test("delete training with DELETE /api/trainings/:id, should return success", async (t: Tap.Test) => {
    t.ok(trainingsId, 'training not yet created');
    const response = await fastify.inject({
        method: "DELETE",
        url: `${trainingsApiEndpoint}/${trainingsId}`,
        headers: ({
            'x-access-token': accessToken
        }),
    });

    t.equal(response.statusCode, 200);
});