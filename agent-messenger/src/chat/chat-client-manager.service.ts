import { Injectable, InternalServerErrorException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ChatClient, ChatThreadClient } from '@azure/communication-chat';
import { getIdentifierRawId, AzureCommunicationTokenCredential } from '@azure/communication-common';
import { CommunicationIdentityClient } from '@azure/communication-identity';
import { NodeService } from '../node/node.service';
import { WidgetRequest, Topic, RelicChatParticipant } from 'relic-ui';
@Injectable()
export class ChatClientManager {
  private chatClients: Map<string, { client: ChatClient; expiresOn: Date }> = new Map();
  private connectionString: string;
  private endpointUrl: string;
  private aiAssistantId: string;
  private identityClient: CommunicationIdentityClient;

  constructor(
    private configService: ConfigService,
    private nodeService: NodeService,
  ) {}

  /**
   * Initializes the chat client manager service.
   * @throws {Error} If no agents are found during initialization.
   */
  async initialize(): Promise<void> {
    this.aiAssistantId = (await this.nodeService.getAIAssistant())?.communicationIdentities?.[0]?.userId; // AI Assistant
    const acsEndpoint = (await this.nodeService.getConfig('ACS.ENDPOINT')).value;
    const acsAccessKey = (await this.nodeService.getConfig('ACS.ACCESS_KEY')).value;
    if (acsEndpoint && acsAccessKey) {
      this.connectionString = `endpoint=${acsEndpoint};accessKey=${acsAccessKey}`;
      this.endpointUrl = acsEndpoint;
      this.identityClient = new CommunicationIdentityClient(this.connectionString);
    } else {
      throw new InternalServerErrorException('Communication services connection string not provided');
    }
  }

  async onModuleInit(): Promise<void> {
    await this.initialize();
  }

  private async getChatClientForUser(acsId: string): Promise<ChatClient> {
    const now = new Date();

    if (this.chatClients.has(acsId)) {
      const clientInfo = this.chatClients.get(acsId);
      const expiryBuffer = 10 * 60 * 1000; // 10 minute in milliseconds

      if (clientInfo.expiresOn.getTime() - now.getTime() > expiryBuffer) {
        return clientInfo.client;
      }

      // Token is about to expire or has expired
      this.chatClients.delete(acsId);
    }

    const { token, expiresOn } = await this.getOrRenewTokenForUser(acsId);
    const chatClient = new ChatClient(this.endpointUrl, new AzureCommunicationTokenCredential(token));

    this.chatClients.set(acsId, { client: chatClient, expiresOn });

    return chatClient;
  }

  async getChatThreadClientForParticipant(
    threadId: string,
    relicChatParticipant?: RelicChatParticipant,
  ): Promise<ChatThreadClient> {
    const acsId: string = relicChatParticipant ? getIdentifierRawId(relicChatParticipant.id) : this.aiAssistantId;
    const aiAssistantChatClient = await this.getChatClientForUser(acsId);
    return aiAssistantChatClient.getChatThreadClient(threadId);
  }

  async getChatThreadClientForAiAssistant(threadId: string): Promise<ChatThreadClient> {
    const chatClient = await this.getChatClientForUser(this.aiAssistantId);
    const chatThreadClient = chatClient.getChatThreadClient(threadId);

    return chatThreadClient;
  }

  async handleError(threadId: string, error: Error, customMessage: string): Promise<void> {
    if (!threadId) {
      //If there is no thread id, we can safely ignore call errors
      return;
    }
    const errorObject: Error = {
      name: error?.name ?? 'Error',
      message: customMessage ? customMessage + error?.message : error?.message,
      stack: error?.stack,
    };
    await this.updateTopic(threadId, null, errorObject);
  }

  async getTopic(threadId: string): Promise<Topic> {
    const aiAssistantThreadClient = await this.getChatThreadClientForAiAssistant(threadId);
    const threadProperty = await aiAssistantThreadClient.getProperties();
    const topic = JSON.parse(threadProperty.topic) as Topic;
    return topic;
  }

  async updateTopic(
    threadId: string,
    threadMessage?: string,
    error?: Error,
    cleanMessage?: boolean,
    messageId?: string,
    widgetRequest?: WidgetRequest,
  ): Promise<void> {
    const aiAssistantThreadClient = await this.getChatThreadClientForAiAssistant(threadId);
    const topic = await this.getTopic(threadId);
    let updateTopic: boolean = false;
    if (messageId && widgetRequest) {
      topic.interactiveWidget = {
        requestMessageId: messageId,
        widgetData: widgetRequest,
      };
      updateTopic = true;
    }
    if (threadMessage && topic.threadMessage !== threadMessage) {
      topic.threadMessage = threadMessage;
      updateTopic = true;
    }
    if (error) {
      topic.error = error;
      updateTopic = true;
    }
    if (cleanMessage) {
      delete topic.threadMessage;
      delete topic.interactiveWidget;
      delete topic.error;
      updateTopic = true;
    }
    if (updateTopic) await aiAssistantThreadClient.updateTopic(JSON.stringify(topic));
  }

  private async getOrRenewTokenForUser(acsId: string): Promise<{ token: string; expiresOn: Date }> {
    const user = { communicationUserId: acsId };
    const { token, expiresOn } = await this.identityClient.getToken(user, ['chat']);

    return {
      token,
      expiresOn,
    };
  }
}
