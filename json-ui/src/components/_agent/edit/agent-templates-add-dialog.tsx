import { useTranslate } from '@refinedev/core';
import React from 'react';
import {
  Control,
  Controller,
  FieldErrors,
  FieldValues,
  SubmitHandler,
  UseFormReset,
  UseFormSetValue,
} from 'react-hook-form';

import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  TextField,
} from '@mui/material';
import { Templates } from 'src/types/agent-types';

interface Props {
  templatesSelectedRow: Templates | undefined;
  templatesRowData: Templates[];
  saveButtonProps: any;
  showAddDialog: boolean;
  setTemplatesRowData: React.Dispatch<React.SetStateAction<Templates[]>>;
  setShowAddDialog: React.Dispatch<React.SetStateAction<boolean>>;
  setTemplatesSelectedRow: React.Dispatch<
    React.SetStateAction<Templates | undefined>
  >;
  setValue: UseFormSetValue<FieldValues>;
  formErrors: FieldErrors<FieldValues>;
  formReset: UseFormReset<FieldValues>;
  handleSubmit: any;
  formControl: Control<FieldValues, object>;
}

const AgentTemplatesAddDialog = ({
  templatesSelectedRow,
  templatesRowData,
  saveButtonProps,
  showAddDialog,
  setTemplatesRowData,
  setShowAddDialog,
  setTemplatesSelectedRow,
  setValue,
  formErrors,
  formReset,
  handleSubmit,
  formControl,
}: Props) => {
  const translate = useTranslate();
  const onSubmit: SubmitHandler<Templates> = (data: Templates) => {
    let newTemplatesRowData;
    if (templatesSelectedRow) {
      const index = templatesRowData.findIndex(
        (row: Templates) => row.id === templatesSelectedRow?.id,
      );
      newTemplatesRowData = [...templatesRowData];
      newTemplatesRowData[index] = {
        ...templatesSelectedRow,
        ...data,
      };
    } else {
      newTemplatesRowData = [
        ...templatesRowData,
        {
          id: templatesRowData.length + 1,
          event: data.event,
          greetingTemplate: data.greetingTemplate,
        },
      ];
    }
    setTemplatesRowData(newTemplatesRowData);
    setTemplatesSelectedRow(undefined);
    setShowAddDialog(false);
    formReset();

    setValue(
      'relicAssistantSetup.greetingTemplates',
      newTemplatesRowData.map(({ event, greetingTemplate }) => ({
        event,
        greetingTemplate,
      })),
    );
    saveButtonProps.onClick();
  };

  const handleClose = () => {
    setShowAddDialog(false);
    setTemplatesSelectedRow(undefined);
    formReset();
  };
  return (
    <Dialog
      open={showAddDialog}
      fullWidth
      maxWidth="sm"
      onClose={handleClose}
      aria-labelledby="alert-dialog-title"
      aria-describedby="alert-dialog-description"
    >
      <DialogTitle id="alert-dialog-title">
        {templatesSelectedRow
          ? translate('agents.editTemplate')
          : translate('agents.createTemplate')}
      </DialogTitle>
      <Divider />
      <DialogContent>
        <Box my={3}>
          <Controller
            name="event"
            control={formControl}
            rules={{ required: true }}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                type="text"
                name="event"
                InputLabelProps={{ shrink: true }}
                label={translate('agents.fields.event')}
                error={!!(formErrors as any)?.event}
                required
                helperText={(formErrors as any)?.event?.message}
              />
            )}
          />
        </Box>
        <Box my={3}>
          <Controller
            name="greetingTemplate"
            control={formControl}
            rules={{ required: true }}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                type="text"
                name="greetingTemplate"
                InputLabelProps={{ shrink: true }}
                label={translate('agents.fields.greetingTemplate')}
                error={!!(formErrors as any)?.greetingTemplate}
                multiline
                maxRows={4}
                minRows={4}
                required
                helperText={(formErrors as any)?.greetingTemplate?.message}
              />
            )}
          />
        </Box>
      </DialogContent>
      <Divider />
      <DialogActions>
        <Button onClick={handleClose}>{translate('buttons.cancel')}</Button>
        <Button
          variant="contained"
          onClick={() => {
            handleSubmit(onSubmit)();
          }}
        >
          {translate('buttons.save')}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default AgentTemplatesAddDialog;
