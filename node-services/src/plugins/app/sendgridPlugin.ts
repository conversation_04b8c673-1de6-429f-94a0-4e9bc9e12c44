'use strict';

import { FastifyPluginAsync } from 'fastify';
import config from 'config';
import { convert } from 'html-to-text';
import { EmailClient } from '@azure/communication-email';
import { EmailNotificationOptions, WelcomeUserNotificationOptions } from '../../types/notification';
import Handlebars from 'handlebars';

/** Mail (Sendgrid) plugin
 *  Plugin provides following decorator:
 *      - mail : mail client using @sendgrid/mail
 *  Plugin options:
 *      - sendgridApiKey : sendgrid api key
 * 
 * @param {FastifyInstance} encapsulatedInstance - Fastify instance
 * @param {Object} options - Plugin options
 */

const azureEmailPlugin: FastifyPluginAsync = async (encapsulatedInstance: any, options: any) => {
    try {
        const client = new EmailClient(config.get("MAIL.AZURE.CONNECTION"));
        const senderEmail = config.get('MAIL.AZURE.FROM') as string;

        encapsulatedInstance.decorate('sendMail', async (options: EmailNotificationOptions) => {
            const emailMessage = {
                senderAddress:  senderEmail,
                content: {
                    subject: options.subject,
                    plainText: options.text,
                    html: options.html,
                },
                recipients: {
                    to: [{ address: options.recipient }],
                },
            };

            const poller = await client.beginSend(emailMessage);
            const result = await poller.pollUntilDone();
            encapsulatedInstance.log.info('Email sent: ', result);
        });

        encapsulatedInstance.decorate('sendWelcomeMail', async (mailOptions: WelcomeUserNotificationOptions) => {
            const template = Handlebars.compile(mailOptions.template);
            const html: string = template(mailOptions.data);
            const text: string = convert(html);
            const options: EmailNotificationOptions = {
                recipient: mailOptions.email,
                subject: mailOptions.subject ?? "Welcome to RelicCare",
                text: text,
                html: html,
            };
            await encapsulatedInstance.sendMail(options);
        });

    } catch (err) {
        console.error("Error setting up Azure email client: ", err);
    }
};

export default azureEmailPlugin;