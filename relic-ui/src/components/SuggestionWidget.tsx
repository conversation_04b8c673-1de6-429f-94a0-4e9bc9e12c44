/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import { Box, Button, Grid } from '@mui/material';
import Form from '@rjsf/mui';
import validator from '@rjsf/validator-ajv8';
import { useChatThreadClient } from '@azure/communication-react';
import { useThreadContext } from './RelicThreadProvider';
import type { Suggestion, WidgetRequest, WidgetResponse } from '../types';
import DefaultMessage from './DefaultMessage';

// const widgetSchema: any = {
//   type: 'object',
//   properties: {
//     requestOptions: {
//       type: 'array',
//       items: {
//         type: 'object',
//         properties: {
//           label: { type: 'string' },
//           altText: {
//             type: 'array',
//             items: { type: 'string' },
//           },
//           value: {
//             oneOf: [
//               { type: 'boolean' },
//               { type: 'number' },
//               { type: 'string' },
//             ],
//           },
//         },
//         required: ['label', 'value'],
//       },
//     },
//   },
// };

const widgetSchema: any = {
  type: 'object',
  properties: {
    requestOptions: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          label: { type: 'string' },
          altText: {
            type: 'array',
            items: { type: 'string' },
          },
          value: {
            oneOf: [{ type: 'boolean' }, { type: 'number' }, { type: 'string' }],
          },
          intl: {
            type: 'object',
            properties: {
              locale: { type: 'string' },
              label: { type: 'string' },
              altText: {
                type: 'array',
                items: { type: 'string' },
              },
            },
            required: ['locale', 'label'],
          },
        },
        required: ['label', 'value'],
      },
    },
  },
};

const widgetUiSchema = {
  requestOptions: {
    'ui:widget': 'requestOptions',
    'ui:style': { marginTop: '-15px' },
  },
};

interface SuggestionsControlProps {
  value: Suggestion[];
  messageProps: any;
  widgetData: WidgetRequest;
  onWidgetClick: (suggestion: Suggestion, widgetData: WidgetRequest) => void;
}

const SuggestionsControl: React.FC<SuggestionsControlProps> = ({ value, messageProps, widgetData, onWidgetClick }) => {
  const { displayInEnglish } = useThreadContext();

  const ChatThreadClient = useChatThreadClient();
  const messageId = messageProps.message.messageId;

  const constructMetadata = (suggestion: Suggestion) => {
    const widgetDataResponse = {
      type: 'WidgetResponse',
      widget: 'SuggestionWidget',
      requestMessageId: messageId,
      action: widgetData.action,
      actionParams: widgetData.actionParams,
      requestedById: widgetData.requestedById,
      requestOptions: widgetData.requestOptions,
      response: suggestion,
    };
    const metadata: any = {
      type: 'widget',
      widgetData: JSON.stringify(widgetDataResponse),
    };
    if (suggestion?.intl?.label) {
      metadata.messageIntl = JSON.stringify({
        content: suggestion.intl.label,
        locale: suggestion.intl.locale,
      });
    }
    console.log('[SuggestionWidget] metadata', metadata);
    return metadata;
  };

  const renderLabel = (suggestion: Suggestion) => {
    if (!displayInEnglish && suggestion.intl?.label) {
      return suggestion.intl.label;
    }
    return suggestion.label;
  };
  return (
    <>
      <Grid display="flex" gap={1} sx={{ mx: 0, my: 0 }} justifyContent="flex-start">
        {value.map((suggestion: Suggestion, index: any) => (
          <Button
            variant="outlined"
            color="inherit"
            id={`suggestion-${index}`}
            size="small"
            onClick={() => {
              ChatThreadClient.sendMessage(
                { content: suggestion.label },
                {
                  metadata: constructMetadata(suggestion),
                  senderDisplayName: '', // messages from users have no display name
                },
              );
              onWidgetClick(suggestion, widgetData); // this is dummy funciton; leaving for future use
            }}
            sx={{ minWidth: '150px', mx: 0.5, textTransform: 'none' }}
          >
            {renderLabel(suggestion)}
          </Button>
        ))}
      </Grid>
    </>
  );
};

type SuggestionWidgetProps = {
  defaultOnRender: (props: any) => JSX.Element;
  messageProps: any;
  widgetData: WidgetRequest | WidgetResponse;
  onWidgetClick: (suggestion: Suggestion, widgetData: WidgetRequest) => void;
};

const isWidgetRequest = (widgetData: WidgetRequest | WidgetResponse): widgetData is WidgetRequest =>
  widgetData.type === 'WidgetRequest';

const SuggestionWidget: React.FC<SuggestionWidgetProps> = ({
  defaultOnRender,
  messageProps,
  widgetData,
  onWidgetClick,
}) => {
  const { topic } = useThreadContext();

  // show the form if the requestMessageId in the topic matches this message
  const showForm = topic?.interactiveWidget?.requestMessageId === messageProps.message.messageId;

  if (isWidgetRequest(widgetData)) {
    // request from agent
    return (
      <>
        <DefaultMessage defaultOnRender={defaultOnRender} messageProps={messageProps} />
        {showForm && (
          <Box>
            <Form
              schema={widgetSchema}
              uiSchema={widgetUiSchema}
              formData={{ requestOptions: widgetData.requestOptions }}
              validator={validator}
              widgets={{
                requestOptions: (props) => (
                  <SuggestionsControl
                    {...props}
                    messageProps={messageProps}
                    widgetData={widgetData as WidgetRequest}
                    onWidgetClick={onWidgetClick}
                  />
                ),
              }}
            >
              <div style={{ display: 'none' }}></div>
            </Form>
          </Box>
        )}
      </>
    );
  } else {
    // response from user
    return <DefaultMessage defaultOnRender={defaultOnRender} messageProps={messageProps} />;
  }
};

export default SuggestionWidget;
