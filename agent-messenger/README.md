# Agent Messenger Service

The Agent Messenger Service is a [NestJS](https://github.com/nestjs/nest)-based server-side application that enables AI Assistants (agents) to communicate with users through Azure Communication Service (ACS) Chat & Voice. When a message is sent to an ACS Chat or call, the server is triggered by an event and sends back responses from the agents.


## Getting Started


### Installation

1. Clone the repository and navigate to the project directory.
2. Install dependencies using pnpm:

```bash
$ pnpm install
```

### Configuration

Create a `.env` file in the project root and provide the following environment variables. All other configurations required for Agent Messenger are done through Node Services config file.


```bash
RELIC_NODE_SERVICE_URL="https://node-services.calmbay-07fbcdc7.eastus.azurecontainerapps.io"
MEDPLUM_CLIENT_ID="1940c3ef-8b6f-4ac0-a66c-d6aca3bff786"
MEDPLUM_CLIENT_SECRET=**************
```


### Running the app



```bash
# Development mode
$ pnpm run start

# Watch mode
$ pnpm run dev

# Production mode
$ pnpm run prod
```

### Running Agent Messenger locally

To test Agent Messenger locally, several steps are required.
1. Download [ngrok](https://ngrok.com/downloads/) on your computer. You will need to use ngrok to expose your local agent messenger instance over internet.

```bash
# map your local agent messenger port to ngrok
$ ngrok http 3001
```

2. Once ngrok is mapped, it will give you a URL like https://**************.ngrok-free.app. This URL is available over internet.
3. Configure this URL on Azure Communication Services events usig Azure CLI or portal interface. CLI commands are given below
```bash
$ az login

# update agent messenger event subscription so it stops receiving chat messages & responding to them
# wait patiently after issuing this command as it takes some time to run
$ az eventgrid event-subscription update \
    --name agent-messenger \
    --source-resource-id /subscriptions/56b648be-c67e-4ae0-a13d-1e471401974f/resourceGroups/ai-counsellor/providers/Microsoft.Communication/CommunicationServices/yusuke-test-3/ \
    --included-event-types Microsoft.Communication.ChatThreadPropertiesUpdated Microsoft.Communication.ChatMessageReceivedInThread \
    --endpoint-type webhook \
    --endpoint https://agent-messenger.calmbay-07fbcdc7.eastus.azurecontainerapps.io/event \
    --subject-begins-with xxxxxxxxxx

# update agent messenger call subscription so it stops receiving incoming calls & responding to them
# wait patiently after issuing this command as it takes some time to run
$ az eventgrid event-subscription update \
    --name agent-messenger-call \
    --source-resource-id /subscriptions/56b648be-c67e-4ae0-a13d-1e471401974f/resourceGroups/ai-counsellor/providers/Microsoft.Communication/CommunicationServices/yusuke-test-3/ \
    --included-event-types Microsoft.Communication.IncomingCall \
    --endpoint-type webhook \
    --endpoint https://agent-messenger.calmbay-07fbcdc7.eastus.azurecontainerapps.io/call/incoming \
    --subject-begins-with xxxxxxxxxx

# create event subscription for receiving chat messages & responding to them
$ az eventgrid event-subscription create \
    --name [Unique Name] \
    --source-resource-id /subscriptions/56b648be-c67e-4ae0-a13d-1e471401974f/resourceGroups/ai-counsellor/providers/Microsoft.Communication/CommunicationServices/yusuke-test-3/ \
    --included-event-types Microsoft.Communication.ChatThreadPropertiesUpdated Microsoft.Communication.ChatMessageReceivedInThread \
    --endpoint-type webhook \
    --endpoint [Your ngrok End point]/event

# create event subscription for receiving incoming calls and picking them up
$ az eventgrid event-subscription create \
    --name [Unique Name] \
    --source-resource-id /subscriptions/56b648be-c67e-4ae0-a13d-1e471401974f/resourceGroups/ai-counsellor/providers/Microsoft.Communication/CommunicationServices/yusuke-test-3/ \
    --included-event-types Microsoft.Communication.IncomingCall \
    --endpoint-type webhook \
    --endpoint [Your ngrok End point]/call/incoming
```
4. Once you are done testing, you should update your newly created subscriptions by using ``--subject-begins-with``. This effectively disables the webhook.

### Testing

```bash
# unit tests
$ pnpm run test

# e2e tests
$ pnpm run test:e2e

# test coverage
$ pnpm run test:cov

# type check of test scripts
$ pnpm run type-check:test
```


### Debugging

1. Open the project in VSCode and launch the "JavaScript Debug Terminal".
2. Set breakpoints in the code and start the development server:

```bash
$ pnpm run start:dev
```

3. Trigger an event by sending a POST request:

```bash
$ curl -X POST http://localhost:3000/event -H "Content-Type: application/json" -d @test/fixtures/eventRequestData.json
```


### Logging

The application uses the nestjs-pino library for logging, which is a NestJS integration for the Pino logger. Log messages are output to the console and can be configured using the LogLevel enum (e.g., log, error, warn, debug, verbose).


### Pre-Push Checklist

Before pushing changes, run the following commands:


```bash
$ pnpm run build
$ pnpm run format   # run prettier
$ pnpm run lint     # run eslint
$ pnpm run test     # unit tests
$ pnpm run test:e2e # e2e tests
```


### Docker Build

To catch potential issues early, build the Docker image locally before pushing changes to the repository. If you delete large files under agent-messenger/ and relic-ui/ (e.g., node_modules/), the process will be significantly faster.


```bash
# Move to the root of the services repository
$ docker build -f app/agent-messenger .
```
