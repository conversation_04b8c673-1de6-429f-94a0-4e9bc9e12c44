//sample run command
//mongosh "mongodb://localhost:27017/relic-care" -f add_field_title.js
try {
    const cursor = db.threads.find({
        $or: [
            { 'threadSubject.title': { $exists: false } }, // Missing
            { 'threadSubject.title': { $exists: true, $eq: '' } } // Exists but is empty
        ]
    });
    let count = 0;
    cursor.forEach(function (doc) {
        let participant = null;

        if (Array.isArray(doc.participants)) {
            participant = doc.participants.find(p => p.resourceType === 'Patient') || // patient participant
                doc.participants.find(p => p.resourceType === 'Practitioner' && p.role === null) || // human practitioner
                doc.participants.find(p => p.resourceType === 'Practitioner' && p.role !== 'System Agent') || // AI agent
                doc.participants.find(p => p.resourceType === 'Practitioner' && p.role === 'System Agent'); // AI assistant
        }

        if (participant !== null) {
            const name = participant.displayName || participant.resourceType;
            const language = doc.threadSubject?.patientLanguage?.display || 'English';
            const title = `${name} - ${language}`;
            db.threads.updateOne(
                { threadId: doc.threadId },
                { $set: { 'threadSubject.title': title } }
            );
            count++;
        }
    });
    printjson({ message: 'Update threads collection complete.', totalUpdatedRecords: count });
} catch (e) {
    printjson({ message: 'Update threads collection failed.', error: e });
}