import { Injectable, OnModuleInit } from '@nestjs/common';
import { Logger } from '@nestjs/common';
import { NodeService } from 'src/node/node.service';
import { RedisService } from 'src/redis/redis.service';
import { ChatClientManager } from 'src/chat/chat-client-manager.service';
import { NewRelicChatParticipant, NewThread, RelicAgent, RelicChatParticipant, RelicPractitioner, Thread, ThreadSubject, Topic } from 'relic-ui';
import { CommunicationIdentifierKind } from '@azure/communication-common';
import { AgentService } from 'src/agent/agent.service';

@Injectable()
export class ThreadService implements OnModuleInit {
  private readonly logger = new Logger(ThreadService.name);
  constructor(
    private chatClientManager: ChatClientManager,
    private readonly nodeService: NodeService,
    private readonly redisService: RedisService,
    private readonly agentService: AgentService,
  ) {
    this.logger.log('ThreadContextService constructor called');
  }

  onModuleInit() {
    this.logger.log('ThreadContextService initialized');
  }

  async isZeroMessageThread(thread: Thread, threadAssistant: RelicChatParticipant): Promise<boolean> {
    const threadClient = await this.chatClientManager.getChatThreadClientForParticipant(
      thread.threadId,
      threadAssistant,
    );
    for await (const message of threadClient.listMessages()) {
      if (message.type === 'text' || message.type === 'html') {
        return false;
      }
    }
    return true;
  }

  async createThread(caller: CommunicationIdentifierKind, callee: CommunicationIdentifierKind): Promise<Thread> {
    const participants: NewRelicChatParticipant[] = [];
    // Get practitioner identity from caller
    let relicPractitioner: RelicPractitioner;
    switch (caller.kind) {
      case 'phoneNumber':
        relicPractitioner = await this.nodeService.getPractitioner(caller.phoneNumber);
        participants.push({
          resourceType: relicPractitioner.resourceType,
          resourceId: relicPractitioner.id,
          mobilePhone: relicPractitioner.mobilePhone,
        });
        break;
      case 'communicationUser':
        relicPractitioner = await this.nodeService.getPractitioner(caller.communicationUserId);
        participants.push({
          resourceType: relicPractitioner.resourceType,
          resourceId: relicPractitioner.id,
          mobilePhone: relicPractitioner.mobilePhone,
        });
        break;
      case 'microsoftTeamsUser':
        relicPractitioner = await this.nodeService.getPractitioner(caller.microsoftTeamsUserId);
        participants.push({
          resourceType: relicPractitioner.resourceType,
          resourceId: relicPractitioner.id,
          mobilePhone: relicPractitioner.mobilePhone,
        });
        break;
    }
    // Get agent identity from callee
    let relicAgent: RelicAgent;
    switch (callee.kind) {
      case 'phoneNumber':
        relicAgent = this.agentService.getAgentList().find((agent) => agent.mobilePhone === callee.phoneNumber);
        participants.push({
          resourceType: relicAgent.resourceType,
          resourceId: relicAgent.id,
          mobilePhone: relicAgent.mobilePhone,
          type: relicAgent.type,
          role: relicAgent.role
        });
        break;
      case 'communicationUser':
        relicAgent = this.agentService.getAgentList().find((agent) => agent.communicationIdentities.some((identity) => identity.userId === callee.communicationUserId));
        participants.push({
          resourceType: relicAgent.resourceType,
          resourceId: relicAgent.id,
          mobilePhone: relicAgent.mobilePhone,
          type: relicAgent.type,
          role: relicAgent.role,
        });
        break;
      case 'microsoftTeamsUser':
        relicAgent = this.agentService.getAgentList().find((agent) => agent.communicationIdentities.some((identity) => identity.userId === callee.microsoftTeamsUserId));
        participants.push({
          resourceType: relicAgent.resourceType,
          resourceId: relicAgent.id,
          mobilePhone: relicAgent.mobilePhone,
          type: relicAgent.type,
          role: relicAgent.role,
        });
        break;
    }

    const threadSubject: ThreadSubject = {
      organizationId: this.agentService.getAiAssistant().organizationId,
      threadOwner: {
        resourceType: this.agentService.getAiAssistant().resourceType,
        id: this.agentService.getAiAssistant().id,
      },
      targetPhoneNumber: relicAgent.mobilePhone,
      title: 'Incoming call from ' + relicPractitioner.name + ' to ' + relicAgent.name,
    }

    const newThread: NewThread = {
      endpoint: relicPractitioner.communicationIdentities.find((identity) => identity.userId).endpoint,
      threadSubject: threadSubject,
      participants: participants,
      inviteVia: 'none',
      status: 'active'
    }
    return await this.nodeService.createThread(newThread);
  }

}
