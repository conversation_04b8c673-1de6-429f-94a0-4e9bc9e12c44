import * as React from 'react';
import { useTranslate } from '@refinedev/core';
import { DeleteButtonProps } from '@refinedev/mui';
import { useRouter } from 'src/routes/hooks';

import Popover from '@mui/material/Popover';
import EditOutlined from '@mui/icons-material/EditOutlined';
import Button, { buttonClasses } from '@mui/material/Button';

import { ReturnType as PopoverProps } from 'src/hooks/use-popover';

import { ConfirmDeleteDialog } from '../../refine-customs/delete-btn';

type DetailsPopoverProps = {
  popover: PopoverProps;
  onOpenEdit: VoidFunction;
  deleteButtonProps?:DeleteButtonProps;
};

export default function OrganizationDetailsPopover({ popover, onOpenEdit, deleteButtonProps }: DetailsPopoverProps) {
  const translate = useTranslate();
  const router = useRouter();

  return (
    <>
      <Popover
        open={!!popover.open}
        anchorEl={popover.open}
        onClose={popover.onClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        slotProps={{
          paper: {
            sx: {
              mt: 0.5,
              minWidth: 160,
              [`& .${buttonClasses.root}`]: {
                px: 2,
                width: 1,
                display: 'flex',
                justifyContent: 'flex-start',
                fontWeight: 'fontWeightMedium',
              },
            },
          },
        }}
      >
        <Button startIcon={<EditOutlined />} onClick={onOpenEdit}>
          Edit
        </Button>

        <ConfirmDeleteDialog
          deleteButtonProps={{
            ...deleteButtonProps,
            confirmTitle: translate('organizations.titles.delete'),
            confirmContent: translate('content.confirm.delete'),
            onSuccess: () => {
              console.log('Organization deleted successfully');
              router.push('/organizations'); // redirect to list after delete
            },
          }}
        />

      </Popover>

    </>
  );
}
