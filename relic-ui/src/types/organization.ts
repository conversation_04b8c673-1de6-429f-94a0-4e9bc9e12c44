import { CommunicationProviderSchema, IdentityProvider, IdentityProviderSchema, RelicCommunicationLanguageSchema } from './relicBase';
import { Filter } from 'mongodb';
import { Type, Static } from '@sinclair/typebox';

export type RelicOrganizationType = {
  definition: 'http://terminology.hl7.org/CodeSystem/organization-type';
  code: string;
  display: string;
};

// Template Schema and Type
export const TemplateSchema = Type.Object({
  patientSummary: Type.String(),
  agentSummary: Type.String(),
  welcomeEmail: Type.String(),
  welcomeSms: Type.String(),
  chatNotificationEmail: Type.String(),
  chatNotificationSms: Type.String(),
});
export type Template = Static<typeof TemplateSchema>;

// DefaultAgent Schema and Type
export const DefaultAgentSchema = Type.Object({
  id: Type.String(),
  type: Type.String(),
});
export type DefaultAgent = Static<typeof DefaultAgentSchema>;

// Endpoint Schema and Type
export const EndpointSchema = Type.Object({
  service: Type.Array(Type.String()),
  endpoint: Type.String(),
  provider: CommunicationProviderSchema,
  defaultAgents: Type.Optional(Type.Array(DefaultAgentSchema)),
});
export type Endpoint = Static<typeof EndpointSchema>;

// RelicClientApplication Schema and Type
export const RelicClientApplicationSchema = Type.Object({
  id: Type.String(),
  resourceType: Type.String(),
  compartment: Type.String(),
});
export type RelicClientApplication = Static<typeof RelicClientApplicationSchema>;

// FhirStore Schema and Type
export const FhirStoreSchema = Type.Object({
  clientApplication: Type.Optional(RelicClientApplicationSchema),
  defaultLanguage: RelicCommunicationLanguageSchema,
  provider: Type.String(),
});
export type FhirStore = Static<typeof FhirStoreSchema>;

// Location Schema and Type
export const LocationSchema = Type.Object({
  id: Type.String(),
  resourceType: Type.String(),
  status: Type.String(),
  name: Type.String(),
  phone: Type.String(),
  fax: Type.String(),
});
export type Location = Static<typeof LocationSchema>;

// Address Schema and Type
export const AddressSchema = Type.Object({
  use: Type.String(),
  type: Type.String(),
  line: Type.Array(Type.String()),
  city: Type.String(),
  district: Type.String(),
  state: Type.String(),
  postalCode: Type.String(),
  country: Type.String(),
});
export type Address = Static<typeof AddressSchema>;

// To be fixed: This should be replaced by RelicOrganizationFilters
export type OrganizationFilters = {
  id?: { $in: string[] };
  active?: boolean;
  name?: string;
};

export type RelicOrganizationFilters = Filter<RelicOrganization> & {
  _start?: number;
  _count?: number;
  _sort?: string;
  _order?: 'asc' | 'desc';
  _search?: string;
  id?: string | string[];
};

export type RelicClientSecret = {
  type: 'ehr';
  provider: IdentityProvider;
  secret: string;
};

// RelicOrganization Schema and Type
export const RelicOrganizationSchema = Type.Object({
  id: Type.Optional(Type.String()),
  resourceType: Type.Optional(Type.String()),
  type: Type.String(), // To be fixed: needs to be an enum
  phone: Type.Optional(Type.String()),
  fax: Type.Optional(Type.String()),
  usNpiId: Type.Optional(Type.String()),
  active: Type.Optional(Type.Boolean()),
  name: Type.String(),
  website: Type.Optional(Type.String()),
  location: Type.Optional(Type.Array(LocationSchema)),
  address: Type.Optional(AddressSchema),
  fhirStore: Type.Optional(FhirStoreSchema),
  endpoints: Type.Optional(Type.Array(EndpointSchema)),
  template: Type.Optional(TemplateSchema),
  supportedLanguages: Type.Optional(Type.Array(RelicCommunicationLanguageSchema)),
  pointClickCare: Type.Optional(Type.Object({
    id: Type.String(),
  })),
  externalIdentifier: Type.Optional(Type.Object({
    id: Type.String(),
    provider: IdentityProviderSchema,
  })),
});
export type RelicOrganization = Static<typeof RelicOrganizationSchema>;
