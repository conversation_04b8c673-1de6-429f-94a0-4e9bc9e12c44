import React from 'react';
import type { RefineLayoutThemedTitleProps } from '@refinedev/mui';
import { useLink, useRouterType, useRouterContext } from '@refinedev/core';

import MuiLink from '@mui/material/Link';
import Typography from '@mui/material/Typography';
import PolicyIcon from '@mui/icons-material/Policy';
import HelpCenterIcon from '@mui/icons-material/HelpCenter';

export const ThemedSiderFooterV2: React.FC<RefineLayoutThemedTitleProps> = ({
  collapsed,
}) => {
  const routerType = useRouterType();
  const Link = useLink();
  const { Link: LegacyLink } = useRouterContext();

  const ActiveLink = routerType === 'legacy' ? LegacyLink : Link;

  return (
    <>
      <MuiLink
        to={`${import.meta.env.VITE_SUPPORT_LINK}`}
        target="_blank"
        component={ActiveLink}
        color="text.secondary"
        sx={{
          display: 'flex',
          alignItems: 'center',
          gap: '6px',
        }}
      >
        <HelpCenterIcon />
        {!collapsed && (
          <Typography
            fontSize="inherit"
            textOverflow="ellipsis"
            overflow="hidden"
          >
            Support
          </Typography>
        )}
      </MuiLink>
      {!collapsed && (
        <MuiLink
          to={`${import.meta.env.VITE_PRIVACY_LINK}`}
          target="_blank"
          component={ActiveLink}
          color="text.secondary"
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: '6px',
          }}
        >
          <PolicyIcon />
          <Typography
            color="text.secondary"
            fontSize="inherit"
            textOverflow="ellipsis"
            overflow="hidden"
          >
            Privacy Policy
          </Typography>
        </MuiLink>
      )}
    </>
  );
};
