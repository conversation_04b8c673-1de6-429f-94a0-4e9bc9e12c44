import { CrudFilters } from '@refinedev/core';

/**
 * Compares two arrays of CrudFilters for equality
 * @param prevFilters - Previous array of filters
 * @param newFilters - New array of filters to compare
 * @returns boolean indicating if the filter arrays are equal
 */
export const areFiltersEqual = (
  prevFilters: CrudFilters,
  newFilters: CrudFilters,
): boolean => {
  if (prevFilters.length !== newFilters.length) return false;

  return prevFilters.every((filter, index) => {
    return (
      filter.operator === newFilters[index].operator &&
      filter.value === newFilters[index].value
    );
  });
};
