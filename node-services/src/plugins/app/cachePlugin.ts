import { FastifyInstance, FastifyPluginAsync, FastifyPluginOptions, FastifyRequest } from "fastify";
import { RelicMessageQueue } from "../../services/relic.queue";
import { requestContext } from "@fastify/request-context";

const cachePlugin: FastifyPluginAsync = async (encapsulatedInstance: FastifyInstance, options: FastifyPluginOptions) => {
    const defaultExpirySeconds = 60 * 5; //5 mins
    const redis = RelicMessageQueue.getRedisConnection();
    encapsulatedInstance.decorate('redis', redis);

    // limit caching to following routes.
    // The dependency is injected in a route dependent manner which is a problem. TBD - To be fixed.
    const routeCacheConfigs = [
        { route: '/api/medplum/organizations', expiryAtMS: 60 * 60 },
        { route: '/api/pcc/organizations', expiryAtMS: 60 * 60 },
        { route: '/api/medplum/patients', expiryAtMS: defaultExpirySeconds, excludedSubRoutes: ['/api/medplum/patients/:id/chat'] },
        { route: '/api/pcc/patients', expiryAtMS: defaultExpirySeconds, excludedSubRoutes: ['/api/pcc/patients/:id/chat'] },
    ];

    function getRouteCacheConfiguration(req: FastifyRequest) {
        if (req.method !== 'GET') {
            return null;
        }

        const route = req.routeOptions?.url ?? '';
        if (!route) {
            return null;
        }

        return routeCacheConfigs.find(config => {
            const isExcluded = config.excludedSubRoutes?.some(subRoute => route.includes(subRoute)) ?? false;
            return route.includes(config.route) && !isExcluded;
        }) ?? null;
    }

    // Waiting on solution for https://reliccare.atlassian.net/browse/CB1-628
    // Commented to make testing easier.
    // encapsulatedInstance.addHook('preHandler', async function (req: any, rep: any) {
    //     const cfg = getRouteCacheConfiguration(req);
    //     if (!cfg) {
    //         return;
    //     }
    //     req.key = generateRequestKey(req);

    //     const cachedValue = await req.server.getFromCache(req.key);
    //     if (cachedValue) {
    //         req.isCached = true;
    //         rep.header('Content-Type', 'application/json');
    //         rep.send(cachedValue);
    //         encapsulatedInstance.log.info(`sending cached payload with key="${req.key}"`);
    //     }
    // });

    encapsulatedInstance.addHook('onSend', async function (req: any, rep: any, payload: any) {
        const cfg = getRouteCacheConfiguration(req);
        if (cfg && rep.statusCode >= 200 && rep.statusCode < 300 && payload != null && !req.isCached) {
            const key = req.key ?? generateRequestKey(req);
            req.server.addToCache(key, payload, cfg.expiryAtMS);
        }
        return payload;
    });

    function generateRequestKey(req: any) {
        const { headers, query, hostname, ip, url } = req;
        let organization = requestContext.get('organizationId');
        if (!organization) {
            encapsulatedInstance.log.warn('Organization Id not found in request context. Cannot generate cache key.');
            organization = '';
        }
        const path = url?.split('?')?.at(0) ?? req.routeOptions.url;
        let key = `nodeservices:type=request:url=${path}:origin=${hostname}:ip=${ip}:org=${organization}`;
        const decodedToken: any = requestContext.get('decodedJwtToken');
        if (decodedToken) {
            const sub = decodedToken.payload?.sub;
            const aud = decodedToken.payload?.aud ?? decodedToken.payload?.login_id; //aud for pcc, loginId for medplum
            key = key.concat(':', `sub=${sub}`, ':', `aud=${aud}`);
        } else {
            const accessToken = headers['x-access-token'] ?? '';
            const idToken = headers['x-id-token'] ?? '';
            key = key.concat(':', `accesstoken=${accessToken}`, ':', `idtoken=${idToken}`);
        }
        if (Object.keys(query).length > 0) {
            const queries = [];
            for (let entry of Object.entries(query)) {
                queries.push(`${entry[0]}=${entry[1] ?? ''}`)
            }
            key = key.concat(':', queries.join(':'));
        }

        return key;
    }

    function generateUserIdentityKey(req: any) {
        const { headers } = req;
        const accessToken = headers['x-access-token'] ?? '';
        const idToken = headers['x-id-token'] ?? '';
        const key = `nodeservices:type=user:accesstoken=${accessToken}:idtoken=${idToken}`;
        return key;
    }

    encapsulatedInstance.decorate('generateUserIdentityKey', generateUserIdentityKey);

    encapsulatedInstance.decorate('getFromCache', async function (key: string) {
        try {
            const value = await (encapsulatedInstance as any).redis.get(key);
            return JSON.parse(value);
        } catch (err) {
            encapsulatedInstance.log.error(err, `error retrieving value from cache with key="${key}"`);
            return null;
        }
    });

    encapsulatedInstance.decorate('addToCache', async function (key: string, value: any, expireAt?: number) {
        try {
            if (value == null) {
                return;
            }
            (encapsulatedInstance as any).redis.set(key, JSON.stringify(value), 'EX', expireAt ?? defaultExpirySeconds);
        } catch (err) {
            encapsulatedInstance.log.error(err, `error saving value to cache for key=${key}`);
        }
    });

    encapsulatedInstance.decorate('deleteFromCache', async function (key: string) {
        try {
            await (encapsulatedInstance as any).redis.del(key);
        } catch (err) {
            encapsulatedInstance.log.error(err, `error deleting value from cache with key="${key}"`);
        }
    });
}

export default cachePlugin;