# syntax=docker/dockerfile:1
FROM node:22-slim AS base
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
RUN corepack enable
RUN corepack prepare pnpm@10.0.0 --activate

WORKDIR /services
COPY ./relic-ui ./relic-ui
COPY ./node-services ./node-services
COPY ./agent-messenger ./agent-messenger
COPY ./json-ui ./json-ui
COPY ./.npmrc ./.npmrc
COPY ./package.json ./package.json
COPY ./pnpm-lock.yaml ./pnpm-lock.yaml
COPY ./pnpm-workspace.yaml ./pnpm-workspace.yaml
COPY ./turbo.json ./turbo.json

RUN --mount=type=cache,id=pnpm,target=/pnpm/store pnpm --filter agent-messenger... --filter relic-ui... install --frozen-lockfile
RUN pnpm --filter relic-ui... run build
RUN pnpm --filter agent-messenger... run build


WORKDIR /services/agent-messenger
EXPOSE 3001
CMD [ "node", "-r", "newrelic", "dist/main.js" ]