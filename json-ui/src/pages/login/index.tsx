import {
  useInvalidate,
  useLink,
  useRouterContext,
  useRouterType,
} from '@refinedev/core';
import React, { useEffect } from 'react';

import Box from '@mui/material/Box';
import { buttonClasses } from '@mui/material/Button';
import MuiLink from '@mui/material/Link';
import Typography from '@mui/material/Typography';

import WebsiteHeader from 'src/components/header/websiteHeader';
import SignInTab from 'src/components/login/sign-in-tab';

import { HealthCheckWrapper } from 'src/components/login/health-check-wrapper';
import { AuthPage } from './swizzled';

export const Login: React.FC = () => {
  const routerType = useRouterType();
  const Link = useLink();
  const { Link: LegacyLink } = useRouterContext();
  const ActiveLink = routerType === 'legacy' ? LegacyLink : Link;
  const invalidate = useInvalidate();

  useEffect(() => {
    localStorage.removeItem('accessPolicy');
  }, []);

  useEffect(() => {
    //A better approach will be to obtain all resources using a refine hook and invalidate all of the lists.
    invalidate({
      resource: 'documents',
      invalidates: ['list', 'many'],
    });
    invalidate({
      resource: 'patients',
      invalidates: ['list', 'many'],
    });
    invalidate({
      resource: 'organizations',
      invalidates: ['list', 'many'],
    });
  }, [invalidate]);

  return (
    <>
      <HealthCheckWrapper>
        {({ isLoading }) => (
          <>
            <WebsiteHeader />

            <AuthPage
              type="login"
              registerLink=""
              forgotPasswordLink={
                <MuiLink
                  to={`${import.meta.env.VITE_SUPPORT_LINK}`}
                  target="_blank"
                  component={ActiveLink}
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '6px',
                  }}
                >
                  <Typography
                    fontSize="14px"
                    textOverflow="ellipsis"
                    overflow="hidden"
                  >
                    Need help?
                  </Typography>
                </MuiLink>
              }
              title=""
              wrapperProps={{
                sx: {
                  position: 'relative',
                  '&::before': {
                    width: 1,
                    height: 1,
                    zIndex: -1,
                    content: "''",
                    opacity: 0.24,
                    position: 'absolute',
                    backgroundSize: 'cover',
                    backgroundRepeat: 'no-repeat',
                    backgroundPosition: 'center center',
                    backgroundImage: 'url(/images/overlay_4.jpg)',
                  },
                  '& .MuiContainer-root': {
                    justifyContent: 'flex-start',
                  },
                },
              }}
              contentProps={{
                title: 'Login',
                sx: {
                  borderRadius: 2,
                  boxShadow: theme => theme.customShadows.z1,
                  [`& .${buttonClasses.root}`]: {
                    mt: 0,
                    fontSize: 15,
                    height: 'auto',
                    padding: '14px 21px',
                  },
                  '& .MuiTextField-root': {
                    marginTop: 1,
                  },
                  '& .MuiBox-root': {
                    marginTop: 2,
                    alignContent: 'top',
                  },
                  '& .MuiFormControlLabel-root': {
                    marginLeft: -1,
                  },
                  '@media (max-width: 600px)': {
                    [`& .${buttonClasses.root}`]: {
                      height: 48,
                    },
                  },
                },
              }}
              renderContent={(
                content: React.ReactNode,
                title: React.ReactNode,
              ) => (
                <Box>
                  <SignInTab
                    title={title}
                    content={content}
                    isLoading={isLoading}
                  />
                </Box>
              )}
            />
          </>
        )}
      </HealthCheckWrapper>
    </>
  );
};
