// import fp from 'fastify-plugin';
import { v4 as uuidv4 } from 'uuid';
import { RelicPractitioner, RelicAgent } from 'relic-ui';
import { FastifyInstance, FastifyPluginAsync, FastifyPluginOptions } from 'fastify';
import { relicRequire } from '../../utils/common-js';
import agentPlugin from './agentPlugin';
import { CommunicationIdentity } from 'relic-ui';

const { AdminRole, MemberRole } = relicRequire('relic-ui/schema');

declare module 'fastify' {
  interface FastifyInstance {
    // Used by agentPlugin to convert RelicAgent to RelicPractitioner
    toRelicPractitioner(practitioner: RelicAgent): RelicPractitioner;
    // Used only by routes. We cannot use searchRelicAgents here due to encapsulation. To be fixed.
    getPractitioners(filters: any): Promise<{ practitioners: RelicPractitioner[]; totalCount: number }>;
    // Get operation - used by routes and auth hooks.
    getPractitioner(practitionerId: string): Promise<RelicPractitioner | null>;
    // Used by routes and createAgent
    createPractitioner(relicPractitioner: RelicPractitioner): Promise<RelicPractitioner>;
    // Used only by routes
    updatePractitioner(relicPractitionerUpdates: RelicPractitioner): Promise<RelicPractitioner>;
    // Used by updateAgent
    saveRelicPractitioner(resource: RelicPractitioner): Promise<RelicPractitioner>;
    getPractitionerCommunicationIdentity(id: string): Promise<CommunicationIdentity>;
  }
}

// Cannot use `FastifyInstance` in the plugin signature because higher level plugins are not typed as `FastifyInstance`.
const practitionerPlugin: FastifyPluginAsync = async (fastify: FastifyInstance, options: FastifyPluginOptions) => {
  fastify.log.info('Practitioner Plugin loading...');

  if (!fastify.mongo.reliccare?.db) {
    throw new Error('reliccare database is not available');
  }

  const relicPractitionerCollection = fastify.mongo.reliccare.db.collection<RelicPractitioner>('practitioners');
  const relicAgentCollection = fastify.mongo.reliccare.db.collection<RelicPractitioner>('agents');

  fastify.decorate('toRelicPractitioner', function (practitioner: RelicAgent): RelicPractitioner {
    const relicPractitioner: RelicPractitioner = {
      id: practitioner.id,
      resourceType: 'Practitioner',
      enabled: practitioner.active,
      organizationId: practitioner.organizationId,
      name: practitioner.name,
      mobilePhone: practitioner.mobilePhone,
      email: practitioner.email,
      communicationIdentities: practitioner.communicationIdentities,
      role: { name: 'admin' },
      provider: practitioner.provider,
      position: practitioner.position,
    };
    return relicPractitioner;
  });

  fastify.decorate('getPractitioners', async function (filters: any): Promise<{
    practitioners: RelicPractitioner[];
    totalCount: number;
  }> {
    let { filter, _sort, _order, _start, _end, _search } = filters;
    const practitioners = relicPractitionerCollection;
    const agents = relicAgentCollection;
    //TODO: due to encapsulation issues, DB query is updated to avoid using searchRelicAgents.
    //TODO: To be fixed whether we want to stick with searchRelicAgents + encapsulation
    // const agents: RelicAgent[] = await fastify.searchRelicAgents();

    // Get all practitioner IDs that exist in agents collection
    // const agentIds = agents.map(agent => agent.id);
    // if (filter.id) {
    //     filter.id = { ...(filter.id), $nin: agentIds }
    // } else {
    //     filter.id = { $nin: agentIds }
    // }

    // Add search filter if _search is provided
    if (_search) {
      filter['$or'] = [
        { name: { $regex: _search, $options: 'i' } },
        { email: { $regex: _search, $options: 'i' } },
        { mobilePhone: { $regex: _search, $options: 'i' } },
      ];
    }

    const totalCount = await practitioners.countDocuments(filter, {});

    const sortOptions = _sort ? { [_sort]: _order === 'desc' ? -1 : 1 } : { name: _order === 'desc' ? -1 : 1 };

    // Add pagination if _start and _end are provided
    const skip = _start !== undefined ? parseInt(_start) : 0;
    const limit = _end !== undefined ? parseInt(_end) - skip : 10; // Default limit to 10 if _end is not provided

    const practitionersList = await practitioners
      .aggregate<RelicPractitioner>(
        [
          {
            $lookup: {
              from: agents.collectionName,
              localField: 'id',
              foreignField: 'id',
              as: 'agentData',
            },
          },
          {
            $match: {
              ...filter,
              agentData: { $eq: [] },
            },
          },
          {
            $project: {
              agentData: 0,
              _id: 0,
            },
          },
          {
            $sort: sortOptions,
          },
          {
            $skip: skip,
          },
          {
            $limit: limit,
          },
        ],
        {
          collation: {
            locale: 'en',
            strength: 2,
          },
        },
      )
      .toArray();

    return { practitioners: practitionersList, totalCount };
  });

  fastify.decorate('getPractitioner', async function (practitionerId: string): Promise<RelicPractitioner | null> {
    if (fastify.isPossiblePhoneNumber(practitionerId)) {
      practitionerId = fastify.formatPhoneNumber(practitionerId);
    }
    const practitioner = await relicPractitionerCollection.findOne({
      $or: [
        { id: practitionerId },
        { 'communicationIdentities.userId': practitionerId },
        { mobilePhone: practitionerId },
        { email: practitionerId },
      ],
    });

    return practitioner;
  });

  fastify.decorate(
    'createPractitioner',
    async function (relicPractitioner: RelicPractitioner): Promise<RelicPractitioner> {
      // Generate missing fields for relicPractitioner
      relicPractitioner.id = relicPractitioner.id || uuidv4();
      relicPractitioner.resourceType = relicPractitioner.resourceType || 'Practitioner';
      relicPractitioner.role =
        relicPractitioner.email &&
        ['reliccare.com', 'minimals.cc'].includes(relicPractitioner.email.split('@').at(-1) || '')
          ? AdminRole
          : MemberRole;
      if (relicPractitioner.mobilePhone) {
        relicPractitioner.mobilePhone = fastify.formatPhoneNumber(relicPractitioner.mobilePhone);
      }

      // Check if an email or mobilePhone is assigned to an existing practitioner within the organization
      const { email, mobilePhone, organizationId } = relicPractitioner;
      const conflictConditions = [];
      if (email) {
        conflictConditions.push({ email });
      }
      if (mobilePhone) {
        conflictConditions.push({ mobilePhone });
      }

      if (conflictConditions.length > 0) {
        const existingPractitioner = await relicPractitionerCollection.findOne({
          organizationId,
          $or: conflictConditions,
        });

        if (existingPractitioner) {
          throw fastify.httpErrors.conflict(
            'An account with this email or mobile phone already exists. Contact support for assistance.',
          );
        }
      }

      // Generate Communication Identities for the practitioner based on the organization
      const identity = await fastify.acs.createIdentity(relicPractitioner);
      relicPractitioner.communicationIdentities = [identity];

      // Save the Relic Practitioner
      return await fastify.saveRelicPractitioner(relicPractitioner);
    },
  );

  fastify.decorate(
    'updatePractitioner',
    async function (relicPractitionerUpdates: RelicPractitioner): Promise<RelicPractitioner> {
      if (!relicPractitionerUpdates.id) {
        throw fastify.httpErrors.badRequest('Practitioner id is required.');
      }

      // Find relicPractitioner
      let relicPractitioner = await relicPractitionerCollection.findOne({ id: relicPractitionerUpdates.id });
      if (!relicPractitioner) {
        throw fastify.httpErrors.notFound('Practitioner not found.');
      }
      relicPractitioner = { ...relicPractitioner, ...relicPractitionerUpdates };
      if (relicPractitioner.communicationIdentities?.[0]) {
        relicPractitioner.communicationIdentities[0].displayName = relicPractitioner.name; //Update the name
      }
      const updatedPractitioner: RelicPractitioner = await fastify.saveRelicPractitioner(relicPractitioner);
      return updatedPractitioner;
    },
  );

  fastify.decorate('saveRelicPractitioner', async function (resource: RelicPractitioner): Promise<RelicPractitioner> {
    return (await relicPractitionerCollection.findOneAndUpdate(
      { id: resource.id },
      { $set: resource },
      { upsert: true, returnDocument: 'after', projection: { _id: 0 } },
    )) as RelicPractitioner;
  });

  fastify.decorate('getPractitionerCommunicationIdentity', async function (id: string): Promise<CommunicationIdentity> {
    const relicPractitioner = (await relicPractitionerCollection.findOne({
      $or: [{ id: id }, { 'communicationIdentities.userId': id }, { mobilePhone: id }, { email: id }],
    })) as RelicPractitioner;
    if (relicPractitioner && Array.isArray(relicPractitioner.communicationIdentities) && relicPractitioner?.communicationIdentities.length > 0) {
      const communicationIdentity = relicPractitioner.communicationIdentities[0];
      if (communicationIdentity) {
        const identity = await fastify.acs.refreshIdentity(relicPractitioner, communicationIdentity);
        identity.displayName = relicPractitioner.name ? relicPractitioner.name : 'Practitioner';
        return identity;
      }
    }
    throw fastify.httpErrors.internalServerError('Practitioner communication identity not found');
  });

  fastify.register(agentPlugin, options);
};

// export default fp(practitionerPlugin);
export default practitionerPlugin;
