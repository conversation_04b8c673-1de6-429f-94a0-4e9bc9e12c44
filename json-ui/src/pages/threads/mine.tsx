import React from 'react';
import { useDataGrid } from '@refinedev/mui';
import { IUser, IUserIdentity, RelicChatParticipant } from 'relic-ui';
import { CrudFilter, useTranslate, useGetIdentity } from '@refinedev/core';

import { Box, Card } from '@mui/material';
import { GridColDef } from '@mui/x-data-grid-pro';

import ListThreads from 'src/components/_threads/list/threads-list';

export const MyConversations = () => {
  const { data: currentUser } = useGetIdentity<IUser>();
  const currentIdentity: IUserIdentity =
    currentUser?.userIdentity as IUserIdentity;

  const translate = useTranslate();

  const currentRole: string = currentIdentity?.role?.name
    ? currentIdentity?.role?.name
    : 'member';

  const initialFilters: CrudFilter[] = React.useMemo(() => {
    const me: RelicChatParticipant = {
      id: {
        communicationUserId:
          currentIdentity?.communicationIdentities[0]?.userId,
      },
      resourceId: currentIdentity?.id,
      resourceType:
        currentIdentity?.resourceType === 'Patient'
          ? 'Patient'
          : 'Practitioner',
    };
    const myThreadsFilter: CrudFilter = {
      field: 'participant',
      value: encodeURIComponent(JSON.stringify(me)),
      operator: 'eq',
    };
    return [myThreadsFilter];
  }, [currentIdentity]);

  const {
    dataGridProps: listThreadsProps,
    filters,
    setFilters,
  } = useDataGrid({
    resource: 'communication/chat/threads',
    sorters: {
      initial: [
        {
          field: 'updateDate',
          order: 'desc',
        },
      ],
    },
    filters: {
      mode: 'server',
      permanent: initialFilters,
    },
  });

  const threadsColumns = React.useMemo<GridColDef[]>(() => [], []);

  return (
    <Card>
      <Box sx={{ height: 'calc(100vh - 96px)', width: '100%' }}>
        <ListThreads
          {...listThreadsProps}
          setFilters={setFilters}
          filters={filters}
          title={translate('threads.titles.mine')}
          columns={threadsColumns}
        />
      </Box>
    </Card>
  );
};
