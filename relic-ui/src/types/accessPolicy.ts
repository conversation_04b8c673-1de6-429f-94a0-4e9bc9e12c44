export type AccessPolicy = {
  resourceType: 'AccessPolicy';
  name: 'Patient' | 'RelatedPerson' | 'StaffMember' | 'StaffAdmin' | 'AIStaff';
  resource: Resource[];
};

export type Resource = {
  resource: string;
  canAccess?: CanAccess[] | 'all';
  constraint?: string;
  alias?: <PERSON><PERSON>[];
  canRead?: boolean;
  canUpdate?: boolean;
  canDelete?: boolean;
  canCreate?: boolean;
  canList?: boolean;
};

export type Alias = {
  target: string;
  resource: string;
};

export type CanAccess = {
  name: string;
  allowed: boolean;
};

export type ResourceAccessConfigParams = {
  resource: string;
  action: 'read' | 'update' | 'delete' | 'list' | 'create';
};
