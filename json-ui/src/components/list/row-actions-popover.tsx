import * as React from 'react';
import { useTranslate } from '@refinedev/core';
import { ShowButton, EditButton, ShowButtonProps, EditButtonProps, DeleteButtonProps } from '@refinedev/mui';

import Popover from '@mui/material/Popover';
import IconButton from '@mui/material/IconButton';
import CheckIcon from '@mui/icons-material/Check';
import RefreshIcon from '@mui/icons-material/Refresh';
import TranslateIcon from '@mui/icons-material/Translate'; // Import the retry icon
import Button, { buttonClasses } from '@mui/material/Button';

import { usePathname } from 'src/routes/hooks';

import { usePopover } from 'src/hooks/use-popover';

import Iconify from 'src/components/iconify';
import { ConfirmDeleteDialog } from 'src/components/refine-customs/delete-btn';

import MarkInactiveDialog from '../_patient/list/mark-inactive-dialog';

// ----------------------------------------------------------------------

type Props = {
  rowId: string;
  translateButtonProps?: {
    onConfirm: VoidFunction;
  };
  markInactiveButtonProps?: {
    onConfirm: VoidFunction;
  };
  deleteButtonProps?: DeleteButtonProps & {
    confirmContent?: string;
  };
  showButtonProps?: ShowButtonProps;
  editButtonProps?: EditButtonProps;
  retryButtonProps?: {
    onConfirm: VoidFunction;
  };
};

export default function RowActionsPopover({
  rowId,
  translateButtonProps,
  showButtonProps,
  editButtonProps,
  markInactiveButtonProps,
  deleteButtonProps,
  retryButtonProps,
}: Props) {
  const translate = useTranslate();

  const popover = usePopover();

  const pathname = usePathname();

  const [openMarkInactive, setOpenMarkInactive] = React.useState(false);

  React.useEffect(() => {
    if (popover.open) {
      popover.onClose();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pathname]);

  const handleOpenMarkInactive = () => {
    setOpenMarkInactive(true);
  };

  const handleCloseMarkInactive = () => {
    setOpenMarkInactive(false);
  };

  return (
    <>
      <IconButton
        onClick={popover.onOpen}
        sx={{
          ...(popover.open && {
            bgcolor: 'action.hover',
          }),
        }}
      >
        <Iconify icon="eva:more-horizontal-fill" />
      </IconButton>

      <Popover
        open={Boolean(popover.open)}
        anchorEl={popover.open}
        onClose={popover.onClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        slotProps={{
          paper: {
            sx: {
              mt: 0.5,
              minWidth: 160,
              display: 'flex',
              flexDirection: 'column',
              a: {
                color: 'inherit',
              },
              [`& .${buttonClasses.root}`]: {
                px: 2,
                width: 1,
                justifyContent: 'flex-start',
                fontWeight: 'fontWeightMedium',
              },
            },
          },
        }}
      >
        {showButtonProps && <ShowButton recordItemId={rowId} size="small" {...showButtonProps} />}

        {editButtonProps && <EditButton recordItemId={rowId} size="small" {...editButtonProps} />}

        {translateButtonProps && (
          <Button
            startIcon={<TranslateIcon />}
            onClick={() => {
              translateButtonProps.onConfirm();
              popover.onClose();
            }}
          >
            {translate('buttons.translate')}
          </Button>
        )}

        {retryButtonProps && (
          <Button
            startIcon={<RefreshIcon />}
            onClick={() => {
              retryButtonProps.onConfirm();
              popover.onClose();
            }}
          >
            {translate('buttons.retry')}
          </Button>
        )}

        {markInactiveButtonProps && (
          <Button
            startIcon={<CheckIcon />}
            onClick={() => {
              handleOpenMarkInactive();
              popover.onClose();
            }}
          >
            {translate('buttons.markInactive')}
          </Button>
        )}

        {deleteButtonProps && (
          <ConfirmDeleteDialog
            rowId={rowId}
            deleteButtonProps={{
              ...deleteButtonProps,
            }}
          />
        )}
      </Popover>

      <MarkInactiveDialog
        open={openMarkInactive}
        onClose={handleCloseMarkInactive}
        onConfirm={markInactiveButtonProps?.onConfirm}
      />

    </>
  );
}
