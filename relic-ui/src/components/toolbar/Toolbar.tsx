import { useState, useEffect } from 'react';
import { ControlBarButton, EndCallButton } from '@azure/communication-react';
import { useThreadContext } from '../RelicThreadProvider';
import { Box } from '@mui/material';
import { ThreadState, ToolbarProps } from '../../types';
import availableLanguages from '../../data/languages.json';
import MessageIcon from '@mui/icons-material/Message';
import CommentIcon from '@mui/icons-material/Comment';
import SettingsIcon from '@mui/icons-material/Settings';
import PlaylistAddIcon from '@mui/icons-material/PlaylistAdd';
import DeleteForeverIcon from '@mui/icons-material/DeleteForever';
import MicIcon from '@mui/icons-material/Mic';
import CallEndIcon from '@mui/icons-material/CallEnd';
import { useUserContext } from '../RelicUserProvider';
import CallDialog from './CallDialog';
import NewThreadDialog from './NewThreadDialog';

const Toolbar = ({ hideNewButton = false, hideCallButtons, hideTranslation = false, showResponseManagement }: ToolbarProps) => {
  const {
    startPhoneCall,
    hangUp,
    threadState,
    updateThreadState,
    displayInEnglish,
    updateDisplayInEnglish,
    updateThreadError,
    startVoiceCall,
    thread,
  } = useThreadContext();
  const { myIdentity } = useUserContext();
  const [isDialogOpen, setDialogOpen] = useState<boolean>(false);
  const [isNewThreadDialogOpen, setNewThreadDialogOpen] = useState<boolean>(false);
  const [phoneNumberError, setPhoneNumberError] = useState<string | null>(null);
  const [languageError, setLanguageError] = useState<string | null>(null);
  const [showTranslation, setShowTranslation] = useState<boolean>(!hideTranslation);

  useEffect(() => {
    if (
      !hideTranslation &&
      thread?.threadSubject &&
      thread?.threadSubject?.patientLanguage &&
      thread?.threadSubject?.patientLanguage.code &&
      (thread?.threadSubject?.patientLanguage.code.toLowerCase() === 'en' ||
        thread?.threadSubject?.patientLanguage.code.toLowerCase() === 'en-us')
    ) {
      setShowTranslation(false);
    }
  }, []);

  useEffect(() => {
    if (thread?.threadSubject?.patientLanguage) {
      const isSupportedLanguage = availableLanguages.some(
        (language) => language.code === thread?.threadSubject?.patientLanguage?.code,
      );

      if (isSupportedLanguage) {
        setLanguageError(null);
      } else {
        setLanguageError(
          `${thread?.threadSubject?.patientLanguage?.display} is not currently supported by Relic AI. Please choose another language.`,
        );
      }
    }
  }, [thread?.threadSubject?.patientLanguage]);

  const doHangUp = async () => {
    await hangUp();
    handleDialogClose();
  };

  const doPhoneCall = async () => {
    if (!thread?.threadSubject?.targetPhoneNumber || thread?.threadSubject?.targetPhoneNumber.length !== 12) {
      setPhoneNumberError('Please enter a valid 10-digit phone number.');
      return;
    }
    if (!thread?.threadSubject?.patientLanguage) {
      setLanguageError('Please select a language.');
      return;
    }
    try {
      if (threadState !== 'connected') {
        startPhoneCall();
      }
      handleDialogClose();
    } catch (error) {
      console.error('Error starting phone call:', error);
      updateThreadState(ThreadState.Idle);
      updateThreadError('An error occurred while starting the call.');
    }
  };

  const doVoiceCall = async () => {
    if (!thread?.threadSubject?.patientLanguage) {
      setLanguageError('Please select a language.');
      return;
    }
    try {
      if (threadState !== ThreadState.Connected) {
        startVoiceCall();
      }
      handleDialogClose();
    } catch (error) {
      console.error('Error starting voice call:', error);
      updateThreadState(ThreadState.Idle);
      updateThreadError('An error occurred.');
    }
  };

  const toggleDisplayInEnglish = () => {
    updateDisplayInEnglish(!displayInEnglish);
  };

  const handleDialogOpen = () => {
    setDialogOpen(true);
  };

  const handleDialogClose = () => {
    setDialogOpen(false);
  };

  const handleNewThreadDialogOpen = () => {
    setNewThreadDialogOpen(true);
  };
  const handleNewThreadDialogClose = () => {
    setNewThreadDialogOpen(false);
  };

  return (
    <Box sx={{ position: 'absolute', alignSelf: 'flex-end', padding: '10px 0', zIndex: '999' }}>
      <Box
        sx={{
          display: 'flex',
          backgroundColor: '#FFFFFF',
          borderRadius: '8px',
          boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)',
        }}
      >
        {!hideNewButton && (
          <ControlBarButton
          onClick={handleNewThreadDialogOpen}
          onRenderIcon={() => <PlaylistAddIcon />}
          strings={{ label: 'New' }}
          aria-label="New Chat"
          showLabel={true}
          />
        )}
        {!hideCallButtons && (
          <>
            {threadState === 'connected' ? (
              <EndCallButton
                onClick={doHangUp}
                onRenderIcon={() => <CallEndIcon />}
                strings={{ label: 'Hang up' }}
                showLabel={true}
              />
            ) : (
              <ControlBarButton
                onClick={handleDialogOpen}
                onRenderIcon={() => <MicIcon />}
                strings={{ label: 'Voice' }}
                showLabel={true}
                disabled={threadState === ThreadState.Connecting || threadState === ThreadState.ReadOnly}
              />
            )}
          </>
        )}
        {showTranslation && (
          <ControlBarButton
            onRenderIcon={() => (displayInEnglish ? <MessageIcon /> : <CommentIcon />)}
            strings={{ label: displayInEnglish ? 'English' : thread?.threadSubject?.patientLanguage?.display }}
            aria-label="Translate"
            showLabel={true}
            onClick={toggleDisplayInEnglish}
          />
        )}
        {/* {showResponseManagement && (
          <ControlBarButton
            onRenderIcon={() => <SettingsIcon />}
            strings={{ label: 'Manage' }}
            aria-label="Manage Responses"
            showLabel={true}
            disabled={threadState === ThreadState.ReadOnly}
          />
        )} */}
      </Box>

      {myIdentity && (
        <CallDialog
          isDialogOpen={isDialogOpen}
          doVoiceCall={doVoiceCall}
          doPhoneCall={doPhoneCall}
          handleDialogClose={handleDialogClose}
          languageError={languageError}
          phoneNumberError={phoneNumberError}
          setLanguageError={setLanguageError}
          setPhoneNumberError={setPhoneNumberError}
        />
      )}
      {myIdentity && isNewThreadDialogOpen && (
        <NewThreadDialog
          isNewThreadDialogOpen={isNewThreadDialogOpen}
          handleNewThreadDialogClose={handleNewThreadDialogClose}
        />
      )}
    </Box>
  );
};

export default Toolbar;
