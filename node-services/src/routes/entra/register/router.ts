import { FastifyInstance, FastifyPluginOptions, FastifyReply, FastifyRequest } from 'fastify';
import { RelicPractitioner } from 'relic-ui';

import { relicRequire } from '../../../utils/common-js';
const { RelicPractitionerSchema } = relicRequire('relic-ui/schema');

export default async function (fastify: FastifyInstance, opts: FastifyPluginOptions) {
  fastify.post(
    '/',
    {
      schema: {
        ...opts.schema,
        body: RelicPractitionerSchema,
        summary: `Create a new Practitioner`,
        tags: ['Practitioner Service'],
        description: `Creates a new Practitioner with Azure Entra identity`,
      },
    },
    async function (req: FastifyRequest, rep: FastifyReply): Promise<RelicPractitioner> {
      const relicPractitioner: RelicPractitioner = await fastify.createPractitioner(req.body as RelicPractitioner);
      if (fastify.identityService.createUser) {
        await fastify.identityService.createUser(relicPractitioner);
      }
      return relicPractitioner;
    },
  );
}
