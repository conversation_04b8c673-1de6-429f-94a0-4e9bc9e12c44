import tap from "tap";
import { fastify } from "../index";
import { MedplumClient, MedplumClientOptions, EmailPasswordLoginRequest, ProfileResource } from '@medplum/core';
import { JSDOM } from 'jsdom';
import { randomUUID } from "crypto";

// Create a new JSDOM instance
const { window } = new JSDOM();
// Mock global objects
global.window = window as unknown as Window & typeof globalThis;
global.document = window.document;
global.Element = window.Element;

const sessionStorageMock: Storage = {
  store: {} as Record<string, string>,
  getItem: function (key: string) {
    return this.store[key] || null;
  },
  setItem: function (key: string, value: string) {
    this.store[key] = value.toString();
  },
  removeItem: function (key: string) {
    delete this.store[key];
  },
  clear: function () {
    this.store = {};
  },
  get length() {
    return Object.keys(this.store).length;
  },
  key: function (index: number) {
    return Object.keys(this.store)[index] || null;
  },
};

global.sessionStorage = sessionStorageMock;

// Test Data for Patient Testing.
const practitionerEmail: string = "<EMAIL>";
const practitionerPwd: string = "Z1sZ9N3F8tcycDDK";

const patientUnitTestName: string = "test name";
const patientUnitTestEmail: string = "<EMAIL>";
const patientUnitTestGender: string = "Male";
const patientUnitTestDOB: string = "2000-06-02";
const patientUnitTestMaritalStatus: string = "Married";
const patientPhone: string = "(+1)-************";
const patientHomePhone: string = "(+1)-************";
const patientUpdatedPhone: string = "**********";
const patientRelatedPersonName: string = "Mary Carlos";
const patientRelatedEmail: string = "<EMAIL>";
const patientRelatedGender: string = "Female";
const patientRelatedDOB: string = "1950-02-20";
const patientLocDisplay: string = "DEV - Sunrise Senior Living";
const patientOrganizationId: string = "e759d587-9810-4c38-8c8e-acd418d940b6";

const patientConditionNote: string = "In last visit";
const patientConditionText: string = "Hypertension 2";
const patientConditionOnSetDate: string = "2024-06-03";

let accessToken: any;
let createdPatientId = '';
let createdConditionId = '';
const medplumOptions: MedplumClientOptions = {
  verbose: true,
};
const medplum: MedplumClient = new MedplumClient(medplumOptions);

const loginToMedplum = async (
  medplum: MedplumClient,
  email: string,
  password: string
): Promise<ProfileResource> => {
  const loginRequest: EmailPasswordLoginRequest = {
    email,
    password,
  };
  const loginResponse = await medplum.startLogin(loginRequest);
  const userProfile = await medplum.processCode(loginResponse.code as string);
  accessToken = medplum.getActiveLogin()?.accessToken;
  return userProfile;
};

tap.teardown(async () => {
  await fastify.close();
  process.exit(0);
}); //after running all tests
tap.before(async () => { //run before running all tests
  await loginToMedplum(medplum, practitionerEmail, practitionerPwd);
});

tap.test("requests the health check up, should return success", async (t: Tap.Test) => {
  const response = await fastify.inject({
    method: "GET",
    url: `/`,
    headers: ({
      'x-access-token': accessToken
    }),
  });

  t.equal(response.statusCode, 200);
  t.equal(response.body, 'Node Services');
});

tap.test("create Patient with POST `/api/patients/, should return success", async (t) => {
  const response = await fastify.inject({
    method: "POST",
    url: `/api/patients/`,
    payload: {
      "name": patientUnitTestName,
      "email": patientUnitTestEmail,
      "gender": patientUnitTestGender,
      "birthDate": patientUnitTestDOB,
      "maritalStatus": patientUnitTestMaritalStatus,
      "active": true,
      "mobilePhone": patientPhone,
      "homePhone": patientHomePhone,
      "sendInvite": true,
      "link": [
        {
          "mobilePhone": patientPhone,
          "homePhone": patientHomePhone,
          "resourceType": "RelatedPerson",
          "relationship": {
            "system": "http://terminology.hl7.org/CodeSystem/v3-RoleCode",
            "code": "WIFE",
            "display": "wife"
          },
          "use": "official",
          "name": patientRelatedPersonName,
          "email": patientRelatedEmail,
          "gender": patientRelatedGender,
          "birthDate": patientRelatedDOB,
          "Location": {
            "Location": {
              "reference": `Organization/${patientOrganizationId}`,
              "display": patientLocDisplay
            }
          },
          "communicationLanguage": [
            {
              "system": "http://hl7.org/fhir/ValueSet/languages",
              "code": "en-US",
              "display": "English (United States)",
              "preferred": true
            }
          ],
        }
      ],
      "Location": {
        "Location": {
          "reference": `Organization/${patientOrganizationId}`,
          "display": patientLocDisplay
        }
      },
      "communicationLanguage": [
        {
          "system": "http://hl7.org/fhir/ValueSet/languages",
          "code": "en-US",
          "display": "English (United States)",
          "preferred": true
        }
      ],
      "organizationId": patientOrganizationId
    },
    headers: ({
      'Content-Type': 'application/json',
      'x-access-token': accessToken,
    }),
  });

  t.equal(response.statusCode, 200);
  const responseBody = JSON.parse(response.body);
  createdPatientId = responseBody.id;
  t.ok(responseBody.id);
  t.ok(responseBody.active);
  t.equal(responseBody.resourceType, 'Patient');
  t.equal(responseBody.name, patientUnitTestName);
  t.equal(responseBody.email, patientUnitTestEmail);
});

tap.test("update patient's mobilePhone with PATCH `/api/patients/{id}/, should return success", async (t) => {
  const response = await fastify.inject({
    method: "PATCH",
    url: `/api/patients/${createdPatientId}`,
    payload: {
      "mobilePhone": patientUpdatedPhone
    },
    headers: ({
      'Content-Type': 'application/json',
      'x-access-token': accessToken,
    }),
  });

  t.equal(response.statusCode, 200);
  const responseBody = JSON.parse(response.body);
  t.equal(responseBody.id, createdPatientId);
  t.ok(responseBody.active);
  t.equal(responseBody.name, patientUnitTestName);
  t.equal(responseBody.mobilePhone, patientUpdatedPhone);
});

tap.test("update patient's primaryLanguage with PATCH `/api/patients/{id}/, should return success", async (t) => {
  const response = await fastify.inject({
    method: "PATCH",
    url: `/api/patients/${createdPatientId}`,
    payload: {
      "primaryLanguage": {
        "system": "http://hl7.org/fhir/ValueSet/languages",
        "code": "en-US",
        "display": "English (United States)",
        "preferred": true
      }
    },
    headers: ({
      'Content-Type': 'application/json',
      'x-access-token': accessToken,
    }),
  });

  t.equal(response.statusCode, 200);
  const responseBody = JSON.parse(response.body);
  t.equal(responseBody.id, createdPatientId);
  t.ok(responseBody.active);
  t.equal(responseBody.name, patientUnitTestName);
  t.equal(responseBody.communicationLanguage[0]?.code, "en-US");
  t.equal(responseBody.communicationLanguage[0]?.display, "English (United States)");
  t.equal(responseBody.communicationLanguage[0]?.system, "http://hl7.org/fhir/ValueSet/languages");
  t.ok(responseBody.communicationLanguage[0]?.preferred);
});


tap.test("update patient details with PATCH `/api/patients/{id}/, should return success", async (t) => {
  const response = await fastify.inject({
    method: "PATCH",
    url: `/api/patients/${createdPatientId}`,
    payload: {
      "homePhone": patientHomePhone,
      "email": patientUnitTestEmail,
      "mobilePhone": patientUpdatedPhone,
      "primaryLanguage": {
        "system": "http://hl7.org/fhir/ValueSet/languages",
        "code": "en-US",
        "display": "English (United States)",
        "preferred": true
      }
    },
    headers: ({
      'Content-Type': 'application/json',
      'x-access-token': accessToken,
    }),
  });

  t.equal(response.statusCode, 200);
  const responseBody = JSON.parse(response.body);
  t.equal(responseBody.id, createdPatientId);
  t.ok(responseBody.active);
  t.equal(responseBody.name, patientUnitTestName);
  t.equal(responseBody.email, patientUnitTestEmail);
  t.equal(responseBody.mobilePhone, patientUpdatedPhone);
  t.equal(responseBody.communicationLanguage[0]?.code, "en-US");
  t.equal(responseBody.communicationLanguage[0]?.display, "English (United States)");
  t.equal(responseBody.communicationLanguage[0]?.system, "http://hl7.org/fhir/ValueSet/languages");
  t.ok(responseBody.communicationLanguage[0]?.preferred);
});

tap.test("get patient list with GET `/api/patients/` using practitioner credentials, should return success", async (t: Tap.Test) => {
  const response = await fastify.inject({
    method: "GET",
    url: `/api/patients/`,
    query: "_start=0&_end=2&active=true",
    headers: ({
      'x-access-token': accessToken
    }),
  });

  t.equal(response.statusCode, 200);
  const responseBody = JSON.parse(response.body);
  t.equal(responseBody[0].resourceType, 'Patient'); //checking the first resourceType
  t.equal(Array.from(responseBody).filter((d: any) => d.active == false || d.active == 'false').length, 0); //checking the first resourceType
  t.ok(Array.from(responseBody).length <= 2); //checking the first resourceType
});

tap.test("get patient details with GET `/api/patients/${patientId}/`, should return success", async (t) => {
  const response = await fastify.inject({
    method: "GET",
    url: `/api/patients/${createdPatientId}`,
    headers: ({
      'x-access-token': accessToken,
    }),
  });

  t.equal(response.statusCode, 200);
  const responseBody = JSON.parse(response.body);
  t.equal(responseBody.resourceType, 'Patient');
  t.equal(responseBody.id, createdPatientId);
  t.ok(responseBody.active);
  t.equal(responseBody.resourceType, 'Patient');
  t.equal(responseBody.name, patientUnitTestName);
  t.equal(responseBody.email, patientUnitTestEmail);
});

tap.test("get patient's default chat with GET `/api/patients/{id}/chat/`, should return success", async (t) => {
  const response = await fastify.inject({
    method: "GET",
    url: `/api/patients/${createdPatientId}/chat`,
    headers: ({
      'x-access-token': accessToken,
    }),
  });

  t.equal(response.statusCode, 200);
  const responseBody = JSON.parse(response.body);
  t.ok(responseBody.threadId);
  if (responseBody?.threadSubject?.type) {
    const threadSubjectType = responseBody.threadSubject.type;
    t.ok(threadSubjectType === "Caregiver", 'Default practitioner thread Test - Thread type should be "Default"')
  }
});

tap.test("add patient's condition with GET `/api/patients/{patientId}/conditions/, should return success", async (t) => {
  const response = await fastify.inject({
    method: "POST",
    url: `/api/patients/${createdPatientId}/conditions/`,
    payload: {
      "note": patientConditionNote,
      "code": {
        "text": patientConditionText
      },
      "clinicalStatus": "Active",
      "verificationStatus": "Confirmed",
      "onsetDateTime": patientConditionOnSetDate
    },
    headers: ({
      'Content-Type': 'application/json',
      'x-access-token': accessToken,
    }),
  });

  t.equal(response.statusCode, 200);
  const responseBody = JSON.parse(response.body);
  t.equal(responseBody.resourceType, 'Condition');
  createdConditionId = responseBody.id;
});

tap.test("get patient's condition with GET `/api/patients/{patientId}/conditions/`, should return success", async (t) => {
  const response = await fastify.inject({
    method: "GET",
    url: `/api/patients/${createdPatientId}/conditions`,
    headers: ({
      'x-access-token': accessToken,
    }),
  });

  t.equal(response.statusCode, 200);
  const responseBody = JSON.parse(response.body);
  //we should check that conditions already exists, since the POST condition has been done already
  t.ok(responseBody[0]);
  t.equal(responseBody[0].resourceType, 'Condition');
  t.equal(responseBody[0].id, createdConditionId);
});

tap.test("delete patient's condition with DELETE `/api/patients/{patientId}/conditions/{conditionId}`, should return success", async (t) => {
  const response = await fastify.inject({
    method: "DELETE",
    url: `/api/patients/${createdPatientId}/conditions/${createdConditionId}`,
    headers: ({
      'x-access-token': accessToken,
    }),
  });

  t.equal(response.statusCode, 200);
});

tap.test("delete patient with DELETE `/api/patients/{id}/`, should return success", async (t) => {
  const response = await fastify.inject({
    method: "DELETE",
    url: `/api/patients/${createdPatientId}/`,
    headers: ({
      'x-access-token': accessToken,
    }),
  });

  t.equal(response.statusCode, 200);
});

tap.test("get patient details after delete, with GET `/api/patients/${patientId}/`, should return success and active=false", async (t) => {
  let attempts = 0;
  let maxAttempts = 5;
  let delay = 2000;
  while (attempts < maxAttempts) {
    const response = await fastify.inject({
      method: "GET",
      url: `/api/patients/${createdPatientId}`,
      headers: ({
        'x-access-token': accessToken,
      }),
    });

    t.equal(response.statusCode, 200);
    t.equal(response.statusMessage, 'OK');
    const responseBody = JSON.parse(response.body);
    if (!responseBody.active) {
      t.equal(responseBody.active, false); // active = false represents deleted patient
      t.equal(responseBody.resourceType, 'Patient');
      t.equal(responseBody.name, patientUnitTestName);
      t.equal(responseBody.email, patientUnitTestEmail);
      break;
    }
    attempts++;
    await new Promise(resolve => setTimeout(resolve, delay));
  }
});

tap.test("get patient details of unknown patient with GET `/api/patients/${patientId}/`, should return status 404", async (t) => {
  const response = await fastify.inject({
    method: "GET",
    url: `/api/patients/${randomUUID()}`,
    headers: ({
      'x-access-token': accessToken,
    }),
  });

  t.equal(response.statusCode, 404);
});

tap.test("create patient details with incorrect payload, should return status 400", async (t) => {
  const response = await fastify.inject({
    method: "POST",
    url: `/api/patients/`,
    payload: {},
    headers: ({
      'Content-Type': 'application/json',
      'x-access-token': accessToken,
    }),
  });

  t.equal(response.statusCode, 400);
});
