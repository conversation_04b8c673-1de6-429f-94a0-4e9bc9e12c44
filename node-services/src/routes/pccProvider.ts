import path from 'path';
import config from 'config';
import proxy from '@fastify/http-proxy';
import { FastifyHttpProxyOptions } from '@fastify/http-proxy';
import { FastifyInstance, FastifyPluginOptions, FastifyPluginAsync, FastifyRequest, FastifyReply } from 'fastify';
import fastifyAutoload from '@fastify/autoload';
import { PccService } from '../services/ehr/ehr.pcc';
import { IUser, RelicOrganization } from 'relic-ui';
import { PccFacility } from 'relic-ui';
import fastifyJwt, { FastifyJWTOptions, SignPayloadType, TokenOrHeader } from '@fastify/jwt';
import { IdentityServiceInterface } from '../services/identity/identity.interface';
import { EhrServiceInterface } from '../services/ehr/ehr.interface';
import { requestContext } from '@fastify/request-context';

declare module 'fastify' {
  export interface FastifyInstance {
    ehrService: EhrServiceInterface;
    identityService: IdentityServiceInterface;
    searchPccFacilities(query: any, organizationId?: string): Promise<{ total: number; data: PccFacility[] }>;
    savePccFacility(orgUuid: string, facId: number, enabled: boolean): Promise<PccFacility>;
    verifyJwt(req: FastifyRequest, rep: FastifyReply): Promise<void>;
  }
}

declare module '@fastify/request-context' {
  interface RequestContextData {
    pccAccessToken: string; //TODO: To be fixed or removed. This is a legacy token for PCC
  }
}

const pccProvider: FastifyPluginAsync = async (fastify: FastifyInstance, opts: FastifyPluginOptions) => {
  // providerService decorator for PccService
  if (!fastify.hasDecorator('ehrService')) {
    fastify.decorate('ehrService', new PccService(fastify, opts));
  }
  fastify.log.info('providerService(Pcc) decorator successful.');

  // identityService decorator for PccService
  if (!fastify.hasDecorator('identityService')) {
    fastify.decorate('identityService', new PccService(fastify, opts));
  }
  fastify.log.info('identityService(Pcc) decorator successful.');

  if (!fastify.mongo.reliccare?.db) {
    throw new Error('reliccare database is not available');
  }

  const pccFacilitiesCollection = fastify.mongo.reliccare.db.collection<PccFacility>('pccFacilities');
  const relicOrganizations = fastify.mongo.reliccare.db.collection<RelicOrganization>('organizations');

  //TODO: To be fixed.
  async function searchPccFacilities(
    query: any,
    organizationId: string,
  ): Promise<{ total: number; data: PccFacility[] }> {
    let pccOrgUuids: string[];
    const relicOrganization: RelicOrganization = await fastify.orgService.getOrganization(organizationId);
    if (!relicOrganization) {
      throw fastify.httpErrors.notFound(`organization not found, id:${organizationId}`);
    }
    if (!relicOrganization.pointClickCare?.id) {
      throw fastify.httpErrors.badRequest(`Organization is not a PointClickCare organization, id:${organizationId}`);
    }
    pccOrgUuids = [relicOrganization.pointClickCare?.id];
    const facilitiesFromPcc = await fastify.ehrService.searchFacilities(pccOrgUuids, query);

    if (facilitiesFromPcc.data.length == 0) {
      return {
        total: facilitiesFromPcc.total,
        data: facilitiesFromPcc.data as PccFacility[],
      };
    }

    const facilityIds: number[] = (facilitiesFromPcc.data as PccFacility[])?.map((f) => f.facId) as number[];
    const relicPccFacilities = await pccFacilitiesCollection
      .find({
        facId: { $in: facilityIds },
        relicOrganizationId: relicOrganization.id,
      })
      .toArray();
    return { total: facilitiesFromPcc.total, data: relicPccFacilities };
  }

  //TODO: To be fixed.
  async function savePccFacility(orgUuid: string, facId: number, enabled: boolean): Promise<PccFacility> {
    const relicOrganization: RelicOrganization = (await relicOrganizations.findOne({
      'pointClickCare.id': orgUuid.toUpperCase(),
    })) as RelicOrganization;
    if (!relicOrganization) {
      throw fastify.httpErrors.notFound(`PointClickCare organization id not found, id:${orgUuid}.`);
    }
    const facility = (await fastify.ehrService.getFacilityById(facId.toString())) as PccFacility;

    facility.id = orgUuid + '-' + facId;
    facility.enabled = enabled;
    facility.relicOrganizationId = relicOrganization.id;
    return (await pccFacilitiesCollection.findOneAndUpdate(
      { id: facility.id },
      { $set: facility },
      { returnDocument: 'after', upsert: true },
    )) as PccFacility;
  }

  fastify.decorate('searchPccFacilities', searchPccFacilities);
  fastify.decorate('savePccFacility', savePccFacility);

  // jwt configuration for Pcc
  const pccJwtConfig: FastifyJWTOptions = {
    namespace: 'pcc',
    jwtVerify: 'jwtVerify',
    secret: function (req: FastifyRequest, token: TokenOrHeader, callback: Function) {
      let secret = config.get('PCC.CLIENT_ID') + ':' + config.get('PCC.CLIENT_SECRET');
      const isRequestFromLocal =
        req.headers['origin']?.includes('localhost') || req.headers['referer']?.includes('localhost');
      if (isRequestFromLocal) {
        secret = config.get('PCC.LOCALHOST_CLIENT_ID') + ':' + config.get('PCC.LOCALHOST_CLIENT_SECRET');
      }
      callback(null, secret);
    },
    verify: {
      complete: true,
      algorithms: ['HS256'],
      //We refresh pcc access token on the front end.
      //As a result our idToken is expired when access tokens are refreshed.
      //PCC does not provide a mechanism to refresh id tokens, so we are ignoring expiration.
      ignoreExpiration: true,
      allowedIss: ['PointClickCare'],
      extractToken: function (req: FastifyRequest): string {
        return req.headers['x-id-token'] as string;
      },
    },
    messages: {
      badRequestErrorMessage: `header x-id-token is missing or invalid`,
    },
    formatUser: async function (tokenPayload: SignPayloadType): Promise<IUser> {
      /* for Pcc, token response contains metadata including orgUuid.
      Show below is a sample token response. Hence whoAmI request would have valid orgUuid in request context.
        {
          "access_token": "kmGlp8nokmpPvsx2ZJwF0VJUQTaK",
          "refresh_token": "fiSFjabVrxXOJZpRdZsg0BSxgALOrN8f",
          "expires_in": "7199",
          "refresh_token_expires_in": "1295999",
          "metadata": {
              "orgUuid": "E8952BB3-9995-4345-AD26-DC587176D615"
          }
        }    
      */
      const relicAiUser = await fastify.identityService.whoAmI(tokenPayload);
      return relicAiUser;
    },
  };

  // jwt registration for Pcc
  fastify.register(fastifyJwt, pccJwtConfig);

  /**
   * Verifies the PCC JWT token from the incoming request, constructs the user identity,
   * and sets relevant authentication and user context values into the request context.
   *
   * This function is intended to be used as an authentication hook in Fastify.
   *
   * For Pcc, orgUuid has to be passed in the request.
   * verifyJwt hook converts orgUuid to relicOrganizationId and sets it in request context.
   * @param req - The Fastify request object.
   * @param rep - The Fastify reply object.
   * @returns A Promise that resolves when verification and context setup are complete.
   * @throws Will throw if JWT verification fails.
   */
  async function verifyJwt(req: FastifyRequest, rep: FastifyReply) {
    const organizationId = req.headers['x-organization-id'] as string;
    // organizationId contains Pcc orgUuid
    requestContext.set('organizationId', organizationId);
    requestContext.set('accessToken', req.headers['x-access-token'] as string);
    requestContext.set('pccAccessToken', req.headers['x-access-token'] as string);
    const relicAiUser = (await req.jwtVerify()) as IUser;
    requestContext.set('user', relicAiUser);
    if (relicAiUser.userIdentity) {
      requestContext.set('whoami', relicAiUser.userIdentity);
    }
    // organizationId is set to Relic AI organizationId
    if (relicAiUser.organizationId) {
      requestContext.set('organizationId', relicAiUser.organizationId);
      await fastify.setMyOrganization(relicAiUser.organizationId);
    }
    //TODO: Legacy needs these values available in requestcontext. To be fixed by using req.user value
    if (relicAiUser.decodedJwtToken) {
      requestContext.set('decodedJwtToken', relicAiUser.decodedJwtToken);
    }
  }

  async function verifyAccess(req: FastifyRequest, rep: FastifyReply) {
    return true; // Placeholder for access verification logic. TBD: To be fixed later.
  }

  fastify.addHook('onRequest', fastify.auth([verifyJwt, verifyAccess], { relation: 'and' }));

  fastify.log.info('Pcc Routes loading...');

  //Publishing Pcc Routes
  await fastify.register(fastifyAutoload, {
    dir: path.join(__dirname, './api'),
    options: {
      ...opts,
      prefix: '/api/pcc',
    },
    autoHooks: true,
    cascadeHooks: true,
    routeParams: true,
    ignoreFilter: (filePath: string) => {
      return filePath.includes('patients');
    },
  });

  // Pcc Specific Services exposed via Pcc Routes
  await fastify.register(fastifyAutoload, {
    dir: path.join(__dirname, './pcc'),
    options: {
      ...opts,
      prefix: '/api/pcc',
    },
    autoHooks: true,
    cascadeHooks: true,
    routeParams: true,
  });

  // separate instance for Flowise proxy. Documented in src/plugins/external/swagger.ts > transform function
  fastify.register(proxy, {
    upstream: config.get('FLOWISE.API_URL'),
    prefix: '/api/pcc/flowise',
    httpMethods: ['GET'],
    replyOptions: {
      rewriteRequestHeaders: (originalReq: any, headers: any) => ({
        Authorization: `Bearer ${config.get('FLOWISE.API_KEY')}`,
        'Content-Type': 'application/json',
      }),
    },
  } as FastifyHttpProxyOptions);

  fastify.log.info('Pcc Routes loaded successfully.');
};

export default pccProvider;
