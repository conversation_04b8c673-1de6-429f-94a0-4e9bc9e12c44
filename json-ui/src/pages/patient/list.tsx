import { IUser } from 'relic-ui';
import { RelicPatient } from 'relic-ui';
import React, { useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { <PERSON><PERSON>ield, EmailField, useDataGrid } from '@refinedev/mui';
import {
  useMany,
  CrudSort,
  useDelete,
  useOnError,
  CrudFilter,
  useTranslate,
  LogicalFilter,
  useGetIdentity,
  IResourceComponentsProps,
} from '@refinedev/core';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Skeleton from '@mui/material/Skeleton';
import useMediaQuery from '@mui/material/useMediaQuery';
import ToggleButtonGroup from '@mui/material/ToggleButtonGroup';
import ToggleButton, { ToggleButtonProps } from '@mui/material/ToggleButton';
import {
  GridColDef,
  DataGridPro,
  GridCellParams,
  GridPaginationMeta,
} from '@mui/x-data-grid-pro';

import { useRouter } from 'src/routes/hooks';

import { useSearch } from 'src/hooks/use-search';

import { RowActionsPopover } from 'src/components/list';
import { NewButton } from 'src/components/refine-customs/new-btn';
import { DataGridToolbar } from 'src/components/list/data-grid-toolbar';

// ----------------------------------------------------------------------

export const PatientList: React.FC<IResourceComponentsProps> = () => {
  const translate = useTranslate();

  const router = useRouter();

  const location = useLocation();

  const navigate = useNavigate();

  const { mutate: onError } = useOnError();

  const { mutate: onDelete } = useDelete();

  const { data: currentUser } = useGetIdentity<IUser>();

  const { globalSearch } = useSearch();

  const [viewButtonEl, setViewButtonEl] =
    React.useState<HTMLButtonElement | null>(null);

  const isSmallScreen = useMediaQuery('(max-width:600px)');

  const currentIdentityProvider: string = React.useMemo(() => {
    const identityProvider: string = (currentUser?.userIdentity
      ?.provider as string)
      ? (currentUser?.userIdentity?.provider as string)
      : 'medplum';
    return identityProvider;
  }, [currentUser]);

  const initialSorters: CrudSort[] = React.useMemo(
    () => [
      {
        field: 'name',
        order: 'asc',
      },
    ],
    [],
  );

  const initialFilters: CrudFilter[] = React.useMemo(() => {
    const filter: CrudFilter = {
      field: 'patientStatus',
      value: 'current',
      operator: 'eq',
    };
    return currentIdentityProvider === 'pcc' ? [filter] : [];
  }, [currentIdentityProvider]);

  const { dataGridProps, filters, setFilters, setCurrent } =
    useDataGrid<RelicPatient>({
      sorters: {
        initial: initialSorters,
      },
      filters: {
        initial: initialFilters,
      },
      queryOptions: {
        enabled: true,
      },
    });

  const patientStatusFilter: CrudFilter | undefined = filters.find(
    filter => (filter as LogicalFilter).field === 'patientStatus',
  );

  const { data: organizationData, isLoading: organizationIsLoading } = useMany({
    resource: 'organizations',
    ids: dataGridProps?.rows?.map((item: any) => item?.organizationId) ?? [],
    queryOptions: {
      enabled: !!dataGridProps?.rows,
      staleTime: 5 * 60 * 1000,
    },
  });

  useEffect(() => {
    const urlParams = new URLSearchParams(location.search);

    // These parameters may exist in the URL after a successful PCC login
    if (urlParams.has('code') || urlParams.has('state')) {
      // Delete these parameters since these are single use.
      urlParams.delete('code');
      urlParams.delete('state');

      navigate(
        {
          pathname: location.pathname,
          search: urlParams.toString(),
        },
        { replace: true },
      );
    }
  }, [location, navigate]);

  useEffect(() => {
    try {
      setFilters((prevFilters: CrudFilter[]) => {
        // Update search filter while retaining others
        const updatedFilters = prevFilters.filter(
          (filter: CrudFilter) => (filter as LogicalFilter).field !== 'search',
        );
        updatedFilters.push({
          field: 'search',
          value: globalSearch.length > 0 ? globalSearch : '',
          operator: 'contains',
        });
        return updatedFilters;
      });
      setCurrent(1); // Reset the page number to 1 when the search changes
    } catch (error) {
      onError(error);
    }
  }, [globalSearch]);

  //Medplum Grid - Start
  const handleMarkInactiveClick = React.useCallback(
    (params: GridCellParams) => {
      if (params.field !== 'actions') {
        onDelete({
          resource: 'patients',
          id: params.id,
          successNotification: (data, values, resource) => ({
            message: translate('notifications.markInactiveSuccess', {
              resource,
            }),
            description: 'Successful',
            type: 'success',
          }),
          errorNotification: error => ({
            message: translate('notifications.markInactiveError', {
              resource: 'condition',
              statusCode: error?.statusCode,
            }),
            description: error?.message,
            type: 'error',
          }),
        });
      }
    },
    [onDelete, translate],
  );

  const handleOnCellClick = React.useCallback(
    (params: GridCellParams) => {
      if (params.field !== 'actions') {
        router.push(`/patients/edit/${params.id}?name=${params.row.name}`);
      }
    },
    [router],
  );

  const medplumColumns = React.useMemo<GridColDef[]>(
    () => [
      {
        field: 'name',
        headerName: translate('patients.fields.name'),
        flex: 1,
        minWidth: 200,
      },
      {
        field: 'birthDate',
        headerName: translate('patients.fields.birthDate'),
        flex: 1,
        minWidth: 150,
        renderCell: function render({ value }) {
          return <DateField value={value} />;
        },
      },
      {
        field: 'email',
        headerName: translate('patients.fields.email'),
        flex: 1,
        minWidth: 200,
        renderCell: function render({ value }) {
          return <EmailField value={value} />;
        },
      },
      {
        field: 'primaryLanguage',
        headerName: translate('patients.fields.primaryLanguage'),
        flex: 1,
        maxWidth: 200,
        renderCell: function render({ row, value }) {
          return value?.display
            ? value?.display
            : organizationData?.data?.find(
                item => item.id === row?.organizationId,
              )?.fhirStore?.defaultLanguage?.display || '-';
        },
      },
      {
        field: 'organizationId',
        headerName: translate('patients.fields.organizationId'),
        flex: 1,
        minWidth: 200,
        hide: true,
        renderCell: function render({ value }) {
          return organizationIsLoading ? (
            <Skeleton
              variant="rectangular"
              sx={{ width: 0.5, height: 16, borderRadius: 0.5 }}
            />
          ) : (
            organizationData?.data?.find(item => item.id === value)?.name || '-'
          );
        },
      },
      {
        field: 'actions',
        headerName: translate('table.actions'),
        sortable: false,
        renderCell: function render({ row }) {
          return (
            <>
              <RowActionsPopover
                rowId={row.id}
                showButtonProps={{
                  onClick: () =>
                    router.push(`/patients/edit/${row.id}?name=${row.name}`),
                }}
                markInactiveButtonProps={{
                  onConfirm: () => handleMarkInactiveClick(row),
                }}
                deleteButtonProps={{
                  confirmTitle: translate('patients.titles.delete'),
                  confirmContent: `Are you sure you want to delete ${row.name}?`,
                  resource: 'patients',
                  recordItemId: row.id,
                }}
              />
            </>
          );
        },
        align: 'center',
        headerAlign: 'center',
        minWidth: 80,
      },
    ],
    [
      translate,
      organizationData?.data,
      organizationIsLoading,
      router,
      handleMarkInactiveClick,
    ],
  );

  const displayMedplumColumns: GridColDef[] = React.useMemo(() => {
    const smallScreenColumns: GridColDef[] = medplumColumns.filter(
      column => column.field === 'name' || column.field === 'actions',
    );
    return isSmallScreen
      ? smallScreenColumns
      : (medplumColumns as GridColDef[]);
  }, [medplumColumns, isSmallScreen]);

  const MedplumToolbar = React.useCallback(
    () => (
      <DataGridToolbar
        setViewButtonEl={setViewButtonEl}
        title={translate('patients.patients')}
        // actions={
        //   <NewButton
        //     href={'/patients/create'}
        //     label={translate('buttons.create')}
        //   />
        // }
      />
    ),
    [translate],
  );

  const renderMedplumGrid: JSX.Element = (
    <DataGridPro
      {...dataGridProps}
      rows={currentIdentityProvider === 'medplum' ? dataGridProps.rows : []}
      pagination
      sx={{
        '& .MuiDataGrid-cell': {
          display: 'flex',
          alignSelf: 'center', // Center aligns the items vertically
          height: '100%', // Ensure the cell takes full height for vertical centering,
          border: 'none',
        },
      }}
      initialState={{
        columns: {
          columnVisibilityModel: {
            organizationId: false,
          },
        },
      }}
      disableColumnMenu
      columns={displayMedplumColumns}
      localeText={{ toolbarColumns: 'View' }}
      onCellClick={handleOnCellClick}
      slots={{
        toolbar: MedplumToolbar,
      }}
      slotProps={{
        panel: {
          anchorEl: viewButtonEl,
        },
        toolbar: {
          onClick: (e: React.MouseEvent) => {
            e.stopPropagation();
            setViewButtonEl(null);
          },
        },
      }}
    />
  );
  //Medplum Grid - End

  //PCC Grid - Start
  const patientFilters: ToggleButtonProps[] = [
    { value: 'new', name: translate('patients.status.new') },
    { value: 'current', name: translate('patients.status.current') },
    { value: 'discharged', name: translate('patients.status.discharged') },
  ];

  const currentPatientFilter = patientStatusFilter
    ? patientStatusFilter
    : initialFilters[0];

  const pccColumns = React.useMemo<GridColDef[]>(
    () => [
      {
        field: 'name',
        headerName: translate('patients.fields.name'),
        flex: 1,
        minWidth: 200,
      },
      {
        field: 'birthDate',
        headerName: translate('patients.fields.birthDate'),
        flex: 1,
        minWidth: 150,
        renderCell: function render({ value }) {
          return <DateField value={value} />;
        },
      },
      {
        field: 'bedLocation',
        headerName: translate('patients.fields.location'),
        flex: 1,
        minWidth: 200,
      },
      {
        field: 'primaryLanguage',
        headerName: translate('patients.fields.primaryLanguage'),
        flex: 1,
        maxWidth: 200,
        renderCell: function render({ row, value }) {
          return value?.display
            ? value?.display
            : organizationData?.data?.find(
                item => item.id === row?.organizationId,
              )?.fhirStore?.defaultLanguage?.display || '-';
        },
      },
      {
        field: 'organizationId',
        headerName: translate('patients.fields.organizationId'),
        flex: 1,
        minWidth: 200,
        hide: true,
        renderCell: function render({ value }) {
          return organizationIsLoading ? (
            <Skeleton
              variant="rectangular"
              sx={{ width: 0.5, height: 16, borderRadius: 0.5 }}
            />
          ) : (
            organizationData?.data?.find(item => item.id === value)?.name || '-'
          );
        },
      },
      {
        field: 'actions',
        headerName: translate('table.actions'),
        sortable: false,
        renderCell: function render({ row }) {
          return (
            <>
              <RowActionsPopover
                rowId={row.id}
                showButtonProps={{
                  onClick: () =>
                    router.push(`/patients/edit/${row.id}?name=${row.name}`),
                }}
              />
            </>
          );
        },
        align: 'center',
        headerAlign: 'center',
        minWidth: 80,
      },
    ],
    [
      translate,
      organizationData?.data,
      organizationIsLoading,
      router,
      handleMarkInactiveClick,
    ],
  );

  const displayPccColumns: GridColDef[] = React.useMemo(() => {
    const smallScreenColumns: GridColDef[] = pccColumns.filter(
      column => column.field === 'name' || column.field === 'actions',
    );
    return isSmallScreen ? smallScreenColumns : (pccColumns as GridColDef[]);
  }, [pccColumns, isSmallScreen]);

  const handleFilterChange = React.useCallback(
    (_event: React.MouseEvent<HTMLElement>, newFilter: string) => {
      setFilters((prevFilters: CrudFilter[]) => {
        // Update status filter while retaining others
        const updatedFilters = prevFilters.filter(
          (filter: CrudFilter) =>
            (filter as LogicalFilter).field !== 'patientStatus',
        );
        updatedFilters.push({
          field: 'patientStatus',
          value: newFilter,
          operator: 'eq',
        });
        return updatedFilters;
      });
    },
    [setFilters],
  );

  const renderStatusFilter = (): JSX.Element => (
    <Box sx={{ display: 'flex' }}>
      <ToggleButtonGroup
        value={currentPatientFilter?.value}
        exclusive={true}
        size="small"
        onChange={handleFilterChange}
      >
        {patientFilters.map((props, index) => (
          <ToggleButton
            key={index}
            {...props}
            selected={props.value === currentPatientFilter?.value}
          >
            {props.name}
          </ToggleButton>
        ))}
      </ToggleButtonGroup>
    </Box>
  );
  const PccToolbar = React.useCallback(
    () => (
      <DataGridToolbar
        title={translate('patients.patients')}
        statusFilter={renderStatusFilter()}
      />
    ),
    [translate, currentPatientFilter],
  );

  //Pcc does not return the total count of records in the response, so we need to calculate it
  const { paginationMeta, rowCount } = React.useMemo(() => {
    const pageRowCount: number = dataGridProps?.rowCount || 0;
    const pageSize: number = dataGridProps?.paginationModel?.pageSize || 0;
    const page: number = dataGridProps?.paginationModel?.page || 0;
    const paginationMeta: GridPaginationMeta = {
      hasNextPage: pageRowCount && pageSize ? pageRowCount === pageSize : false,
    };
    const rowCount: number =
      pageRowCount && pageSize && pageRowCount !== pageSize
        ? page * pageSize + pageRowCount
        : -1;
    return { paginationMeta, rowCount };
  }, [dataGridProps?.rowCount, dataGridProps?.paginationModel]);

  const renderPccGrid: JSX.Element = (
    <>
      <DataGridPro
        {...dataGridProps}
        rowCount={rowCount}
        pagination
        paginationMeta={paginationMeta}
        sx={{
          '& .MuiDataGrid-cell': {
            display: 'flex',
            alignSelf: 'center', // Center aligns the items vertically
            height: '100%', // Ensure the cell takes full height for vertical centering,
            border: 'none',
          },
        }}
        initialState={{
          columns: {
            columnVisibilityModel: {
              organizationId: false,
            },
          },
        }}
        disableColumnMenu
        columns={displayPccColumns}
        localeText={{ toolbarColumns: 'View' }}
        onCellClick={handleOnCellClick}
        slots={{
          toolbar: PccToolbar,
        }}
        slotProps={{
          panel: {
            anchorEl: viewButtonEl,
          },
        }}
      />
    </>
  );
  //Pcc Grid - End

  return (
    <Card>
      <Box sx={{ height: 'calc(100vh - 96px)', width: '100%' }}>
        {currentIdentityProvider === 'medplum' && renderMedplumGrid}
        {currentIdentityProvider === 'pcc' && renderPccGrid}
      </Box>
    </Card>
  );
};
