import { FastifyInstance, FastifyPluginAsync, FastifyPluginOptions } from 'fastify';
import config from 'config';
import { parseReference, formatHumanName, formatCodeableConcept } from '@medplum/core';
import { Condition, Encounter, Patient, Related<PERSON>erson, Bundle, Observation, ResourceType } from '@medplum/fhirtypes';
import { PccPatient, RelicPatient, PatientEverything, RelicOrganization, CommunicationIdentity, RelicCommunicationLanguage } from 'relic-ui';
import { initializeFhirEncounter, patientToRelicPatient } from '../../types/relicPatientConversion';
import trainingsPlugin from './trainingsPlugin';
import { requestContext } from '@fastify/request-context';
import capitalize from 'lodash/capitalize';

declare module 'fastify' {
    interface FastifyInstance {
        toRelicPatient: (patient: Patient | PccPatient, relicPatient: RelicPatient) => RelicPatient;
        toRelicPatients: (patients: (Patient | PccPatient)[]) => RelicPatient[];
        upsertRelicPatient: (patient: Patient | PccPatient) => Promise<RelicPatient>;
        createPatient: (request: any) => Promise<PatientEverything>;

        //TODO: To be fixed. Temporarily set requestBody with type format
        updatePatient: (id: string, requestBody: {
            patientData: Patient, 
            email: string,
            mobilePhone: string,
            homePhone: string
        }) => Promise<RelicPatient>;
        fetchPatientById: (id: string, reply?: any) => Promise<PatientEverything>;
        markPatientInactive: (id: string) => Promise<PatientEverything>;
        saveRelicPatient: (relicPatient: RelicPatient) => Promise<RelicPatient>;
        deleteRelicPatient: (id: string) => Promise<RelicPatient>;
        getPatientCommunicationIdentity: (id: string) => Promise<CommunicationIdentity>;
        // relicPatients is now provided by fhirPluginTemp at the app level
    }
}       

// Full plugin implementation copied from app/patientPlugin.ts, minus the relicPatients decorator
export const patientPlugin: FastifyPluginAsync = async (fastify: FastifyInstance, options: FastifyPluginOptions) => {
    fastify.log.info('Patient Plugin loading...');
    // relicPatients decorator moved to fhirPluginTemp for global availability. To be fixed later.
    // Moved from app/patientPlugin.ts. The relicPatients decorator is now in fhirPluginTemp.

    // Called only from a route.
    // --- Begin full implementation from app/patientPlugin.ts, minus relicPatients decorator ---

    fastify.decorate("toRelicPatient", function (patient: Patient | PccPatient, relicPatient: RelicPatient): RelicPatient {
        const myOrganization: RelicOrganization = requestContext.get('organization');
        let primaryLanguage: RelicCommunicationLanguage = null;
        const supportedLanguages: RelicCommunicationLanguage[] = myOrganization.supportedLanguages ?? [];
        // fhir patient object
        if ('resourceType' in patient && patient.resourceType === 'Patient') {
            // Validate organization Id matching with request context.
            if (!patient.managingOrganization || !patient.managingOrganization.reference) {
                throw fastify.httpErrors.preconditionFailed('Patient must belong to an organization.');
            }
            const orgId = parseReference(patient.managingOrganization)[1];
            if (orgId !== myOrganization.id) {
                throw fastify.httpErrors.preconditionFailed(`Patient must belong to your organization - ${myOrganization.name}.`);
            }
            // Identify primary language for Patient Communication
            if (patient.communication && supportedLanguages.length > 0) {
                const preferredComm = patient.communication.find(comm => comm.preferred) || patient.communication[0];
                primaryLanguage = {
                    code: preferredComm.language?.coding[0]?.code || 'en-US',
                    display: preferredComm.language?.coding[0]?.display || 'English',
                    preferred: preferredComm.preferred || false,
                }                
            }
            // Medplum is the master for Patient data. We always overwrite relicPatient with Medplum data.
            relicPatient = {
              ...relicPatient,
              id: patient.id,
              active: patient.active || false,
              name: formatHumanName(patient.name[0]),
              birthDate: patient.birthDate,
              email: patient.telecom?.find((t) => t.system === 'email')?.value ?? '',
              emailVerified: true,
              mobilePhone: fastify.formatPhoneNumber(
                patient.telecom?.find((t) => t.system === 'phone' && t.use === 'mobile')?.value ??
                ''),
              homePhone: fastify.formatPhoneNumber(
                patient.telecom?.find((t) => t.system === 'phone' && t.use === 'home')?.value ??
                ''),
              gender: capitalize(patient.gender),
              maritalStatus: formatCodeableConcept(patient.maritalStatus),
              communicationLanguage: patient.communication?.map((comm) => ({
                code: comm.language?.coding[0]?.code || 'en-US',
                display: comm.language?.coding[0]?.display || 'English',
                system: comm.language?.coding[0]?.system || 'http://hl7.org/fhir/sid/us-languages',
                preferred: comm.preferred || false,
              })) || [],
              managingOrganization: patient.managingOrganization,
              bedLocation: '',
              patientStatus: patient.active ? 'Current' : 'Discharged',
              primaryLanguage: primaryLanguage,
            };
        }
        // pcc patient object
        if ('orgUuid' in patient && patient.orgUuid) {
            // Validate organization Id matching with request context.
            if (patient.orgUuid !== myOrganization.pointClickCare.id) {
                throw fastify.httpErrors.preconditionFailed(`Patient must belong to your organization - ${myOrganization.name}.`);
            }
            if (patient.languageCode && supportedLanguages.length > 0) {
                patient.languageCode = patient.languageCode.toLowerCase() === 'en' ? 'en-US' : patient.languageCode;
                primaryLanguage = supportedLanguages?.find((lang) => lang.code === patient.languageCode);
            } else if (patient.languageDesc && supportedLanguages.length > 0) {
                primaryLanguage = supportedLanguages?.find((lang) => lang.display === patient.languageDesc);
            }
            relicPatient = {
                ...relicPatient,
                id: `${patient.patientId}`,
                active: patient.patientStatus !== 'Discharged',
                name: `${[patient.lastName, patient.firstName].join(', ').trim()} ${patient.suffix ?? ''} (${
                    patient.medicalRecordNumber
                })`.trim(),
                birthDate: patient.birthDate,
                email: patient.email ?? '',
                emailVerified: false,
                mobilePhone: patient.cellPhone ?? patient.homePhone ?? patient.ituPhone ?? '',
                homePhone: patient.homePhone ?? patient.cellPhone ?? patient.ituPhone ?? '',
                gender: capitalize(patient.gender),
                maritalStatus: capitalize(patient.maritalStatus),
                communicationLanguage: [{
                    ...primaryLanguage,
                    preferred: true,
                }],
                managingOrganization: {
                    reference: patient.orgUuid.toUpperCase(),
                },
                bedLocation: `${[patient.floorDesc ?? '', patient.roomDesc ?? ''].join(' ')} - ${patient.bedDesc ?? ''}`.trim(),
                patientStatus: patient.patientStatus as 'New' | 'Current' | 'Discharged',
                primaryLanguage: primaryLanguage,
            }
        }
        return relicPatient;
    });

    fastify.decorate("toRelicPatients", function (patients: (Patient | PccPatient)[]): RelicPatient[] {
        const relicPatients: RelicPatient[] = [];
        for (const patient of patients) {
            const relicPatient = fastify.toRelicPatient(patient as Patient, {} as RelicPatient);
            if (relicPatient) {
                relicPatients.push(relicPatient);
            }
        }
        return relicPatients;
    });

    fastify.decorate("upsertRelicPatient", async function (patient: Patient | PccPatient): Promise<RelicPatient> {
        const myOrganization: RelicOrganization = requestContext.get('organization');
        let relicPatient: RelicPatient = {
            id: undefined,
            organizationId: myOrganization.id,
            resourceType: 'Patient',
            height: undefined,
            weight: undefined,
            link: undefined,
            Condition: undefined,
            obfuscationMap: undefined,
            summary: undefined,
            goal: 'My goal is to recover from my illness as soon as possible, get discharged and be home with my loved ones.',
            expectation: 'Dignified care with love, respect and compassion.',
            communicationIdentities: undefined,
        };
        if (!myOrganization) {
            throw fastify.httpErrors.preconditionFailed('Organization not found in request context.');
        }
        // fhir patient object
        if ('resourceType' in patient && patient.resourceType === 'Patient') {
            const found = await fastify.relicPatients().findOne({id:patient.id});
            if (found) {
                relicPatient = found;
            }
        }
        // pcc patient object
        if ('orgUuid' in patient && patient.orgUuid) {
            const found = await fastify.relicPatients().findOne({id:`${patient.patientId}`});
            if (found) {
                relicPatient = found;
            }
        }
        relicPatient = fastify.toRelicPatient(patient, relicPatient);
        if (!relicPatient.communicationIdentities || relicPatient.communicationIdentities.length === 0) {
            const commIdentity = await fastify.acs.createIdentity(relicPatient);
            relicPatient.communicationIdentities = [commIdentity];
        }
        await fastify.saveRelicPatient(relicPatient);
        return relicPatient;
    });

    fastify.decorate("createPatient", async function (request: any): Promise<PatientEverything> {
        let createdPatient: Patient = null;
        let createdEncounter: Encounter = null;
        let createdRelatedPersons: RelatedPerson[] = [];
        let createdConditions: Condition[] = [];
        let createdResources = new Set(['Patient', 'Encounter', 'RelatedPerson', 'Condition']);
        let patientEverything: PatientEverything = null;

        const fhirApi = fastify.fhirClient();
        try {
            const patientData: Patient = request.body.patientData;
            const relicPatient: RelicPatient = request.body.relicPatient;
            const relatedPersonData: RelatedPerson[] = request.body.relatedPersonData;
            const conditionData: Condition[] = request.body.conditionData;

            const organization: RelicOrganization = request.organization;
            const firstEncounter: Encounter = initializeFhirEncounter(organization.location[0], relicPatient as RelicPatient)

            if (relicPatient.id) {
                return fastify.fetchPatientById(relicPatient.id);
            }
            if (!relicPatient.organizationId) {
                throw fastify.httpErrors.preconditionFailed('Requested patient not associated with any organization or facility.');
            }
            createdPatient = await fhirApi.createResource(patientData);
            relicPatient.id = createdPatient.id;
            relicPatient.resourceType = createdPatient.resourceType;

            createdResources.add('Patient');
            if (firstEncounter) {
                firstEncounter.subject.reference = `Patient/${createdPatient.id}`;
                const createdEncounter: Encounter = await fhirApi.createResource(firstEncounter);
                fastify.log.info(`Encounter Created: ${createdEncounter.id}`);
                createdResources.add('Encounter');
            }
            if (relatedPersonData) {
                createdRelatedPersons = await Promise.all(relatedPersonData.map(async (eachRelatedPerson: RelatedPerson) => {
                    eachRelatedPerson.patient.reference = `Patient/${createdPatient.id}`;
                    const createdRelatedPerson: RelatedPerson = await fhirApi.createResource(eachRelatedPerson);
                    fastify.log.info(`RelatedPerson Created: ${createdRelatedPerson.id}`);
                    createdResources.add('RelatedPerson');
                    return createdRelatedPerson;
                }))
            }
            if (conditionData) {
                createdConditions = await Promise.all(conditionData.map(async (condition: Condition) => {
                    condition.subject.reference = `Patient/${createdPatient.id}`;
                    condition.encounter.reference = `Encounter/${createdEncounter.id}`;
                    const createdCondition: Condition = await fhirApi.createResource(condition);
                    fastify.log.info(`Condition Created: ${createdCondition.id}`)
                    createdResources.add('Condition');
                    return createdCondition;
                }));
            }
            const identity = await fastify.acs.createIdentity(relicPatient);
            relicPatient.communicationIdentities = [identity];
            try{
                const b2cid : string = await (fastify as any).createB2cPatient(relicPatient as RelicPatient);
                relicPatient.b2cid = b2cid;
            }
            catch(err){
                fastify.log.error(`Create B2C user failed, ${err.code}, ${err.message}`);
            }
            await fastify.saveRelicPatient(relicPatient);

            const primaryLanguage={
                code:createdPatient.communication ? createdPatient.communication[0]?.language?.coding[0].code:'',
                display:createdPatient.communication ? createdPatient.communication[0]?.language?.coding[0]?.display:'',
                system: createdPatient.communication ? createdPatient.communication[0]?.language?.coding[0]?.system:''
            }
            patientEverything = {
                ...relicPatient,
                Patient: createdPatient,
                PrimaryLanguage:primaryLanguage,
                RelatedPersons: createdRelatedPersons,
                Encounter: createdEncounter,
                Conditions: createdConditions
            }
            return patientEverything;
        } catch (error) {
            fastify.log.error(error);
            for (const resourceType of createdResources) {
                await deletePatientResources(createdPatient.id, resourceType);
            }
            fastify.deleteRelicPatient(createdPatient.id);
            throw new Error(`Error creating patient: ${error.message}`);
        }
    });

    fastify.decorate("updatePatient", async function (id: string, requestBody: {
        patientData: Patient, 
        email: string,
        mobilePhone: string,
        homePhone: string
    }): Promise<RelicPatient> {
        const fhirClient = fastify.fhirClient();
        const patient: Patient = requestBody.patientData;
        // First update the master patient record in Medplum/FHIR
        let updatedPatient = await fhirClient.updateResource({ ...patient }) as Patient;
        // Then run this updated patient in RelicPatient
        const relicPatient = fastify.upsertRelicPatient(updatedPatient as Patient);
        return relicPatient;
    });

    fastify.decorate("fetchPatientById", async function (id:string, reply?: any): Promise<PatientEverything>{
        try {
            const fhirApi = fastify.fhirClient();
            let fhirPatient: Patient = null;
            let encounters: Encounter[] = [];
            let conditions: Condition[] = [];
            let relatedPersons: RelatedPerson[] = [];
            let observations: Observation[] = [];
            let patient: PatientEverything = null;
            let organization:RelicOrganization = null;

            const patientResponse: Bundle = await fhirApi.readPatientEverything(id);
            if (patientResponse.entry) {
                for (const entry of patientResponse.entry) {
                    const resource = entry.resource;
                    if (resource.resourceType === 'Patient') {
                        fhirPatient = resource as Patient;
                        patient = {
                            id: fhirPatient.id,
                            resourceType:fhirPatient.resourceType,
                            organizationId: parseReference(fhirPatient.managingOrganization)[1],
                            Patient: fhirPatient,
                        }
                    }
                    if (resource.resourceType === 'Encounter') {
                        encounters.push(resource as Encounter);
                    }
                    if (resource.resourceType === 'Condition') {
                        conditions.push(resource as Condition);
                    }
                    if (resource.resourceType === 'RelatedPerson') {
                        relatedPersons.push(resource as RelatedPerson);
                    }
                    if (resource.resourceType === 'Observation') {
                        observations.push(resource as Observation);
                    }
                }
            } else {
                throw new Error('Patient data is not available');
            }
            if (fhirPatient.managingOrganization) {
                const organizationId: string = fhirPatient.managingOrganization.reference.split('/')[1];
                organization = await fastify.orgService.getOrganization(organizationId);
            } else {
                throw fastify.httpErrors.preconditionFailed(
                  'Patient not associated with any organization or facility.'
                );
            }
            if (!organization) {
                throw fastify.httpErrors.preconditionFailed(
                  `Patient Organization - ${
                    fhirPatient.managingOrganization.reference.split('/')[1]
                  } is not on-boarded.`
                );
            }
            let relicPatient:RelicPatient = await fastify.relicPatients().findOne({id:id});
            if (relicPatient) {
                patient = {
                    ...relicPatient,
                    Patient: fhirPatient,
                    Encounter: encounters[0],
                    Conditions: conditions,
                    RelatedPersons: relatedPersons,
                    Observations: observations,
                }
            }
            let updatedRelicPatient: RelicPatient = patientToRelicPatient(patient, organization);
            if (!updatedRelicPatient.b2cid) {
                try {
                    updatedRelicPatient.b2cid = relicPatient.b2cid
                        ? relicPatient.b2cid
                        : await (fastify as any).createB2cPatient(relicPatient as RelicPatient)
                }
                catch(err){
                    fastify.log.error(`Create B2C user failed, ${err.code}, ${err.message}`);
                }
            }
            relicPatient = await fastify.saveRelicPatient(updatedRelicPatient);
            patient = {
                ...relicPatient,
                Patient: fhirPatient,
                Encounter: encounters[0],
                Conditions: conditions,
                RelatedPersons: relatedPersons,
                Observations: observations,
                goal: relicPatient?.goal ?? 'My goal is to recover from my illness as soon as possible, get discharged and be home with my loved ones.',
                expectation: relicPatient?.expectation ?? 'Dignified care with love, respect and compassion.',
            }
            return patient;
        } catch (error) {
            fastify.log.error(error);
            throw new Error(`error fetching patient details: ${error.message}`);
        }
    });

    fastify.decorate("markPatientInactive", async function (id: string): Promise<PatientEverything> {
        let retries = 0;
        let patient: PatientEverything = null;
        const maxRetries: number = config.get('PATIENT.DELETE_RETRY_LIMIT');
        while (retries < maxRetries) {
            try {
                const fhirClient = fastify.fhirClient();
                const currentResource: Patient = await fhirClient.readResource('Patient', id);
                const updatedResource: Patient = { ...currentResource, active: false };
                const response: Patient = await fhirClient.updateResource(updatedResource);
                fastify.log.info(`Patient with ID ${id} marked as inactive`);
                patient = {
                    id: response.id,
                    resourceType: response.resourceType,
                    organizationId: parseReference(response.managingOrganization)[1],
                    Patient: response,
                }
                return patient;
            } catch (error) {
                fastify.log.error(`Attempt ${retries + 1}: Error marking Patient inactive for patient with ID ${id}: ${error.message}`);
                retries++;
                if (retries >= maxRetries) {
                    fastify.log.error(`Failed to mark Patient inactive after ${maxRetries} attempts: ${error.message}`);
                    throw new Error(`Failed to mark Patient inactive: ${error.message}`);
                }
                await delay(1000);
            }
        }
    });

    fastify.decorate("saveRelicPatient", async function(relicPatient: RelicPatient){
        if (!relicPatient.id || !relicPatient.resourceType || !relicPatient.organizationId || !relicPatient.communicationIdentities) {
            throw fastify.httpErrors.badRequest('Save failed. Invalid Patient data');
        }
        return await fastify.relicPatients().findOneAndUpdate(
            {"id": relicPatient.id},
            {"$set": relicPatient},
            {"upsert": true, "returnDocument": "after"} // removed setDefaultsOnInsert
        );
    });

    fastify.decorate("deleteRelicPatient", async function(id: string){
        return await fastify.relicPatients().findOneAndDelete({ id: id });
    });

    fastify.decorate("getPatientCommunicationIdentity", async function(id:string): Promise<CommunicationIdentity>{
        const relicPatient = (await fastify.relicPatients().findOne({ 
            $or: [
                { id: id },
                { 'communicationIdentities.userId': id },
                { 'mobilePhone': id },
                { 'email': id }
            ]
            }));
        const identity = await fastify.acs.refreshIdentity(relicPatient, relicPatient.communicationIdentities[0]);
        identity.displayName = relicPatient.name ? relicPatient.name : 'Patient';
        return identity;
    });

    async function deletePatientResources(id: string, resourceType: string): Promise<void> {
        let retries = 0;
        const maxRetries: number = config.get('PATIENT.DELETE_RETRY_LIMIT');
        let deletedResources = 0;
        const fhirClient = fastify.fhirClient();
        while (retries < maxRetries) {
            try {
                if (resourceType === 'Patient') {
                    await fhirClient.deleteResource(resourceType, id);
                    fastify.log.info(`Patient with ID ${id} deleted successfully`);
                    break;
                } else {
                    const searchResults = await fhirClient.search(resourceType as ResourceType, { patient: id });
                    const resources = searchResults.entry || [];
                    for (const resource of resources) {
                        const resourceId = resource.resource.id;
                        await fhirClient.deleteResource(resourceType as ResourceType, resourceId);
                        fastify.log.info(`${resourceType} with ID ${resourceId} deleted successfully`);
                        deletedResources++;
                    }
                    fastify.log.info(`${deletedResources} ${resourceType}s for patient with ID ${id} have been deleted`);
                    break;
                }
            } catch (error) {
                fastify.log.error(`Attempt ${retries + 1}: Error deleting ${resourceType} for patient with ID ${id}: ${error.message}`);
                retries++;
                if (retries === maxRetries) {
                    throw new Error(`Failed to delete ${resourceType} for patient after ${maxRetries} attempts: ${error.message}`);
                }
                await delay(1000);
            }
        }
    }
    // --- End full implementation ---

    fastify.register(trainingsPlugin, options);
}

function delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
}

export default patientPlugin;
