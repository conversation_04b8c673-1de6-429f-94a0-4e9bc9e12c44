import { IResourceComponentsProps, useCustom } from '@refinedev/core';
import { RelicAgent, Thread } from 'relic-ui';

import React from 'react';
import { ThreadsView } from 'src/components/_threads/show/threads-view';

// ----------------------------------------------------------------------
export const Sandbox: React.FC<IResourceComponentsProps> = () => {
  // Check sessionStorage for defaultAgent
  const [defaultAgent, setDefaultAgent] = React.useState<RelicAgent | null>(
    () => {
      const defaultAgentFromSession = sessionStorage.getItem('defaultAgent');
      return defaultAgentFromSession
        ? (JSON.parse(defaultAgentFromSession) as RelicAgent)
        : null;
    },
  );

  // Fetch default agent if not in sessionStorage
  useCustom<RelicAgent>({
    url: 'agents/default',
    method: 'get',
    queryOptions: {
      enabled: !defaultAgent,
      onSuccess: data => {
        sessionStorage.setItem('defaultAgent', JSON.stringify(data.data));
        setDefaultAgent(data.data);
      },
    },
  });

  // Fetch conversation that current user is having with the default agent
  const { data: threadData } = useCustom<Thread>({
    url: defaultAgent ? `practitioners/${defaultAgent.id}/chat` : '',
    method: 'get',
    queryOptions: {
      enabled: !!defaultAgent,
    },
  });

  return <ThreadsView threadId={threadData?.data.threadId ?? ''} />;
};
