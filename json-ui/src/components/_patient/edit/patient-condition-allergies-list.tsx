import React from 'react';
import { List } from '@refinedev/mui';

import {
  DataGridPro,
  gridClasses,
  DataGridProProps,
  GridPaginationMeta,
} from '@mui/x-data-grid-pro';

import { useResponsive } from 'src/hooks/use-responsive';

// ----------------------------------------------------------------------

export default function ConditionAllergiesList({
  ...dataGridProps
}: DataGridProProps) {
  const upMd = useResponsive('up', 'md');

  /* This is calculating the pagination metadata and row count for a DataGrid component.
  It is handling a scenario where the total count of records is not directly provided in the
  response from the server (Pcc), so it needs to be calculated based on the current page, page size,
  and the number of records on the page. */
  const { paginationMeta, rowCount } = React.useMemo(() => {
    const pageRowCount: number = dataGridProps?.rowCount || 0;
    const pageSize: number = dataGridProps?.paginationModel?.pageSize || 0;
    const page: number = dataGridProps?.paginationModel?.page || 0;
    const paginationMeta: GridPaginationMeta = {
      hasNextPage: pageRowCount && pageSize ? pageRowCount === pageSize : false,
    };
    const rowCount: number =
      pageRowCount && pageSize && pageRowCount !== pageSize
        ? page * pageSize + pageRowCount
        : -1;
    return { paginationMeta, rowCount };
  }, [dataGridProps?.rowCount, dataGridProps?.paginationModel]);

  return (
    <List
      headerProps={{
        // title: translate('conditions.conditions'),
        sx: {
          display: 'none',
        },
      }}
      contentProps={{
        sx: {
          p: '0 !important',
        },
      }}
      breadcrumb={null}
      wrapperProps={{
        sx: {
          boxShadow: 'none',
          background: 'transparent',
          [`& .${gridClasses.root}`]: {
            [`& .${gridClasses.cell}`]: {
              py: 1,
            },
            [`& .${gridClasses.columnHeader}`]: {
              bgcolor: 'transparent',
            },
            [`& .${gridClasses.columnHeaders}`]: {
              bgcolor: 'transparent',
              borderBottomStyle: 'dashed',
            },
          },
        },
      }}
    >
      <DataGridPro
        {...dataGridProps}
        pagination
        paginationMeta={paginationMeta}
        rowCount={rowCount}
        autoHeight
        getRowHeight={() => 'auto'}
        pageSizeOptions={[25, 50, 100]}
        columnVisibilityModel={{
          note: upMd,
          clinicalStatus: upMd,
        }}
      />
    </List>
  );
}
