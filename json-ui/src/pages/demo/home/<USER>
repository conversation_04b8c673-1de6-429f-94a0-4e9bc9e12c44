import { IUser } from 'relic-ui';
import { useGetIdentity } from '@refinedev/core';
import React, { useState, useEffect } from 'react';

import {
  Grid,
  Card,
  Button,
  CardMedia,
  Container,
  Typography,
  CardContent,
  CardActions,
} from '@mui/material';

interface GridItem {
  id: number;
  title: string;
  description: string;
  image: string;
  demoUrl: string;
}

const gridItems: GridItem[] = [
  {
    id: 1,
    title: 'AI Survey Assistant',
    description:
      'An AI Assistant that can help you fill out surveys and questionnaires.',
    image: '/images/ai-survey.png',
    demoUrl: '',
  },
  {
    id: 2,
    title: 'AI Audit & Compliance Assistant',
    description:
      'Get help for audit and compliance-related questions from Florence - an AI Assistant.',
    image: '/images/ai-compliance.png',
    demoUrl: '',
  },
];
export const DemoScenarios: React.FC = () => {
  const { data: currentUser } = useGetIdentity<IUser>();
  const [localGridItems, setLocalGridItems] = useState<GridItem[]>(gridItems);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    localGridItems.forEach(item => {
      switch (item.id) {
        case 1:
          item.demoUrl = currentUser?.accessToken
            ? '/demo/register?scenario=patient_satisfaction_survey'
            : '/demo/demo-register?scenario=patient_satisfaction_survey';
          break;
        case 2:
          item.demoUrl = currentUser?.accessToken
            ? '/demo/register?scenario=regulatory_compliance_qa'
            : '/demo/demo-register?scenario=regulatory_compliance_qa';
          break;
      }
    });
    setLocalGridItems(localGridItems);
    setLoading(false);
  }, [currentUser, localGridItems]);

  return (
    <>
      {!loading && (
        <Container maxWidth="lg" sx={{ py: 4 }}>
          <Grid container spacing={3}>
            {localGridItems.map(
              item =>
                item.demoUrl && (
                  <Grid item key={item.id} xs={12} sm={6} md={6}>
                    <Card
                      sx={{
                        height: '100%',
                        display: 'flex',
                        flexDirection: 'column',
                      }}
                    >
                      <CardMedia
                        component="img"
                        image={item.image}
                        alt={item.title}
                        sx={{
                          objectFit: 'cover',
                        }}
                      />
                      <CardContent sx={{ flexGrow: 1 }}>
                        <Typography gutterBottom variant="h5" component="h2">
                          {item.title}
                        </Typography>
                        <Typography>{item.description}</Typography>
                      </CardContent>
                      <CardActions>
                        <Button
                          fullWidth
                          variant="contained"
                          href={item.demoUrl}
                          sx={{ mt: 'auto' }}
                        >
                          View Demo
                        </Button>
                      </CardActions>
                    </Card>
                  </Grid>
                ),
            )}
          </Grid>
        </Container>
      )}
    </>
  );
};
