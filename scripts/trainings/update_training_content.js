try {
    db.trainingModules.find({}).forEach(async ({ id, organizationId }) => {
        let count = await db.trainingContents.countDocuments({ moduleId: id, requestType: 'url' });
        while (count > 0) {
            printjson({ moduleId: id, count, message: 'updating type=url for requestType=url' });
            try {
                await db.trainingContents.updateMany({ moduleId: id, requestType: 'url' }, { $set: { type: 'url' }, $unset: { requestType: "" } });
            } catch (err) {
                if (err.code != 16500) {
                    throw err;
                }
            }
            count = await db.trainingContents.countDocuments({ moduleId: id, requestType: 'url' });
        }
        count = await db.trainingContents.countDocuments({ moduleId: id, organizationId: { $exists: false } });
        while (count > 0) {
            printjson({ moduleId: id, count, message: 'updating organizationId' });
            try {
                await db.trainingContents.updateMany({ moduleId: id }, { $set: { organizationId } });
            } catch (err) {
                if (err.code != 16500) {
                    throw err;
                }
            }
            count = await db.trainingContents.countDocuments({ moduleId: id, organizationId: { $exists: false } });
        }

        count = await db.trainingContents.countDocuments({ moduleId: id, requestType: { $exists: true } });
        while (count > 0) {
            printjson({ moduleId: id, count, message: 'removing requestType' });
            try {
                await db.trainingContents.updateMany({ moduleId: id }, { $unset: { requestType: "" } });
            } catch (err) {
                if (err.code != 16500) {
                    throw err;
                }
            }
            count = await db.trainingContents.countDocuments({ moduleId: id, requestType: { $exists: true } });
        }
    });
} catch (err) {
    printjson({ message: '[error] Update failed', err: err.details || err.message || err });
}