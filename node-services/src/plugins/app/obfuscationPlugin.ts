'use strict'

import { FastifyInstance, FastifyPluginAsync } from 'fastify'
import config from 'config'
import { faker } from '@faker-js/faker'
import { ObfuscationMap, RelicPatient } from 'relic-ui'
import { CleanMap, Field, ObfuscationDocument, PatientObfuscationResponse, PluginOptions } from '../../types/obfuscation'
import { InsertOneResult, UpdateResult, DeleteResult } from 'mongodb'

const _fields = [{ field: 'name' }, { field: 'name_1' }, { field: 'name_2' }, { field: 'name_3' }, { field: 'email', type: 'email' }]
const _regex = new RegExp(/^\w*_\d/)

/**
 * _generateObfuscationMap creates obfuscationMap for resource, returns obfuscationMap and cleanMap
 * @param resource : object for obfuscations
 * @param fields : fields or rules for obfuscation
 * @returns {obfuscationMap, cleanMap}
 *  - obfuscationMap: format {value: "<actual value>", obfuscatedValue: "<fake value>"}
 *  - cleanMap: format {obfuscatedValue: "<fake value>"}, to be saved in db collection
 */
const _generateObfuscationMap = function (fields: Field[], resource: RelicPatient): { obfuscationMap: ObfuscationMap; cleanMap: CleanMap } {
    let obfuscationMap = {} as ObfuscationMap
    let cleanMap = {} as CleanMap //to be saved in db
    ;(fields || []).forEach(({ field, type }) => {
        let value: string
        if (_regex.test(field)) {
            let values = (field || '').split('_') || []
            let n = Number(values.slice(-1))
            values.splice(-1)
            let f = values.join('_')
            value = ((resource[f] || '').split(' ') || [])[n - 1] || null
        } else {
            value = resource[field] || null
        }

        let obfuscatedValue = _fakeValue(type)
        obfuscationMap[field] = { value, obfuscatedValue }
        cleanMap[field] = { obfuscatedValue }
    })

    return { obfuscationMap, cleanMap }
}

const _fakeValue = function (type?: string): string {
    if (type == 'email') {
        return faker.internet.email({ provider: 't3st.com' })
    }
    return faker.string.alpha({ length: { min: 5, max: 20 } })
}

/**
 * _reconstructObfuscationMap reconstructs obfuscationMap with map values from db and provided resource
 * @param field
 * @param obfuscationMap
 * @param resource
 * @returns
 */
const _reconstructObfuscationMap = function (fields: Field[], obfuscationMap: ObfuscationMap, resource: RelicPatient): ObfuscationMap {
    ;(fields || []).forEach(({ field, type }) => {
        let value
        if (_regex.test(field)) {
            let values = (field || '').split('_') || []
            let n = Number(values.slice(-1))
            values.splice(-1)
            let f = values.join('_')
            value = ((resource[f] || '').split(' ') || [])[n - 1] || null
        } else {
            value = resource[field] || null
        }

        if (obfuscationMap.hasOwnProperty(field)) {
            obfuscationMap[field]['value'] = value
        } else {
            obfuscationMap[field] = { value, obfuscatedValue: _fakeValue(type) }
        }
    })

    return obfuscationMap
}

/** Obfuscation Plugin
 *  Plugin provides following decorator:
 *      - obfuscationApi : provides functions to Save, Get and Delete obfuscationMap
 * Plugin options:
 *      - obfuscationFields : fields need for obfuscation, format [{field: "string", type: "string"}]
 *          - field: refers to the resource field
 *          - type: optional, refers to fake value type, currently accepts 'email'.
 *                  for unknown types, random string will be returned
 *
 *  Sample usage:
 *      fastify.obfuscationApi.Save(patient) //saves obfuscationMap to db, returns the generated obfuscationMap
 *      fastify.obfuscationApi.Get({resourceType: "Patient", "id": "<patient id>", "organizationId": "<patient org id>" }) //returns obfuscationMap based on parameters
 *      fastify.obfuscationApi.Delete({resourceType: "Patient", "id": "<patient id>", "organizationId": "<patient org id>" }) //deletes obfuscationMap based on parameters
 *
 * @param {FastifyInstance} encapsulatedInstance - Fastify instance
 * @param {Object} options - Plugin options
 */
const obfuscationPlugin: FastifyPluginAsync = async (encapsulatedInstance: FastifyInstance, options: PluginOptions) => {
    try {
        let { obfuscationFields } = options
        const fields: Field[] = obfuscationFields || config.get('OBFUSCATION.FIELDS') || _fields
        const patientsCollection = await encapsulatedInstance.mongo.reliccare.db.collection<RelicPatient>('patients');

        const obfuscationApi = {
            collection: patientsCollection, //db collection
            fields: fields,
            Create: async function (resource: RelicPatient): Promise<{ obfuscationMap: ObfuscationMap; cleanMap: CleanMap }> {
                const { obfuscationMap, cleanMap } = _generateObfuscationMap(fields, resource)
                return { obfuscationMap, cleanMap }
            },
            /**
             * Saves or updates an obfuscation document in the MongoDB collection.
             * @param {ObfuscationDocument} resource - The obfuscation document to save or update.
             * @returns {Promise<InsertOneResult | UpdateResult>} - The response from the MongoDB operation.
             * @throws {Error} - Throws an error if the database operation fails.
             */
            Save: async function (resource: ObfuscationDocument): Promise<InsertOneResult | UpdateResult> {
                //save to collection
                const { resourceType, id, cleanMap } = resource
                const existingDocument: PatientObfuscationResponse = await this.collection.findOne({ resourceType, id })

                let response: InsertOneResult | UpdateResult
                if (existingDocument) {
                    // Update existing document
                    response = await this.collection.updateOne({ resourceType, id }, { $set: { obfuscationMap: cleanMap } })
                } else {
                    // Insert new document
                    response = await this.collection.insertOne({ resourceType, id, obfuscationMap: cleanMap })
                }
                return response
            },
            /**
             * Retrieves an obfuscation map for a given RelicPatient resource.
             *
             * @async
             * @function Get
             * @param {string} organizationId - The ID of the organization associated with the patient.
             * @param {RelicPatient} resource - The RelicPatient resource for which the obfuscation map is to be retrieved.
             * @returns {Promise<ObfuscationMap | {}>} - A promise that resolves to an obfuscation map if found, or an empty object otherwise.
             *
             * @description
             * This function looks up the obfuscation map in a MongoDB collection using the resource type, patient ID, and organization ID.
             * If an obfuscation map is found, it reconstructs the map with current resource data and returns it. If no map is found,
             * an empty object is returned.
             *
             */
            Get: async function (resource: RelicPatient): Promise<ObfuscationMap | {}> {
                const { resourceType, id } = resource
                let response = await this.collection.findOne({ resourceType, id })
                if (!response) {
                    return {}
                }
                let obfuscationMap = response.obfuscationMap
                const obfuscationResponse = obfuscationMap ? _reconstructObfuscationMap(this.fields, obfuscationMap, resource) : {}
                return obfuscationResponse
            },
            /**
             * Deletes an obfuscation map for a specified resource from the database.
             *
             * @async
             * @function Delete
             * @param {string} resourceType - The type of the FHIR resource
             * @param {string} id - The unique identifier of the resource.
             * @param {string} organizationId - The identifier of the organization associated with the resource.
             * @returns {Promise<DeleteResult>} - A promise that resolves to the result of the delete operation.
             *
             * @description
             * This function deletes an obfuscation map corresponding to a specific resource from the MongoDB collection.
             *
             */
            Delete: async function (resourceType: string, id: string, organizationId: string): Promise<DeleteResult> {
                return await this.collection.findOneAndDelete({ resourceType, id, organizationId });
            }
        }

        encapsulatedInstance.decorate('obfuscationApi', obfuscationApi)
    } catch (err) {
        encapsulatedInstance.log.error(err)
    }
}

export default obfuscationPlugin
