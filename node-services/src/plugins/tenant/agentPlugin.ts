//separated from agentPlugin.ts to easily manage changes
import { FastifyInstance, FastifyPluginAsync, FastifyPluginOptions } from 'fastify';
import { RelicAgent } from 'relic-ui';
// import { RelicPractitioner } from 'relic-ui';
import { RelicOrganization } from 'relic-ui';
import { v4 as uuidv4 } from 'uuid';
import patientPlugin from './patientPlugin';

const SystemAgentType:string = 'System Agent';
declare module 'fastify' {
    interface FastifyInstance {
        // Called via Routes, communicationPlugin and userPlugin.
        getSystemAgent(): Promise<RelicAgent>;
        // Called via Routes and communicationPlugin. 
        getRelicAgent(id: string, filter?: any): Promise<RelicAgent>;
        // Called via Routes and practitionerPlugin - practitionerPlugin to be Fixed.
        searchRelicAgents(filter?: any, mergeDefault?: boolean): Promise<RelicAgent[]>;
        // Only called via routes
        createRelicAgent(relicAgent: RelicAgent): Promise<RelicAgent>;
        // Called via Routes and communicationPlugin. 
        updateRelicAgent(id: string, updateRequest: RelicAgent): Promise<RelicAgent>;
        // Called via Routes only.
        deactivateRelicAgent(id: string): Promise<void>;
        // Called via Routes only.
        getAgentSummary(organization: RelicOrganization, agent: RelicAgent): string;
    }
}

const agentPlugin: FastifyPluginAsync = async (fastify: FastifyInstance, options: FastifyPluginOptions) => {    
    
    const relicAgentAggregate = [
        {
        "$lookup": {
            from: "practitioners",
            localField: "id",
            foreignField: "id",
            as: "relicPractitioner",
        },
        },
        {
        "$unwind": {
            path: "$relicPractitioner",
            preserveNullAndEmptyArrays: true,
        },
        },
        {
        "$project":
            {
            _id:0,
            id: 1,
            resourceType: 1,
            organizationId: 1,
            name: "$relicPractitioner.name",
            mobilePhone: "$relicPractitioner.mobilePhone",
            email: "$relicPractitioner.email",
            version: 1,
            type: 1,
            active: 1,
            role: 1,
            azureAssistantSetup: 1,
            relicAssistantSetup: 1,
            env: 1,
            publicData: 1,
            communicationIdentities:
                "$relicPractitioner.communicationIdentities",
            perspective: 1,
            },
        }
    ];

    // Temporarily adding methods to remove identityPlugin dependency on practitionerPlugin
    // const practitionersCollection = fastify.mongo.reliccare.db.collection("practitioners");
    const agentsCollection = fastify.mongo.reliccare.db.collection("agents");

    const isDuplicateAgent = async function(relicAgent: RelicAgent, excludePractitionerId?:string):Promise<boolean>{
        const { role, organizationId } = relicAgent;
        const filter = {
            "organizationId": organizationId,
            "role.display-role": role['display-role'],
        };
        const result = (await fastify.searchRelicAgents(filter, false) ?? []).filter(p => p.id != excludePractitionerId);
        return result.length > 0;
    };
    
    fastify.decorate("getSystemAgent", async function():Promise<RelicAgent>{
        // fastify.log.info('Decorated fastify properties inside getSystemAgent:', Object.keys(fastify));
        const defaultOrg: RelicOrganization = await fastify.orgService.getRelicCareOrganization();
        const aggregate = [
            ...relicAgentAggregate,
            {"$match": {type: SystemAgentType, organizationId: defaultOrg.id, active:true}}
        ]
        const result:RelicAgent[] = await agentsCollection.aggregate(aggregate).toArray() as RelicAgent[];
        if(result.length == 0){
            throw new Error(`System Agent not found`);
        }
        const agent:RelicAgent = result?.[0];
        for(let identity of agent?.communicationIdentities){
            identity = await fastify.acs.refreshIdentity(agent, identity);
        }
        return agent;
    });

    fastify.decorate("getRelicAgent", async function(id:string, filter:any = {}):Promise<RelicAgent>{
        const result:RelicAgent[] = await fastify.searchRelicAgents({id: id, ...filter});
        if(result.length == 0){
            throw fastify.httpErrors.notFound(`AI Assistant with id=${id} not found`);
        }
        return result?.[0];
    })
    
    fastify.decorate("searchRelicAgents", async function(filter:any = {}, mergeDefault: boolean = true):Promise<RelicAgent[]>{
        const relicCareOrganization = await fastify.orgService.getRelicCareOrganization();

        const aggregationPipeline: any[] = [...relicAgentAggregate];
        let combinedMatch: any = {};
        if(filter.id){
            combinedMatch.$or = [
                {"communicationIdentities.userId": filter.id},
                {"mobilePhone": filter.id},
                {"email": filter.id},
                {"id": filter.id},
            ];
            delete filter.id;
        }
        if (filter.active !== undefined) {
            combinedMatch.active = filter.active;
            delete filter.active;
        }

        if (filter._search) {
            combinedMatch.name = { $regex: filter._search, $options: 'i' };
            delete filter._search;
        }
        const remainingFilterKeys = Object.keys(filter).filter(key => !['_search', '_sort', '_offset', '_count', '$or'].includes(key));
        if (remainingFilterKeys.length > 0) {
            remainingFilterKeys.forEach(key => {
                combinedMatch[key] = filter[key];
            });
        }
        if (Object.keys(combinedMatch).length > 0) {
            aggregationPipeline.push({ "$match": combinedMatch });
        }
        if (filter._sort && Object.keys(filter._sort).length > 0) {
            aggregationPipeline.push({ "$sort": filter._sort });
        }
        if (filter._offset !== undefined) {
            aggregationPipeline.push({ "$skip": filter._offset });
        }
        if (filter._count !== undefined) {
            aggregationPipeline.push({ "$limit": filter._count });
        }

        //Get organization agents
        const orgAgents: RelicAgent[] = await agentsCollection.aggregate(aggregationPipeline).toArray() as RelicAgent[];

        if (mergeDefault) {
            //Get default agents by just switching OrganizationId in the existing aggregation pipeline
            const defaultOrg: RelicOrganization = relicCareOrganization;
            const matchCriteria = aggregationPipeline.findIndex((aggregationCriteria) => aggregationCriteria.$match);
            if (matchCriteria > -1) {
              aggregationPipeline[matchCriteria].$match.organizationId = defaultOrg.id;
            } else {
              aggregationPipeline.push({ $match: { organizationId: defaultOrg.id } });
            }
            const defaultAgents: RelicAgent[] = await agentsCollection.aggregate(aggregationPipeline).toArray() as RelicAgent[];

            //For each role[display-role] in default agents array, if there is a matching role[display-role] in orgAgents array, 
            //then remove the default agent from the array
            orgAgents.forEach((orgAgent) => {
                const index = defaultAgents.findIndex((agent) => agent.role['display-role'] === orgAgent.role['display-role']);
                if(index > -1){
                    defaultAgents.splice(index, 1);
                }
            });
            //combine the default agents array with the orgAgents array
            orgAgents.push(...defaultAgents);
        }
        //sort orgAgents by filter._sort field if present
        if (filter._sort && Object.keys(filter._sort).length > 0) {
            orgAgents.sort((a, b) => {
                const key = Object.keys(filter._sort)[0];
                return filter._sort[key] === 1 ? a[key] > b[key] ? 1 : -1 : a[key] < b[key] ? 1 : -1;
            });
        }
        return orgAgents;
    });

    fastify.decorate("createRelicAgent", async function (relicAgent: RelicAgent): Promise<RelicAgent> {
        if (await isDuplicateAgent(relicAgent)) {
            throw fastify.httpErrors.badRequest(`Assistant should have unique name and role within the organization.`);
        }
        relicAgent.id = uuidv4().toString();
        const agentPractitioner = fastify.toRelicPractitioner(relicAgent);
        //create and save agent practitioner
        await fastify.createPractitioner(agentPractitioner);
        // Remove practitioner identity fields from relicAgent before saving
        const { name, mobilePhone, email, communicationIdentities, ...agentOnlyFields } = relicAgent;
        await agentsCollection.findOneAndUpdate(
            {"id": relicAgent.id},
            {"$set": agentOnlyFields},
            {"upsert": true}
        );
        return relicAgent;
    });

    fastify.decorate("updateRelicAgent", async function(id:string, updateRequest:RelicAgent):Promise<RelicAgent>{
        const oldResource:RelicAgent = await fastify.getRelicAgent(id);
        
        if(updateRequest.name && await isDuplicateAgent(updateRequest, oldResource.id)){
            throw new Error(`Unable to rename agent. AI Agent with duplicate name ${updateRequest.name} exists`)
        }
        const updatedResource:RelicAgent = {...oldResource, ...updateRequest};
        //update relic resources
        await fastify.saveRelicPractitioner(fastify.toRelicPractitioner(updatedResource));
        // Remove practitioner identity fields from relicAgent before saving
        const { name, mobilePhone, email, communicationIdentities, ...agentOnlyFields } = updatedResource;
        await agentsCollection.findOneAndUpdate({"id": updatedResource.id}, {"$set": agentOnlyFields}, {"returnDocument": "after"});
        return updatedResource;
    });

    fastify.decorate('deactivateRelicAgent', async function(id:string):Promise<void>{
        const oldResource:RelicAgent = await fastify.getRelicAgent(id);
        if (oldResource && oldResource.type == "System Agent") {
            throw fastify.httpErrors.badRequest('System agent cannot be marked as inactive');
        }
        await agentsCollection.findOneAndUpdate({id: oldResource.id}, {"$set": {"active": false}});
    });

    fastify.decorate('getAgentSummary', function (organization: RelicOrganization, agent: RelicAgent): string {
        const template: string = organization.template?.agentSummary ?? 'Your name is {{name}}.';
        const templateString = Handlebars.compile(template);
        const replace = {
        name: agent.name,
        }
        const result = templateString(replace);
        return result;
    });


    fastify.register(patientPlugin, options);
}

export default agentPlugin;