import tap from "tap";
import { fastify } from "../index";
import { MedplumClient, MedplumClientOptions, EmailPasswordLoginRequest, ProfileResource } from '@medplum/core';
import { JSDOM } from 'jsdom';
import { BlockBlobClient, BlobServiceClient, generateBlobSASQueryParameters, BlobSASSignatureValues, StorageSharedKeyCredential, SASProtocol, ContainerSASPermissions } from '@azure/storage-blob';
import config from "config";
import fastifyOverride from "fastify-override";

// Create a new JSDOM instance
const { window } = new JSDOM();
// Mock global objects
global.window = window as unknown as Window & typeof globalThis;
global.document = window.document;
global.Element = window.Element;

const sessionStorageMock: Storage = {
  store: {} as Record<string, string>,
  getItem: function (key: string) {
    return this.store[key] || null;
  },
  setItem: function (key: string, value: string) {
    this.store[key] = value.toString();
  },
  removeItem: function (key: string) {
    delete this.store[key];
  },
  clear: function () {
    this.store = {};
  },
  get length() {
    return Object.keys(this.store).length;
  },
  key: function (index: number) {
    return Object.keys(this.store)[index] || null;
  },
};

global.sessionStorage = sessionStorageMock;

// Test Data for document services Testing.
const practitionerEmail: string = "<EMAIL>";
const practitionerPwd: string = "Z1sZ9N3F8tcycDDK";
const incorrectDocId: string = "6313ca8e-31fc-41bd-9e95-88c95652f46";
const fileName: string = "filename_rename_unit_test_case.pdf";
const organizationId: string = "6d8a08d3-1b3e-44f7-98c1-f36fee9b5a9a";
const docLanguage: string = "En-US";
const docType: string = "application/json";
const docHeader: number = 0;
const docFooter: number = 0;
let accessToken: any;
let createdDocumentId: any;
let blobServiceClient: BlobServiceClient | null = null;
let generatedDocumentId: string;
const medplumOptions: MedplumClientOptions = {
  verbose: true,
};
const medplum: MedplumClient = new MedplumClient(medplumOptions);
const loginToMedplum = async (
  medplum: MedplumClient,
  email: string,
  password: string
): Promise<ProfileResource> => {
  const loginRequest: EmailPasswordLoginRequest = {
    email,
    password,
  };
  const loginResponse = await medplum.startLogin(loginRequest);
  const userProfile = await medplum.processCode(loginResponse.code as string);
  accessToken = medplum.getActiveLogin()?.accessToken.toString();
  return userProfile;
};

const generateAzureStorageSASToken = async (containerName: string, permissions?: string) => {
  const accountName: string = config.get('AZURE.DOCUMENT_STORAGE_ACCOUNT_NAME') as string;
  const accountKey: string = config.get('AZURE.DOCUMENT_STORAGE_ACCOUNT_KEY') as string;
  const startsOn: Date = new Date();
  const expiresOn: Date = new Date(startsOn.getTime() + (10 * 60 * 1000)); // 10mins
  if (!permissions) {
    permissions = 'rwc'; // read,create,write
  }
  const blobServiceClient = BlobServiceClient.fromConnectionString(`DefaultEndpointsProtocol=https;AccountName=${accountName};AccountKey=${accountKey};EndpointSuffix=core.windows.net`);
  const containerClient = blobServiceClient.getContainerClient(containerName);
  await containerClient.createIfNotExists();

  const sasOptions: BlobSASSignatureValues = {
    containerName: containerName,
    permissions: ContainerSASPermissions.parse(permissions),
    startsOn: startsOn,
    expiresOn: expiresOn,
    protocol: SASProtocol.Https
  };

  return generateBlobSASQueryParameters(sasOptions, blobServiceClient.credential as StorageSharedKeyCredential).toString();
};

const fetchAzureToken = async (organizationId: string) => {
  try {
    const token: string = await generateAzureStorageSASToken(organizationId);
    return token;
  } catch (error) {
    console.error('Failed to fetch Azure token:', error);
    throw new Error('Failed to fetch Azure token');
  }
};

const uploadToAzureBlobStorage = async (file: Buffer, organizationId: string, documentId: string): Promise<string> => {
  try {
    if (!blobServiceClient) {
      const sasToken = await fetchAzureToken(organizationId);
      if (!sasToken) {
        throw new Error('Failed to get SAS token');
      }
      const account = 'facility'
      blobServiceClient = new BlobServiceClient(`https://${account}.blob.core.windows.net/?${sasToken}`);
    }

    const containerClient = blobServiceClient.getContainerClient(organizationId);
    const blockBlobClient: BlockBlobClient = containerClient.getBlockBlobClient(documentId);
    await blockBlobClient.uploadData(file, {
      blobHTTPHeaders: { blobContentType: 'application/pdf' },
      blockSize: 4 * 1024 * 1024,
      concurrency: 20,
    });
    return blockBlobClient.url;
  } catch (error) {
    console.error('Failed to upload file:', error);
    throw new Error('Failed to upload file. Please try again later.');
  }
};

tap.teardown(async () => {
  await fastify.close();
  process.exit(0);
}); //after running all tests
tap.before(async () => { //run before running all tests
  await loginToMedplum(medplum, practitionerEmail, practitionerPwd);
  await fastify.register(fastifyOverride, {
    override: {
      decorators: {
        decorate: {
          "createTranslatedDocument": async function (req:any, id: string, options: any){
            return {
              id: crypto.randomUUID(),
              organizationId: organizationId,
              language: options.targetLanguage.toLowerCase(),
              type: "application/pdf",
              url: '',
              filename: "test-pdf-only.pdf",
              documentId: "test-pdf-only.pdf",
              isUploaded: false,
              deleted: false,
              status: "pending",
              header: 0,
              footer: 0,
              access: [],
              translationType: options.translationType
          }
          }
        }
      }
    }
  })
});

tap.test("request the health check up, should return success", async (t: Tap.Test) => {
  const response = await fastify.inject({
    method: "GET",
    url: `/`,
    headers: ({
      'x-access-token': accessToken
    }),
  });

  t.equal(response.statusCode, 200);
  t.equal(response.body, 'Node Services');
});

tap.test("upload a document with POST `/api/documents/`, should return success", async (t) => {
  const extension = fileName.substring(fileName.lastIndexOf('.') + 1);
  const basename = fileName.substring(0, fileName.lastIndexOf('.'));
  const documentId = `${basename}_${crypto.randomUUID()}.${extension}`;
  const pdfContent = 'This is a sample PDF file content';
  const fileBuffer = Buffer.from(pdfContent);

  const documentUploadedUrl = await uploadToAzureBlobStorage(fileBuffer, organizationId, documentId);
  if (!documentUploadedUrl) {
    console.log('Failed to upload the document.');
    return;
  }

  const response = await fastify.inject({
    method: "POST",
    url: `/api/documents/`,
    payload: {
      "filename": fileName,
      "documentId": documentId,
      "url": documentUploadedUrl,
      "language": docLanguage,
      "type": docType,
      "header": docHeader,
      "footer": docFooter
    },
    headers: ({
      'x-access-token': accessToken,
    }),

  });

  t.equal(response.statusCode, 200);
  const responseBody = JSON.parse(response.body);
  createdDocumentId = responseBody.id;
  t.equal(responseBody.organizationId, organizationId);
  t.equal(responseBody.filename, fileName);
  t.equal(responseBody.isUploaded, true);
  t.equal(responseBody.status, 'done');
});

tap.test("get document list with GET `/api/documents/` using practitioner credentials, should return success", async (t: Tap.Test) => {
  const response = await fastify.inject({
    method: "GET",
    url: `/api/documents/`,
    headers: ({
      'x-access-token': accessToken
    }),
  });

  t.equal(response.statusCode, 200);
  const responseBody = JSON.parse(response.body);
  t.ok(Array.isArray(responseBody), "Response body should be an array");
  t.ok(responseBody.length >= 1, "At least one document should be returned");
  t.ok(responseBody[0].id, "First document should have an id");
});

tap.test("get a specific document with GET `/api/documents/{docId}/`, should return success", async (t) => {
  const uniqueQueryParam = `nocache=${new Date().getTime()}`;
  const response = await fastify.inject({
    method: "GET",
    url: `/api/documents/${createdDocumentId}?${uniqueQueryParam}`,
    headers: ({
      'x-access-token': accessToken,
    }),
  });

  t.equal(response.statusCode, 200);
  t.equal(response.statusMessage, 'OK');
  const responseBody = JSON.parse(response.body);
  t.equal(responseBody.id, createdDocumentId);
  t.equal(responseBody.sourceDocumentId, createdDocumentId);
  t.equal(responseBody.isUploaded, true);
  t.equal(responseBody.deleted, false);
});

tap.test("update a document details with `/api/documents/{id}`, should return success", async (t) => {
  const response = await fastify.inject({
    method: "PATCH",
    url: `/api/documents/${createdDocumentId}`,
    payload: {
      "filename": fileName,
      "header": 0,
      "footer": 0
    },
    headers: ({
      'x-access-token': accessToken,
      'x-organization-id': organizationId
    }),
  });

  t.equal(response.statusCode, 200);
  t.equal(response.statusMessage, 'OK');
  const responseBody = JSON.parse(response.body);
  t.equal(responseBody.id, createdDocumentId);
  t.equal(responseBody.filename, fileName);
  t.equal(responseBody.sourceDocumentId, createdDocumentId);
  t.equal(responseBody.isUploaded, true);
  t.equal(responseBody.deleted, false);
});

tap.test("generate a tranlsated document with POST `/api/documents/${createdDocumentId}/translate?targetLanguage`, should return success", async (t) => {
  const response = await fastify.inject({
    method: "POST",
    url: `/api/documents/${createdDocumentId}/translate?targetLanguage=my`,

    payload: JSON.stringify({
      "targetLanguage": "my",
      "translationType": "bilingual"
    }),
    headers: ({
      'x-access-token': accessToken,
      'Content-Type': 'application/json'
    }),
  });

  t.equal(response.statusCode, 200);
  t.equal(response.statusMessage, 'OK');
  const responseBody = JSON.parse(response.body);
  generatedDocumentId = responseBody.id;
  t.equal(responseBody.status, "pending");
});

tap.test("delete document with DELETE `/api/documents/{id}`, should return success", async (t) => {
  const response = await fastify.inject({
    method: "DELETE",
    url: `/api/documents/${createdDocumentId}`,
    headers: ({
      'x-access-token': accessToken,
    }),
  });
  t.equal(response.statusCode, 200);
  t.equal(response.statusMessage, 'OK');
});

tap.test("get document details after delete, with GET `/api/documents/{docId}/`, should return success and deleted=true", async (t) => {
  const uniqueQueryParam = `nocache=${new Date().getTime()}`;
  const response = await fastify.inject({
    method: "GET",
    url: `/api/documents/${createdDocumentId}?${uniqueQueryParam}`,
    headers: ({
      'x-access-token': accessToken,
    }),
  });
  t.equal(response.statusCode, 200);
  t.equal(response.statusMessage, 'OK');
  const responseBody = JSON.parse(response.body);
  t.equal(responseBody.id, createdDocumentId);
  t.equal(responseBody.organizationId, organizationId);
  t.equal(responseBody.isUploaded, true);
  t.equal(responseBody.deleted, true);
});

tap.test("get document details of unknown document with GET `/api/documents/{docId}/`, should return status 404", async (t) => {
  const response = await fastify.inject({
    method: "GET",
    url: `/api/documents/${incorrectDocId}`,
    headers: ({
      'x-access-token': accessToken,
    }),
  });

  const responseBody = JSON.parse(response.body);
  t.equal(responseBody.statusCode, 404);
  t.equal(responseBody.error, "Not Found");
  t.equal(responseBody.message, `document not found, id:${incorrectDocId}`);
});