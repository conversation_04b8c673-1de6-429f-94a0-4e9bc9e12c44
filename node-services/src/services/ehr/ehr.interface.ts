import { RelicCondition, RelicPatient } from 'relic-ui';
import { PccCarePlanFocus, PccFacility, PccPatientCarePlan } from 'relic-ui';
import { RelicCarePlan, RelicFocus } from '@/types/relicCarePlan';
import { Location } from 'relic-ui';

export interface EhrServiceInterface {
  /**
   * @param query request search query or filter
   */
  searchPatients(query: any): Promise<{ total: number; data: RelicPatient[] }>;
  /**
   *
   * @param id Patient Id
   * @param options optional filters and settings
   */
  getPatientById(id: string, options?: any): Promise<RelicPatient>;

  getPatientConditions(patientId: string, query: any): Promise<{ total: number; data: RelicCondition[] }>;

  getPatientCarePlans(
    patientId: string,
    query: any,
  ): Promise<{ total: number; data: PccPatientCarePlan[] | RelicCarePlan[] }>;

  getCarePlanFocuses(
    focusIds: number[],
    query: any,
  ): Promise<{ total: number; data: PccCarePlanFocus[] | RelicFocus[] }>;

  getFacilityById(id: string): Promise<PccFacility | Location>;

  searchFacilities(orgIds: string[], query: any): Promise<{ total: number; data: PccFacility[] | Location[] }>;
}
