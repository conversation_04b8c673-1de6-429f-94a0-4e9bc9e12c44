{"companies": [{"id": "9efcd1a0-d984-494a-a0cc-3518e87fbba6", "object": "company", "createdAt": "2023-11-24T23:36:57.930394278Z", "name": "Contoso Healthcare", "fallbackColor": "#EC4E20", "iconImageUrl": "https://lightout-portal.s3-accelerate.amazonaws.com/public/us-west-2_Ru9GCpktM/images/profile_pictures/c907a3a5-4dd4-4a10-b5e0-1719e359f395/da1d29ee-a493-41cf-aec3-da01640f2bdf", "leadInternalUserId": "c907a3a5-4dd4-4a10-b5e0-1719e359f395", "isPlaceholder": false, "assigneeInternalUserIds": ["c907a3a5-4dd4-4a10-b5e0-1719e359f395"]}, {"id": "50114cf4-f3c9-45a7-b9a4-57fdb9e0a9e4", "object": "company", "createdAt": "2023-09-12T08:50:04Z", "name": "Sunrise Senior Living - Santa Monica", "fallbackColor": "#EC4E20", "iconImageUrl": "", "leadInternalUserId": "c907a3a5-4dd4-4a10-b5e0-1719e359f395", "isPlaceholder": false, "assigneeInternalUserIds": ["c907a3a5-4dd4-4a10-b5e0-1719e359f395"]}, {"id": "be25bb51-3366-4474-b0f3-e8efccc0af05", "object": "company", "createdAt": "2023-09-12T08:55:26Z", "name": "San Diego Post-Acute Center", "fallbackColor": "#AF4D98", "iconImageUrl": "", "leadInternalUserId": "c907a3a5-4dd4-4a10-b5e0-1719e359f395", "isPlaceholder": false, "assigneeInternalUserIds": ["c907a3a5-4dd4-4a10-b5e0-1719e359f395"]}, {"id": "316d1a21-ac47-464c-970e-4fb0ca3b92c2", "object": "company", "createdAt": "2023-11-17T08:14:15.451384102Z", "name": "hemant", "fallbackColor": "#EC4E20", "iconImageUrl": "", "leadInternalUserId": "c907a3a5-4dd4-4a10-b5e0-1719e359f395", "isPlaceholder": false, "assigneeInternalUserIds": ["c907a3a5-4dd4-4a10-b5e0-1719e359f395"]}, {"id": "bc9c7e46-f0f1-481a-a606-f57069035d2b", "object": "company", "createdAt": "2023-08-04T02:57:43Z", "name": "Austin Care", "fallbackColor": "#7CAE7A", "iconImageUrl": "https://lightout-portal.s3-accelerate.amazonaws.com/public/us-west-2_Ru9GCpktM/images/profile_pictures/c907a3a5-4dd4-4a10-b5e0-1719e359f395/e2938e8e-e2a2-48c6-a069-315c3fd8ef9d", "leadInternalUserId": "c907a3a5-4dd4-4a10-b5e0-1719e359f395", "isPlaceholder": false, "assigneeInternalUserIds": ["c907a3a5-4dd4-4a10-b5e0-1719e359f395"]}, {"id": "b7fefcaa-a30a-4e1d-b0f2-f4181fee5367", "object": "company", "createdAt": "2023-09-08T06:17:37Z", "name": "Relic Care, Inc", "fallbackColor": "#7CAE7A", "iconImageUrl": "", "leadInternalUserId": "c907a3a5-4dd4-4a10-b5e0-1719e359f395", "isPlaceholder": false, "assigneeInternalUserIds": ["c907a3a5-4dd4-4a10-b5e0-1719e359f395"]}], "organizations": [{"id": "6d8a08d3-1b3e-44f7-98c1-f36fee9b5a9a", "resourceType": "Organization", "type": "prov", "active": true, "name": "Relic Care, Inc", "website": "https://reliccare.com", "location": [{"id": "6666412b-a5e8-44bf-a5ea-ddc46e8a2453", "resourceType": "Location", "status": "active", "name": "Relic Care, Inc", "phone": "+12138749039", "fax": "+12136259304"}], "copilotCompany": {"id": "b7fefcaa-a30a-4e1d-b0f2-f4181fee5367", "name": "Relic Care, Inc", "iconImageUrl": "https://sandiegopostacutecenter.com/assets/logo.png", "fallbackColor": "#7CAE7A", "leadInternalUserId": "c907a3a5-4dd4-4a10-b5e0-1719e359f395"}, "fhirStore": {"ClientApplication": {"id": "872fe0a8-fda8-49c7-b9ef-0bfadb8d509b", "resourceType": "ClientApplication", "compartment": "Project/51b61d21-e073-4cfa-adbc-e4cd247e1fec"}, "defaultLanguage": {"system": "http://hl7.org/fhir/ValueSet/languages", "code": "en-US", "display": "English - US"}}, "endpoints": [{"service": ["chat", "voip"], "endpoint": "https://yusuke-test-3.unitedstates.communication.azure.com/", "provider": "Azure Communication Service", "defaultAgents": [{"id": "acs id of the agent", "type": "Patient Agent"}, {"id": "acs id of the agent", "type": "Staff Agent"}]}], "template": {"patientSummary": "{{name}} is a {{age}} old {{sex}} suffering from {{conditions}}, and under treatment at {{facilityName}} as inpatient"}}, {"id": "7760ffcd-64d2-43df-9548-055b257dfaee", "resourceType": "Organization", "type": "prov", "active": true, "name": "Sunrise Senior Living", "website": "https://sunriseseniorliving.com", "location": [{"id": "112f4240-3f61-42e1-996e-0a550cdb94dd", "resourceType": "Location", "status": "active", "name": "Sunrise Senior Living - Santa Monica", "phone": "+13103849382", "fax": "+13103849383"}], "copilotCompany": {"id": "50114cf4-f3c9-45a7-b9a4-57fdb9e0a9e4", "name": "Sunrise Senior Living", "iconImageUrl": "https://sunriseseniorliving.com/assets/logo.png", "fallbackColor": "blue", "leadInternalUserId": "c907a3a5-4dd4-4a10-b5e0-1719e359f395"}, "fhirStore": {"ClientApplication": {"id": "1940c3ef-8b6f-4ac0-a66c-d6aca3bff786", "resourceType": "ClientApplication", "compartment": "Project/ae9023d0-0710-4398-bec4-74cba82d7718"}, "defaultLanguage": {"system": "http://hl7.org/fhir/ValueSet/languages", "code": "en-US", "display": "English - US"}}, "endpoints": [{"service": ["chat", "voip"], "endpoint": "https://yusuke-test-3.unitedstates.communication.azure.com/", "provider": "Azure Communication Service", "defaultAgents": [{"id": "acs id of the agent", "type": "Patient Agent"}, {"id": "acs id of the agent", "type": "Staff Agent"}]}, {"service": ["patient-portal"], "endpoint": "https://sunrise.myrelic.care/", "provider": "Relic Care"}], "template": {"patientSummary": "{{name}} is a {{age}} old {{sex}} suffering from {{conditions}}, and under treatment at {{facilityName}} as inpatient"}}, {"id": "e759d587-9810-4c38-8c8e-acd418d940b6", "resourceType": "Organization", "type": "prov", "active": true, "name": "San Diego Post-Acute Center", "website": "https://sdpostacute.com/", "location": [{"id": "3b38bec4-405b-4df8-9391-a3234991edf8", "resourceType": "Location", "status": "active", "name": "San Diego Post-Acute Center", "phone": "+16193848292", "fax": "+16199338201"}], "copilotCompany": {"id": "be25bb51-3366-4474-b0f3-e8efccc0af05", "name": "San Diego Post-Acute Center", "iconImageUrl": "https://cdn-yoloboulder-media.nyc3.digitaloceanspaces.com/sites/38/2022/02/18094113/sandiego-postacute-logo-favi-150x150.png", "fallbackColor": "#AF4D98", "leadInternalUserId": "c907a3a5-4dd4-4a10-b5e0-1719e359f395"}, "fhirStore": {"ClientApplication": {"id": "872fe0a8-fda8-49c7-b9ef-0bfadb8d509b", "resourceType": "ClientApplication", "compartment": "Project/51b61d21-e073-4cfa-adbc-e4cd247e1fec"}, "defaultLanguage": {"system": "http://hl7.org/fhir/ValueSet/languages", "code": "en-US", "display": "English - US"}}, "endpoints": [{"service": ["chat", "voip"], "endpoint": "https://yusuke-test-3.unitedstates.communication.azure.com/", "provider": "Azure Communication Service", "defaultAgents": [{"id": "acs id of the agent", "type": "Patient Agent"}, {"id": "acs id of the agent", "type": "Staff Agent"}]}, {"service": ["patient-portal"], "endpoint": "https://sdpostacute.myrelic.care/", "provider": "Relic Care"}], "template": {"patientSummary": "{{name}} is a {{age}} old {{sex}} suffering from {{conditions}}, and under treatment at {{facilityName}} as inpatient"}}, {"id": "d72577f8-ef90-4c3a-82bb-a1e9c2258ea2", "resourceType": "Organization", "type": "prov", "active": true, "name": "Contoso Healthcare", "website": "https://contosohealthcare.com/", "location": [{"id": "c6217af2-320b-49b2-a636-22c0677859f1", "resourceType": "Location", "status": "active", "name": "Contoso Healthare - Glendale", "phone": "+18879929937", "fax": "+13438939302"}], "copilotCompany": {"id": "9efcd1a0-d984-494a-a0cc-3518e87fbba6", "name": "Contoso Healthare", "iconImageUrl": "https://lightout-portal.s3-accelerate.amazonaws.com/public/us-west-2_Ru9GCpktM/images/profile_pictures/c907a3a5-4dd4-4a10-b5e0-1719e359f395/da1d29ee-a493-41cf-aec3-da01640f2bdf", "fallbackColor": "#AF4D98", "leadInternalUserId": "c907a3a5-4dd4-4a10-b5e0-1719e359f395"}, "fhirStore": {"ClientApplication": {"id": "872fe0a8-fda8-49c7-b9ef-0bfadb8d509b", "resourceType": "ClientApplication", "compartment": "Project/51b61d21-e073-4cfa-adbc-e4cd247e1fec"}, "defaultLanguage": {"system": "http://hl7.org/fhir/ValueSet/languages", "code": "en-US", "display": "English - US"}}, "endpoints": [{"service": ["chat", "voip"], "endpoint": "https://yusuke-test-3.unitedstates.communication.azure.com/", "provider": "Azure Communication Service", "defaultAgents": [{"id": "acs id of the agent", "type": "Patient Agent"}, {"id": "acs id of the agent", "type": "Staff Agent"}]}, {"service": ["patient-portal"], "endpoint": "https://contoso.myrelic.care/", "provider": "Relic Care"}], "template": {"patientSummary": "{{name}} is a {{age}} old {{sex}} suffering from {{conditions}}, and under treatment at {{facilityName}} as inpatient"}}], "patients": [{"id": "685556e6-3ea6-449e-8230-695469e61e64", "organizationId": "7760ffcd-64d2-43df-9548-055b257dfaee", "resourceType": "Patient", "active": true, "name": "<PERSON>", "email": "<EMAIL>", "emailVerified": true, "mobilePhone": "**********", "homePhone": "", "gender": "Male", "birthDate": "1949-02-20", "maritalStatus": "Married", "summary": "I am a 74 years old male suffering from History of hypertension (situation), Chronic Obstructive Pulmonary Disease, Diabetes, and under treatment at ABC Hospital as inpatient.", "link": [{"id": "685556e6-3ea6-449e-8230-695469e61e64", "resourceType": "<PERSON><PERSON><PERSON><PERSON>", "relationship": {"system": "http://terminology.hl7.org/CodeSystem/v3-RoleCode", "code": "WIFE", "display": "wife"}, "use": "official", "name": "<PERSON>", "email": "<EMAIL>", "mobilePhone": "**********", "homePhone": "", "gender": "Female", "birthDate": "1950-02-20", "communicationLanguage": [{"system": "http://hl7.org/fhir/ValueSet/languages", "code": "en-US", "display": "English - US", "preferred": true}]}], "communicationLanguage": [{"system": "http://hl7.org/fhir/ValueSet/languages", "code": "en-US", "display": "English - US", "preferred": true}], "communicationIdentities": [{"userId": "685556e6-3ea6-449e-8230-695469e61e64", "service": ["chat", "voip"], "endpoint": "https://yusuke-test-3.unitedstates.communication.azure.com", "secret": {"token": "eyJhbGciOiJSUzI1NiIsImtpZCI6IjVFODQ4MjE0Qzc3MDczQUU1QzJCREU1Q0NENTQ0ODlEREYyQzRDODQiLCJ4NXQiOiJYb1NDRk1kd2M2NWNLOTVjelZSSW5kOHNUSVEiLCJ0eXAiOiJKV1QifQ.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Vczeb3wVZ37CEWd3O88sp6Hj1w7EvEqoawyqFq97b_tl_IuARPDcpadmB4Y2FArqN19nFVaTIihRoDkSWAk7QxN3FrQuo7XozaVlwe8oCUNCxKBxzoPaeHvHL9EaM9fQM1G6y_piOSGrk6I-fdchA8Co9jBdE4iKyZ1awhkCw6onodAA3gtBLgHsMs3e9ZfaX-8WjDwo3cimpATEZ_moH1cAVfCr_E1GjDehkLta-y1xKGrRWDE_MmMD9FHvMSgWcXkmyFfLu47wk8mnM_vOM-urPqfyNRfDJJRHwc0ZPY3ZoVlLvSZGLdGYyz8zIEvr9CH8zN_VCZCeSeyBwMMCAA", "expiresOn": "2023-11-29T03:09:23.138Z"}, "threads": [{"threadId": "threadId1", "threadSubject": "default"}]}, {"userId": "685556e6-3ea6-449e-8230-695469e61e65", "service": ["chat", "voip"], "endpoint": "https://another-server.unitedstates.communication.azure.com", "secret": {"token": "eyJhbGciOiJSUzI1NiIsImtpZCI6IjVFODQ4MjE0Qzc3MDczQUU1QzJCREU1Q0NENTQ0ODlEREYyQzRDODQiLCJ4NXQiOiJYb1NDRk1kd2M2NWNLOTVjelZSSW5kOHNUSVEiLCJ0eXAiOiJKV1QifQ.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Vczeb3wVZ37CEWd3O88sp6Hj1w7EvEqoawyqFq97b_tl_IuARPDcpadmB4Y2FArqN19nFVaTIihRoDkSWAk7QxN3FrQuo7XozaVlwe8oCUNCxKBxzoPaeHvHL9EaM9fQM1G6y_piOSGrk6I-fdchA8Co9jBdE4iKyZ1awhkCw6onodAA3gtBLgHsMs3e9ZfaX-8WjDwo3cimpATEZ_moH1cAVfCr_E1GjDehkLta-y1xKGrRWDE_MmMD9FHvMSgWcXkmyFfLu47wk8mnM_vOM-urPqfyNRfDJJRHwc0ZPY3ZoVlLvSZGLdGYyz8zIEvr9CH8zN_VCZCeSeyBwMMCAA", "expiresOn": "2023-11-29T03:09:23.138Z"}, "threads": [{"threadId": "threadId2", "threadSubject": "default"}]}], "managingOrganization": {"reference": "Organization/7760ffcd-64d2-43df-9548-055b257dfaee", "display": "Sunrise Senior Living"}, "location": {"reference": "Location/112f4240-3f61-42e1-996e-0a550cdb94dd", "display": "Sunrise Senior Living - Santa Monica"}, "conditions": [{"id": "9ca95b8f-3a15-4f14-8c47-c2bb188b0729", "resourceType": "Condition", "clinicalStatus": "active", "verificationStatus": "confirmed", "code": {"text": "Moderate major depression"}, "onsetDateTime": "2019-02-20", "abatementDateTime": "2023-08-15", "note": "<PERSON><PERSON> has been diagnosed with moderate major depression"}, {"id": "d7a6e1ef-4c26-416d-8701-d921c934e728", "resourceType": "Condition", "clinicalStatus": "active", "verificationStatus": "confirmed", "code": {"text": "History of hypertension (situation)"}, "onsetDateTime": "2016-07-17", "note": "Patient has hypertension"}], "allergyIntolerances": [{"id": "cf8ca870-5e5e-424d-8e44-0904a68ea3e2", "resourceType": "AllergyIntolerance", "clinicalStatus": "active", "verificationStatus": "confirmed", "code": {"text": "Cashew nuts"}, "type": "allergy", "category": "food", "onsetDateTime": "2019-02-20", "abatementDateTime": "2023-08-15", "note": "Patient reports that the allergy was confirmed by a doctor"}, {"id": "16a62bd7-0680-4d60-9c03-9ff6ef04abdf", "resourceType": "AllergyIntolerance", "clinicalStatus": "active", "verificationStatus": "confirmed", "code": {"text": "Penicillin G"}, "category": "medication", "onsetDateTime": "2015-02-21"}], "obfuscationMap": {"name": {"value": "<PERSON>", "obfuscatedValue": "<PERSON><PERSON><PERSON>"}, "name_1": {"value": "<PERSON>", "obfuscatedValue": "Pskwe"}, "name_2": {"value": "<PERSON>", "obfuscatedValue": "<PERSON><PERSON><PERSON>"}, "name_3": {"value": null, "obfuscatedValue": "Ksdhwo"}, "email": {"value": "<EMAIL>", "obfuscatedValue": "<EMAIL>"}}}, {"id": "c7ede6e9-4782-40dc-9766-5b8f4534fc86", "organizationId": "7760ffcd-64d2-43df-9548-055b257dfaee", "resourceType": "Patient", "active": true, "name": "<PERSON>", "email": "<EMAIL>", "emailVerified": false, "mobilePhone": "", "homePhone": "", "gender": "Female", "birthDate": "1953-01-01", "maritalStatus": "Widowed", "link": [], "summary": "I am a 70 years old female suffering from Obesity, Gall Bladder Stone, and under treatment at ABC Hospital as inpatient.", "communicationLanguage": [{"system": "http://hl7.org/fhir/ValueSet/languages", "code": "en-US", "display": "English - US", "preferred": true}], "communicationIdentities": [{"userId": "685556e6-3ea6-449e-8230-695469e61e66", "service": ["chat", "voip"], "endpoint": "https://yusuke-test-3.unitedstates.communication.azure.com", "secret": {"token": "eyJhbGciOiJSUzI1NiIsImtpZCI6IjVFODQ4MjE0Qzc3MDczQUU1QzJCREU1Q0NENTQ0ODlEREYyQzRDODQiLCJ4NXQiOiJYb1NDRk1kd2M2NWNLOTVjelZSSW5kOHNUSVEiLCJ0eXAiOiJKV1QifQ.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Vczeb3wVZ37CEWd3O88sp6Hj1w7EvEqoawyqFq97b_tl_IuARPDcpadmB4Y2FArqN19nFVaTIihRoDkSWAk7QxN3FrQuo7XozaVlwe8oCUNCxKBxzoPaeHvHL9EaM9fQM1G6y_piOSGrk6I-fdchA8Co9jBdE4iKyZ1awhkCw6onodAA3gtBLgHsMs3e9ZfaX-8WjDwo3cimpATEZ_moH1cAVfCr_E1GjDehkLta-y1xKGrRWDE_MmMD9FHvMSgWcXkmyFfLu47wk8mnM_vOM-urPqfyNRfDJJRHwc0ZPY3ZoVlLvSZGLdGYyz8zIEvr9CH8zN_VCZCeSeyBwMMCAA", "expiresOn": "2023-11-29T03:09:23.138Z"}, "threads": [{"threadId": "threadId3", "threadSubject": "default"}]}, {"userId": "685556e6-3ea6-449e-8230-695469e61e67", "service": ["chat", "voip"], "endpoint": "https://another-server.unitedstates.communication.azure.com", "secret": {"token": "eyJhbGciOiJSUzI1NiIsImtpZCI6IjVFODQ4MjE0Qzc3MDczQUU1QzJCREU1Q0NENTQ0ODlEREYyQzRDODQiLCJ4NXQiOiJYb1NDRk1kd2M2NWNLOTVjelZSSW5kOHNUSVEiLCJ0eXAiOiJKV1QifQ.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Vczeb3wVZ37CEWd3O88sp6Hj1w7EvEqoawyqFq97b_tl_IuARPDcpadmB4Y2FArqN19nFVaTIihRoDkSWAk7QxN3FrQuo7XozaVlwe8oCUNCxKBxzoPaeHvHL9EaM9fQM1G6y_piOSGrk6I-fdchA8Co9jBdE4iKyZ1awhkCw6onodAA3gtBLgHsMs3e9ZfaX-8WjDwo3cimpATEZ_moH1cAVfCr_E1GjDehkLta-y1xKGrRWDE_MmMD9FHvMSgWcXkmyFfLu47wk8mnM_vOM-urPqfyNRfDJJRHwc0ZPY3ZoVlLvSZGLdGYyz8zIEvr9CH8zN_VCZCeSeyBwMMCAA", "expiresOn": "2023-11-29T03:09:23.138Z"}, "threads": [{"threadId": "threadId4", "threadSubject": "default"}]}], "managingOrganization": {"reference": "Organization/7760ffcd-64d2-43df-9548-055b257dfaee", "display": "Sunrise Senior Living"}, "location": {"reference": "Location/112f4240-3f61-42e1-996e-0a550cdb94dd", "display": "Sunrise Senior Living - Santa Monica"}, "conditions": [{"id": "e92cf3b0-d467-4f53-b34f-a1dcf5641f83", "resourceType": "Condition", "clinicalStatus": "active", "verificationStatus": "confirmed", "code": {"text": "Gall Bladder Stone"}, "onsetDateTime": "2019-02-20", "note": null}, {"id": "d8f5ab69-6365-40fb-8ec7-9fe626b330a5", "resourceType": "Condition", "clinicalStatus": "active", "verificationStatus": "confirmed", "code": {"text": "Obesity"}, "onsetDateTime": "2016-07-17", "note": null}], "obfuscationMap": {"name": {"value": "<PERSON>", "obfuscatedValue": "<PERSON><PERSON><PERSON>"}, "name_1": {"value": "<PERSON>", "obfuscatedValue": "Pskwe"}, "name_2": {"value": "<PERSON>", "obfuscatedValue": "<PERSON><PERSON><PERSON>"}, "name_3": {"value": null, "obfuscatedValue": "Ksdhwo"}, "email": {"value": "<EMAIL>", "obfuscatedValue": "<EMAIL>"}}}], "conditions": [{"id": "9ca95b8f-3a15-4f14-8c47-c2bb188b0729", "resourceType": "Condition", "clinicalStatus": "active", "verificationStatus": "confirmed", "code": {"text": "Moderate major depression"}, "onsetDateTime": "2019-02-20", "abatementDateTime": "2023-08-15", "note": "<PERSON><PERSON> has been diagnosed with moderate major depression", "patientId": "685556e6-3ea6-449e-8230-695469e61e64"}, {"id": "d7a6e1ef-4c26-416d-8701-d921c934e728", "resourceType": "Condition", "clinicalStatus": "active", "verificationStatus": "confirmed", "code": {"text": "History of hypertension (situation)"}, "onsetDateTime": "2016-07-17", "note": "Patient has hypertension", "patientId": "685556e6-3ea6-449e-8230-695469e61e64"}, {"id": "e92cf3b0-d467-4f53-b34f-a1dcf5641f83", "resourceType": "Condition", "clinicalStatus": "active", "verificationStatus": "confirmed", "code": {"text": "Gall Bladder Stone"}, "onsetDateTime": "2019-02-20", "note": null, "patientId": "c7ede6e9-4782-40dc-9766-5b8f4534fc86"}, {"id": "d8f5ab69-6365-40fb-8ec7-9fe626b330a5", "resourceType": "Condition", "clinicalStatus": "active", "verificationStatus": "confirmed", "code": {"text": "Obesity"}, "onsetDateTime": "2016-07-17", "note": null, "patientId": "c7ede6e9-4782-40dc-9766-5b8f4534fc86"}], "links": [{"id": "685556e6-3ea6-449e-8230-695469e61e64", "resourceType": "<PERSON><PERSON><PERSON><PERSON>", "relationship": {"system": "http://terminology.hl7.org/CodeSystem/v3-RoleCode", "code": "WIFE", "display": "wife"}, "use": "official", "name": "<PERSON>", "email": "<EMAIL>", "mobilePhone": "**********", "homePhone": "", "gender": "Female", "birthDate": "1950-02-20", "communicationLanguage": [{"system": "http://hl7.org/fhir/ValueSet/languages", "code": "en-US", "display": "English - US", "preferred": true}], "patientId": "685556e6-3ea6-449e-8230-695469e61e64"}], "locations": [{"id": "6666412b-a5e8-44bf-a5ea-ddc46e8a2453", "resourceType": "Location", "status": "active", "name": "Relic Care, Inc", "phone": "+12138749039", "fax": "+12136259304", "organizationId": "6d8a08d3-1b3e-44f7-98c1-f36fee9b5a9a"}, {"id": "112f4240-3f61-42e1-996e-0a550cdb94dd", "resourceType": "Location", "status": "active", "name": "Sunrise Senior Living - Santa Monica", "phone": "+13103849382", "fax": "+13103849383", "organizationId": "7760ffcd-64d2-43df-9548-055b257dfaee"}, {"id": "3b38bec4-405b-4df8-9391-a3234991edf8", "resourceType": "Location", "status": "active", "name": "San Diego Post-Acute Center", "phone": "+16193848292", "fax": "+16199338201", "organizationId": "be25bb51-3366-4474-b0f3-e8efccc0af05"}, {"id": "c6217af2-320b-49b2-a636-22c0677859f1", "resourceType": "Location", "status": "active", "name": "Contoso Healthare - Glendale", "phone": "+18879929937", "fax": "+13438939302", "organizationId": "d72577f8-ef90-4c3a-82bb-a1e9c2258ea2"}], "allergyIntolerances": [{"id": "cf8ca870-5e5e-424d-8e44-0904a68ea3e2", "resourceType": "AllergyIntolerance", "clinicalStatus": "active", "verificationStatus": "confirmed", "code": {"text": "Cashew nuts"}, "type": "allergy", "category": "food", "onsetDateTime": "2019-02-20", "abatementDateTime": "2023-08-15", "note": "Patient reports that the allergy was confirmed by a doctor", "patientId": "685556e6-3ea6-449e-8230-695469e61e64"}, {"id": "16a62bd7-0680-4d60-9c03-9ff6ef04abdf", "resourceType": "AllergyIntolerance", "clinicalStatus": "active", "verificationStatus": "confirmed", "code": {"text": "Penicillin G"}, "category": "medication", "onsetDateTime": "2015-02-21", "patientId": "685556e6-3ea6-449e-8230-695469e61e64"}], "threads": [{"id": "659fa00b402cf8831c07e55a", "threadId": "19:MPdE9uujCAP8lcXCvJ88vd01jVpje77dCzYTdASlkgk1@thread.v2", "endpoint": "https://yusuke-test-3.unitedstates.communication.azure.com/", "threadSubject": {"type": "Questionnaire", "organizationId": "7760ffcd-64d2-43df-9548-055b257dfaee", "questionnaireId": "659fa00b402cf8831c07e55b"}, "threadTopic": "This field will come from ACS where we will continue to maintain thread topic.", "participants": [{"id": {"communicationUserId": "8:acs:8ca4fbc2-e63c-4283-a5b7-feb83074370e_0000001b-57e2-3536-ac00-343a0d000c9a"}}, {"id": {"communicationUserId": "8:acs:8ca4fbc2-e63c-4283-a5b7-feb83074370e_0000001d-76f3-8f70-80f5-8b3a0d006eea"}}], "status": "active"}, {"id": "659fa00b402cf8831c07e55c", "threadId": "19:MPdE9uujCAP8lcXCvJ88vd01jVpje77dCzYTdASlkgk2@thread.v2", "endpoint": "https://yusuke-test-3.unitedstates.communication.azure.com/", "threadSubject": {"type": "Questionnaire", "questionnaireId": "659fa00b402cf8831c07e55d", "organizationId": "7760ffcd-64d2-43df-9548-055b257dfaee"}, "threadTopic": "This field will come from ACS where we will continue to maintain thread topic.", "participants": [{"id": {"communicationUserId": "8:acs:8ca4fbc2-e63c-4283-a5b7-feb83074370e_0000001b-57e2-3536-ac00-343a0d000c9a"}}, {"id": {"communicationUserId": "8:acs:8ca4fbc2-e63c-4283-a5b7-feb83074370e_0000001d-76f3-8f70-80f5-8b3a0d006eea"}}], "status": "close"}, {"id": "659fa00b402cf8831c07e55e", "threadId": "19:MPdE9uujCAP8lcXCvJ88vd01jVpje77dCzYTdASlkgk3@thread.v2", "endpoint": "https://yusuke-test-3.unitedstates.communication.azure.com/", "threadSubject": {"type": "<PERSON><PERSON><PERSON>", "organizationId": "7760ffcd-64d2-43df-9548-055b257dfaee"}, "threadTopic": {"currentAgentAcsId": "8:acs:8ca4fbc2-e63c-4283-a5b7-feb83074370e_0000001d-76f3-8f70-80f5-8b3a0d006eea", "threadOwner": {"resourceType": "Patient", "id": "659fa00b402cf8831c07e561"}, "organizationId": "7760ffcd-64d2-43df-9548-055b257dfaee"}, "participants": [{"id": {"communicationUserId": "8:acs:8ca4fbc2-e63c-4283-a5b7-feb83074370e_0000001b-57e2-3536-ac00-343a0d000c9a"}}, {"id": {"communicationUserId": "8:acs:8ca4fbc2-e63c-4283-a5b7-feb83074370e_0000001d-76f3-8f70-80f5-8b3a0d006eea"}}], "status": "active"}], "files": [{"id": "aaf0ec03-8f54-459b-b30c-71b05ae7aa3e", "object": "file", "channelId": "us-west-2_Ru9GCpktM/50114cf4-f3c9-45a7-b9a4-57fdb9e0a9e4", "fields": {"linkUrl": "", "path": "Admission Packet.pdf", "name": "Admission Packet.pdf"}, "downloadUrl": "https://ridebill-userfiles-mobilehub-310871815.s3-accelerate.amazonaws.com/protected/us-west-2%3A9e2e3b34-f3cd-4a11-8be3-73b349064ad3/files/2024-01-18T20%3A08%3A05.811Z/Admission%20Packet.pdf"}, {"id": "ba31a06f-fd1e-43fb-880e-f54fd105a01e", "object": "file", "channelId": "us-west-2_Ru9GCpktM/50114cf4-f3c9-45a7-b9a4-57fdb9e0a9e4", "fields": {"linkUrl": "", "path": "Fall Risk - <PERSON>.pdf", "name": "Fall Risk - <PERSON>.pdf"}, "downloadUrl": "https://ridebill-userfiles-mobilehub-310871815.s3-accelerate.amazonaws.com/protected/us-west-2%3A9e2e3b34-f3cd-4a11-8be3-73b349064ad3/files/2024-01-18T20%3A09%3A32.950Z/Fall%20Risk%20-%20John%20Hopkins.pdf"}], "facilities": [{"id": "0d34f0b5-018b-4612-b26a-de82465fc48f", "facId": 50, "facilityCode": "6640", "facilityName": "FACILITY_50", "active": true, "addressLine1": "389 Skidmore Road", "addressLine2": "P.O.Box # 21678-9087", "bedCount": 150, "billingStyleCountry": "USA", "city": "<PERSON><PERSON><PERSON>", "country": "USA", "countryId": 100, "emailAddress": "<EMAIL>", "environment": "www7", "facilityStatus": "Live", "fax": "(*************", "headOffice": false, "healthType": "SNF", "lineOfBusiness": {"longDesc": "Skilled Nursing Facility", "shortDesc": "SNF"}, "orgDbType": "T", "orgName": "A.G. <PERSON> Home", "orgUuid": "1750abe8-1b7c-4e26-969c-9d7076ff8c02", "phone": "(*************", "postalCode": "75835-1810", "state": "TX", "timeZone": "America/Chicago", "timeZoneOffset": -3, "enabled": false, "organizationId": "9c84cf9c-b42a-4dbd-86c5-50c886f0ccb9"}, {"id": "2156f244-bb65-42eb-9d96-50dbf696a108", "facId": 22, "facilityCode": "6642", "facilityName": "FACILITY_22", "active": true, "addressLine1": "708 Garrah Lane", "addressLine2": "P.O.Box # 31428-9233", "bedCount": 120, "billingStyleCountry": "USA", "city": "Dade City", "country": "USA", "countryId": 5032, "emailAddress": "<EMAIL>", "environment": "www7", "facilityStatus": "Live", "fax": "(*************", "headOffice": false, "healthType": "SNF", "lineOfBusiness": {"longDesc": "Skilled Nursing Facility", "shortDesc": "SNF"}, "orgDbType": "P", "orgName": "A.G. <PERSON> Home", "orgUuid": "1950abe8-1b7c-4e26-967c-9d7076ff8c02", "phone": "(*************", "postalCode": "33525-4526", "state": "FL", "timeZone": "America/New_York", "timeZoneOffset": 13, "enabled": false, "organizationId": "7760ffcd-64d2-43df-9548-055b257dfaee"}]}