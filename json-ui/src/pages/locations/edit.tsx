import * as React from 'react';
import { FieldValues } from 'react-hook-form';
import { useForm } from '@refinedev/react-hook-form';
import { useBack, useTranslate } from '@refinedev/core';

import Stack from '@mui/material/Stack';
import Dialog from '@mui/material/Dialog';
import Switch from '@mui/material/Switch';
import { TextField } from '@mui/material';
import FormHelperText from '@mui/material/FormHelperText';
import FormControlLabel from '@mui/material/FormControlLabel';

import PhoneInput from 'src/components/phone-input';
import { Edit } from 'src/components/refine-customs/edit';
import * as organizationDropdown from 'src/components/dropdown/organizationDropdown';

export const LocationEdit = () => {
  const translate = useTranslate();
  const back = useBack();
  const [open, setOpen] = React.useState(true);
  const {
    watch,
    saveButtonProps,
    refineCore: { queryResult, onFinish },
    register,
    handleSubmit,
    control,
    formState: { errors },
  } = useForm({
    refineCoreProps: {
      redirect: false,
    },
  });
  const values = watch();
  const handleClose = () => {
    setOpen(false);
    back();
  };
  const locationsData = queryResult?.data?.data;

  const onSubmit = async (data: FieldValues) => {
    await onFinish(data);
    handleClose();
  };

  return (
    <Dialog
      fullWidth
      maxWidth="sm"
      open={open}
      onClose={handleClose}
      aria-labelledby="alert-dialog-title"
      aria-describedby="alert-dialog-description"
    >
      <Edit
        saveButtonProps={{
          ...saveButtonProps,
          onClick: handleSubmit(onSubmit),
        }}
        deleteButtonProps={{
          confirmTitle: translate('locations.titles.delete'),
          confirmContent: `Are you sure you want to delete ${locationsData?.name}?`,
        }}
        title={translate('locations.titles.edit')}
        onClose={handleClose}
      >
        <Stack spacing={2.5} component="form" autoComplete="off">
          <TextField
            {...register('name', {
              required: 'This field is required',
            })}
            error={!!(errors as any)?.name}
            helperText={(errors as any)?.name?.message}
            fullWidth
            InputLabelProps={{ shrink: true }}
            type="text"
            label={translate('locations.fields.name')}
            name="name"
            required
          />

          <PhoneInput
            control={control}
            label={translate('locations.fields.phone')}
            name="phone"
            value={locationsData?.phone}
          />
          <PhoneInput
            control={control}
            label={translate('locations.fields.fax')}
            name="fax"
            value={locationsData?.fax}
          />

          <organizationDropdown.OrganizationDropdown
            control={control}
            name="organizationId"
            label={translate('locations.fields.organizationId')}
            value={locationsData?.organizationId}
            error={!!(errors as any)?.organizationId}
            helperText={(errors as any)?.organizationId?.message}
            disabled={!!locationsData?.organizationId}
          />
          <div>
            <FormControlLabel
              label={translate('locations.fields.status')}
              control={
                <Switch
                  {...register('status')}
                  name="status"
                  checked={!!values.status}
                />
              }
            />
            {!!errors.status && (
              <FormHelperText error sx={{ px: 2 }}>
                {(errors as any)?.status?.message}
              </FormHelperText>
            )}
          </div>
        </Stack>
      </Edit>
    </Dialog>
  );
};
