<?xml version="1.0" encoding="utf-8" ?>
<TrustFrameworkPolicy xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns:xsd="http://www.w3.org/2001/XMLSchema"
  xmlns="http://schemas.microsoft.com/online/cpim/schemas/2013/06" PolicySchemaVersion="0.3.0.0" TenantId="patientauth.onmicrosoft.com" PolicyId="B2C_1A_Signin_Email_Relic" PublicPolicyUri="http://patientauth.onmicrosoft.com/B2C_1A_Signin_Email_Relic" DeploymentMode="Development" UserJourneyRecorderEndpoint="urn:journeyrecorder:applicationinsights">
  <!-- for AppInsights, copy these to above DeploymentMode="Development" UserJourneyRecorderEndpoint="urn:journeyrecorder:applicationinsights" 
  -->

  <BasePolicy>
    <TenantId>patientauth.onmicrosoft.com</TenantId>
    <PolicyId>B2C_1A_TrustFrameworkExtensionsRelic</PolicyId>
  </BasePolicy>

  <BuildingBlocks>
    <ClaimsSchema>
      <!-- Custom Relic Email field for Email Login. -->
      <ClaimType Id="relicEmail">
        <DisplayName>Email Address</DisplayName>
        <DataType>string</DataType>
        <UserHelpText>Email address - <EMAIL> format</UserHelpText>
        <UserInputType>EmailBox</UserInputType>
        <Restriction>
          <Pattern RegularExpression="^[a-zA-Z0-9!#$%&amp;'+^_`{}~-]+(?:\.[a-zA-Z0-9!#$%&amp;'+^_`{}~-]+)*@(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]*[a-zA-Z0-9])?\.)+[a-zA-Z0-9](?:[a-zA-Z0-9-]*[a-zA-Z0-9])?$" HelpText="Please enter a valid email address." />
        </Restriction>
      </ClaimType>

      <!-- Signin Email field for Directory Lookup. -->
      <ClaimType Id="signInNames.emailAddress">
        <DisplayName>Email Address</DisplayName>
        <DataType>string</DataType>
        <UserHelpText>Enter Email Address</UserHelpText>
      </ClaimType>

      <!-- ReadOnly Email field for OTP Screen. Display masked email. -->
      <ClaimType Id="readOnlyEmail">
        <DisplayName />
        <DataType>string</DataType>
        <Mask Type="Regex" Regex="(?&lt;=.).(?=.*@)">*</Mask>        
        <UserHelpText />
        <UserInputType>Readonly</UserInputType>
      </ClaimType>
    </ClaimsSchema>

    <ClaimsTransformations>
      <ClaimsTransformation Id="CopyRelicEmailToSignInNames" TransformationMethod="FormatStringClaim">
        <InputClaims>
          <InputClaim ClaimTypeReferenceId="relicEmail" TransformationClaimType="inputClaim" />
        </InputClaims>
        <InputParameters>
          <InputParameter Id="stringFormat" DataType="string" Value="{0}" />
        </InputParameters>
        <OutputClaims>
          <OutputClaim ClaimTypeReferenceId="signInNames.emailAddress" TransformationClaimType="outputClaim" />
        </OutputClaims>
      </ClaimsTransformation>
      <ClaimsTransformation Id="CopyRelicEmailToReadOnly" TransformationMethod="FormatStringClaim">
        <InputClaims>
          <InputClaim ClaimTypeReferenceId="relicEmail" TransformationClaimType="inputClaim" />
        </InputClaims>
        <InputParameters>
          <InputParameter Id="stringFormat" DataType="string" Value="{0}" />
        </InputParameters>
        <OutputClaims>
          <OutputClaim ClaimTypeReferenceId="readOnlyEmail" TransformationClaimType="outputClaim" />
        </OutputClaims>
      </ClaimsTransformation>
    </ClaimsTransformations>
  </BuildingBlocks>

  <ClaimsProviders>
    <ClaimsProvider>
      <DisplayName>Validate Email on Sign In</DisplayName>
      <TechnicalProfiles>
        <!-- Session Management -->
        <TechnicalProfile Id="SM-AAD">
          <PersistedClaims>
            <PersistedClaim ClaimTypeReferenceId="relicEmail" />
            <PersistedClaim ClaimTypeReferenceId="readOnlyEmail" />
            <PersistedClaim ClaimTypeReferenceId="signInNames.emailAddress" />
            <PersistedClaim ClaimTypeReferenceId="userPrincipalName" />
          </PersistedClaims>
        </TechnicalProfile>

        <!-- OTP UI / Verify Email UI. Outputs Verified.Email -->
        <TechnicalProfile Id="EmailVerifyOnSignIn">
          <DisplayName>EmailVerifyOnSignIn</DisplayName>
          <Protocol Name="Proprietary" Handler="Web.TPEngine.Providers.SelfAssertedAttributeProvider, Web.TPEngine, Version=*******, Culture=neutral, PublicKeyToken=null" />
          <Metadata>
            <Item Key="ContentDefinitionReferenceId">api.localaccountpasswordreset</Item>
          </Metadata>
          <InputClaims>
            <InputClaim ClaimTypeReferenceId="readOnlyEmail" />
          </InputClaims>
          <OutputClaims>
            <OutputClaim ClaimTypeReferenceId="readOnlyEmail" PartnerClaimType="Verified.Email" Required="true" />
          </OutputClaims>
          <UseTechnicalProfileForSessionManagement ReferenceId="SM-Noop" />
        </TechnicalProfile>

        <!-- Copy Relic Email to Sign In & Read Only Email. Add them to session. -->
        <TechnicalProfile Id="CopyRelicEmail">
          <DisplayName>Combine country code and national number</DisplayName>
          <Protocol Name="Proprietary" Handler="Web.TPEngine.Providers.ClaimsTransformationProtocolProvider, Web.TPEngine, Version=*******, Culture=neutral, PublicKeyToken=null" />
          <InputClaimsTransformations>
            <InputClaimsTransformation ReferenceId="CopyRelicEmailToSignInNames" />
            <InputClaimsTransformation ReferenceId="CopyRelicEmailToReadOnly" />
          </InputClaimsTransformations>
          <OutputClaims>
            <OutputClaim ClaimTypeReferenceId="signInNames.emailAddress" />
            <OutputClaim ClaimTypeReferenceId="readOnlyEmail" />
          </OutputClaims>
          <UseTechnicalProfileForSessionManagement ReferenceId="SM-AAD" />
        </TechnicalProfile>

        <!-- Read Azure AD B2C User for existing email. Raise an error if user does not exist. -->
        <TechnicalProfile Id="AAD-UserReadUsingEmail">
          <Metadata>
            <Item Key="Operation">Read</Item>
            <Item Key="RaiseErrorIfClaimsPrincipalDoesNotExist">true</Item>
            <Item Key="UserMessageIfClaimsPrincipalDoesNotExist">We can't seem to find your account. Please try again.</Item>
          </Metadata>
          <IncludeInSso>false</IncludeInSso>
          <InputClaims>
            <InputClaim ClaimTypeReferenceId="signInNames.emailAddress" Required="true" />
          </InputClaims>
          <OutputClaims>
            <!-- Required claims -->
            <OutputClaim ClaimTypeReferenceId="objectId" />
            <OutputClaim ClaimTypeReferenceId="authenticationSource" DefaultValue="localAccountAuthentication" />
            <OutputClaim ClaimTypeReferenceId="signInNames.emailAddress" />
            <!-- Optional claims -->
            <OutputClaim ClaimTypeReferenceId="displayname" />
            <OutputClaim ClaimTypeReferenceId="userPrincipalName" />
          </OutputClaims>
          <IncludeTechnicalProfile ReferenceId="AAD-Common" />
        </TechnicalProfile>

        <!-- Accept Relic Email from UI & check if the Email is found in the directory. -->
        <TechnicalProfile Id="SelfAsserted-LocalAccountSignin-Custom">
          <DisplayName>Custom Email Signin</DisplayName>
          <Protocol Name="Proprietary" Handler="Web.TPEngine.Providers.SelfAssertedAttributeProvider, Web.TPEngine, Version=*******, Culture=neutral, PublicKeyToken=null" />
          <Metadata>
            <Item Key="ContentDefinitionReferenceId">api.selfasserted</Item>
            <Item Key="IncludeClaimResolvingInClaimsHandling">true</Item>
            <Item Key="RaiseErrorIfClaimsPrincipalDoesNotExist">true</Item>
          </Metadata>
          <IncludeInSso>false</IncludeInSso>
          <InputClaims>
            <InputClaim ClaimTypeReferenceId="relicEmail" DefaultValue="{OIDC:LoginHint}" AlwaysUseDefaultValue="true" />
          </InputClaims>
          <OutputClaims>
            <OutputClaim ClaimTypeReferenceId="signInNames.emailAddress" Required="true" />
            <OutputClaim ClaimTypeReferenceId="readOnlyEmail" Required="true" />
            <OutputClaim ClaimTypeReferenceId="relicEmail" Required="true" />
            <OutputClaim ClaimTypeReferenceId="objectId" Required="false" />
            <OutputClaim ClaimTypeReferenceId="displayname" />
            <OutputClaim ClaimTypeReferenceId="userPrincipalName" />
          </OutputClaims>
          <ValidationTechnicalProfiles>
            <ValidationTechnicalProfile ReferenceId="CopyRelicEmail" />
            <ValidationTechnicalProfile ReferenceId="AAD-UserReadUsingEmail" ContinueOnError="false" />
          </ValidationTechnicalProfiles>
          <UseTechnicalProfileForSessionManagement ReferenceId="SM-AAD" />
        </TechnicalProfile>

        <!-- Extract Id Tokens and put in the session-->
        <TechnicalProfile Id="IdTokenHint_ExtractClaims">
          <DisplayName> My ID Token Hint TechnicalProfile</DisplayName>
          <Protocol Name="None" />
          <Metadata>          
            <!--Sample action required: replace with your endpoint location -->
            <!-- <Item Key="METADATA">https://your-app.azurewebsites.net/.well-known/openid-configuration</Item> -->
            <Item Key="IdTokenAudience">aadB2c</Item>
            <Item Key="issuer">node-services</Item>
          </Metadata>
          <CryptographicKeys>
            <Key Id="client_secret" StorageReferenceId="B2C_1A_IdTokenHintKey" />
          </CryptographicKeys>
          <OutputClaims>
            <!--Sample: Read the email claim from the id_token_hint-->
            <OutputClaim ClaimTypeReferenceId="relicEmail" Required="true" />
          </OutputClaims>
          <UseTechnicalProfileForSessionManagement ReferenceId="SM-AAD" />
        </TechnicalProfile>

        <!-- Read AAD B2C User record using email -->
        <TechnicalProfile Id="RELIC-UserReadUsingEmailAddress">
          <Metadata>
            <Item Key="Operation">Read</Item>
            <Item Key="RaiseErrorIfClaimsPrincipalDoesNotExist">true</Item>
          </Metadata>
          <IncludeInSso>false</IncludeInSso>
          <InputClaims>
            <InputClaim ClaimTypeReferenceId="relicEmail" PartnerClaimType="signInNames.emailAddress" Required="true" />
          </InputClaims>
          <OutputClaims>
            <OutputClaim ClaimTypeReferenceId="objectId" />
            <OutputClaim ClaimTypeReferenceId="authenticationSource" DefaultValue="localAccountAuthentication" />
            <OutputClaim ClaimTypeReferenceId="strongAuthenticationPhoneNumber" />
            <OutputClaim ClaimTypeReferenceId="signInNames.emailAddress" Required="true" />
            <OutputClaim ClaimTypeReferenceId="accountEnabled" />
            <OutputClaim ClaimTypeReferenceId="userPrincipalName" />
            <OutputClaim ClaimTypeReferenceId="displayName" />
          </OutputClaims>
          <OutputClaimsTransformations>
            <OutputClaimsTransformation ReferenceId="AssertAccountEnabledIsTrue" />
          </OutputClaimsTransformations>
          <IncludeTechnicalProfile ReferenceId="AAD-Common" />
        </TechnicalProfile>

      </TechnicalProfiles>
    </ClaimsProvider>
  </ClaimsProviders>

  <UserJourneys>
    <UserJourney Id="SignInWithEmailVerification">
      <OrchestrationSteps>
        <!-- First screen with email address text box -->
        <!-- <OrchestrationStep Order="1" Type="ClaimsExchange">
          <ClaimsExchanges>
            <ClaimsExchange Id="SignIn" TechnicalProfileReferenceId="SelfAsserted-LocalAccountSignin-Custom" />
          </ClaimsExchanges>
        </OrchestrationStep> -->

        <!--Read the input claims from the id_token_hint-->
        <OrchestrationStep Order="1" Type="GetClaims" CpimIssuerTechnicalProfileReferenceId="IdTokenHint_ExtractClaims" />

        <!--Pull record from AAD B2C to ensure that we have the record-->
        <OrchestrationStep Order="2" Type="ClaimsExchange">
          <ClaimsExchanges>
            <ClaimsExchange Id="SignIn" TechnicalProfileReferenceId="RELIC-UserReadUsingEmailAddress" />
          </ClaimsExchanges>
        </OrchestrationStep>

        <!--Copy email to read only email field-->
        <OrchestrationStep Order="3" Type="ClaimsExchange">
          <ClaimsExchanges>
            <ClaimsExchange Id="CopyEmail" TechnicalProfileReferenceId="CopyRelicEmail" />
          </ClaimsExchanges>
        </OrchestrationStep>

        <!-- Email Verification Step with OTP -->
        <OrchestrationStep Order="4" Type="ClaimsExchange">
          <Preconditions>
            <Precondition Type="ClaimsExist" ExecuteActionsIf="true">
              <Value>newUser</Value>
              <Action>SkipThisOrchestrationStep</Action>
            </Precondition>
          </Preconditions>
          <ClaimsExchanges>
            <ClaimsExchange Id="EmailVerifyOnSignIn" TechnicalProfileReferenceId="EmailVerifyOnSignIn" />
          </ClaimsExchanges>
        </OrchestrationStep>


        <!-- This step reads any user attributes that we may not have received during claims exchange. -->
        <OrchestrationStep Order="5" Type="ClaimsExchange">
          <ClaimsExchanges>
            <ClaimsExchange Id="AADUserReadWithObjectId" TechnicalProfileReferenceId="AAD-UserReadUsingObjectId" />
          </ClaimsExchanges>
        </OrchestrationStep>

        <OrchestrationStep Order="6" Type="SendClaims" CpimIssuerTechnicalProfileReferenceId="JwtIssuer" />

      </OrchestrationSteps>
      <ClientDefinition ReferenceId="DefaultWeb" />
    </UserJourney>
  </UserJourneys>

  <RelyingParty>
    <DefaultUserJourney ReferenceId="SignInWithEmailVerification" />
    <UserJourneyBehaviors>
      <!-- for AppInsight - add your key
      <JourneyInsights TelemetryEngine="ApplicationInsights" InstrumentationKey="...guid..." DeveloperMode="true" ClientEnabled="true" ServerEnabled="true" TelemetryVersion="1.0.0" /> 
    -->
      <JourneyInsights TelemetryEngine="ApplicationInsights" InstrumentationKey="1c3959d5-0561-494e-95d5-adaf198bb783" DeveloperMode="true" ClientEnabled="true" ServerEnabled="true" TelemetryVersion="1.0.0" /> 
      <ScriptExecution>Allow</ScriptExecution>
    </UserJourneyBehaviors>
    <TechnicalProfile Id="PolicyProfile">
      <DisplayName>PolicyProfile</DisplayName>
      <Protocol Name="OpenIdConnect" />
      <InputClaims>
        <InputClaim ClaimTypeReferenceId="relicEmail"/>
      </InputClaims>      
      <OutputClaims>
        <OutputClaim ClaimTypeReferenceId="displayName" />
        <OutputClaim ClaimTypeReferenceId="signInNames.emailAddress" />
        <OutputClaim ClaimTypeReferenceId="objectId" PartnerClaimType="sub" />
        <OutputClaim ClaimTypeReferenceId="tenantId" AlwaysUseDefaultValue="true" DefaultValue="{Policy:TenantObjectId}" />
        <OutputClaim ClaimTypeReferenceId="userPrincipalName" />
      </OutputClaims>
      <SubjectNamingInfo ClaimType="sub" />
    </TechnicalProfile>
  </RelyingParty>
</TrustFrameworkPolicy>