import * as React from 'react';
import { IResourceComponentsProps } from '@refinedev/core';

import { useRouter } from 'src/routes/hooks';

import CreateDocument from 'src/components/_document/document-create';

// ----------------------------------------------------------------------

export const DocumentCreate: React.FC<IResourceComponentsProps> = () => {

  const router = useRouter();

  const [openDocument, setOpenDocument] = React.useState(true);

  const onCloseDocument = () => {
    setOpenDocument(false);
    router.push('/documents');
}

  return (
    <>
      {openDocument && <CreateDocument open={openDocument} onClose={onCloseDocument} />}    
    </>
  );
};
