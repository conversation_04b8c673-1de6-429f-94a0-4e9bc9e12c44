import { FastifyInstance, FastifyPluginOptions, FastifyReply, FastifyRequest } from 'fastify';
import { PractitionerFilters, RelicAgent, RelicIdParam, NewThread, Endpoint } from 'relic-ui';
import { RelicChatParticipant, Thread, ThreadsWithCount } from 'relic-ui';
import { requestContext } from '@fastify/request-context';
import { relicRequire } from '../../../utils/common-js';
import { mergeSchemas } from '@fastify/merge-json-schemas';
interface PractitionerIdParams {
  Params: RelicIdParam;
}
const { RelicIdParamSchema, PractitionerFiltersSchema, ListQuerySchema } = relicRequire('relic-ui/schema');

export default async function (fastify: FastifyInstance, opts: FastifyPluginOptions) {
  fastify.get(
    '/',
    {
      schema: {
        ...opts.schema,
        querystring: mergeSchemas([PractitionerFiltersSchema, ListQuerySchema]),
        summary: `Get all practitioners for your org or filter.`,
        tags: ['Practitioner Service'],
        description: `Fetches all practitioners for your org or filters them based on provided criteria.`,
      },
    },
    async function getPractitioners(
      req: FastifyRequest<{ Querystring: PractitionerFilters }>,
      rep: FastifyReply,
    ) {
      let { id, organizationId, name, email, mobilePhone, _start, _end, _sort, _order, _search } = req.query;
      const filter = {} as PractitionerFilters;

      if (Array.isArray(id)) {
        const validIdArray = id.filter((id) => id !== undefined);
        filter.id = { $in: validIdArray };
      }
      if (name !== undefined) {
        filter.name = name;
      }
      if (email !== undefined) {
        filter.email = email;
      }
      if (mobilePhone !== undefined) {
        filter.mobilePhone = mobilePhone;
      }
      if (organizationId !== undefined) {
        filter.organizationId = organizationId;
      }
      const filters = {
        filter,
        _search,
        _count: (Number(_end) || 0) - (Number(_start) || 0) > 0 ? (Number(_end) || 0) - (Number(_start) || 0) : 20,
        _sort,
        _order,
        _start,
        _end,
      };
      // Obtain all agents and filter out practitioners that are agents
      const agents: RelicAgent[] = await fastify.searchRelicAgents();
      const agentIds = agents.map((agent) => agent.id);
      filter.id = filter.id ? { ...filter.id, $nin: agentIds } : { $nin: agentIds };
      const { practitioners, totalCount } = await fastify.getPractitioners(filters);
      rep.header('x-total-count', totalCount);
      return practitioners;
    },
  );

  fastify.get(
    '/:id/chat',
    {
      schema: {
        ...opts.schema,
        params: RelicIdParamSchema,
        summary: `Get Practitioner's default thread or a thread with Practitioner participant depending on the caller's resource type.`,
        tags: ['Communication Service'],
        description: `Returns Practitioner's Default Chat thread if caller is Practitioner. Returns Caregiver Chat thread between Practitioner and Patient if caller is Patient`,
      },
    },
    async function getPractitionerThread(req: FastifyRequest<PractitionerIdParams>, rep: FastifyReply) {
      const me = requestContext.get('whoami');
      const organization = req.requestContext.get('organization');
      if (!me) {
        throw fastify.httpErrors.unauthorized('User not authenticated');
      }
      const filter = {} as {
        status: 'active' | 'closed';
        relicChatParticipants: RelicChatParticipant[];
      };
      const participants: RelicChatParticipant[] = [];
      participants.push({
        resourceId: me.id,
        resourceType: me.resourceType === 'Patient' ? 'Patient' : 'Practitioner', //I can be patient or practitioner
        id: { id: '' }, //ParticipantACS id will be patched by Communication Plugin
      });
      participants.push({
        resourceId: req.params.id,
        resourceType: 'Practitioner', //Param is a practitioner since this is a practitioner route
        id: { id: '' }, //ParticipantACS id will be patched by Communication Plugin
      });
      filter.status = 'active';
      filter.relicChatParticipants = participants;
      const threadsWithCount: ThreadsWithCount = await fastify.getRelicThreads(filter);
      const threads: Thread[] = threadsWithCount.threads;
      if (threads?.[0]) {
        return threads[0];
      }
      const newThread: NewThread = {
        endpoint: organization.endpoints.find((e: Endpoint) => e.provider === 'Azure Communication Service').endpoint,
        threadSubject: {
          organizationId: me.portalIdentity.organizationId || '',
          threadOwner: {
            id: me.id,
            resourceType: me.resourceType,
          },
        },
        participants: participants,
        status: 'active',
        inviteVia: 'none',
      };

      return await fastify.createRelicThread(newThread);
    },
  );
}
