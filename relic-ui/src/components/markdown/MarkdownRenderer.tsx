import React, { useMemo } from 'react';
import ReactMarkDown from 'react-markdown';
import { Components } from 'react-markdown/lib';
import rehypeRaw from 'rehype-raw';
import rehypeReact from 'rehype-react';
import remarkGfm from 'remark-gfm';
import { Box } from '@mui/material';

interface MarkdownRendererProps {
  content: string;
  components?: Partial<Components>;
  className?: string;
  style?: React.CSSProperties;
}

/**
 * MarkdownRenderer component - A versatile component for rendering markdown content
 * Used by both ChatMessageRenderer for chat messages and CitationContent for citation rendering
 *
 * @param content - The markdown content to render
 * @param components - Custom components to override default markdown rendering
 * @param className - Optional CSS class name for the container
 * @param style - Optional inline styles for the container
 */
const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({ content, components = {}, className, style }) => {
  const memoizedComponents = useMemo(() => components, [components]);

  // Default styling for the markdown container
  const defaultStyle: React.CSSProperties = {
    lineHeight: '1.6',
    fontSize: '14px',
    wordBreak: 'break-word',
    ...style,
  };

  return (
    <Box
      className={className}
      style={defaultStyle}
      sx={{
        // Remove default margins from paragraphs throughout the markdown content
        '& p': {
          marginBlockStart: '2px!important',
          marginBlockEnd: '2px!important',
          marginInlineStart: '2px!important',
          marginInlineEnd: '2px!important',
          paddingTop: '0rem!important',
        },
      }}
    >
      <ReactMarkDown
        children={content}
        components={memoizedComponents}
        // Plugins to process raw HTML, sanitize it, and integrate React components
        rehypePlugins={[rehypeRaw, [rehypeReact, { components: memoizedComponents }]]}
        // Plugin for GitHub Flavored Markdown
        remarkPlugins={[remarkGfm]}
      />
    </Box>
  );
};

export default MarkdownRenderer;
