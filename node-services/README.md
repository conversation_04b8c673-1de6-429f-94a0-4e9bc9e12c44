<!-- ABOUT THE PROJECT -->
## About The Project

This is a starter project for API development. It provides several features out of the box needed for API development:
1. Logging - APIs would need logging and pino is built into Fastify. Can be configured as per requirements.
2. Fastify Bootstrapping - To build a custom fastify instance as per requirements. The starter template uses certain plugins but more plugins are available in [Fastify Ecosystem](https://fastify.dev/ecosystem).
3. JWT plugin & auth - Simple security via JWT to secure API calls. Can be extended to protect all end points.
4. Mongo DB plugin - Fastify connections to Mongo DB. More plugins can be added as needed.
5. Dynamic Route Registration - No manual registrations. Ability to build multiple api suites such as /fhir/, /acs/ etc depending on requirements. Individual endpoints are registered dynamically as route files are added.

## About Implemented Routes

Sample routes have been implemented that demonstrate some of the best practices around implementing routes.
1. Routes can be grouped under a folder and then prefixed (as per needs and not necessarily folder names). Please implement different folders and prefixes as required.
2. Schema Validation - For POST requests, validations can be done with JSON schema validator. The same has been demonstrated in sample routes implemented.
3. Error Handling - Handle your errors using standard node constructs and meaningful API errors would be thrown. No special constructs or design required.
4. Mongo Connectivity - Mongo connectivity is available through Fastify across all routes.
5. Sample route implementation - Routes implemented with JSON schema validation, error handling & mongo connectivity are available. These can be kept for future references.

## About Automated API Testing

API Testing can be a challenge specially with a small team. However, unit tests can be written in a similar manner as other node applications without any special requirements.
1. Automated API Testing through [tap](https://node-tap.org/) - A set of easy to understand API tests. 
2. APIs can be tested without running the server. Test scripts invoke the API using [Fastify Injection](https://fastify.dev/docs/latest/Guides/Testing/#benefits-of-using-fastifyinject).
3. Sample API Testing Script is available to demonstrate how to develop unit tests.

## Running Node Services & Environment Files

Node-Services can be run in DEV mode or TEST mode. DEV mode runs the server in dev mode while TEST mode runs the test scripts without the need of running a server.
### DEV

```sh
cd node-services
npm i
npm run dev
```

### TEST

```sh
cd node-services
npm i
npm run test
```

### .ENV

```
PORT=3000
MONGO_URL=[Your Mongo DB URL]
SECRET=TEST
```
## API End Points

  - GET `http://localhost:3000/api/chat/agents/{acsInstanceName}/{locationId}`

    Path Parameters
``` 
    acsInstanceName: Name of the ACS instance
    locationId: Id of the location 
```

    Headers
```

    Response 
```
    [{
        acsId: `Azure Communication Service id for the Agent`,
        agentName: `Name of the Agent`,
        type: `Type of the Agent [Person / AI]`,
        role: {
            practitioner-role: `Role of the Agent acording to FHIR`,
            display-role: `Display role of the Agent`
        }
    }]
```

  - GET `http://localhost:3000/api/chat/agents/{acsInstanceName}/{acsId}/assistant`

    Path Parameters
``` 
    acsInstanceName: `Name of the ACS instance`
    locationId: `Id of the location`
```
    Headers
```    

    Response
```
    {
        _id: `mongoDB autogenerated ID`,
        acsId: `Azure Communication Service id for the Agent.`,
        agentName: `Name of the agent`,
        acsToken: {
            token: `JWT Token`,
            expiresOn: `Expiry of the token in UTC`
        },
        version: `Agent version`,
        type: `type of the AI Agent [Person / AI]`,
        status: `States whether an Agent is active / inactive`,
        role: {
            practitioner-role: `Role of the Agent according to FHIR`,
            display-role: `Display Role of the Agent`
        }
        azureAssistantSetup: `Description of the Agent`,
        relicAssistantSetup: {
            greetingTemplates: [
                {
                    event: `` ,
                    greetingTemplate: `` 
                }
            ],
            kbLinked: [],
            kbPromptTemplate: ``
        }
    }
``` 

  - POST `http://localhost:3000/api/register`

    Request
```
    {
      username: `username`,
      password: `password for the user`
    }
```


 - POST `http://localhost:3000/api/login`

    Request
```
    {
      username: `username`,
      password: `password for the user`
    }
```

    Response
```
    {
        result: `JWT token`
    }
```    
 - POST `http://localhost:3000/api/organizations`

    Request
```
    {
        "name": "Official name of the organization",
        "branch": "Branch of the organization",
        "location": "location of the organization",
        "address": {
            "use": "Indicates the usage of the address (home, work, temporary, old, or for billing)",
            "type": "Indicates the physical form of the address (postal, physical, or both)",
            "line": [
                "Street name and details"
            ],
            "city": "City in which the organization is located",
            "district": "District City in which the organization is located",
            "state": "State name or code in which organization is located",
            "postalCode": "Postal code of the organization's area",
            "country": "Country name or code in which the organization operates"
        },
        "contact": {
            "phone": "Contact phone number of the organization",
            "email": "Contact email address of the organization",
            "website": "Website of the organization's website"
        }
    }
```

    Response
```
    {
    "success": true,
    "message": {
        "description": "Description message",
        "id": {
            "id": "Unique identifier for the organization in MongoDB"
        },
        "copilot": {
            "id": "Unique identifier for the organization in Copilot"
        },
        "ehr": {
            "id": "Unique identifier for the organization in EHR/FHIR"
        },
        "name": "Official name of the organization",
        "branch": "Branch of the organization",
        "location": "location of the organization",
        "address": {
            "use": "Indicates the usage of the address (home, work, temporary, old, or for billing)",
            "type": "Indicates the physical form of the address (postal, physical, or both)",
            "line": [
                "Street name and details"
            ],
            "city": "City in which the organization is located",
            "district": "District City in which the organization is located",
            "state": "State name or code in which organization is located",
            "postalCode": "Postal code of the organization's area",
            "country": "Country name or code in which the organization operates"
        },
        "contact": {
            "phone": "Contact phone number of the organization",
            "email": "Contact email address of the organization",
            "website": "Website of the organization's website"
        }
    }
}
```    
