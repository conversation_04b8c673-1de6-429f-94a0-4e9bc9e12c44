# syntax=docker/dockerfile:1

FROM node:22-alpine As prod-build 
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
RUN corepack enable

WORKDIR /app
COPY ./relic-ui ./relic-ui

WORKDIR /app/relic-ui
RUN rm -rf node_modules
RUN --mount=type=cache,id=pnpm,target=/pnpm/store pnpm install
RUN pnpm run build

WORKDIR /app
COPY ./json-ui ./json-ui

FROM prod-build AS build
WORKDIR /app/json-ui
RUN --mount=type=cache,id=pnpm,target=/pnpm/store pnpm install --frozen-lockfile
RUN NODE_OPTIONS=--max-old-space-size=8192 pnpm run build

EXPOSE 3000

ENV ADDRESS=0.0.0.0 PORT=3000

CMD ["pnpm", "preview", "--port", "3000", "--host", "0.0.0.0"]