// import config from 'config';
import Fastify, { FastifyServerOptions } from 'fastify';
import fp from 'fastify-plugin';
import config from 'config';
// Import library to exit fastify process, gracefully (if possible)
import closeWithGrace from 'close-with-grace';

import nodeServiceApp from './src/app';

function getLoggerOptions() {
  // Only if the program is running in an interactive terminal
  if (process.stdout.isTTY) {
    return {
      level: 'info',
      transport: {
        target: 'pino-pretty',
        options: {
          translateTime: 'HH:MM:ss Z',
          ignore: 'pid,hostname',
        },
      },
    };
  }

  return { level: process.env.LOG_LEVEL ?? 'silent' };
}

// Determine if running on localhost
const isLocalhost =
  ['localhost', '127.0.0.1', '0.0.0.0', '::1'].includes(config.get('SERVER.HOST')) ||
  process.env.NODE_ENV === 'development';

const baseOptions: FastifyServerOptions = {
  disableRequestLogging: !isLocalhost,
  logger: getLoggerOptions(),
  ajv: {
    customOptions: {
      coerceTypes: 'array', // change type of data to match type keyword
      removeAdditional: 'all', // Remove additional body properties
    },
  },
};

const app = Fastify(baseOptions);

async function init() {
  // Register your application as a normal plugin.
  // fp must be used to override default error handler
  app.register(fp(nodeServiceApp));

  // Delay is the number of milliseconds for the graceful close to finish
  closeWithGrace({ delay: (process.env.FASTIFY_CLOSE_GRACE_DELAY as unknown as number) ?? 500 }, async ({ err }) => {
    if (err != null) {
      app.log.error(err);
    }

    await app.close();
  });

  await app.ready();

  try {
    // Start listening.
    await app.listen({
      host: config.get('SERVER.HOST'),
      port: config.get('SERVER.PORT'),
    });
  } catch (err) {
    app.log.error(err);
    process.exit(1);
  }
}

init().catch((err) => {
  console.error('Error starting node-services server:', err);
});
