import * as React from 'react';
import { useTranslate } from '@refinedev/core';

import Popover from '@mui/material/Popover';
import IconButton from '@mui/material/IconButton';
import Button, { buttonClasses } from '@mui/material/Button';
import { EditOutlined, DeleteOutline } from '@mui/icons-material';

import { usePathname } from 'src/routes/hooks';

import { usePopover } from 'src/hooks/use-popover';

import Iconify from 'src/components/iconify';

// ----------------------------------------------------------------------

type Props = {
  onEdit: () => void;
  onDelete: () => void;
};

export default function AgentsRowActionsPopover({ onEdit, onDelete }: Props) {
  const translate = useTranslate();

  const popover = usePopover();

  return (
    <>
      <IconButton
        onClick={popover.onOpen}
        sx={{
          ...(popover.open && {
            bgcolor: 'action.hover',
          }),
        }}
      >
        <Iconify icon="eva:more-horizontal-fill" />
      </IconButton>

      <Popover
        open={Boolean(popover.open)}
        anchorEl={popover.open}
        onClose={popover.onClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        slotProps={{
          paper: {
            sx: {
              mt: 0.5,
              minWidth: 160,
              display: 'flex',
              flexDirection: 'column',
              a: {
                color: 'inherit',
              },
              [`& .${buttonClasses.root}`]: {
                px: 2,
                width: 1,
                justifyContent: 'flex-start',
                fontWeight: 'fontWeightMedium',
              },
            },
          },
        }}
      >
        <Button
          startIcon={<EditOutlined sx={{ selfAlign: 'center' }} />}
          title={'Edit'}
          sx={{ minWidth: 0 }}
          onClick={() => {
            onEdit();
            popover.onClose();
          }}
        >
          {translate('buttons.edit')}
        </Button>
        <Button
          color="error"
          size="small"
          startIcon={<DeleteOutline sx={{ selfAlign: 'center' }} />}
          sx={{ minWidth: 'auto' }}
          onClick={() => {
            onDelete();
            popover.onClose();
          }}
        >
          {translate('buttons.delete')}
        </Button>
      </Popover>
    </>
  );
}
