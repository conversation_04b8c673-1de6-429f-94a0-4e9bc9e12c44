import { useTranslate } from '@refinedev/core';
import { useForm } from '@refinedev/react-hook-form';
import React from 'react';
import { FieldValues } from 'react-hook-form';
import { useLocation } from 'react-router-dom';

import { Box, CircularProgress, Stack } from '@mui/material';

import { useRouter } from 'src/routes/hooks';

import { usePopover } from 'src/hooks/use-popover';

import {
  AgentAddPopover,
  AgentDetailsPopover,
  AgentDrawer,
  AgentPromptTab,
  AgentSamplesAddDialog,
  AgentSampleTab,
  AgentTabs,
  AgentTemplatesAddDialog,
  AgentTemplatesTab,
  AgentToolbar,
} from 'src/components/_agent';
import { Samples, Templates } from 'src/types/agent-types';
import { RelicAgent } from 'relic-ui';

const DRAWER_WIDTH = {
  lg: 400,
  md: 350,
  sm: 300,
  xs: '100%',
};

const TABS = [
  { value: 'prompt', label: 'agents.tabs.prompt' },
  { value: 'samples', label: 'agents.tabs.samples' },
  { value: 'templates', label: 'agents.tabs.templates' },
  // { value: 'knowledgeBase', label: 'agents.tabs.knowledgeBase' },
];

export const AgentEdit = () => {
  const translate = useTranslate();
  const router = useRouter();
  const [showAgent, setShowAgent] = React.useState(true);
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);

  const [showAddTemplateDialog, setShowAddTemplateDialog] =
    React.useState(false);
  const [showAddSamplesDialog, setShowAddSamplesDialog] = React.useState(false);

  const initialTab = queryParams.get('tab') || TABS[0].value;

  const [currentTab, setCurrentTab] = React.useState(
    TABS.find(tab => tab.value === initialTab)?.value,
  );
  const [templatesRowData, setTemplatesRowData] = React.useState<Templates[]>(
    [],
  );
  const [templatesSelectedRow, setTemplatesSelectedRow] =
    React.useState<Templates>();

  const [samplesRowData, setSamplesRowData] = React.useState<Samples[]>([]);
  const [samplesSelectedRow, setSamplesSelectedRow] = React.useState<Samples>();

  const [showEdit, setShowEdit] = React.useState(false);
  const popoverDetails = usePopover();
  const popoverAdd = usePopover();

  const onSubmit = async (values: FieldValues) => {
    delete values.name;
    await onFinish(values);
    setShowEdit(false);
  };

  const handleBack = () => {
    router.back();
  };
  const {
    saveButtonProps,
    refineCore: { queryResult, onFinish },
    register,
    control,
    handleSubmit,
    formState: { errors, isSubmitting },
    setValue,
  } = useForm({
    refineCoreProps: {
      redirect: false,
    },
  });

  const agentData = queryResult?.data?.data as RelicAgent;

  const handleChangeTab = React.useCallback(
    (event: React.SyntheticEvent, newValue: string) => {
      setCurrentTab(newValue);
    },
    [],
  );

  const {
    handleSubmit: handleSampleFormSubmit,
    control: samplesFormControl,
    formState: { errors: samplesFormErrors },
    reset: sampleFormReset,
    setValue: setSampleFormValue,
  } = useForm<Samples>({
    defaultValues: {},
  });

  const {
    handleSubmit: handleTemplateFormSubmit,
    control: templatesFormControl,
    formState: { errors: templatesFormErrors },
    reset: templateFormReset,
    setValue: setTemplatesFormValue,
  } = useForm<Templates>({
    defaultValues: {
      event: '',
      greetingTemplate: '',
    },
  });

  return (
    <>
      <AgentDetailsPopover
        popover={popoverDetails}
        onOpenEdit={() => {
          setShowEdit(true);
          popoverDetails.onClose();
        }}
      />

      <AgentAddPopover
        popover={popoverAdd}
        onSamplesAddClick={() => {
          setSamplesSelectedRow(undefined);
          sampleFormReset();
          setShowAddSamplesDialog(true);
          popoverAdd.onClose();
        }}
        onTemplatesAddClick={() => {
          setTemplatesSelectedRow(undefined);
          templateFormReset();
          setShowAddTemplateDialog(true);
          popoverAdd.onClose();
        }}
      />

      <AgentToolbar
        onBack={handleBack}
        onToggleAgent={() => setShowAgent(!showAgent)}
        agentName={agentData?.name}
        showAgent={showAgent}
        onOpenAdd={popoverAdd.onOpen}
      />
      <Stack
        sx={{
          ...(showAgent && {
            width: {
              sm: `calc(100% - ${DRAWER_WIDTH.sm}px)`,
              md: `calc(100% - ${DRAWER_WIDTH.md}px)`,
              lg: `calc(100% - ${DRAWER_WIDTH.lg}px)`,
            },
          }),
        }}
      >
        <AgentTabs
          tabOptions={TABS}
          currentTab={currentTab as string}
          onChangeTab={handleChangeTab}
        />

        {queryResult?.isLoading && (
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              minHeight: '50vh',
            }}
          >
            <CircularProgress color="inherit" />
          </Box>
        )}

        {!queryResult?.isLoading && currentTab === 'prompt' && (
          <AgentPromptTab
            control={control}
            agentData={agentData}
            errors={errors}
            saveButtonProps={{
              ...saveButtonProps,
              onClick: handleSubmit(onSubmit),
            }}
          />
        )}

        {!queryResult?.isLoading && currentTab === 'samples' && (
          <AgentSampleTab
            agentData={agentData}
            setValue={setValue}
            setShowAddDialog={setShowAddSamplesDialog}
            samplesRowData={samplesRowData}
            setSamplesRowData={setSamplesRowData}
            samplesSelectedRow={samplesSelectedRow}
            setSamplesSelectedRow={setSamplesSelectedRow}
            setFormValue={setSampleFormValue}
            saveButtonProps={{
              ...saveButtonProps,
              onClick: handleSubmit(onSubmit),
            }}
          />
        )}

        {!queryResult?.isLoading && currentTab === 'templates' && (
          <AgentTemplatesTab
            setValue={setValue}
            setShowAddDialog={setShowAddTemplateDialog}
            agentData={agentData}
            templatesRowData={templatesRowData}
            setTemplatesRowData={setTemplatesRowData}
            templatesSelectedRow={templatesSelectedRow}
            setTemplatesSelectedRow={setTemplatesSelectedRow}
            setFormValue={setTemplatesFormValue}
            saveButtonProps={{
              ...saveButtonProps,
              onClick: handleSubmit(onSubmit),
            }}
          />
        )}

        {!queryResult?.isLoading && currentTab === 'knowledgeBase' && (
          <>{/* knowledge base */}</>
        )}
      </Stack>

      <AgentDrawer
        showEdit={showEdit}
        setShowEdit={setShowEdit}
        showAgent={showAgent}
        popoverDetails={popoverDetails}
        agentData={agentData}
        loading={queryResult?.isLoading || isSubmitting}
      />

      <AgentSamplesAddDialog
        samplesSelectedRow={samplesSelectedRow}
        samplesRowData={samplesRowData}
        saveButtonProps={saveButtonProps}
        showAddDialog={showAddSamplesDialog}
        setSamplesRowData={setSamplesRowData}
        setSamplesSelectedRow={setSamplesSelectedRow}
        setShowAddDialog={setShowAddSamplesDialog}
        setValue={setValue}
        formErrors={samplesFormErrors}
        formReset={sampleFormReset}
        handleSubmit={handleSampleFormSubmit}
        formControl={samplesFormControl}
      />
      <AgentTemplatesAddDialog
        templatesSelectedRow={templatesSelectedRow}
        templatesRowData={templatesRowData}
        saveButtonProps={saveButtonProps}
        showAddDialog={showAddTemplateDialog}
        setTemplatesRowData={setTemplatesRowData}
        setTemplatesSelectedRow={setTemplatesSelectedRow}
        setShowAddDialog={setShowAddTemplateDialog}
        setValue={setValue}
        formErrors={templatesFormErrors}
        formReset={templateFormReset}
        handleSubmit={handleTemplateFormSubmit}
        formControl={templatesFormControl}
      />
    </>
  );
};
