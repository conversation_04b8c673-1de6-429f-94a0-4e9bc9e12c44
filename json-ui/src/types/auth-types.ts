import type { AuthPageProps, RegisterFormTypes } from '@refinedev/core';
import type { UseFormProps } from '@refinedev/react-hook-form';

import type { BoxProps } from '@mui/material/Box';
import type { CardProps } from '@mui/material/Card';

interface IBaseLogin {
  id?: string;
  name?: string;
  provider: string;
  b2cAccount?: string;
  organizationId?: string;
  scope?: string;
  tokenType?: string;
}

export interface ILogin extends IBaseLogin {}

export interface IActiveLogin extends IBaseLogin {
  accessToken: string;
  idToken?: string;
  expiresIn?: number;
  refreshToken?: string;
  refreshTokenExpiresIn?: number;
}

export interface LoginFormTypes {
  email?: string;
  id?: string;
  type?: string;
  password?: string;
  remember?: boolean;
  providerName?: string;
  redirectPath?: string;
}

export interface FormPropsType extends UseFormProps {
  onSubmit?: (values: RegisterFormTypes) => void;
}

export type AuthProps = AuthPageProps<BoxProps, CardProps, FormPropsType>;
