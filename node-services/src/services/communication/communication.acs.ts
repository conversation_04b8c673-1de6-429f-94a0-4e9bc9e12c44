import config from 'config'
import { FastifyInstance, FastifyPluginOptions } from 'fastify'
import { CommunicationIdentityClient, TokenScope } from '@azure/communication-identity'
import { RelicOrganization, RelicPractitioner, RelicPatient, RelicAgent, CommunicationIdentity, Thread, RelicChatParticipant, Topic } from 'relic-ui'
import { AzureKeyCredential } from '@azure/core-auth'
import { CommunicationServiceInterface } from './communication.interface'
import { ChatClient, ChatParticipant, CreateChatThreadOptions, CreateChatThreadRequest } from '@azure/communication-chat'
import { AzureCommunicationTokenCredential } from '@azure/communication-common'

export class AcsCommunicationService implements CommunicationServiceInterface {
  private fastify: FastifyInstance

  constructor(fastify: FastifyInstance, options: FastifyPluginOptions) {
    // Initialize any necessary resources or configurations here.
    this.fastify = fastify
  }

  private async getChatClient(resource: RelicAgent | RelicPractitioner | RelicPatient): Promise<ChatClient> {
    if (!resource.communicationIdentities) {
      throw new Error('Resource has no communication identities.');
    }
    const endpoint = resource.communicationIdentities.find((identity) => identity.service.includes('chat'))?.endpoint;
    // To be fixed: Tokens may be expired and need to be refreshed.
    const tokenCredential = new AzureCommunicationTokenCredential(
      resource.communicationIdentities.find((identity) => identity.service.includes('chat'))?.secret.token,
    );
    return new ChatClient(endpoint, tokenCredential);
  }

  private async getIdentityClient(organizationId: string): Promise<CommunicationIdentityClient> {
    const organization: RelicOrganization = await this.fastify.orgService.getOrganization(organizationId);
    const endpoint = (organization.endpoints?.find((e) => e.service.includes('chat'))).endpoint;
    // accessKey should also be available in the organization record.
    const accessKey = config.get('ACS.ACCESS_KEY');
    const tokenCredential = new AzureKeyCredential(accessKey as string)
    const acsIdentityClient = new CommunicationIdentityClient(endpoint, tokenCredential)
    return acsIdentityClient
  }

  private createTopic(ownerId: string, ownerType: string, organizationId: string, agentId: string): Topic {
    const topic: Topic = {
        currentAgentAcsId: agentId
    }
    return topic
  }

  public async createIdentity(resource: RelicPractitioner | RelicPatient): Promise<CommunicationIdentity> {
    const { organizationId } = resource
    let displayName: string = resource.name

    const organization: RelicOrganization = await this.fastify.orgService.getOrganization(organizationId)
    const services = (organization.endpoints?.find((e) => e.service.includes('chat'))).service as TokenScope[]
    const endpoint = (organization.endpoints?.find((e) => e.service.includes('chat'))).endpoint

    const acsClient = await this.getIdentityClient(organizationId)
    const acsUser = await acsClient.createUserAndToken(services)

    const identity: CommunicationIdentity = {
      userId: acsUser.user.communicationUserId,
      displayName: displayName,
      service: services,
      endpoint: endpoint,
      secret: {
        token: acsUser.token,
        expiresOn: acsUser.expiresOn,
      },
      threads: [],
    };
    return identity
  }

  public async refreshIdentity(resource: RelicPatient | RelicPractitioner | RelicAgent, identity: CommunicationIdentity): Promise<CommunicationIdentity> {
      if (!identity) {
          return null
      }
      const threshold = 1 * 30 * 60 * 1000 //30min
      const expiresOn = new Date(identity?.secret?.expiresOn)
      expiresOn.setTime(expiresOn.getTime() - threshold)
      const isAlmostExpired = expiresOn <= new Date()
      const { organizationId } = resource;
      if (isAlmostExpired) {
        const client = await this.getIdentityClient(organizationId) as CommunicationIdentityClient
        const tokenResponse = await client.getToken({ communicationUserId: identity?.userId }, identity?.service as any)
        identity.secret = tokenResponse
      }
      return identity
  }

  public async createThread(thread: Thread, createdBy: RelicAgent | RelicPractitioner | RelicPatient ): Promise<Thread> {
    const participantAgent = thread.participants.find((p) => p.resourceType === 'Practitioner' && p.type && p.type !== 'System Agent') as RelicChatParticipant;
    const participantAgentAcsId = participantAgent && (participantAgent.id as { communicationUserId: string }).communicationUserId
    const topic: Topic = this.createTopic(
        thread.threadSubject.threadOwner && thread.threadSubject.threadOwner.id,
        thread.threadSubject.threadOwner && thread.threadSubject.threadOwner.resourceType,
        thread.threadSubject.organizationId,
        participantAgent && participantAgentAcsId,
    )
    
    const chatClient = await this.getChatClient(createdBy);
    const request: CreateChatThreadRequest = { topic: JSON.stringify(topic) };
    const options: CreateChatThreadOptions = { participants: thread.participants as ChatParticipant[] };
    const acsThread = await chatClient.createChatThread(request, options);
    thread = {
      ...thread,
      threadId: acsThread.chatThread.id,
      threadTopic: topic,
    }
    return thread;
  }
}