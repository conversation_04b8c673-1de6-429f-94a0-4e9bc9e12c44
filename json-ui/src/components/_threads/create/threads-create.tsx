import { useState } from 'react';
import { SaveButton } from '@refinedev/mui';
import { FieldValues } from 'react-hook-form';
import { useEffect, useCallback } from 'react';
import { useForm } from '@refinedev/react-hook-form';
import { useCreate, useTranslate, useGetIdentity } from '@refinedev/core';
import {
  RelicAgent,
  RelicPatient,
  RelicOrganization,
  NewThread,
  ThreadSubject,
  IUser,
  NewRelicChatParticipant,
} from 'relic-ui';

import { Send } from '@mui/icons-material';
import { Stack, Dialog, Button, TextField } from '@mui/material';

import { useRouter } from 'src/routes/hooks';

import { Create } from 'src/components/refine-customs/create';
import { ResidentDropdown } from 'src/components/dropdown/residentDropdown';
import InvitationDropdown from 'src/components/dropdown/InvitationDropdown';
import AiParticipantRoleDropdown from 'src/components/dropdown/aiRoleDropdown';
import { OrganizationDropdown } from 'src/components/dropdown/organizationDropdown';

type Props = {
  open: boolean;
  onClose: VoidFunction;
  currentOrganization?: RelicOrganization;
  currentPatient?: RelicPatient;
};

const CreateThread = ({
  open,
  onClose,
  currentOrganization,
  currentPatient,
}: Props) => {
  const translate = useTranslate();
  const router = useRouter();
  const { data: currentUser } = useGetIdentity<IUser>();
  const [selectedAgent, setSelectedAgent] = useState<RelicAgent | null>(null);
  const [selectedPatient, setSelectedPatient] = useState<RelicPatient | null>(currentPatient || null);
  const { mutate, isLoading } = useCreate({
    mutationOptions: {
      onSuccess: ({ data }: any) => {
        router.push(
          `/threads/show/${data?.threadId}?threadId=${data?.threadId}&name=${data?.threadSubject?.title}`,
        );
      },
    },
  });
  const {
    watch,
    control,
    register,
    handleSubmit,
    saveButtonProps,
    setValue,
    formState: { errors },
    refineCore: { formLoading },
  } = useForm();

  const values = watch();

  const onSubmit = async (values: FieldValues) => {
    await handleThreadSaveOrInvite(values, selectedAgent, selectedPatient);
  };

  const handleSendInvitation = async () => {
    await handleThreadSaveOrInvite(values, selectedAgent, selectedPatient);
  };

  const handleThreadSaveOrInvite = async (
    formData: FieldValues,
    selectedAgent: RelicAgent | null,
    selectedPatient: RelicPatient | null,
  ) => {
    try {

      // Define participants array with both agent and patient
      const participants: NewRelicChatParticipant[] = [
        {
          resourceId: selectedAgent?.id,
          resourceType: 'Practitioner',
          role: selectedAgent?.role,
          type: selectedAgent?.type as "Patient Agent" | "Staff Agent" | "System Agent" | undefined,
        },
        {
          resourceId: selectedPatient?.id,
          resourceType: 'Patient',
        },
      ];

      const threadSubject: ThreadSubject = {
        organizationId: formData.organizationId,
        title: formData.title,
        targetPhoneNumber: formData.cellphone,
        patientLanguage: selectedPatient?.primaryLanguage ?? currentOrganization?.fhirStore?.defaultLanguage,
        threadOwner: {
          id: currentUser?.userIdentity?.id as string,
          resourceType: currentUser?.userIdentity?.resourceType as string,
        }
      };

      const newThread: NewThread = {
        endpoint: currentOrganization?.endpoints?.find((endpoint) => endpoint.provider === 'Azure Communication Service')?.endpoint as string,
        threadSubject: threadSubject,
        participants: participants,
        status: 'active',
        inviteVia: formData.invitation ?? 'none',
      }

      // Execute the mutation with constructed payload (commented out for testing)
      mutate({
        resource: 'communication/chat/threads',
        values: newThread,
      });

    } catch (error) {
      // Provide an error message for any failure in processing
      console.error('Error in handleThreadSaveOrInvite:', error);
    }
  };

  const handlePatientChange = useCallback(
    (patient?: RelicPatient | null) => {
      if (patient) {
        setValue('cellphone', patient.mobilePhone || '');
        setValue('email', patient.email || '');
        setSelectedPatient(patient);
      } else {
        setValue('cellphone', '');
        setValue('email', '');
        setSelectedPatient(null);
      }
    },
    [setValue],
  );

  useEffect(() => {
    if (currentPatient) {
      setValue('patientId', currentPatient.id);
      setValue('cellphone', currentPatient.mobilePhone || '');
      setValue('email', currentPatient.email || '');
    }
  }, [currentPatient, setValue]);

  return (
    <Dialog
      fullWidth
      maxWidth="sm"
      open={open}
      onClose={onClose}
      aria-labelledby="alert-dialog-title"
      aria-describedby="alert-dialog-description"
    >
      <Create
        isLoading={formLoading || isLoading}
        saveButtonProps={{
          ...saveButtonProps,
          onClick: handleSubmit(onSubmit),
        }}
        footerButtons={({ saveButtonProps }) => (
          <>
            {onClose && (
              <Button variant="outlined" onClick={onClose}>
                Cancel
              </Button>
            )}
            <Button variant="outlined" onClick={handleSendInvitation}>
              <Send fontSize="small" sx={{ mr: 1 }} />
              Send Invitation
            </Button>
            <SaveButton {...saveButtonProps} />
          </>
        )}
        title={translate('threads.titles.create')}
        onClose={onClose}
      >
        <Stack component="form" spacing={2.5} autoComplete="off">
          <TextField
            {...register('title', {
              required: 'This field is required',
            })}
            error={!!(errors as any)?.title}
            helperText={(errors as any)?.title?.message}
            margin="normal"
            required
            fullWidth
            InputLabelProps={{ shrink: true }}
            type="text"
            label={translate('threads.fields.title')}
            name="title"
          />
          <OrganizationDropdown
            control={control}
            name="organizationId"
            label={translate('threads.fields.organization')}
            value={currentOrganization?.id ?? null}
            error={!!(errors as any)?.organizationId}
            helperText={(errors as any)?.organizationId?.message}
            disabled={!!currentOrganization}
          />
          <ResidentDropdown
            control={control}
            name="patientId"
            label={translate('threads.fields.resident')}
            value={selectedPatient ? (selectedPatient.id as string) : ''}
            error={!!(errors as any)?.patientId}
            helperText={(errors as any)?.patientId?.message}
            onChange={handlePatientChange}
            disabled={!!currentPatient}
          />
          <AiParticipantRoleDropdown
            control={control}
            name="aiParticipant"
            label={translate('threads.fields.ai-participant')}
            error={!!(errors as any)?.aiParticipant}
            helperText={(errors as any)?.aiParticipant?.message}
            organizationId={
              currentOrganization
                ? currentOrganization.id
                : values.organizationId
            }
            currentPatientId={values.patientId ?? currentPatient?.id}
            value={values.aiParticipant ?? null}
            setSelectedAgent={setSelectedAgent}
          />
          <InvitationDropdown
            control={control}
            name="invitation"
            label={translate('threads.fields.invitation')}
            value={values.invitation ?? 'email'}
            error={!!(errors as any)?.invitation}
            helperText={(errors as any)?.invitation?.message}
          />
          {(values.invitation === 'cellphone' ||
            values.invitation === 'both') && (
            <TextField
              {...register('cellphone', {
                required: 'This field is required',
              })}
              error={!!(errors as any)?.cellphone}
              helperText={(errors as any)?.cellphone?.message}
              margin="normal"
              fullWidth
              InputLabelProps={{ shrink: true }}
              type="text"
              label={translate('threads.fields.cellphone')}
              name="cellphone"
            />
          )}
          {(values.invitation === 'email' || values.invitation === 'both') && (
            <TextField
              {...register('email', {
                required: 'This field is required',
              })}
              error={!!(errors as any)?.email}
              helperText={(errors as any)?.email?.message}
              margin="normal"
              fullWidth
              InputLabelProps={{ shrink: true }}
              type="text"
              label={translate('threads.fields.email')}
              name="email"
            />
          )}
        </Stack>
      </Create>
    </Dialog>
  );
};

export default CreateThread;
