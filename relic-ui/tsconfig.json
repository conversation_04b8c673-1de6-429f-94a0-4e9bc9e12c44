{"compilerOptions": {"target": "ESNext", "module": "ESNext", "moduleResolution": "node", "declaration": true, "declarationDir": "./dist", "outDir": "./dist", "jsx": "react-jsx", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "resolveJsonModule": true, "forceConsistentCasingInFileNames": true, "allowSyntheticDefaultImports": true}, "include": ["src/**/*.ts", "src/**/*.tsx"], "exclude": ["node_modules"]}