import { TrainingModule } from 'relic-ui';
import { FieldValues } from 'react-hook-form';
import { useForm } from '@refinedev/react-hook-form';
import { useCreate, useTranslate } from '@refinedev/core';

import { Stack, Dialog, TextField } from '@mui/material';

import { useRouter } from 'src/routes/hooks';

import { Create } from 'src/components/refine-customs/create';
import { AgentRoleDropDown } from 'src/components/dropdown/agentRoleDropdown';
import { OrganizationDropdown } from 'src/components/dropdown/organizationDropdown';

type Props = {
  open: boolean;
  onClose: VoidFunction;
  organizationId?: string;
};
const CreateTrainingModules = ({ open, onClose, organizationId }: Props) => {
  const translate = useTranslate();
  const router = useRouter();
  const {
    mutate: createTrainingModule,
    isLoading: createTrainingModuleLoading,
  } = useCreate({
    mutationOptions: {
      onSuccess: data => {
        const trainingModulesId = data?.data?.id;
        onClose();
        router.push(`/trainings/modules/edit/${trainingModulesId}`);
      },
    },
  });
  const {
    watch,
    control,
    handleSubmit,
    saveButtonProps,
    register,
    formState: { errors },
    refineCore: { formLoading },
  } = useForm<TrainingModule>({
    defaultValues: {
      name: '',
      organizationId: organizationId ?? '',
      description: '',
    },
  });

  const onSubmit = (values: FieldValues) => {
    try {
      const trainingModule: TrainingModule = {
        id: '',
        name: values.name,
        organizationId: values.organizationId,
        storage: {
          containerName: '',
          datasourceName: '',
        },
        description: values.description,
        role: values.role,
      };
      createTrainingModule({
        values: trainingModule,
        resource: 'trainings/modules',
      });
    } catch (error) {
      console.error(error);
    }
  };

  const values: TrainingModule = watch() as unknown as TrainingModule;

  return (
    <Dialog
      fullWidth
      maxWidth="sm"
      open={open}
      onClose={onClose}
      aria-labelledby="alert-dialog-title"
      aria-describedby="alert-dialog-description"
    >
      <Create
        isLoading={formLoading || createTrainingModuleLoading}
        saveButtonProps={{
          ...saveButtonProps,
          disabled: !values.name || !values.organizationId,
          onClick: handleSubmit(onSubmit),
        }}
        title={translate('trainings-modules.titles.create')}
      >
        <Stack component="form" spacing={2.5} autoComplete="off">
          <OrganizationDropdown
            control={control}
            name="organizationId"
            label={translate('trainings-modules.fields.organizationId')}
            error={!!(errors as any)?.organizationId}
            helperText={(errors as any)?.organizationId?.message}
            value={organizationId ?? null}
          />
          <TextField
            {...register('name', {
              required: 'This field is required',
            })}
            error={!!(errors as any)?.name}
            helperText={(errors as any)?.name?.message}
            margin="normal"
            fullWidth
            InputLabelProps={{ shrink: true }}
            type="text"
            label={translate('trainings-modules.fields.name')}
            name="name"
          />
          <TextField
            {...register('description', {
              required: 'This field is required',
            })}
            error={!!(errors as any)?.description}
            helperText={(errors as any)?.description?.message}
            margin="normal"
            fullWidth
            InputLabelProps={{ shrink: true }}
            type="text"
            multiline
            maxRows={4}
            minRows={4}
            label={translate('trainings-modules.fields.description')}
            name="description"
          />

          <AgentRoleDropDown
            control={control}
            name="role"
            label={translate('trainings-modules.fields.role')}
            value={
              values.role
                ? {
                    'display-role': values.role['display-role'] || '',
                    'practitioner-role': values.role['practitioner-role'] || '',
                  }
                : null
            }
            error={!!(errors as any)?.role}
            helperText={(errors as any)?.role?.message}
          />
        </Stack>
      </Create>
    </Dialog>
  );
};

export default CreateTrainingModules;
