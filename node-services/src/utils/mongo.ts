import { RelicSearchQuery } from "@/types/request";

/**
 * Experimental
 * Utility to build mongo query options from RelicSearchQuery
 * For complex queries, manually create them
 */
export class MongoQueryBuilder {
    _input: Partial<RelicSearchQuery>;

    _filter: any;
    _options: {
        skip: number;
        limit: number;
        projection: any;
        sort: any;
    }

    constructor() {
        this._input = {
            _start: 0,
            _end: 25,
            _order: 'asc'
        };
        this._filter = {};
    }

    withQuery(query: Partial<RelicSearchQuery>) {
        this._input = {
            ...this._input,
            ...query
        };
        if (query.organizationId) {
            this.withOrganizationId(query.organizationId);
        }
        if (query.id) {
            this.withId(query.id)
        }

        query.foreignFields?.forEach(({ key, value }) => {
            this.withForeignId(key, value);
        })
        return this;
    }

    //different collections may have different field name for organization id
    withOrganizationId(value: string, fieldPath: string = 'organizationId') {
        if (!(value && value.trim().length > 0)) return this;
        this._input.organizationId = value.trim();
        this._filter[fieldPath] = this._input.organizationId;
        return this;
    }

    /**
     * Add filter field id
     * @param {string} value 
     */
    withId(value: string) {
        if (!(value && value.trim().length > 0)) return this;
        this._input.id = value.trim();
        this._filter['id'] = this._input.id;
        return this;
    }

    /**
     * 
     * @param value 
     * @param foreignKey 
     * @returns 
     */
    withForeignId(foreignKey: string, value: string) {
        if (!(value && value.trim().length > 0 && foreignKey && foreignKey.trim().length > 0)) return this;
        value = value.trim();
        foreignKey = foreignKey.trim();
        this._input[foreignKey] = value;
        this._filter[foreignKey] = value;
        return this;
    };

    /**
     * 
     * @param fieldPath 
     * @param value 
     * @returns 
     */
    withField(fieldPath: string, value: string) {
        if (!(value && value.trim().length > 0 && fieldPath && fieldPath.trim().length > 0)) return this;
        value = value.trim();
        fieldPath = fieldPath.trim();
        this._input[fieldPath] = value;
        this._filter[fieldPath] = value;
        return this;
    }

    /**
     * 
     * @param fieldPath 
     * @param value 
     * @returns 
     */
    withBooleanField(fieldPath: string, value: boolean) {
        if (!(fieldPath && fieldPath.trim().length > 0)) return this;
        fieldPath = fieldPath.trim();
        this._input[fieldPath] = value;
        this._filter[fieldPath] = value;
        return this;
    }

    /**
     * If false, record must no be deleted
     * @param value 
     * @param fieldPath 
     */
    deleted(value: boolean = false, fieldPath: string = 'deleted') {
        this._filter[fieldPath.trim()] = value;
        return this;
    }


    /**
     * Regex matching for _search
     * Need to explicitly call this function if _search is available
     * @param value 
     * @param fieldPaths 
     * @param condition 
     * @returns 
     */
    withSearchField(value: string, fieldPaths: string[], condition?: '$or' | '$and') {
        if (!(value && value.trim().length > 0 && fieldPaths.length > 0)) return this;
        value = value.trim();
        const searchFields = [];
        condition = condition || '$or';
        searchFields.push(...(fieldPaths.map(field => {
            return {
                [field]: { $regex: value, $options: 'i' }
            }
        })));
        this._filter[condition] = [
            ...(this._filter[condition] || []),
            ...searchFields
        ]
        return this;
    }

    build(): { filter: any, options: { skip: number; limit: number; projection: any; sort: any; } } {
        //mongo options for pagination and sort
        const skip = Math.max(0, this._input._start);
        const limit = Math.max(1, this._input._end - skip);
        const projection = { _id: 0 };
        const sort = { [this._input._sort]: this._input._order == 'asc' ? 1 : -1 };
        this._options = { skip, limit, projection, sort };

        return {
            filter: this._filter,
            options: this._options
        }
    }
}