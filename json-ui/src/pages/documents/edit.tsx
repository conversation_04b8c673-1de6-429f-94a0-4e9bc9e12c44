import { Edit } from "@refinedev/mui";
import { useForm } from "@refinedev/react-hook-form";
import { useTranslate, IResourceComponentsProps } from "@refinedev/core";

import { Box, TextField } from "@mui/material";

export const DocumentEdit: React.FC<IResourceComponentsProps> = () => {
    const translate = useTranslate();
    const {
        saveButtonProps,
        register,
        formState: { errors },
    } = useForm();

    return (
        <Edit saveButtonProps={saveButtonProps}>
            <Box
                component="form"
                sx={{ display: "flex", flexDirection: "column" }}
                autoComplete="off"
            >
                <TextField
                    {...register("id", {
                        required: "This field is required",
                    })}
                    error={!!(errors as any)?.id}
                    helperText={(errors as any)?.id?.message}
                    margin="normal"
                    fullWidth
                    InputLabelProps={{ shrink: true }}
                    type="text"
                    label={translate("files.fields.id")}
                    name="id"
                    disabled
                />
                <TextField
                    {...register("object", {
                        required: "This field is required",
                    })}
                    error={!!(errors as any)?.object}
                    helperText={(errors as any)?.object?.message}
                    margin="normal"
                    fullWidth
                    InputLabelProps={{ shrink: true }}
                    type="text"
                    label={translate("files.fields.type")}
                    name="object"
                />
                <TextField
                    {...register("fields.name", {
                        required: "This field is required",
                    })}
                    error={!!(errors as any)?.fields?.name}
                    helperText={(errors as any)?.fields?.name?.message}
                    margin="normal"
                    fullWidth
                    InputLabelProps={{ shrink: true }}
                    type="text"
                    label={translate("files.fields.name")}
                    name="fields.name"
                />
                <TextField
                    {...register("downloadUrl", {
                        required: "This field is required",
                    })}
                    error={!!(errors as any)?.downloadUrl}
                    helperText={(errors as any)?.downloadUrl?.message}
                    margin="normal"
                    fullWidth
                    InputLabelProps={{ shrink: true }}
                    multiline
                    label={translate("files.fields.downloadUrl")}
                    name="downloadUrl"
                />
            </Box>
        </Edit>
    );
};
