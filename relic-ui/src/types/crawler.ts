import { PlaywrightCrawlerOptions, ProxyConfiguration } from '@crawlee/playwright';
import { RelicDocument } from './document';
import { StorageLocation } from './storage';
export interface URLLike {
    id: string; //uuid
    sourceDocumentId: string; //parent page id
    url: string;
    depth?: number;
    children?: URLLike[];
}

//Crawler definition
export interface Crawler {
    id: string;
    organizationId: string;
    name: string;
    apify: {
        actorId: string; //Apify Actor ID
        actorTaskId?: string; //Apify Actor Task ID, if available, run the task
    };
    configuration: RelicCrawlerOptions;
    default: boolean; //if true, use as default crawler for all crawl request within the organization
}

//CrawlerConfiguration configuration to use when running the crawler
export interface RelicCrawlerOptions extends PlaywrightCrawlerOptions {
    removeElementCssSelector: string[]; //html tags to be removed from the scraped content
    maxUrlsToCrawl: number; //maximum limit of crawled pages
    useReadableContent: boolean; //if true, only readable content is extracted
    maxDepth: number;
    crawlEmbeddedLinks: boolean; //if true, crawl embedded links
    relevanceParams?: RelevanceParams;
}

// CrawlerOutput output format of the scraped content per webpage
export type ScraperOutput = Omit<RelicDocument, 'status'> & {
    jsonContentUrl: string; //json content url
    /**
     * Sitemap of the crawled pages
     * Sitemap is sent to the queue when crawler finished
     * only available if runAsEstimator=true and onCrawlerFinishedJobName is provided
     */
    sitemap?: URLLike[];
    /**
     * pending: initial status
     * inprogress: crawler is running the scraping + crawling process
     * estimating_sitemap: url-estimator is running to estimate the number of URLs to crawl
     * sitemap_ready: url-estimator has finished and sitemap is ready
     * sitemap_failed: url-estimator has failed
     * done: training content is ready (terminal status)
     * failed: training content failed (terminal status)
     * 
     * Sample status transitions:
     * 1. pending -> inprogress -> done (normal crawling request)
     * 2. pending -> estimating_sitemap -> sitemap_ready (estimate request)
     * 3. pending -> estimating_sitemap -> sitemap_ready -> inprogress -> done (estimate + crawl request)
     * 4. pending -> estimating_sitemap -> sitemap_failed
     */
    status: 'pending' | 'estimating_sitemap' | 'sitemap_ready' | 'sitemap_failed' | 'inprogress' | 'failed' | 'done';
}

export interface RelicMessageQueueParams {
    queueName: string;
    jobName: string;
    port: number;
    host: string;
    password: string;
    tlsServername: string;
    maxRetriesPerRequest: number | null;
    moduleId?: string;
    organizationId?: string;
    createdBy?: {
        id: string;
        resourceType: string;
    };
}

export interface RelevanceParams {
    relevanceApiEndpoint: string; //url relevance check api endpoint
    subject: string;
    intent: string;
}

//CrawlerRunRequest run the crawler
export interface CrawlRequest {
    crawlStorage: StorageLocation; //if not provided, crawler will not upload content to storage   
    relicMessageQueueParams: RelicMessageQueueParams; //if not provided, crawler will not send notifications to queue
    urls: URLLike[];
    options: RelicCrawlerOptions; //additional configuration
    proxyConfiguration?: ProxyConfiguration;
    /**
        * If true, crawler will only estimate URLs 
        * File Scraping is skipped
        * Per-page scraperOutput is not send to the queue
        * Scraped content is not uploaded to storage
        * OnCrawlerFinished, scraperOutput is sent to the queue with sitemap
        */
    isEstimatorRun?: boolean;
}

/**
 * JSON format to be stored in the storage container
 */
export type ScrapedContent = {
    url: string;
    title: string;
    content: string;
    description: string;
    originalFilepath: string;
}

