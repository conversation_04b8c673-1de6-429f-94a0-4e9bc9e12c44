import React from 'react';
import { RelicOrganization } from 'relic-ui';
import { useAutocomplete } from '@refinedev/mui';
import { Control, Controller } from 'react-hook-form';

import TextField from '@mui/material/TextField';
import Autocomplete from '@mui/material/Autocomplete';

interface OrganizationDropdownProps {
  control: Control<any>;
  name: string;
  label: string;
  value: string | null;
  error: boolean;
  helperText: string;
  onChange?: (value: RelicOrganization | null) => void;
  disabled?: boolean;
}

export const OrganizationDropdown: React.FC<OrganizationDropdownProps> = ({
  control,
  name,
  label,
  value,
  error,
  helperText,
  onChange,
  disabled,
}) => {
  if (disabled && !value) {
    throw new Error('When disabled is true, value must not be empty.');
  }

  const { autocompleteProps: organizationAutocompleteProps } = useAutocomplete({
    resource: 'organizations',
    filters: disabled
      ? [
          {
            field: 'id',
            value: value,
            operator: 'eq',
          },
        ]
      : [],
  });

  return (
    <Controller
      control={control}
      name={name}
      defaultValue={value}
      rules={{ required: 'This field is required' }}
      render={({ field }) => {
        const { onChange: fieldOnChange, value: fieldValue } = field;
        return (
          <Autocomplete
            loading={organizationAutocompleteProps.loading}
            options={organizationAutocompleteProps.options}
            onInputChange={organizationAutocompleteProps.onInputChange}
            disabled={disabled}
            autoHighlight
            getOptionLabel={(option: RelicOrganization) =>
              organizationAutocompleteProps.options?.find(
                org => org.id === option.id,
              )?.name ?? ''
            }
            isOptionEqualToValue={(option, value) => {
              return (
                value === undefined ||
                option?.id?.toString() === (value?.id ?? value).toString()
              );
            }}
            onChange={(_, newValue) => {
              fieldOnChange(newValue && newValue.id ? newValue.id : null);
              if (onChange) {
                onChange(newValue ? newValue : null);
              }
            }}
            renderInput={params => (
              <TextField
                {...params}
                disabled={disabled}
                label={label}
                variant="outlined"
                error={error}
                helperText={helperText}
                required
              />
            )}
            value={
              organizationAutocompleteProps.options?.find(
                org => org.id === fieldValue,
              ) || null
            }
          />
        );
      }}
    />
  );
};
