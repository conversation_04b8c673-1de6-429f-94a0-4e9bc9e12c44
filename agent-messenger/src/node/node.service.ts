import { HttpService } from '@nestjs/axios';
import { Logger } from '@nestjs/common';
import { Injectable, OnModuleInit, NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { MedplumClient } from '@medplum/core';
import { RelicAgent, RelicPatient, RelicPractitioner, Thread, IUserIdentity, NewThread } from 'relic-ui';
import { FileSource } from '@azure/communication-call-automation';
import { lastValueFrom } from 'rxjs';

type TranslateResponse = {
  languageCode: string;
  translation?: string;
};

const waitMusic = '/public/pixabay-Relaxing_Music.mp3';

@Injectable()
export class NodeService implements OnModuleInit {
  private readonly logger = new Logger(NodeService.name);
  private medplumClient: MedplumClient;
  private initializationPromise: Promise<void>;
  private organizationId: string;
  private serviceUrl: string;

  constructor(
    private httpService: HttpService,
    private configService: ConfigService,
  ) {
    this.medplumClient = new MedplumClient();
    const serviceHost = this.configService.get<string>('RELIC_NODE_SERVICE_HOST');
    const servicePort = this.configService.get<string>('RELIC_NODE_SERVICE_PORT');
    const serviceProtocol = serviceHost === 'localhost' ? 'http' : 'https';
    if (!serviceHost || !servicePort || !serviceProtocol) {
      throw new NotFoundException('Node service host, port or protocol is missing or incorrect.');
    }
    // TBD: To be fixed. Currently, agent-messenger is connecting as a Medplum Client Application, which is treated as an admin with access to all threads.
    // This means that the serviceUrl is not provider-specific but used across all providers as a workaround.
    this.serviceUrl =
      serviceProtocol === 'http'
        ? `${serviceProtocol}://${serviceHost}:${servicePort}/api/medplum`
        : `${serviceProtocol}://${serviceHost}/api/medplum`;
  }

  async initialize(): Promise<void> {
    const MEDPLUM_CLIENT_ID = this.configService.get<string>('MEDPLUM_CLIENT_ID');
    const MEDPLUM_CLIENT_SECRET = this.configService.get<string>('MEDPLUM_CLIENT_SECRET');
    if (!MEDPLUM_CLIENT_ID || !MEDPLUM_CLIENT_SECRET) {
      throw new NotFoundException('Medplum client ID or secret is missing or incorrect.');
    }
    await this.medplumClient.startClientLogin(MEDPLUM_CLIENT_ID, MEDPLUM_CLIENT_SECRET);
  }

  async onModuleInit(): Promise<void> {
    this.initializationPromise = this.initialize();
    await this.initializationPromise;
  }

  public setOrganizationId(organizationId: string): void {
    this.organizationId = organizationId;
  }

  private async request<T>(endpoint: string, serializedQueryParams?: string): Promise<T> {
    await this.initializationPromise;

    const url = this.serviceUrl;
    const fullUrl = serializedQueryParams ? `${url}${endpoint}?${serializedQueryParams}` : `${url}${endpoint}`;

    await this.medplumClient.refreshIfExpired();
    const access_token = this.medplumClient.getActiveLogin()?.accessToken;

    const response = await lastValueFrom(
      this.httpService.get<T>(fullUrl, {
        headers: {
          'x-access-token': access_token,
          'x-organization-id': this.organizationId ?? '',
        },
      }),
    );
    return response.data;
  }

  private serializeQueryParams(params: Record<string, string | number>): string {
    return Object.entries(params)
      .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
      .join('&');
  }

  private async post<T>(endpoint: string, data?: any): Promise<T> {
    await this.initializationPromise;

    const url = this.serviceUrl;
    const fullUrl = `${url}${endpoint}`;

    await this.medplumClient.refreshIfExpired();
    const access_token = this.medplumClient.getActiveLogin()?.accessToken;

    const response = await lastValueFrom(
      this.httpService.post<T>(fullUrl, data, {
        headers: {
          'x-access-token': access_token,
          'x-organization-id': this.organizationId ?? '',
        },
      }),
    );
    return response.data;
  }

  private async patch<T>(endpoint: string, data?: any): Promise<T> {
    await this.initializationPromise;

    const url = this.serviceUrl;
    const fullUrl = `${url}${endpoint}`;

    await this.medplumClient.refreshIfExpired();
    const access_token = this.medplumClient.getActiveLogin()?.accessToken;

    const response = await lastValueFrom(
      this.httpService.patch<T>(fullUrl, data, {
        headers: {
          'x-access-token': access_token,
          'x-organization-id': this.organizationId ?? '',
        },
      }),
    );
    return response.data;
  }

  // Not in use
  async whoAmI(): Promise<IUserIdentity> {
    return this.request<IUserIdentity>(`/whoami`);
  }

  // Used by all services during initialization
  async getConfig(configId: string): Promise<{ value: string }> {
    return this.request<{ value: string }>(`/config/${configId}`);
  }

  // Used by agent service to obtain a list of all agents and their ids.
  async getAllAgents(): Promise<RelicAgent[]> {
    if (this.organizationId) {
      return this.request<RelicAgent[]>(`/agents?organizationId=${this.organizationId}`);
    } else {
      return this.request<RelicAgent[]>('/agents');
    }
  }

  // Used by agent service to obtain a specific agent by its id.
  async getAgent(id: string): Promise<RelicAgent> {
    return this.request<RelicAgent>(`/agents/${id}`);
  }

  // Used by everyone to obtain Relic AI Assistant.
  async getAIAssistant(): Promise<RelicAgent> {
    return this.request<RelicAgent>(`/agents/aiassistant`);
  }

  // Used by call service to obtain the call music. Guest API.
  async getCallMusic(): Promise<FileSource> {
    return {
      url: `${this.serviceUrl}${waitMusic}`,
      kind: 'fileSource',
    } as FileSource;
  }

  // Used only if the thread owner is a patient. Hence only medplum patients can use this method.
  async getPatientData(guid: string): Promise<RelicPatient> {
    return this.request<RelicPatient>(`/patients/${guid}/`);
  }

  // Used by ThreadService to get practitioner / caller details.
  async getPractitioner(id: string): Promise<RelicPractitioner> {
    const encodedId = encodeURIComponent(id);
    return this.request<RelicPractitioner>(`/practitioners/${encodedId}`);
  }

  // Not in use
  /* eslint-disable @typescript-eslint/no-unused-vars */
  async getPatientFromCallerId(callerId: string): Promise<RelicPatient> {
    // TODO: Implement this
    const guid = 'a0d4d41e-8463-4603-a704-237c70f194ee'; // <EMAIL>
    return this.getPatientData(guid);
  }

  // Not in use
  async getAcsIdFromCallerId(callerId: string): Promise<string> {
    // TODO: Implement this
    return '8:acs:8ca4fbc2-e63c-4283-a5b7-feb83074370e_0000001e-284b-f622-c811-24482200f47c'; // <EMAIL>
  }

  // Not in use
  async getLocaleFromCallerId(callerId: string): Promise<string> {
    // TODO: Implement this or something equlvalent
    return 'fr'; // <EMAIL>
  }
  /* eslint-enable @typescript-eslint/no-unused-vars */

  // This works across tenant boundaries since Medplum ClientApplication login is treated as an admin with access to all threads.
  async getThreadById(threadId: string): Promise<Thread> {
    const encodedThreadId = encodeURIComponent(threadId);
    return this.request<Thread>(`/communication/chat/threads/${encodedThreadId}`);
  }

  // Not in use
  async getThreadForPractitioner(resourceId: string): Promise<Thread> {
    return this.request<Thread>(`/practitioners/${resourceId}/chat`);
  }

  // Not in use
  async getThreads(queryParams: Record<string, string>): Promise<Thread[]> {
    const serializedQueryParams = this.serializeQueryParams(queryParams);
    return this.request<Thread[]>(`/communication/chat/threads`, serializedQueryParams);
  }

  // Not in use
  async getDefaultThread(patientId: string): Promise<Thread[]> {
    const queryParams = `type=Default&participants=${patientId}&status=active`;
    return this.request<Thread[]>('/communication/chat/threads', queryParams); // the length of the list should be 1
  }

  // Simple translation service so provider does not matter.
  async translate(text: string, targetLang: string, sourceLang?: string): Promise<TranslateResponse> {
    const requestBody = {
      text: [text],
      target_lang: targetLang,
      source_lang: sourceLang,
    };
    return await this.post<TranslateResponse>('/translate', requestBody);
  }

  // Used by ThreadService to create a new thread.
  async createThread(threadRequest: NewThread): Promise<Thread> {
    return this.post<Thread>('/communication/chat/threads', threadRequest);
  }
}
