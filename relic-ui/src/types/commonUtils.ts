import { RelicChatParticipant, Thread } from './communication';

export const constructThreadTitle = (thread: Thread): string => {
    let participant = thread.participants.find(p => p.resourceType === 'Patient') as RelicChatParticipant ??
        thread.participants.find(p => p.resourceType === 'Practitioner' && p.type === null) as RelicChatParticipant ??
        thread.participants.find(p => p.resourceType === 'Practitioner' && p.type !== 'System Agent') as RelicChatParticipant ??
        thread.participants.find(p => p.resourceType === 'Practitioner' && p.type === 'System Agent') as RelicChatParticipant;
    
    return `${participant?.displayName || participant?.resourceType} - ${thread.threadSubject.patientLanguage?.display || thread.threadSubject.patientLanguage?.code || 'English'}`;
};
