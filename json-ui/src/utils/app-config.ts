let appName: string | undefined;

export const setAppName = (value: string) => {
  appName = value;
};

export const getAppName = () => {
  if (!appName) {
    throw new Error('App name has not been set.');
  }
  return appName;
};

export const getNodeServicesApi = (): string => {
  const host = import.meta.env.VITE_NODE_SERVICES_HOST;
  const port = import.meta.env.VITE_NODE_SERVICES_PORT;
  if (!host || !port) {
    throw new Error(
      'VITE_NODE_SERVICES_HOST or VITE_NODE_SERVICES_PORT is not set.',
    );
  }
  if (host === 'localhost' || host === '127.0.0.1' || host === '0.0.0.0') {
    return `http://${host}:${port}`;
  }
  return `https://${host}`;
};
