import { useTranslation } from 'react-i18next';
import 'react-toastify/dist/ReactToastify.css';
import { ToastContainer } from 'react-toastify';
import dataProvider from '@refinedev/simple-rest';
import { Route, Routes, Outlet } from 'react-router-dom';
import { MuiInferencer } from '@refinedev/inferencer/mui';
import { Refine, CanAccess, Authenticated } from '@refinedev/core';
import { ErrorComponent, RefineSnackbarProvider } from '@refinedev/mui';
import routerBindings, {
  CatchAllNavigate,
  NavigateToResource,
  DocumentTitleHandler,
  UnsavedChangesNotifier,
} from '@refinedev/react-router-v6';

import SickIcon from '@mui/icons-material/Sick';
import ForumIcon from '@mui/icons-material/Forum';
import ThreePIcon from '@mui/icons-material/ThreeP';
import LogoutIcon from '@mui/icons-material/Logout';
import GroupAddIcon from '@mui/icons-material/GroupAdd';
import BusinessIcon from '@mui/icons-material/Business';
import ViewComfyIcon from '@mui/icons-material/ViewComfy';
import DataObjectIcon from '@mui/icons-material/DataObject';
import PsychologyIcon from '@mui/icons-material/Psychology';
import SmsOutlinedIcon from '@mui/icons-material/SmsOutlined';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import ViewCompactIcon from '@mui/icons-material/ViewCompact';
import AssignmentIndIcon from '@mui/icons-material/AssignmentInd';
import PsychologyAltIcon from '@mui/icons-material/PsychologyAlt';
import LocalHospitalIcon from '@mui/icons-material/LocalHospital';
import CastConnectedIcon from '@mui/icons-material/CastConnected';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import ManageAccountsIcon from '@mui/icons-material/ManageAccounts';
import MedicalInformationIcon from '@mui/icons-material/MedicalInformation';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';

import ThemeProvider from 'src/theme';
import { Login } from 'src/pages/login';
import { SearchProvider } from 'src/providers';
import { relicDataProvider } from 'src/providers';
import { ConditionList } from 'src/pages/condition';
import { Register } from 'src/pages/login/register';
import MyAccountComponent from 'src/pages/my-account';
import { Sandbox, EmbeddedCall } from 'src/pages/chat';
import { TrainingList } from 'src/pages/trainings/list';
import { TrainingEdit } from 'src/pages/trainings/edit';
import UserOnboardingComponent from 'src/pages/onboarding';
import { TrainingsCreate } from 'src/pages/trainings/create';
import { ReportDesign, ReportPreview } from 'src/pages/report';
import { authProviderRelicAi as authProvider } from 'src/providers';
import { FacilityList, FacilityEdit } from 'src/pages/facility';
import { DocumentList, DocumentCreate } from 'src/pages/documents';
import { TrainingModuleList } from 'src/pages/trainings-modules/list';
import { AgentEdit, AgentsList, AgentsCreate } from 'src/pages/agents';
import { TrainingsModulesEdit } from 'src/pages/trainings-modules/edit';
import { notificationProvider } from 'src/providers/notification-provider';
import { PatientList, PatientEdit, PatientCreate } from 'src/pages/patient';
import { TrainingsModulesCreate } from 'src/pages/trainings-modules/create';
import { accessControlProvider } from 'src/providers/access-control-provider';
import { StaffList, StaffEdit, StaffShow, StaffCreate } from 'src/pages/staff';
import {
  LocationEdit,
  LocationList,
  LocationCreate,
} from 'src/pages/locations';
import {
  OrganizationList,
  OrganizationEdit,
  OrganizationCreate,
} from 'src/pages/organization';
import {
  ThreadsEdit,
  ThreadsList,
  ThreadsShow,
  ThreadsCreate,
  MyConversations,
} from 'src/pages/threads';

import { ThemedLayoutV2 } from 'src/components/layout';
import { ThemedTitleV2 } from 'src/components/layout/title';
import { DocumentTitle } from 'src/components/_documentTitle';
import { PortalHeader } from 'src/components/header/portalHeader';
import UnAuthorized from 'src/components/refine-customs/unauthorized';
import { OrganizationIcon } from 'src/components/icon/OrganizationIcon';
import { SearchAwareRouter } from 'src/components/router/search-aware-router';

function App() {
  const { t, i18n } = useTranslation();

  const i18nProvider = {
    translate: (key: string, params: object) => t(key, params),
    changeLocale: (lang: string) => i18n.changeLanguage(lang),
    getLocale: () => i18n.language,
  };

  return (
    <ThemeProvider>
      <RefineSnackbarProvider>
        <Refine
          notificationProvider={notificationProvider}
          routerProvider={routerBindings}
          authProvider={authProvider}
          accessControlProvider={accessControlProvider}
          i18nProvider={i18nProvider}
          dataProvider={{
            default: relicDataProvider,
            json_server: dataProvider('https://relic-json-server.vercel.app'),
          }}
          resources={[
            {
              name: 'my-facility',
              meta: {
                label: 'My Facility',
                icon: <LocalHospitalIcon />,
              },
            },
            {
              name: 'patients',
              meta: {
                parent: 'my-facility',
                label: t('patients.patients'),
                icon: <GroupAddIcon />,
                canDelete: true,
              },
              list: '/patients',
              show: '/patients/show/:id',
              edit: '/patients/edit/:id',
              create: '/patients/create',
            },
            {
              name: 'documents',
              meta: {
                parent: 'my-facility',
                label: t('documents.documents'),
                icon: <ContentCopyIcon />,
                canDelete: true,
              },
              list: '/documents',
              show: '/documents/show/:id',
              edit: '/documents/edit/:id',
              create: '/documents/create',
            },
            {
              name: 'sandbox',
              meta: {
                parent: 'my-facility',
                label: t('chat.staffAi'),
                icon: <ForumIcon />,
              },
              list: '/sandbox',
            },
            {
              name: 'my-organization',
              meta: {
                label: 'My Organization',
                icon: <BusinessIcon />,
              },
            },
            {
              name: 'agents',
              meta: {
                parent: 'my-organization',
                label: t('agents.agents'),
                icon: <ManageAccountsIcon />,
              },
              list: '/agents',
              create: '/agents/create',
              edit: '/agents/edit/:id',
              show: '/agents/show/:id',
            },
            {
              name: 'training-modules',
              meta: {
                parent: 'my-organization',
                label: t('trainings-modules.label'),
                icon: <PsychologyIcon />,
                canDelete: true,
              },
              list: '/trainings/modules',
              create: '/trainings/modules/create',
              edit: '/trainings/modules/edit/:id',
              show: '/trainings/modules/show/:id',
            },
            {
              name: 'staff',
              meta: {
                parent: 'my-organization',
                label: t('staff.title'),
                icon: <AssignmentIndIcon />,
                canDelete: true,
              },
              list: '/staff',
              show: '/staff/show/:id',
              edit: '/staff/edit/:id',
              create: '/staff/create',
            },
            {
              name: 'organizations',
              meta: {
                parent: 'my-organization',
                label: t('organizations.organizations'),
                icon: <BusinessIcon />,
              },
              list: '/organizations',
              show: '/organizations/show/:id',
              edit: '/organizations/edit/:id',
              create: '/organizations/create',
            },
            {
              name: 'my-account',
              meta: {
                label: t('my-account.title'),
                icon: <ManageAccountsIcon />,
              },
              list: '/my-account',
            },
            {
              name: 'logout',
              meta: {
                label: 'Logout',
                icon: <LogoutIcon />,
              },
            },
            {
              name: 'live-data',
              meta: {
                label: 'Live Data',
                icon: <CastConnectedIcon />,
                hide: import.meta.env.MODE !== 'development',
              },
            },
            {
              name: 'facilities',
              meta: {
                parent: 'live-data',
                label: t('facilities.facilities'),
                icon: <MedicalInformationIcon />,
              },
              list: '/facilities',
              edit: '/facilities/edit/:id',
            },
            {
              name: 'trainings',
              meta: {
                parent: 'live-data',
                label: t('trainings.label'),
                icon: <PsychologyAltIcon />,
                canDelete: true,
              },
              list: '/trainings',
              create: '/trainings/create',
              edit: '/trainings/edit/:id',
              show: '/trainings/show/:id',
            },
            {
              name: 'agents',
              meta: {
                parent: 'live-data',
                label: t('agents.agents'),
                icon: <ManageAccountsIcon />,
                canDelete: true,
              },
              list: '/agents',
              create: '/agents/create',
              edit: '/agents/edit/:id',
              show: '/agents/show/:id',
            },
            {
              name: 'threads',
              meta: {
                parent: 'live-data',
                label: t('threads.threads'),
                icon: <SmsOutlinedIcon />,
                canDelete: true,
              },
              list: '/threads',
              create: '/threads/create',
              edit: '/threads/edit/:id',
              show: '/threads/show/:id',
            },
            {
              name: 'json-ui',
              meta: {
                label: 'JSON UI',
                icon: <DataObjectIcon />,
                hide: import.meta.env.MODE !== 'development',
              },
            },
            {
              name: 'conditions',
              meta: {
                parent: 'json-ui',
                icon: <MedicalInformationIcon />,
                dataProviderName: 'json_server',
              },
              list: '/conditions',
              show: '/conditions/show/:id',
              edit: '/conditions/edit/:id',
              create: '/conditions/create',
            },
            {
              name: 'allergyintolerances',
              meta: {
                parent: 'json-ui',
                icon: <SickIcon />,
                dataProviderName: 'json_server',
              },
              list: '/allergyintolerances',
              show: '/allergyintolerances/show/:id',
              edit: '/allergyintolerances/edit/:id',
              create: '/allergyintolerances/create',
            },
            {
              name: 'locations',
              meta: {
                parent: 'json-ui',
                label: t('locations.locations'),
                icon: <GroupAddIcon />,
                dataProviderName: 'json_server',
                canDelete: true,
              },
              list: '/locations',
              edit: '/locations/edit/:id',
              create: '/locations/create',
            },
            {
              name: 'report-content',
              meta: {
                parent: 'json-ui',
                label: 'Report Content',
                icon: <ViewCompactIcon />,
              },
              list: '/report-content',
            },
            {
              name: 'report-layout',
              meta: {
                parent: 'json-ui',
                label: 'Report Layout',
                icon: <ViewComfyIcon />,
              },
              list: '/report-layout',
            },
          ]}
          options={{
            syncWithLocation: true,
            useNewQueryKeys: true,
            projectId: 'uvSWGo-A848GS-kqC6S9',
            reactQuery: {
              clientConfig: {
                defaultOptions: {
                  queries: {
                    staleTime: 5 * 60 * 1000,
                    retry: false,
                  },
                },
              },
            },
          }}
        >
          <LocalizationProvider dateAdapter={AdapterDateFns}>
            <SearchProvider>
              <SearchAwareRouter>
                <Routes>
                  <Route
                    element={
                      <Authenticated
                        key="authenticated-inner"
                        fallback={<CatchAllNavigate to="/login" />}
                      >
                        <ThemedLayoutV2
                          Title={({ collapsed }) => (
                            <ThemedTitleV2
                              // collapsed is a boolean value that indicates whether the <Sidebar> is collapsed or not
                              collapsed={collapsed}
                              icon={<OrganizationIcon />}
                              text="Relic AI Portal"
                            />
                          )}
                          Header={() => <PortalHeader sticky />} //Header with search control
                        >
                          <CanAccess fallback={<UnAuthorized />}>
                            <Outlet />
                          </CanAccess>
                        </ThemedLayoutV2>
                      </Authenticated>
                    }
                  >
                    <Route
                      index
                      element={<NavigateToResource resource="patients" />}
                    />
                    <Route path="organizations">
                      <Route index element={<OrganizationList />} />
                      <Route path="edit/:id" element={<OrganizationEdit />} />
                      <Route path="create" element={<OrganizationCreate />} />
                    </Route>
                    <Route path="patients">
                      <Route index element={<PatientList />} />
                      <Route path="show/:id" element={<MuiInferencer />} />
                      <Route path="edit/:id" element={<PatientEdit />} />
                      <Route path="create" element={<PatientCreate />} />
                    </Route>
                    <Route path="my-account">
                      <Route index element={<MyAccountComponent />} />
                    </Route>
                    <Route path="conditions">
                      <Route index element={<ConditionList />} />
                      <Route path="show/:id" element={<MuiInferencer />} />
                      <Route path="edit/:id" element={<MuiInferencer />} />
                      <Route path="create" element={<MuiInferencer />} />
                    </Route>
                    <Route path="allergyintolerances">
                      <Route index element={<MuiInferencer />} />
                      <Route path="show/:id" element={<MuiInferencer />} />
                      <Route path="edit/:id" element={<MuiInferencer />} />
                      <Route path="create" element={<MuiInferencer />} />
                    </Route>
                    <Route path="locations">
                      <Route index element={<LocationList />} />
                      <Route path="edit/:id" element={<LocationEdit />} />
                      <Route path="create" element={<LocationCreate />} />
                    </Route>
                    <Route path="staff">
                      <Route index element={<StaffList />} />
                      <Route path="show/:id" element={<StaffShow />} />
                      <Route path="edit/:id" element={<StaffEdit />} />
                      <Route path="create" element={<StaffCreate />} />
                    </Route>
                    <Route path="sandbox">
                      <Route index element={<Sandbox />} />
                    </Route>
                    <Route path="embedded-call">
                      <Route index element={<EmbeddedCall />} />
                    </Route>
                    <Route path="documents">
                      <Route index element={<DocumentList />} />
                      <Route path="show/:id" element={<MuiInferencer />} />
                      <Route path="edit/:id" element={<MuiInferencer />} />
                      <Route path="create" element={<DocumentCreate />} />
                    </Route>
                    <Route path="agents">
                      <Route index element={<AgentsList />} />
                      <Route path="show/:id" element={<MuiInferencer />} />
                      <Route path="edit/:id" element={<AgentEdit />} />
                      <Route path="create" element={<AgentsCreate />} />
                    </Route>
                    <Route path="trainings">
                      <Route index element={<TrainingList />} />
                      <Route path="show/:id" element={<MuiInferencer />} />
                      <Route path="edit/:id" element={<TrainingEdit />} />
                      <Route path="create" element={<TrainingsCreate />} />
                      <Route path="modules">
                        <Route index element={<TrainingModuleList />} />
                        <Route path="show/:id" element={<MuiInferencer />} />
                        <Route
                          path="edit/:id"
                          element={<TrainingsModulesEdit />}
                        />
                        <Route
                          path="create"
                          element={<TrainingsModulesCreate />}
                        />
                      </Route>
                    </Route>
                    <Route path="threads">
                      <Route index element={<ThreadsList />} />
                      <Route path="my" element={<MyConversations />} />
                      <Route path="show/:id" element={<ThreadsShow />} />
                      <Route path="edit/:id" element={<ThreadsEdit />} />
                      <Route path="create" element={<ThreadsCreate />} />
                    </Route>
                    <Route path="facilities">
                      <Route index element={<FacilityList />} />
                      <Route path="edit/:id" element={<FacilityEdit />} />
                    </Route>
                    <Route path="report-content">
                      <Route index element={<ReportDesign />} />
                    </Route>
                    <Route path="report-layout">
                      <Route index element={<ReportPreview />} />
                    </Route>
                    <Route path="*" element={<ErrorComponent />} />
                  </Route>
                  <Route path="onboarding">
                    <Route index element={<UserOnboardingComponent />} />
                  </Route>
                  <Route
                    element={
                      <Authenticated
                        key="authenticated-outer"
                        fallback={<Outlet />}
                      >
                        <NavigateToResource />
                      </Authenticated>
                    }
                  >
                    <Route path="/login" element={<Login />} />
                    <Route path="/register" element={<Register />} />
                  </Route>
                </Routes>
              </SearchAwareRouter>
            </SearchProvider>
          </LocalizationProvider>

          <UnsavedChangesNotifier />
          <DocumentTitleHandler handler={DocumentTitle} />
          <ToastContainer />
        </Refine>
      </RefineSnackbarProvider>
    </ThemeProvider>
  );
}

export default App;
