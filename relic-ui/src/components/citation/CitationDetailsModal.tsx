import React, { useState } from 'react';
import {
  Box,
  <PERSON>ton,
  Dialog,
  DialogA<PERSON>,
  DialogContent,
  DialogTitle,
  Divider,
  IconButton,
  Tab,
  Tabs,
  Typography,
} from '@mui/material';
import { Citation } from '../../types';
import CitationViewer from './CitationViewer';

interface CitationDetailsModalProps {
  citations: Citation[];
  showCitationModel?: boolean;
  setShowCitationModel?: React.Dispatch<React.SetStateAction<boolean>>;
  citationIds: string[];
  children: React.ReactNode; // Accept children as a prop
  chunk: boolean;
}
interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
  padding: string;
}

const CitationDetailsModal: React.FC<CitationDetailsModalProps> = ({
  citations,
  citationIds,
  showCitationModel,
  setShowCitationModel,
  children, // Destructure children
  chunk = false,
}) => {
  const [value, setValue] = useState(0);
  const [openModal, setOpenModal] = useState(false);

  const handleOpenPdf = () => {
    setOpenModal(true);
  };

  const handleClosePdf = () => {
    setOpenModal(false);
  };

  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
  };
  const handleClose = () => {
    if (setShowCitationModel) {
      setShowCitationModel(false);
    }
  };

  const allSelectedCitations = citations.filter((c) => citationIds.includes(c.id));

  function a11yProps(index: number) {
    return {
      id: `simple-tab-${index}`,
      'aria-controls': `simple-tabpanel-${index}`,
    };
  }

  function CustomTabPanel(props: TabPanelProps) {
    const { children, value, index, padding, ...other } = props;

    return (
      <Box
        component={'span'}
        role="tabpanel"
        hidden={value !== index}
        id={`simple-tabpanel-${index}`}
        aria-labelledby={`simple-tab-${index}`}
        {...other}
      >
        {value === index && <Box sx={{ padding: padding }}>{children}</Box>}
      </Box>
    );
  }

  return (
    <>
      <React.Fragment>
        <Dialog
          maxWidth="md"
          onClose={handleClose}
          aria-labelledby="customized-dialog-title"
          open={showCitationModel as boolean}
        >
          <DialogTitle sx={{ m: 0, p: 2 }} id="customized-dialog-title">
            <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
              <Tabs value={value} onChange={handleChange}>
                {allSelectedCitations.map((citation, index) => (
                  <Tab
                    key={`citation-tab-header-${citation.id}-${index}`}
                    label={
                      <Typography fontWeight={500} fontSize={15}>
                        {citation.title}
                      </Typography>
                    }
                    {...a11yProps(index)}
                    wrapped
                  />
                ))}
              </Tabs>
            </Box>
          </DialogTitle>
          <DialogContent dividers sx={{ px: 2 }}>
            {/* Display the children content at the top of the modal */}
            {chunk === false && (
              <>
                <Typography
                  sx={{
                    fontStyle: 'italic', // Slant text
                    display: 'inline-block', // Ensure the text is wrapped properly
                  }}
                >
                  <mark style={{ padding: '0px 5px 0px 0px' }}>“{children}”</mark>
                  <Typography component="span" sx={{ fontStyle: 'normal' }}>
                    has been inferred from below extract
                  </Typography>
                </Typography>

                <Divider sx={{ my: 2, borderColor: '#00000047' }} />
              </>
            )}

            {allSelectedCitations.map((citation, index) => {
              citation.content = citation.content.replace(/\\n|\n/g, (match) => `${match} `);
              return (
                <CustomTabPanel
                  value={value}
                  index={index}
                  padding={'0'}
                  key={`citation-content-dialog${citation.id}-${index}`}
                >
                  <Box
                    component={'pre'}
                    sx={{
                      whiteSpace: 'pre-wrap',
                      fontFamily: (theme) => theme.typography.fontFamily,
                    }}
                  >
                    {citation.content}
                  </Box>
                  <CitationViewer
                    open={openModal}
                    onClose={handleClosePdf}
                    citationUrl={citation.url}
                    citationContent={citation.content}
                  />
                </CustomTabPanel>
              );
            })}
          </DialogContent>

          <DialogActions sx={{ m: 0, p: 1 }}>
            {allSelectedCitations.map((citation, index) => (
              <CustomTabPanel
                value={value}
                index={index}
                padding={'10px'}
                key={`citation-action-dialog${citation.id}-${index}`}
              >
                {citation.url && (
                  <Button autoFocus onClick={handleOpenPdf} color="primary" variant="outlined">
                    View Source
                  </Button>
                )}
              </CustomTabPanel>
            ))}
            <Button autoFocus onClick={handleClose} variant="outlined">
              Close
            </Button>
          </DialogActions>
        </Dialog>
      </React.Fragment>
    </>
  );
};

export default CitationDetailsModal;
