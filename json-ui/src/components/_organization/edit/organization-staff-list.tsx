import { List } from '@refinedev/mui';

import {
  DataGridPro,
  gridClasses,
  DataGridProProps,
} from '@mui/x-data-grid-pro';

export default function TabList({ ...other }: DataGridProProps) {
  return (
    <List
      headerProps={{
        sx: {
          display: 'none',
        },
      }}
      contentProps={{
        sx: {
          p: '0 !important',
        },
      }}
      breadcrumb={null}
      wrapperProps={{
        sx: {
          boxShadow: 'none',
          background: 'transparent',
          [`& .${gridClasses.root}`]: {
            [`& .${gridClasses.columnHeader}`]: {
              bgcolor: 'transparent',
            },
            [`& .${gridClasses.columnHeaders}`]: {
              bgcolor: 'transparent',
              borderBottomStyle: 'dashed',
            },
          },
        },
      }}
    >
      <DataGridPro
        {...other}
        sx={{
          '& .MuiDataGrid-cell': {
            display: 'flex',
            alignSelf: 'center', // Center aligns the items vertically
            height: '100%', // Ensure the cell takes full height for vertical centering,
            border: 'none',
          },
        }}
        autoHeight
        pageSizeOptions={[25, 50, 100]}
      />
    </List>
  );
}
