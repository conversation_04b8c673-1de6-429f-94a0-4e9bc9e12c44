import * as React from 'react';
import { FieldValues } from 'react-hook-form';
import { useForm } from '@refinedev/react-hook-form';
import { useTranslate, IResourceComponentsProps } from '@refinedev/core';

import Stack from '@mui/material/Stack';
import Dialog from '@mui/material/Dialog';
import TextField from '@mui/material/TextField';

import { useRouter } from 'src/routes/hooks';

import PhoneInput from 'src/components/phone-input';
import { Create } from 'src/components/refine-customs/create';
import { OrgTypeDropdown } from 'src/components/dropdown/orgTypeDropdown';
import { LanguageDropdown } from 'src/components/dropdown/languageDropdown';

import { RelicOrganization } from 'relic-ui';

import facilityType from '../../data/facilityType.json';

export const OrganizationCreate: React.FC<IResourceComponentsProps> = () => {
  const router = useRouter();
  const translate = useTranslate();

  const [open, setOpen] = React.useState(true);

  const {
    watch,
    control,
    register,
    handleSubmit,
    saveButtonProps,
    formState: { errors },
    refineCore: { formLoading, onFinish },
  } = useForm<RelicOrganization>();

  const localOrgType = watch(
    'type',
    facilityType.find(item => item.code === 'prov'),
  );

  const handleClose = () => {
    setOpen(false);
    router.push('/organizations');
  };

  /**
   * Pre submission handler for create form
   * @param {FieldValues} values
   * @returns void
   */
  const onSubmit = (values: FieldValues) => {
    values.resourceType = 'Organization';
    values.active = true;
    values.type = values.type.code;
    onFinish({
      ...values,
    });
  };

  return (
    <Dialog
      fullWidth
      maxWidth="sm"
      open={open}
      onClose={handleClose}
      aria-labelledby="alert-dialog-title"
      aria-describedby="alert-dialog-description"
    >
      <Create
        isLoading={formLoading}
        saveButtonProps={{
          ...saveButtonProps,
          onClick: handleSubmit(onSubmit),
        }}
        onClose={handleClose}
      >
        <Stack spacing={2.5} component="form" autoComplete="off">
          <TextField
            {...register('name', {
              required: 'This field is required',
            })}
            fullWidth
            type="text"
            name="name"
            InputLabelProps={{ shrink: true }}
            label={translate('organizations.fields.name')}
            required
            error={!!(errors as any)?.name}
            helperText={(errors as any)?.name?.message}
          />

          {/* Type field */}
          <OrgTypeDropdown
            control={control}
            name="type"
            label={translate('organizations.fields.type')}
            value={
              facilityType.find(item => item.code === localOrgType?.code)
                ?.code || null
            }
            error={!!(errors as any)?.type}
            helperText={(errors as any)?.type?.message}
          />

          {/* Website Field */}
          <TextField
            {...register('website', {
              required: 'This field is required',
            })}
            error={!!(errors as any)?.website}
            helperText={(errors as any)?.website?.message}
            placeholder="Add Facility Website Link"
            fullWidth
            InputLabelProps={{ shrink: true }}
            type="text"
            label={translate('organizations.fields.website')}
            name="website"
            required
          />

          {/* Default language */}
          <LanguageDropdown
            control={control}
            name="fhirStore.defaultLanguage"
            label={translate('organizations.fields.language')}
            value={watch('fhirStore.defaultLanguage') || {
              system: 'http://hl7.org/fhir/ValueSet/languages',
              code: 'en-US',
              display: 'English (United States)',
            }}
            error={!!(errors as any)?.fhirStore?.defaultLanguage}
            helperText={(errors as any)?.fhirStore?.defaultLanguage?.message}
          />

          {/* Phone field */}
          <PhoneInput
            control={control}
            label={translate('organizations.fields.phone')}
            name="phone"
          />

          {/* Fax field */}
          <PhoneInput
            control={control}
            label={translate('organizations.fields.fax')}
            name="fax"
          />

          {/* Patient Summary Template field */}
          {/* <TextField
            {...register('template')}
            error={!!(errors as any)?.template}
            helperText={(errors as any)?.template?.message}
            label={translate('organizations.fields.patientSummary')}
            fullWidth
            InputLabelProps={{ shrink: true }}
            multiline
            minRows={4}
            maxRows={8}
            value="{{name}} is a {{age}} old {{sex}} suffering from {{conditions}}, and under treatment at {{facilityName}} as inpatient."
            name="template"
            style={{ width: '100%' }}
          /> */}
        </Stack>
      </Create>
    </Dialog>
  );
};
