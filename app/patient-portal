# syntax=docker/dockerfile:1

FROM node:22-slim AS prod-build
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
RUN corepack enable

WORKDIR /app
COPY ./relic-ui ./relic-ui

WORKDIR /app/relic-ui
RUN rm -rf node_modules
RUN --mount=type=cache,id=pnpm,target=/pnpm/store pnpm install
RUN pnpm run build

WORKDIR /app
COPY ./patient-portal ./patient-portal

FROM prod-build AS build
WORKDIR /app/patient-portal
RUN --mount=type=cache,id=pnpm,target=/pnpm/store pnpm install --frozen-lockfile
RUN NODE_OPTIONS=--max-old-space-size=8192 pnpm run build

EXPOSE 8032
CMD [ "pnpm", "start", "--host", "0.0.0.0" ]