import { useTranslate } from '@refinedev/core';
import { EmailField } from '@refinedev/mui';
import { useMemo } from 'react';
import { IUser, RelicOrganization } from 'relic-ui';

import { Chip } from '@mui/material';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';

import { PhoneInputView } from 'src/components/phone-input';

// ----------------------------------------------------------------------

type AccountDetailsViewProps = {
  showEdit: boolean;
  currentUser: IUser;
  organizationAutocompleteProps: any;
};

export default function AccountDetailsView({
  showEdit,
  currentUser,
  organizationAutocompleteProps,
}: AccountDetailsViewProps) {
  const translate = useTranslate();

  const currentOrganization: RelicOrganization = useMemo(() => {
    return organizationAutocompleteProps?.options?.find(
      (org: RelicOrganization) =>
        org.id === currentUser?.userIdentity?.portalIdentity.organizationId,
    );
  }, [
    currentUser?.userIdentity?.portalIdentity.organizationId,
    organizationAutocompleteProps?.options,
  ]);

  if (!currentUser) {
    return null;
  }

  return (
    <Stack
      spacing={2}
      component="div"
      sx={{
        flexDirection: 'column',
        display: showEdit ? 'none' : 'flex',
      }}
    >
      <div>
        <Typography
          component={'span'}
          variant="subtitle2"
          sx={{
            fontSize: 12,
            color: 'text.secondary',
            display: 'block',
            mb: 0.5,
          }}
        >
          {translate('my-account.fields.name')}
        </Typography>

        <Typography component={'span'} variant="body2">
          {currentUser?.userIdentity?.portalIdentity.name || '-'}
        </Typography>
      </div>

      <div>
        <Typography
          component={'span'}
          variant="subtitle2"
          sx={{
            fontSize: 12,
            color: 'text.secondary',
            display: 'block',
            mb: 0.5,
          }}
        >
          {translate('my-account.fields.email')}
        </Typography>

        <EmailField
          value={currentUser?.userIdentity?.portalIdentity.email || '-'}
        />
      </div>

      <div>
        <PhoneInputView
          value={currentUser?.userIdentity?.portalIdentity.mobilePhone}
          label={translate('my-account.fields.phone')}
        />
      </div>

      <div>
        <Typography
          component={'span'}
          variant="subtitle2"
          sx={{
            fontSize: 12,
            color: 'text.secondary',
            display: 'block',
            mb: 0.5,
          }}
        >
          {translate('my-account.fields.organization')}
        </Typography>

        <Typography component={'span'} variant="body2">
          {currentOrganization?.name || '-'}
        </Typography>
      </div>
      <div>
        <Typography
          component={'span'}
          variant="subtitle2"
          sx={{
            fontSize: 12,
            color: 'text.secondary',
            display: 'block',
            mb: 0.5,
          }}
        >
          {translate('my-account.fields.provider')}
        </Typography>

        <Typography component={'span'} variant="body2">
          <Chip label={currentUser.userIdentity?.provider || '-'} />
        </Typography>
      </div>
    </Stack>
  );
}
