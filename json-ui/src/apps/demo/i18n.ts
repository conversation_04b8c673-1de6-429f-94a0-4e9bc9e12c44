import i18n from 'i18next';
import Backend from 'i18next-http-backend';
import { initReactI18next } from 'react-i18next';
import detector from 'i18next-browser-languagedetector';

i18n
  .use(Backend)
  .use(detector)
  .use(initReactI18next)
  .init({
    supportedLngs: [
      // 'ar',
      // 'hy',
      // 'bg',
      // 'my',
      // 'yue',
      // 'zh',
      'en',
      // 'fil',
      // 'fr',
      // 'hi',
      // 'it',
      // 'ja',
      // 'ko',
      // 'zh',
      // 'fa',
      // 'ru',
      // 'es',
      // 'th',
      // 'vi',
    ],
    backend: {
      loadPath: '/locales/{{lng}}/{{ns}}.json',
    },
    ns: ['common'],
    defaultNS: 'common',
    fallbackLng: ['en'],
  });

export default i18n;
