{"name": "{{indexerName}}", "dataSourceName": "{{dataSourceName}}", "targetIndexName": "{{indexName}}", "skillsetName": "{{skillset<PERSON>ame}}", "schedule": {"interval": "PT24H"}, "parameters": {"configuration": {"indexedFileNameExtensions": ".json", "parsingMode": "json", "dataToExtract": "contentAndMetadata"}}, "fieldMappings": [{"sourceFieldName": "url", "targetFieldName": "url"}, {"sourceFieldName": "content", "targetFieldName": "content"}, {"sourceFieldName": "metadata_storage_path", "targetFieldName": "filepath"}, {"sourceFieldName": "metadata_storage_last_modified", "targetFieldName": "last_updated", "mappingFunction": null}, {"sourceFieldName": "deleted", "targetFieldName": "deleted", "mappingFunction": null}, {"sourceFieldName": "originalFilepath", "targetFieldName": "originalFilepath"}]}