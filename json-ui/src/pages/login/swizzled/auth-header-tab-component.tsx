import { Box, Tab, Tabs } from '@mui/material';

import { useRouter } from 'src/routes/hooks';

const AuthHeaderTab = ({
  defaultTab = 'sign-in',
}: {
  defaultTab?: 'sign-in' | 'create-account';
}) => {
  const router = useRouter();
  const handleChange = (_: React.SyntheticEvent, newValue: string) => {
    if (newValue === 'sign-in') {
      router.push('/login');
    } else if (newValue === 'create-account') {
      router.push('/register');
    }
  };

  return (
    <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
      <Tabs
        value={defaultTab}
        onChange={handleChange}
        aria-label="basic tabs example"
        variant="fullWidth"
      >
        <Tab label="Create Account" value="create-account" />
        <Tab label="Sign In" value="sign-in" />
      </Tabs>
    </Box>
  );
};

export default AuthHeaderTab;
