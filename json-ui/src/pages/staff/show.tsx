import * as React from 'react';
import {
  useOne,
  useShow,
  useTranslate,
  IResourceComponentsProps,
} from '@refinedev/core';
import {
  UrlField,
  DateField,
  EmailField,
  TextFieldComponent as TextField,
} from '@refinedev/mui';

import Box from '@mui/material/Box';
import Stack from '@mui/material/Stack';
import Dialog from '@mui/material/Dialog';
import Skeleton from '@mui/material/Skeleton';

import { useRouter } from 'src/routes/hooks';

import Label from 'src/components/label';
import { Show } from 'src/components/refine-customs/show';

// ----------------------------------------------------------------------

export const StaffShow: React.FC<IResourceComponentsProps> = () => {
  const translate = useTranslate();
  const { query } = useShow();

  const router = useRouter();

  const [open, setOpen] = React.useState(true);

  const { data, isLoading } = query;

  const practitionerData = data?.data;

  const { data: organizationData, isLoading: organizationIsLoading } = useOne({
    resource: 'organizations',
    id: practitionerData?.organizationId || '',
    queryOptions: {
      enabled: !!practitionerData,
    },
  });

  const handleClose = () => {
    setOpen(false);
    router.push('/staff');
  };

  const rows = [
    {
      label: translate('staff.fields.name'),
      value: practitionerData?.name,
    },
    { label: translate('staff.fields.email'), value: practitionerData?.email },
    {
      label: translate('staff.fields.status'),
      value: practitionerData?.enabled,
    },
    {
      label: translate('staff.fields.inviteAccepted'),
      value: practitionerData?.userPrincipalName,
    },
    {
      label: translate('staff.fields.organizationId'),
      value: organizationData?.data?.name,
    },
  ];

  return (
    <Dialog
      fullWidth
      maxWidth="sm"
      open={open}
      onClose={handleClose}
      aria-labelledby="alert-dialog-title"
      aria-describedby="alert-dialog-description"
    >
      <Show onClose={handleClose} isLoading={isLoading}>
        <Stack spacing={3}>
          {rows.map(row => (
            <Stack
              key={row.label}
              spacing={1}
              alignItems="flex-start"
              direction={{ xs: 'column', sm: 'row' }}
            >
              <Box
                component={'span'}
                sx={{
                  typography: 'subtitle2',
                  minWidth: 160,
                }}
              >
                {row.label}
              </Box>

              {isLoading ? (
                <Skeleton
                  variant="rectangular"
                  sx={{ width: 1, height: 16, borderRadius: 0.5 }}
                />
              ) : (
                (['Created At', 'Last Active Date', 'Last Login Date'].includes(
                  row.label,
                ) && <DateField value={row.value} />) ||
                (row.label === 'Email' && <EmailField value={row.value} />) ||
                (row.label === 'Invite Accepted' && (
                  <Label
                    variant="soft"
                    color={
                      ((practitionerData as unknown as any)?.lastLoginDate &&
                        'success') ||
                      'error'
                    }
                  >
                    {(practitionerData as unknown as any)?.lastLoginDate
                      ? 'Yes'
                      : 'No'}
                  </Label>
                )) ||
                (row.label === 'Status' && (
                  <Label
                    variant="soft"
                    color={(row.value && 'success') || 'error'}
                  >
                    {row.value &&
                      translate('staff.fields.statusValues.enabled')}
                    {!row.value &&
                      translate('staff.fields.statusValues.disabled')}
                  </Label>
                )) || <TextField value={row.value} />
              )}
            </Stack>
          ))}
        </Stack>
      </Show>
    </Dialog>
  );
};
