import React from 'react';
import { useForm } from '@refinedev/react-hook-form';

import { Stack } from '@mui/material';

import { useRouter } from 'src/routes/hooks';

import { usePopover } from 'src/hooks/use-popover';

import TrainingViewToolbar from 'src/components/_trainings/edit/training-toolbar';
import TrainingAddPopover from 'src/components/_trainings/edit/training-add-popover';
import TrainingModuleSubGrid from 'src/components/_trainings/edit/training-modules-sub-grid';
import TrainingModulesTabs from 'src/components/_trainings-modules/edit/training-modules-tabs';

const TABS = [
  {
    value: 'training-modules',
    label: 'trainings.tabs.training-modules',
  },
];

export const TrainingEdit = () => {
  const queryParams = new URLSearchParams(location.search);
  const initialTab = queryParams.get('tab') || TABS[0].value;
  const [showAddTrainingModulesDialogue, setShowAddTrainingModulesDialogue] =
    React.useState(false);
  const [currentTab, setCurrentTab] = React.useState(
    TABS.find(tab => tab.value === initialTab)?.value,
  );
  const router = useRouter();
  const popoverAdd = usePopover();

  const {
    refineCore: { query },
    formState: { isLoading },
  } = useForm();

  const trainingsData = query?.data?.data;

  const trainingModules = trainingsData?.modules;

  const onBack = () => {
    router.back();
  };

  const handleChangeTab = React.useCallback(
    (event: React.SyntheticEvent, newValue: string) => {
      setCurrentTab(newValue);
    },
    [],
  );

  return (
    <>
      <TrainingViewToolbar
        title={trainingsData?.name}
        onBack={onBack}
        onOpenAdd={popoverAdd.onOpen}
      />

      <TrainingAddPopover
        popover={popoverAdd}
        showAddTrainingContentDialogue={showAddTrainingModulesDialogue}
        setShowAddTrainingContentDialogue={setShowAddTrainingModulesDialogue}
        onTrainingContentAddClick={() => {
          setShowAddTrainingModulesDialogue(true);
          popoverAdd.onClose();
        }}
      />
      <Stack
        sx={{
          width: '100%',
        }}
      >
        <TrainingModulesTabs
          tabOptions={TABS}
          currentTab={currentTab as string}
          onChangeTab={handleChangeTab}
        />

        {!isLoading && currentTab === 'training-modules' && (
          <>
            <TrainingModuleSubGrid rows={trainingModules} />
          </>
        )}
      </Stack>
    </>
  );
};
