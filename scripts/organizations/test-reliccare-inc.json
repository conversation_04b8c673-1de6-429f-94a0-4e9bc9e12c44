[{"id": "73551112-bda6-4127-b33b-af4ffa82b0d8", "resourceType": "Organization", "type": "bus", "active": true, "name": "TEST - Relic Care, Inc", "website": "localhost:3073", "location": [{"id": "baea9307-d24a-4d1b-9a5f-10986594d324", "resourceType": "Location", "status": "active", "name": "Relic Care, Inc", "phone": "+17882199211", "fax": "+11221232433"}], "copilotCompany": {"id": "90bac878-9a2e-4d71-ac08-fc1a755f3b5a", "name": "TEST - Relic Care, Inc", "iconImageUrl": "https://static.wixstatic.com/media/71fed0_f2a4216af9f847b786e2e02e727b397f~mv2.png/v1/fill/w_416,h_90,al_c,q_85,usm_0.66_1.00_0.01,enc_auto/relic-care-logo-dark.png", "fallbackColor": "#EC4E20", "leadInternalUserId": "ffcd8392-64d2-43df-9548-055b257dfaee"}, "fhirStore": {"clientApplication": {"id": "872fe0a8-fda8-49c7-b9ef-0bfadb8d509b", "resourceType": "ClientApplication", "compartment": "Project/51b61d21-e073-4cfa-adbc-e4cd247e1fec"}, "defaultLanguage": {"system": "http://hl7.org/fhir/ValueSet/languages", "code": "en-US", "display": "English - US"}, "provider": "medplum"}, "defaultChatEndpoint": {"url": "https://relic-acs.unitedstates.communication.azure.com/", "provider": "Azure Communication Service"}, "template": {"patientSummary": "My name is {{name}}. I am a {{age}} years old {{sex}} suffering from {{conditions}} admitted at {{facilityName}}.{{goal}}\n{{expectation}}", "welcomeSms": "Hello {{name}}!\nWelcome to RelicCare!\nTo access your account, please visit {{verifyUrl}}.\nThank you for choosing RelicCare.", "welcomeEmail": "<!DOCTYPE html>\n<html>\n    <head>\n        <meta charset=\"utf-8\">\n        <meta http-equiv=\"X-UA-Compatible\" content=\"IE-edge\">\n        <title>Welcome to RelicCare</title>\n    </head>\n\n    <body>\n        <p>Dear {{name}},</p>\n        <p>Welcome to RelicCare! We are thrilled to have you join our community.</p>\n        <p>To access your account, please visit: <a href=\"{{verifyUrl}}\">Verify my Account</a>.</p>\n        <p>Thank you for choosing RelicCare. We look forward to providing you with a seamless and enjoyable experience.</p>\n        <p>Best regards,<br/>\n            RelicCare\n        </p>\n    </body>\n</html>", "agentSummary": "Your name is {{name}}."}, "endpoints": [{"service": ["chat", "voip"], "endpoint": "https://relic-acs.unitedstates.communication.azure.com/", "provider": "Azure Communication Service", "defaultAgents": [{"id": "848d1965-fdfe-426c-be34-ee065930b764", "type": "Patient Agent"}, {"id": "d47b8b3a-3094-4eab-8274-0129fdd3382e", "type": "Staff Agent"}]}, {"service": ["patient-portal"], "endpoint": "", "provider": "Relic Care"}]}]