import { FastifyInstance, FastifyPluginAsync } from "fastify";
import config from 'config';
import { ActorRun, ApifyClient, TaskCallOptions } from "apify-client";
import { Crawler, CrawlRequest } from "relic-ui";
import { MongoQueryBuilder } from "../../utils/mongo";

declare module 'fastify' {
  export interface FastifyInstance {
    runCrawler(input: CrawlRequest, options?: TaskCallOptions): Promise<ActorRun>;
  }
}

const apifyPlugin: FastifyPluginAsync = async (instance: FastifyInstance, options: any) => {
    const logger = instance.log.child({ plugin: 'apifyPlugin' }, { msgPrefix: '[apify] ' });
    const apifyClient = new ApifyClient({
        token: config.get('APIFY.TOKEN') as string,
        maxRetries: 3,
        minDelayBetweenRetriesMillis: 500, //0.5s
        timeoutSecs: 60 * 60 * 5 //5mins
    });
    const DB_COL_CRAWLERS = 'crawlers';

    async function getDefaultCrawlerFromDB(): Promise<Crawler> {
        const { filter, options: { projection } } = new MongoQueryBuilder()
            .withBooleanField('default', true)
            .build();
        const resource: Crawler = await instance.mongo.reliccare.db.collection<Crawler>(DB_COL_CRAWLERS).findOne(filter, { projection });
        if (!resource) {
            throw instance.httpErrors.notFound(`Default crawler not found.`);
        }
        return resource;
    };

    async function runCrawler(input: CrawlRequest, options?: TaskCallOptions): Promise<ActorRun> {
        logger.info(input, 'Send request to crawler.');
        const crawler: Crawler = await getDefaultCrawlerFromDB();
        if (!(crawler.apify.actorId || crawler.apify.actorTaskId)) {
            throw new Error(`crawler service not available, id: ${crawler.id}`);
        }
        const opts: TaskCallOptions = {
            build: 'latest',
            waitSecs: 10, //don't wait for actor to finish
            timeout: 3 * 60 * 60, //timeout after 3 hours
            ...options
        }
        input.options = {
            ...crawler.configuration,
            ...(input.options || {})
        }

        if (crawler.apify.actorTaskId) { //prioritize running actor task
            return await apifyClient.task(crawler.apify.actorTaskId).call(input as never, opts);
        }
        if (crawler.apify.actorId) {
            return await apifyClient.actor(crawler.apify.actorId).call(input as never, opts);
        }

    }

    instance.decorate('apifyClient', apifyClient);
    instance.decorate('getDefaultCrawlerFromDB', getDefaultCrawlerFromDB);
    instance.decorate('runCrawler', runCrawler);
}

export default apifyPlugin;