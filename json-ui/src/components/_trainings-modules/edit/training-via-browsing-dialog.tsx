import { useCreate, useShow, useTranslate } from '@refinedev/core';
import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';

import {
  Alert,
  Checkbox,
  Dialog,
  FormControlLabel,
  InputAdornment,
  Stack,
  TextField,
  Typography,
} from '@mui/material';
import { Controller, useForm } from 'react-hook-form';
import { Create } from 'src/components/refine-customs/create';

// URL validation regex
const URL_REGEX = /^[^\s]+$/; // Modified to validate just the part after https://

const TrainingViaBrowsingDialog = ({
  open,
  onClose,
  refetchTrainingContent,
}: {
  open: boolean;
  onClose: VoidFunction;
  refetchTrainingContent: VoidFunction;
}) => {
  const [error, setError] = useState<string | null>(null);

  const { id } = useParams();
  const translate = useTranslate();

  const { query } = useShow({
    resource: 'trainings/modules',
    id: id,
  });

  const { mutate, isLoading } = useCreate({
    resource: `trainings/modules/${id}/contents/estimate`,
    invalidates: ['list'],
  });

  const {
    control,
    register,
    reset,
    watch,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm({
    defaultValues: {
      url: '',
      useReadableContent: true,
      maxRequestsPerCrawl: 100,
    },
  });

  const watchUrl = watch('url');
  const watchMaxRequests = watch('maxRequestsPerCrawl');
  const watchUseReadableContent = watch('useReadableContent');

  // Reset state when dialog is closed
  useEffect(() => {
    if (!open) {
      setError(null);
      reset();
    }
  }, [open, reset]);

  const onSubmit = async () => {
    // Validate URL before proceeding
    if (!URL_REGEX.test(watchUrl)) {
      setError('Please enter a valid URL');
      return;
    }

    setError(null);

    try {
      const containerName = query?.data?.data?.storage?.containerName;

      if (!containerName) {
        setError('Training module container not found.');
        return;
      }

      const payloadData = {
        url: `https://${watchUrl}`,
        type: 'url',
        options: {
          maxUrlsToCrawl: watchMaxRequests,
          maxDepth: 5,
          useReadableContent: watchUseReadableContent,
          crawlEmbeddedLinks: true,
        },
      };

      mutate(
        {
          values: payloadData,
          successNotification: () => ({
            message: `Successfully added Training Content for estimation`,
            type: 'success',
          }),
          errorNotification: (error: any) => ({
            message: `Error adding Training Content: ${error.message}`,
            type: 'error',
          }),
        },
        {
          onSuccess: () => {
            refetchTrainingContent();
            handleClose();
          },
          onError: (error: any) => {
            setError(
              error.message ||
                'An error occurred while adding training content',
            );
          },
        },
      );
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    }
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  return (
    <Dialog
      fullWidth
      maxWidth="md"
      open={open}
      onClose={handleClose}
      aria-labelledby="alert-dialog-title"
      aria-describedby="alert-dialog-description"
    >
      <Create
        isLoading={isSubmitting || isLoading}
        title={translate('trainings-content.titles.add-via-browsing')}
        onClose={handleClose}
        saveButtonProps={{
          onClick: handleSubmit(onSubmit),
          disabled: isSubmitting || isLoading,
          loading: isSubmitting || isLoading,
        }}
      >
        <Typography sx={{ typography: 'subtitle3', mb: 3 }}>
          {translate('trainings-content.subtitles.add-via-browsing')}
        </Typography>
        <Stack spacing={2.5} component="form" autoComplete="off">
          {error && <Alert severity="error">{error}</Alert>}

          <TextField
            {...register('url', {
              required: 'This field is required',
              pattern: {
                value: URL_REGEX,
                message: 'Please enter a valid URL',
              },
            })}
            error={!!errors.url}
            helperText={errors.url?.message as string}
            fullWidth
            required
            type="url"
            label={translate('trainings-content.fields.starting_url')}
            name="url"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">https://</InputAdornment>
              ),
            }}
          />

          <Controller
            name="useReadableContent"
            control={control}
            render={({ field }) => (
              <FormControlLabel
                label={translate('trainings-content.fields.readable-content')}
                labelPlacement="end"
                control={<Checkbox {...field} checked={field.value} />}
              />
            )}
          />

          <TextField
            {...register('maxRequestsPerCrawl', {
              required: 'This field is required',
              min: {
                value: 20,
                message: 'Minimum value is 20',
              },
              max: {
                value: 200,
                message: 'Maximum value is 200',
              },
            })}
            error={!!errors.maxRequestsPerCrawl}
            helperText={errors.maxRequestsPerCrawl?.message as string}
            fullWidth
            required
            type="number"
            label={translate('trainings-content.fields.max-requests')}
            name="maxRequestsPerCrawl"
            inputProps={{ min: 20, max: 200 }}
          />
        </Stack>
      </Create>
    </Dialog>
  );
};

export default TrainingViaBrowsingDialog;
