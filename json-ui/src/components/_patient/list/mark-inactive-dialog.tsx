import { useTranslate } from '@refinedev/core';

import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';

// ----------------------------------------------------------------------

type Props = {
  open: boolean;
  onClose: VoidFunction;
  onConfirm?: VoidFunction;
};

export default function MarkInactiveDialog({ open, onClose, onConfirm }: Props) {
  const translate = useTranslate();

  return (
    <Dialog
      fullWidth
      maxWidth="xs"
      open={open}
      onClose={onClose}
      aria-labelledby="alert-dialog-title"
      aria-describedby="alert-dialog-description"
    >
      <DialogTitle id="alert-dialog-title"> {translate('buttons.confirm')}</DialogTitle>

      <DialogContent sx={{ p: 3 }}>Are you sure you want to mark inactive?</DialogContent>

      <DialogActions>
        <Button onClick={onClose}>{translate('buttons.cancel')}</Button>
        <Button
          variant="contained"
          onClick={() => {
            onClose();
            onConfirm?.();
          }}
        >
          {translate('buttons.btnConfirm')}
        </Button>
      </DialogActions>
    </Dialog>
  );
}
