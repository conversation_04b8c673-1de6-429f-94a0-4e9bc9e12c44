import { Thread } from 'relic-ui';
import { useOne, useCustom, useApiUrl } from '@refinedev/core';

export const useThreadData = (
  threadId: string | null,
  accessToken: string,
  authUser: any,
) => {
  const apiUrl = useApiUrl();
  const resourceType =
    authUser?.userIdentity?.resourceType === 'Patient'
      ? 'patients'
      : 'practitioners';

  const {
    data: threadDataDefault,
    isLoading: isLoadingDefault,
    error: defaultError,
  } = useCustom({
    url: `${apiUrl}/${resourceType}/${authUser?.userIdentity?.id}/chat`,
    method: 'get',
    queryOptions: {
      enabled: !!accessToken && !threadId,
    },
  });

  const {
    data: threadDataThreadId,
    isLoading: isLoadingThreadId,
    error: threadError,
  } = useOne({
    resource: `communication/chat/threads`,
    id: threadId!,
    queryOptions: {
      enabled: !!threadId,
    },
  });

  return {
    threadData: threadId ? threadDataThreadId : threadDataDefault,
    isLoading: threadId ? isLoadingThreadId : isLoadingDefault,
    error: threadId ? threadError : defaultError,
  };
};
