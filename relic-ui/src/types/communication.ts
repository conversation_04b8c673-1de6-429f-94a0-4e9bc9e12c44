import { ChatParticipant } from '@azure/communication-chat';
import { CommunicationIdentifier } from '@azure/communication-common';
import { WidgetRequestWithMessageId } from './widget';
import { SecretSchema, Secret, RelicUserSchema, RelicCommunicationLanguageSchema, RoleSchema, RelicUser } from './relicBase';
import { Type, Static } from '@sinclair/typebox';

export const CommunicationIdentitySchema = Type.Object({
  userId: Type.String(),
  displayName: Type.Optional(Type.String()),
  service: Type.Array(Type.String()),
  endpoint: Type.String(),
  secret: SecretSchema,
  threads: Type.Array(Type.String()),
});

// Use Secret type for secret property
export type CommunicationIdentity = Omit<Static<typeof CommunicationIdentitySchema>, 'secret'> & { secret: Secret };

export const TopicSchema = Type.Object({
  currentAgentAcsId: Type.Optional(Type.String()),
  threadMessage: Type.Optional(Type.String()),
});

export type Topic = Static<typeof TopicSchema> & {
  interactiveWidget?: WidgetRequestWithMessageId;
  error?: Error;
};

export type ThreadTopic = string | Topic;

export const ThreadSubjectSchema = Type.Object({
  organizationId: Type.String(),
  questionnaireId: Type.Optional(Type.String()),
  threadOwner: Type.Optional(RelicUserSchema),
  targetPhoneNumber: Type.Optional(Type.String()),
  patientLanguage: Type.Optional(RelicCommunicationLanguageSchema),
  title: Type.Optional(Type.String()),
});

export type ThreadSubject = Static<typeof ThreadSubjectSchema>;

export const RelicChatParticipantSchema = Type.Object({
  resourceType: Type.Optional(Type.Union([Type.Literal('Patient'), Type.Literal('Practitioner')])),
  resourceId: Type.Optional(Type.String()),
  role: Type.Optional(RoleSchema),
  type: Type.Optional(Type.Union([
    Type.Literal('Patient Agent'),
    Type.Literal('Staff Agent'),
    Type.Literal('System Agent')
  ])),
  chatLanguage: Type.Optional(RelicCommunicationLanguageSchema),
  mobilePhone: Type.Optional(Type.String()),
});

export type NewRelicChatParticipant = Static<typeof RelicChatParticipantSchema>;

export type RelicChatParticipant = Partial<ChatParticipant> & NewRelicChatParticipant & {
  speechId?: CommunicationIdentifier;
};

export const ThreadSchema = Type.Object({
  endpoint: Type.String(),
  threadSubject: ThreadSubjectSchema,
  participants: Type.Array(RelicChatParticipantSchema),
  status: Type.Union([Type.Literal('active'), Type.Literal('closed')]),
  inviteVia: Type.Union([
    Type.Literal('email'),
    Type.Literal('cellphone'),
    Type.Literal('both'),
    Type.Literal('none')
  ]),
});

export type NewThread = Static<typeof ThreadSchema>;

export type Thread = NewThread & {
  threadId: string;
  threadTopic: ThreadTopic;
  participants: RelicChatParticipant[];
  createdBy?: RelicUser;
  createDate: Date;
  updatedBy?: RelicUser;
  updateDate?: Date;
};

export type AgentFor = {
  id: string;
  resourceType: string;
};

export type ThreadContext = {
  agentPerspective: string;
  patientSummary: string;
  agentSummary: string;
};

export type ThreadsWithCount = {
  threads: Thread[];
  count: number;
};

export interface ThreadQueryParams {
  patientId?: string; // To get thread with a specific patient
  assistantRole?: string; // To get thread with a specific AI assistant role (e.g. 'compliance')
  participants?: {
    // For explicit participant specification
    id: string;
    type: string;
  }[];
};

type FlowState = {
  myResourceName: string; //Input 
  myResourceType: string; //Input
  myMessage?: string; //Output. Input is sent as a chat message.
  myLastMessage?: string; //Input + Output
  patientLanguage?: string; //Input + Output
  turns?: number; //Internal
  error?: Error; //Internal
  ended?: boolean; //Input + Output
};

export type InterpreterFlowState = FlowState & {
  otherResourceName?: string; //Input + Output
  otherResourceType?: string; //Input + Output
  otherLastMessage?: string; //Input
  speakerFaqs?: [{ //Input
    question: string;
    answer: string;
  }];
  messagesForSwitch?: [{ //Output
    content: string;
    forResourceType: string;
    forResourceName: string;
  }];
};
