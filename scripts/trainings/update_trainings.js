/**
 * cb-662 trainings data model update
 * Update trainings collection
 * add new fields 'indexerName' and 'parsingMode' for each module
 * remove indexer field from module
 * RUN command:
 * mongosh "mongodb://localhost:27017/relic-care" update_trainings.js
 */
db.trainings.find().forEach((doc) => {
    const updatedModules = doc.modules.map((module) => {
        if (module.indexer) {
            module.indexerName = module.indexer.indexerName; // Set the value of indexerName
            module.parsingMode = 'json'; // Set the value of parsingMode
            //NOTE: commented for backward compatibility while cb1-662 changes are not merged
            //delete module.indexer; // Remove the indexer field
        }
        return module;
    });

    // Update the document with the modified modules array
    db.trainings.updateOne(
        { _id: doc._id },
        { $set: { modules: updatedModules }, $unset: { updateDate: "", updatedBy: "" } }
    );
});
