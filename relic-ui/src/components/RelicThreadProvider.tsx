import React, { useRef, createContext, useCallback, useContext, useEffect, useState } from 'react';
import { AzureCommunicationTokenCredential, CommunicationIdentifier, CommunicationUserIdentifier, getIdentifierRawId } from '@azure/communication-common';
import { ChatThreadClient } from '@azure/communication-chat';
import type { ChatThreadPropertiesUpdatedEvent, ChatMessageReceivedEvent, TypingIndicatorReceivedEvent } from '@azure/communication-chat';
import {
  ChatClientProvider,
  ChatThreadClientProvider,
  createStatefulChatClient,
  fromFlatCommunicationIdentifier,
  CommunicationParticipant
} from '@azure/communication-react';
import type { StatefulChatClient } from '@azure/communication-react';
import type { AbortSignalLike } from '@azure/abort-controller'
import { useUserContext } from './RelicUserProvider';
import { extractDisplayNameFromUserIdentity } from '../utils/userUtils';
import type {
  Topic,
  Secret,
  CommunicationIdentity,
  Thread,
  RelicAgent,
  ChatMetadata,  
  IUserIdentity,
  TranslateResponse,
  RelicChatParticipant,
  CallNotificationWidget,
  RelicCommunicationLanguage,
} from '../types';
import { ThreadState } from '../types/toolbar';

interface ThreadContextType {
  chatClient: StatefulChatClient | undefined;
  chatThreadClient: ChatThreadClient | undefined;
  thread: Thread | undefined;
  topic: Topic | undefined;
  displayInEnglish: boolean;
  threadState: ThreadState;
  typingParticipants: CommunicationParticipant[];
  callConnectionId: string | undefined;
  updateThreadState: (threadState: ThreadState) => void;
  translate: (text: string, targetLang: string, sourceLang?: string) => Promise<TranslateResponse>;
  startPhoneCall: () => Promise<void>;
  startVoiceCall: () => Promise<void>;
  getOnCallParticipantIdentity: () => Promise<CommunicationIdentity>;
  hangUp: () => Promise<void>;
  updateThread: (targetPhoneNumber?: string, patientLanguage?: RelicCommunicationLanguage) => void;
  updateDisplayInEnglish: (displayInEnglish: boolean) => void;
  threadError: string | null;
  updateThreadError: (error: string) => void;
  reinitializeThread: () => void;
}

const ThreadContext = createContext<ThreadContextType>({
  chatClient: undefined,
  chatThreadClient: undefined,
  thread: undefined,
  topic: undefined,
  displayInEnglish: false,
  threadState: ThreadState.Idle,
  typingParticipants: [],
  callConnectionId: undefined,
  updateThreadState: () => {
    console.warn('updateThreadState function is not ready.');
  },
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  translate: async (text: string, targetLang: string, sourceLang?: string): Promise<TranslateResponse> => {
    console.warn('translate function is not ready.');
    return { languageCode: 'en', translation: 'translate function is not ready.' };
  },
  startPhoneCall: async () => {
    console.warn('startPhoneCall function is not ready.');
  },
  startVoiceCall: async () => {
    console.warn('startVoiceCall function is not ready.');
  },
  getOnCallParticipantIdentity: async () => {
    console.warn('getOnCallParticipantIdentity function is not ready.');
    return {} as CommunicationIdentity;
  },
  hangUp: async () => {
    console.warn('hangUp function is not ready.');
  },
  updateThread: () => {
    console.warn('updateThread function is not ready.');
  },
  updateDisplayInEnglish: () => {
    console.warn('setThreadLanguage function is not implemented yet');
  },
  threadError: null,
  updateThreadError: () => {
    console.warn('updateThreadError function is not implemented yet');
  },
  reinitializeThread: () => {
    console.warn('reinitializeThread function is not ready.');
  },
});

export interface RelicThreadProviderOptions {
  displayInEnglish: boolean;
  counterPart?: RelicChatParticipant;
  threadState?: ThreadState;
}
``;
export interface RelicThreadProviderProps {
  children: React.ReactNode;
}

async function initializeChatClient(
  myIdentity: IUserIdentity,
  aiAssistant: RelicAgent,
  thread: Thread,
  displayName: string,
  setThreadState: React.Dispatch<React.SetStateAction<ThreadState>>,
) {
  const myParticipation = thread.participants?.find(
    (participant: RelicChatParticipant) => participant.resourceId === myIdentity.id,
  );
  let secret, userId, chatService;
  if (myParticipation) {
    const myCommunicationIds = myIdentity?.communicationIdentities as CommunicationIdentity[];
    const myChatService = myCommunicationIds?.find((identity) => identity.service.includes('chat'));
    if (myChatService && myChatService.endpoint && myChatService.userId && myChatService.secret) {
      userId = myChatService.userId;
      secret = myChatService.secret as never as Secret;
      chatService = myChatService;
    } else {
      throw new Error('Chat service is not configured.');
    }
  } else {
    const aiAsstCommunicationIds = aiAssistant?.communicationIdentities as CommunicationIdentity[];
    const aiAsstChatService = aiAsstCommunicationIds?.find((identity) => identity.service.includes('chat'));
    if (aiAsstChatService && aiAsstChatService.endpoint && aiAsstChatService.userId && aiAsstChatService.secret) {
      userId = aiAsstChatService.userId;
      secret = aiAsstChatService.secret as never as Secret;
      chatService = aiAsstChatService;
      setThreadState(ThreadState.ReadOnly);
    } else {
      throw new Error('Chat service is not configured.');
    }
  }
  const dummyTokenRefresher = async (_abortSignal?: AbortSignalLike): Promise<string> => {
    return 'dummy token';
  }; // TODO: implement token refresher
  const rcUserToken = new AzureCommunicationTokenCredential({
    tokenRefresher: dummyTokenRefresher,
    token: secret && secret.token,
    refreshProactively: true,
  });
  const client = createStatefulChatClient({
    userId: { communicationUserId: userId as string },
    endpoint: chatService.endpoint,
    credential: rcUserToken,
    displayName,
  });
  const threadClient = client.getChatThreadClient(thread.threadId);
  return { client, threadClient };
}

export const RelicThreadProvider: React.FC<RelicThreadProviderProps & RelicThreadProviderOptions> = ({
  children,
  counterPart,
  displayInEnglish: displayInEnglishProp,
  threadState: threadStateProp = ThreadState.Idle,
}) => {
  const {
    accessToken,
    idToken,
    serviceUri,
    agentMessengerUri,
    aiAssistant,
    myDefaultAgent,
    myIdentity,
    providerError,
    updateProviderError,
    updateProviderLoading,
  } = useUserContext();
  const voiceCallWindowRef = useRef<Window | null>(null);
  const [chatClient, setChatClient] = useState<StatefulChatClient | undefined>(undefined);
  const [chatThreadClient, setChatThreadClient] = useState<ChatThreadClient | undefined>(undefined);
  const [threadIdParam, setThreadIdParam] = useState<string | undefined>(undefined);
  const [queryParams, setQueryParams] = useState<URLSearchParams | undefined>(undefined);
  const [thread, setThread] = useState<Thread | undefined>(undefined);
  const [topic, setTopic] = useState<Topic | undefined>(undefined);
  const [callConnectionId, setCallConnectionId] = useState<string | undefined>(undefined);
  const [typingParticipants, setTypingParticipants] = useState<CommunicationParticipant[]>([]);
  const [threadState, setThreadState] = useState<ThreadState>(threadStateProp);
  const [displayInEnglish, setDisplayInEnglish] = useState<boolean>(displayInEnglishProp);
  const [threadError, setThreadError] = useState<string | null>(null);

  const updateThreadError = useCallback((error: string | null): void => {
    if (error) setThreadError(error);
  }, []);

  const updateDisplayInEnglish = useCallback((displayInEnglish: boolean): void => {
    setDisplayInEnglish(displayInEnglish);
  }, []);

  const updateThreadState = useCallback((threadState: ThreadState): void => {
    updateThreadError(null);
    switch (threadState) {
      case ThreadState.Idle:
        setThreadState(ThreadState.Idle);
        break;
      case ThreadState.Connecting:
        setThreadState(ThreadState.Connecting);
        setTopic({
          ...topic,
          currentAgentAcsId: topic?.currentAgentAcsId as string,
          threadMessage: 'Connecting...',
        });
        break;
      case ThreadState.Connected:
        setThreadState(ThreadState.Connected);
        setTopic({
          ...topic,
          currentAgentAcsId: topic?.currentAgentAcsId as string,
          threadMessage: 'Connected',
        });
        break;
      default:
        console.warn('Unknown call connection state:', threadState);
    }
  }, []);

  const updateThread = useCallback(
    (targetPhoneNumber?: string, patientLanguage?: RelicCommunicationLanguage): void => {
      setThread((prevThread) => {
        if (!prevThread) return undefined;
        return {
          ...prevThread,
          threadSubject: {
            ...prevThread.threadSubject,
            targetPhoneNumber: targetPhoneNumber || prevThread.threadSubject.targetPhoneNumber,
            patientLanguage: patientLanguage || prevThread.threadSubject.patientLanguage,
          },
        };
      });
    },
    [],
  );

  // Open window command for voice call
  const openVoiceCallWindow = useCallback((threadIdParam: string) => {
    const width = 500;
    const height = 300;
    const left = (window.screen.width - width) / 2;
    const top = (window.screen.height - height) / 2;
    //Todo: Add handling for host app and it's root path. Can be different than window.origin.
    const appName = window.location.pathname.split('/')[1];
    let origin = window.origin;
    console.log('appName:', appName); // For debugging
    if (appName === 'lobby') {
      origin = window.origin + '/lobby';
    }
    if (appName === 'demo') {
      origin = window.origin + '/demo';
    }
    voiceCallWindowRef.current = window.open(
      origin + `/embedded-call` + `/?${threadIdParam}`,
      'Call Panel',
      `width=${width},height=${height},0,0`,
      //`width=${width},height=${height},left=${left},top=${top}`
    );
  }, []);

  // Event handler for voice call auto start after receiving message from embedded call screen
  useEffect(() => {
    window.addEventListener('message', (event) => {
      if (event.origin !== window.origin) {
        return;
      }
      if (event.data === 'request-start-call' && threadState === ThreadState.Idle) {
        voiceCallWindowRef.current?.postMessage('start-call', window.origin);
      }
      if (event.data === 'request-end-call') {
        updateThreadState(ThreadState.Idle);
      }
    });
    const queryParams = new URLSearchParams(window.location.search);
    if (queryParams) setQueryParams(queryParams);
  }, []);

  useEffect(() => {
    if (topic?.error) {
      updateThreadError(topic.error.message);
      chatThreadClient?.updateTopic(JSON.stringify({ ...topic, error: null }));
    }
    if (topic?.threadMessage && topic.threadMessage.includes('HELLO')) {
      chatThreadClient?.updateTopic(JSON.stringify(topic));
    }
  }, [topic?.error, topic?.threadMessage]);

  useEffect(() => {
    if (queryParams?.get('threadId')) {
      setThreadIdParam(queryParams.get('threadId') as string);
    }
  }, [queryParams]);

  const saveThreadSubject = async (
    patientLanguage: RelicCommunicationLanguage,
    targetPhoneNumber?: string,
  ): Promise<void> => {
    const threadId = chatThreadClient?.threadId;

    if (!threadId) {
      throw new Error('Thread ID not found.');
    }

    const url = `${serviceUri}/communication/chat/threads/${threadId}`;

    const headers = {
      accept: 'application/json',
      'Content-Type': 'application/json',
      'x-access-token': accessToken,
      'x-id-token': idToken ?? '',
      'x-organization-id': myIdentity?.portalIdentity?.organizationId || '',
      'x-hostname': window.location.hostname,
    };

    const body = {
      threadSubject: {
        targetPhoneNumber,
        patientLanguage,
      },
    };

    const response = await fetch(url, {
      method: 'PATCH',
      headers,
      body: JSON.stringify(body),
    });

    const data = await response.json();

    if (!response.ok) {
      console.error('Error updating thread subject:', data.message);
      throw new Error(data.message || `HTTP error! status: ${response.status}`);
    }
  };

  const translate = async (text: string, targetLang: string, sourceLang?: string): Promise<TranslateResponse> => {
    try {
      const url = `${serviceUri}/translate`;
      const options: RequestInit = {
        headers: {
          'Content-Type': 'application/json',
          'x-access-token': accessToken,
          'x-id-token': idToken ?? '',
          'x-organization-id': myIdentity?.portalIdentity?.organizationId || '',
          'x-hostname': window.location.hostname,
        },
        method: 'POST',
        body: JSON.stringify({
          text: [text],
          target_lang: targetLang,
          source_lang: sourceLang,
        }),
      };

      const response = await fetch(url, options);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || `HTTP error! status: ${response.status}`);
      }

      return data;
    } catch (error) {
      const errorMessage = `Failed to translate text: ${(error as Error).message}`;
      updateThreadError(errorMessage);
      throw error;
    }
  };

  const startPhoneCall = async (): Promise<void> => {
    try {
      const threadId = thread?.threadId as string;
      if (queryParams?.get('auto')) {
        queryParams?.delete('auto');
        const url = new URL(window.location.href);
        url.search = queryParams.toString();
        window.history.pushState({}, '', url.toString());
      }
      updateThreadState(ThreadState.Connecting);

      // Check if contact number and language code are available in thread
      if (!thread?.threadSubject?.targetPhoneNumber || !thread?.threadSubject?.patientLanguage?.code) {
        throw new Error('Thread subject contact number or patient language is missing.');
      }

      // Update thread subject before starting the call. Todo: Avoid this call if there are no updates to be made.
      await saveThreadSubject(thread.threadSubject.patientLanguage, thread.threadSubject.targetPhoneNumber);

      const onCallIdentity = await getOnCallParticipantIdentity();

      // Prepare request URL and options
      const url = `${agentMessengerUri}/call/outgoing`;
      const options: RequestInit = {
        headers: {
          'Content-Type': 'application/json',
          'x-access-token': accessToken,
          'x-id-token': idToken ?? '',
          'x-organization-id': myIdentity?.portalIdentity?.organizationId || '',
          'x-hostname': window.location.hostname,
        },
        method: 'POST',
        body: JSON.stringify([
          {
            data: {
              threadId,
              targetPhoneNumber: thread.threadSubject.targetPhoneNumber,
              calleeId: fromFlatCommunicationIdentifier(onCallIdentity.userId as string),
            },
          },
        ]),
      };

      console.log('Making outgoing call request with data:', {
        url,
        threadId,
        targetPhoneNumber: thread.threadSubject.targetPhoneNumber,
      });

      const response = await fetch(url, options);
      const data = await response.json();

      if (!response.ok) {
        console.error('Error response from outgoing call request:', data.message);
        throw new Error(data.message || `HTTP error! status: ${response.status}`);
      }
      console.log('Outgoing call initiated successfully');
    } catch (error) {
      const errorMessage = `Error starting phone call: ${(error as Error).message}`;
      console.error(errorMessage);
      updateThreadError(errorMessage);
      updateThreadState(ThreadState.Idle);
    }
  };

  const startVoiceCall = async (): Promise<void> => {
    try {
      const threadId = thread?.threadId as string;
      if (queryParams?.get('auto')) {
        queryParams?.delete('auto');
        const url = new URL(window.location.href);
        url.search = queryParams.toString();
        window.history.pushState({}, '', url.toString());
      }
      updateThreadState(ThreadState.Connecting);

      // Check if language code is available in thread
      if (!thread?.threadSubject?.patientLanguage?.code) {
        throw new Error('Thread subject contact number or patient language is missing.');
      }

      // Update thread subject before starting the call. Todo: Avoid this call if there are no updates to be made.
      await saveThreadSubject(thread.threadSubject.patientLanguage);

      const threadIdParam = `threadId=${threadId}`;
      openVoiceCallWindow(threadIdParam);
      console.log('Voice call initiated successfully');
    } catch (error) {
      const errorMessage = `Error starting voice call: ${(error as Error).message}`;
      console.error(errorMessage);
      updateThreadError(errorMessage);
      updateThreadState(ThreadState.Idle);
    }
  };

  const getOnCallParticipantIdentity = useCallback(async (): Promise<CommunicationIdentity> => {
    const patientParticipant = thread?.participants?.find((p) => p.resourceType === 'Patient');
    if (patientParticipant) {
      const fetchPatientIdentities = async (patientId: string): Promise<CommunicationIdentity[]> => {
        const url = `${serviceUri}/patients/${patientId}/identities`;

        const headers = {
          'Content-type': 'application/json',
          accept: 'application/json',
          'x-access-token': accessToken,
          'x-id-token': idToken ?? '',
          'x-organization-id': myIdentity?.portalIdentity?.organizationId || '',
          'x-hostname': window.location.hostname,
        };

        const response = await fetch(url, { method: 'GET', headers });

        const data = await response.json();

        if (!response.ok) {
          console.log('Error fetching patient identities:', data);
          throw new Error(data.message || `HTTP error! status: ${response.status}`);
        }
        return data as CommunicationIdentity[];
      };

      const patientIdentities = await fetchPatientIdentities(patientParticipant.resourceId as string);
      return patientIdentities[0] as CommunicationIdentity;
    }
    return myIdentity?.communicationIdentities[0] as CommunicationIdentity;
  }, [accessToken, idToken, serviceUri, myIdentity, thread]);

  const hangUp = async (): Promise<void> => {
    //Hangup the call in the embedded call screen if needed.
    if (voiceCallWindowRef.current) {
      voiceCallWindowRef.current?.postMessage('end-call', window.origin);
    }
    //Update thread state
    updateThreadState(ThreadState.Idle);
    //Hangup the call in the agent messenger
    const threadId = chatThreadClient?.threadId as string;
    console.log(`hanging up call connection id: ${callConnectionId} in thread: ${threadId}`);
    const url = `${agentMessengerUri}/call/hang-up/${threadId}/${callConnectionId}`;
    try {
      const options: RequestInit = {
        headers: {
          'Content-Type': 'application/json',
          'x-access-token': accessToken,
          'x-id-token': idToken ?? '',
          'x-organization-id': myIdentity?.portalIdentity?.organizationId || '',
          'x-hostname': window.location.hostname,
        },
        method: 'POST',
      };

      const response = await fetch(url, options);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || `HTTP error! status: ${response.status}`);
      }
    } catch (error) {
      const errorMessage = `Error disconnecting call: ${(error as Error).message}`;
      updateThreadError(errorMessage);
    }
  };

  // Do not expose this method via context, no error handling done. Treated as a private method.
  const fetchThread = async (threadId: string): Promise<Thread> => {
    const url = `${serviceUri}/communication/chat/threads/${threadId}`;

    const headers = {
      'Content-type': 'application/json',
      accept: 'application/json',
      'x-access-token': accessToken,
      'x-id-token': idToken ?? '',
      'x-organization-id': myIdentity?.portalIdentity?.organizationId || '',
      'x-hostname': window.location.hostname,
    };

    const response = await fetch(url, { method: 'GET', headers });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || `HTTP error! status: ${response.status}`);
    }
    return data as Thread;
  };

  // Do not expose this method via context, no error handling done. Treated as a private method.
  const fetchCounterPartThread = async (counterPart: RelicChatParticipant): Promise<Thread> => {
    const patientPath = 'patients';
    const url =
      counterPart.resourceType === 'Patient'
        ? `${serviceUri}/${patientPath}/${counterPart.resourceId}/chat`
        : `${serviceUri}/practitioners/${counterPart.resourceId}/chat`;

    const headers = {
      'Content-type': 'application/json',
      accept: 'application/json',
      'x-access-token': accessToken,
      'x-id-token': idToken ?? '',
      'x-organization-id': myIdentity?.portalIdentity?.organizationId || '',
      'x-hostname': window.location.hostname,
    };

    try {
      const response = await fetch(url, { method: 'GET', headers });
      const thread = await response.json();

      if (!response.ok) {
        throw new Error(thread.message || `HTTP error! status: ${response.status}`);
      }

      return thread as Thread;
    } catch (error) {
      console.error('Failed to fetch caregiver thread:', (error as any).message);
      throw error;
    }
  };

  // Do not expose this method via context, no error handling done. Treated as a private method.
  const fetchDefaultThread = async (): Promise<Thread> => {
    const headers = {
      accept: 'application/json',
      'x-access-token': accessToken,
      'x-id-token': idToken ?? '',
      'x-organization-id': myIdentity?.portalIdentity?.organizationId || '',
      'x-hostname': window.location.hostname,
    };

    const myDefaultAgentResourceType = myDefaultAgent?.resourceType.toLowerCase();
    const url = `${serviceUri}/${myDefaultAgentResourceType}s/${myDefaultAgent?.id}/chat`;

    try {
      const response = await fetch(url, { method: 'GET', headers });
      const thread = await response.json();

      if (!response.ok) {
        throw new Error(thread.message || `HTTP error! status: ${response.status}`);
      }

      return thread as Thread;
    } catch (error) {
      console.error('Failed to fetch default thread:', (error as any).message);
      throw error;
    }
  };

  const initializeThread = async () => {
    if (!myIdentity || !myIdentity.communicationIdentities[0].secret.token) {
      updateProviderError('User identity or token is missing.');
      return;
    }
    try {
      let thread: Thread;
      if (threadIdParam) {
        thread = await fetchThread(threadIdParam as string);
      } else if (counterPart) {
        thread = await fetchCounterPartThread(counterPart);
      } else {
        thread = await fetchDefaultThread();
      }
      if (!thread) {
        updateProviderError('Thread is not available.');
        return;
      }
      if (!thread.threadSubject) {
        updateProviderError('Thread Subject is not available.');
        return;
      }
      if (!thread.threadSubject?.patientLanguage) {
        updateProviderError('Patient language is not available.');
      }
      setThread(thread);
      const displayName = extractDisplayNameFromUserIdentity(myIdentity as IUserIdentity) ?? '';


      const { client, threadClient } = await initializeChatClient(
        myIdentity,
        aiAssistant as RelicAgent,
        thread,
        displayName,
        setThreadState,
      );
      setChatClient(client);
      setChatThreadClient(threadClient);
    } catch (error) {
      const err = error as Error;
      updateProviderError(`Error while initializing chat client: ${err.message}`);
      console.log(err.stack);
    }
  };

  // Side effect to initialize thread. Called once when myIdentity is available
  useEffect(() => {
    if (myIdentity && aiAssistant) {
      updateProviderLoading(true);
      initializeThread().finally(() => updateProviderLoading(false));
    }
  }, [myIdentity, threadIdParam, aiAssistant]);

  // Function to reinitialize the thread. Useful only if there is no thread Id param
  const reinitializeThread = useCallback(() => {
    if (myIdentity && aiAssistant) {
      updateProviderLoading(true);
      initializeThread().finally(() => updateProviderLoading(false));
    }
  }, [myIdentity, aiAssistant]);

  // set topic at the beginning
  useEffect(() => {
    if (chatThreadClient) {
      const getTopic = async () => {
        const chatThreadProperties = await chatThreadClient.getProperties();
        try {
          const topicData = JSON.parse(chatThreadProperties.topic) as Topic;
          // find if there is a patient or staff agent servicing the thread.
          let agentParticipant = thread?.participants?.find(
            (participant: RelicChatParticipant) => participant.resourceType === 'Practitioner' && (participant.type === 'Patient Agent' || participant.type === 'Staff Agent'),
          );
          // if none then system agent is servicing the thread.
          if (!agentParticipant) {
            agentParticipant = thread?.participants?.find(
              (participant: RelicChatParticipant) => participant.resourceType === 'Practitioner' && participant.type === 'System Agent',
            );  
          };
          topicData.currentAgentAcsId =
            topicData.currentAgentAcsId ?? getIdentifierRawId(agentParticipant?.id as CommunicationIdentifier);
          if (threadState !== ThreadState.Connecting) {
            topicData.threadMessage = `HELLO-${Math.random().toString(36).substring(2, 8).toUpperCase()}`;
          }
          updateThreadError(null);
          setTopic(topicData);
        } catch (error) {
          const err = error as Error;
          updateThreadError(`Error while parsing topic: ${chatThreadProperties.topic}, ${err.message}`);
        }
      };
      getTopic();
      if (queryParams) {
        if (queryParams.get('auto') === 'call') {
          startPhoneCall();
        } else if (queryParams.get('auto') === 'voice') {
          startVoiceCall();
        }
      }
    }
  }, [chatThreadClient]);

  // set thread state update for call start, call end. Also, update topic on chat properties/topic update
  useEffect(() => {
    if (chatClient && chatThreadClient && threadState !== ThreadState.ReadOnly) {
      const startNotifications = async () => {
        await chatClient.startRealtimeNotifications();

        chatClient.on('chatThreadPropertiesUpdated', onChatThreadPropertiesUpdated);
        chatClient.on('chatMessageReceived', onChatMessageReceived);
        chatClient.on('typingIndicatorReceived', onTypingIndicatorReceived);
      };

      const onChatThreadPropertiesUpdated = async (e: ChatThreadPropertiesUpdatedEvent) => {
        const topicData = JSON.parse(e.properties.topic || '{}') as Topic;
        updateThreadError(null);
        setTopic(topicData);
      };

      const onChatMessageReceived = async (e: ChatMessageReceivedEvent) => {
        const messageSenderDisplayName = e.senderDisplayName;
        setTypingParticipants((prevTypingParticipants) => {
          const updatedTypingParticipants = prevTypingParticipants.filter(
            (participant) => participant.displayName !== e.senderDisplayName
          );
          return updatedTypingParticipants;
        });
        const receivedMetadata = e.metadata as ChatMetadata;
        if (receivedMetadata.type === 'widget') {
          const widgetData = JSON.parse(receivedMetadata.widgetData || '{}') as CallNotificationWidget;
          if (e.message?.toLowerCase().includes('ended') && widgetData.widget === 'CallNotificationWidget') {
            console.log('ended message received');
            console.log(`resetting call connection id: ${callConnectionId}`);
            setCallConnectionId(undefined);
            updateThreadState(ThreadState.Idle);
          }
          if (e.message?.toLowerCase().includes('started') && widgetData.widget === 'CallNotificationWidget') {
            console.log('started message received');
            console.log('setting call connection id:', widgetData.callConnectionId);
            setCallConnectionId(widgetData.callConnectionId);
            updateThreadState(ThreadState.Connected);
          }
        }
      };

      const onTypingIndicatorReceived = (e: TypingIndicatorReceivedEvent) => {
        // Ignore typing indicator from self
        if (myIdentity?.communicationIdentities[0].userId !== (e.sender as CommunicationUserIdentifier).communicationUserId) {
          if (!typingParticipants.some((participant) => participant.displayName === e.senderDisplayName)) {
            typingParticipants.push({
              userId: (e.sender as CommunicationUserIdentifier).communicationUserId,
              displayName: e.senderDisplayName,
            });
            setTypingParticipants([...typingParticipants]);
          }  
        }
      };

      startNotifications().catch((error) => updateProviderError(`Error starting notifications: ${error.message}`));

      return () => {
        chatClient.off('chatThreadPropertiesUpdated', onChatThreadPropertiesUpdated);
        chatClient.off('chatMessageReceived', onChatMessageReceived);
        chatClient.off('typingIndicatorReceived', onTypingIndicatorReceived);
      };
    }
  }, [chatClient, chatThreadClient, threadState]);

  return (
    <ThreadContext.Provider
      value={{
        chatClient,
        chatThreadClient,
        thread,
        topic,
        displayInEnglish,
        translate,
        startPhoneCall,
        startVoiceCall,
        getOnCallParticipantIdentity,
        hangUp,
        callConnectionId,
        threadState,
        typingParticipants,        
        updateThreadState,
        updateDisplayInEnglish,
        threadError,
        updateThreadError,
        updateThread,
        reinitializeThread,
      }}
    >
      {!providerError && chatClient && chatThreadClient ? (
        <ChatClientProvider chatClient={chatClient}>
          <ChatThreadClientProvider chatThreadClient={chatThreadClient}>{children}</ChatThreadClientProvider>
        </ChatClientProvider>
      ) : null}
    </ThreadContext.Provider>
  );
};

export const useThreadContext = () => useContext(ThreadContext);
