import { MuiTelInput } from 'mui-tel-input';
import { Path, Control, Controller, FieldValues } from 'react-hook-form';

interface PhoneInputProps<T extends FieldValues = FieldValues> {
  name?: Path<T>;
  label?: string;
  required?: boolean;
  control: Control<T, object>;
  disabled?: boolean;
  value?: string;
}

const PhoneInput = <T extends FieldValues = FieldValues>({
  name = 'phone' as Path<T>,
  label = 'Phone Number',
  control,
  required = false,
  disabled = false,
  value,
  ...props
}: PhoneInputProps<T>) => {
  return (
    <Controller
      name={name}
      control={control}
      defaultValue={value as any}
      render={({ field, fieldState: { error } }) => (
        <MuiTelInput
          {...field}
          defaultCountry="US"
          onlyCountries={['US']}
          forceCallingCode
          disableDropdown
          error={!!error}
          helperText={error ? error.message : ''}
          fullWidth
          InputLabelProps={{ shrink: true }}
          label={label}
          required={required}
          disabled={disabled}
          {...props}
        />
      )}
    />
  );
};

export default PhoneInput;
