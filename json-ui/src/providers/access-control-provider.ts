import { AccessControlProvider } from '@refinedev/core';
import { Alias, Resource, CanAccess, AccessPolicy } from 'relic-ui';
import { CanResponse } from '@refinedev/core/dist/contexts/accessControl/types';

import { stringCompareCaseInsensitive } from 'src/utils/string-compare';

const Unauthorized: CanResponse = {
  can: false,
  reason: 'Unauthorized',
};

const Authorized: CanResponse = {
  can: true,
  reason: 'Authorized',
};

const localhostMenus = ['live-data', 'json-ui'];

const canAccessPortalMenu = (
  policy: AccessPolicy | null,
  resource: string,
  parent?: string,
): boolean => {
  let remoteAccess = true;
  if (
    window.location.hostname !== 'localhost' &&
    localhostMenus.includes(resource)
  ) {
    // Allow access to live data and json-ui resources for local development without accessControl
    remoteAccess = false;
  }
  const resourcePolicy = policy?.resource.find(
    (r: Resource) =>
      stringCompareCaseInsensitive(r.resource, resource) ||
      stringCompareCaseInsensitive(r.resource, parent ?? ''),
  );
  if (!resourcePolicy?.canAccess) return false;

  if (typeof resourcePolicy.canAccess === 'string') {
    return resourcePolicy.canAccess === 'all';
  } else {
    return resourcePolicy.canAccess.some(
      (access: CanAccess) =>
        stringCompareCaseInsensitive(access.name, resource) &&
        access.allowed &&
        remoteAccess,
    );
  }
};

const canAccessResource = (
  policy: AccessPolicy | null,
  resource: string,
  action: string,
  params: any,
): boolean => {
  const resourceParts = resource.split('/');
  const lastResourceParam = resourceParts[resourceParts.length - 1];
  const resourcePolicy = policy?.resource.find(
    (r: Resource) =>
      stringCompareCaseInsensitive(r.resource, lastResourceParam) ||
      r.alias?.some(
        (a: Alias) =>
          stringCompareCaseInsensitive(a.resource, resource) &&
          stringCompareCaseInsensitive(a.target, 'facility-portal'),
      ),
  );
  if (!resourcePolicy) return false;
  switch (action) {
    case 'list':
      return resourcePolicy.canList ?? false;
    case 'show':
      return resourcePolicy.canRead ?? false;
    case 'create':
      return resourcePolicy.canCreate ?? false;
    case 'edit':
      return resourcePolicy.canUpdate ?? false;
    case 'delete':
      return resourcePolicy.canDelete ?? false;
    default:
      return false;
  }
};

export const accessControlProvider: AccessControlProvider = {
  can: async ({ resource, action, params }) => {
    const accessPolicy = JSON.parse(
      localStorage.getItem('accessPolicy') as string,
    ) as AccessPolicy;
    if (!accessPolicy) {
      console.error('Access Policy not found in local storage');
      return Unauthorized;
    }
    if (!resource) return Authorized; // Do not block non resource URLs. Root URL, etc.
    const accessPortalMenu = canAccessPortalMenu(
      accessPolicy,
      resource as string,
      params?.resource?.meta?.parent,
    );
    const accessResource = canAccessResource(
      accessPolicy,
      resource as string,
      action,
      params,
    );
    return accessPortalMenu || accessResource ? Authorized : Unauthorized;
  },
  options: {
    buttons: {
      enableAccessControl: true,
      hideIfUnauthorized: true,
    },
    queryOptions: {
      cacheTime: Authorized.can ? 0 : undefined,
    },
  },
};
