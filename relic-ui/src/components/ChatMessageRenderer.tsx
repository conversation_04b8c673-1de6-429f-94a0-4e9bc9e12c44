import React from 'react';
import { Components } from 'react-markdown/lib';
import { Box, Typography } from '@mui/material';
import MarkdownRenderer from './markdown/MarkdownRenderer';

interface ChatMessageRendererProps {
  content: string;
  className?: string;
  style?: React.CSSProperties;
}

/**
 * ChatMessageRenderer component for rendering chat messages with markdown support
 * This component is specifically designed to handle OpenAI response content
 * that should be rendered as markdown
 */
const ChatMessageRenderer: React.FC<ChatMessageRendererProps> = ({ content, className, style }) => {
  // Define custom components for markdown rendering in chat messages
  const components: Partial<Components> = {
    // Add styling for headings
    h1: ({ children }) => (
      <Typography variant="h4" gutterBottom>
        {children}
      </Typography>
    ),
    h2: ({ children }) => (
      <Typography variant="h5" gutterBottom>
        {children}
      </Typography>
    ),
    h3: ({ children }) => (
      <Typography variant="h6" gutterBottom>
        {children}
      </Typography>
    ),
    // Add styling for lists
    ul: ({ children }) => (
      <Box component="ul" sx={{ paddingLeft: 3 }}>
        {children}
      </Box>
    ),
    ol: ({ children }) => (
      <Box component="ol" sx={{ paddingLeft: 3 }}>
        {children}
      </Box>
    ),
    // Format list items with label and content on separate lines with indentation
    li: ({ children }) => {
      // Process children to extract content from p tags
      const processedChildren = React.Children.map(children, (child) => {
        // If this child is a p element, return only its children
        if (React.isValidElement(child) && child.type === 'p') {
          return child.props.children;
        }
        return child;
      });

      return (
        <Box
          component="li"
          sx={{
            marginBottom: '1rem',
            // Style for proper text flow
            '& > strong, & > b': {
              display: 'block',
              marginBottom: '0.25rem',
            },
            // Add indentation for content after the label
            '& > strong + *, & > b + *': {
              display: 'block',
              marginLeft: '1rem',
            },
            // Remove margin from p tags
            '& p': {
              marginBlockStart: '0px!important',
              marginBlockEnd: '0px!important',
            },
            // For regular list items without labels
            whiteSpace: 'normal',
          }}
        >
          {processedChildren}
        </Box>
      );
    },
    // Add styling for links
    a: ({ href, children }) => (
      <Box
        component="a"
        href={href}
        target="_blank"
        rel="noopener noreferrer"
        sx={{
          color: 'primary.main',
          textDecoration: 'none',
          '&:hover': {
            textDecoration: 'underline',
          },
        }}
      >
        {children}
      </Box>
    ),
    // Add styling for tables
    table: ({ children }) => (
      <Box
        component="table"
        sx={{
          borderCollapse: 'collapse',
          width: '100%',
          marginY: 2,
        }}
      >
        {children}
      </Box>
    ),
    th: ({ children }) => (
      <Box
        component="th"
        sx={{
          border: '1px solid #ddd',
          padding: 1,
          backgroundColor: '#f2f2f2',
          textAlign: 'left',
        }}
      >
        {children}
      </Box>
    ),
    td: ({ children }) => (
      <Box
        component="td"
        sx={{
          border: '1px solid #ddd',
          padding: 1,
        }}
      >
        {children}
      </Box>
    ),
  };

  return <MarkdownRenderer content={content} components={components} className={className} style={style} />;
};

export default ChatMessageRenderer;
