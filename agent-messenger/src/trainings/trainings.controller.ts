import { Controller, Post, Body, HttpException, HttpStatus, Logger, HttpCode } from '@nestjs/common';
import { CheckRelevanceRequest, CheckRelevanceResponse } from '../types/trainings.types';
import { AgentService } from 'src/agent/agent.service';

@Controller('trainings')
export class TrainingsController {
  private readonly logger = new Logger(TrainingsController.name);

  constructor(private readonly agentService: AgentService) { }

  @Post('check-relevance')
  @HttpCode(200)
  async checkRelevance(@Body() request: CheckRelevanceRequest): Promise<CheckRelevanceResponse> {
    const { subject, intent, urls } = request;

    if (!(subject && intent && urls.length > 0)) {
      throw new HttpException('Missing required fields [subject, intent, urls].', HttpStatus.BAD_REQUEST);
    }

    const result: CheckRelevanceResponse = [];

    for (const { url, summary } of urls) {
      if (!(url || summary)) {
        continue;
      }
      try {
        const agentResponse = await this.agentService.generateResponseForCrawler(subject, intent, url, summary);
        const isRelevant = agentResponse.toLowerCase().includes('yes');
        result.push({
          url,
          relevant: isRelevant
        });
        this.logger.log(`Checked relevance for URL: ${url}, Result: ${isRelevant}, Response: ${agentResponse}`);
      } catch (err) {
        this.logger.error({ err, url }, 'Error checking relevance for URL.');
        throw new HttpException('Failed to check relevance', HttpStatus.INTERNAL_SERVER_ERROR);
      }
    }

    return result;
  }
}