import { Test, TestingModule } from '@nestjs/testing';
import { EventController } from './event.controller';
import { ChatService } from '../chat/chat.service';

describe('EventController', () => {
  let controller: EventController;
  let mockChatService: jest.Mocked<ChatService>;
  let processChatMessageReceivedInThreadSpy: jest.SpyInstance;

  beforeEach(async () => {
    mockChatService = {
      processChatMessageReceivedInThread: jest.fn(),
    } as any as jest.Mocked<ChatService>;

    processChatMessageReceivedInThreadSpy = jest.spyOn(mockChatService, 'processChatMessageReceivedInThread');

    const module: TestingModule = await Test.createTestingModule({
      controllers: [EventController],
      providers: [
        {
          provide: ChatService,
          useValue: mockChatService,
        },
      ],
    }).compile();

    controller = module.get<EventController>(EventController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('handleEventGridEvent', () => {
    it("invokes ChatService's processChatMessageReceivedInThread for Microsoft.Communication.ChatMessageReceivedInThread events", async () => {
      const mockEvent = [
        {
          eventType: 'Microsoft.Communication.ChatMessageReceivedInThread',
          eventTime: new Date(),
          id: 'mock-id',
          subject: 'mock-subject',
          dataVersion: 'mock-data-version',
          data: {
            threadId: 'mock-thread-id',
            messageBody: 'mock-message-body',
          },
        },
      ];

      await controller.handleEventGridEvent(mockEvent);
      expect(processChatMessageReceivedInThreadSpy).toHaveBeenCalledWith(mockEvent[0].data);
    });

    it('should not call processChatMessageReceivedInThread for a different event type', async () => {
      const mockEvent = [
        {
          eventType: 'Different.Event.Type',
          eventTime: new Date(),
          id: 'mock-id',
          subject: 'mock-subject',
          dataVersion: 'mock-data-version',
          data: {
            threadId: 'mock-thread-id',
            messageBody: 'mock-message-body',
          },
        },
      ];

      await controller.handleEventGridEvent(mockEvent);
      expect(processChatMessageReceivedInThreadSpy).not.toHaveBeenCalled();
    });
  });
});
