import React from 'react';
import { useDataGrid } from '@refinedev/mui';

import { Box, Card } from '@mui/material';
import { GridColDef } from '@mui/x-data-grid-pro';

import ListTrainingsModule from 'src/components/_trainings-modules/list/trainings-modules-list';

export const TrainingModuleList = () => {
  const {
    dataGridProps: ListTrainingsModuleProps,
    filters,
    setFilters,
  } = useDataGrid({
    resource: 'trainings/modules',
    filters: {
      mode: 'server',
    },
  });

  const trainingsModuleColumns = React.useMemo<GridColDef[]>(() => [], []);

  return (
    <Card>
      <Box sx={{ height: 'calc(100vh - 96px)', width: '100%' }}>
        <ListTrainingsModule
          {...ListTrainingsModuleProps}
          columns={trainingsModuleColumns}
          filters={filters}
          setFilters={setFilters}
        />
      </Box>
    </Card>
  );
};
