MONGODB_URI="***********************************************************************************************************************************************************************************************************************************************************************"
DATABASE="relic-db"
ORGANIZATIONS="organizations"
AGENTS="agents"
PRACTITIONERS="practitioners"

# Import relic organization
mongoimport --uri $MONGODB_URI --collection $ORGANIZATIONS --db $DATABASE --file ./$ORGANIZATIONS/test-reliccare-inc.json --jsonArray
echo "Imported relic organization"

# Import relic agents
mongoimport --uri $MONGODB_URI --collection $AGENTS --db $DATABASE --file ./$AGENTS/relic-care-ai-assistant.json --jsonArray
mongoimport --uri $MONGODB_URI --collection $AGENTS --db $DATABASE --file ./$AGENTS/relic-care-florence.json --jsonArray
mongoimport --uri $MONGODB_URI --collection $AGENTS --db $DATABASE --file ./$AGENTS/relic-care-sydney.json --jsonArray
mongoimport --uri $MONGODB_URI --collection $AGENTS --db $DATABASE --file ./$AGENTS/relic-care-training-assistant.json --jsonArray
echo "Imported relic agents"

# Import relic practitioners
mongoimport --uri $MONGODB_URI --collection $PRACTITIONERS --db $DATABASE --file ./$PRACTITIONERS/relic-care-ai-assistant.json --jsonArray
mongoimport --uri $MONGODB_URI --collection $PRACTITIONERS --db $DATABASE --file ./$PRACTITIONERS/relic-care-florence.json --jsonArray
mongoimport --uri $MONGODB_URI --collection $PRACTITIONERS --db $DATABASE --file ./$PRACTITIONERS/relic-care-sydney.json --jsonArray
mongoimport --uri $MONGODB_URI --collection $AGENTS --db $DATABASE --file ./$AGENTS/relic-care-training-assistant.json --jsonArray
echo "Imported relic practitioners"