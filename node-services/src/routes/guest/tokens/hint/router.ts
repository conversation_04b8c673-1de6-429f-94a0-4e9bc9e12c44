import { FastifyInstance } from "fastify";
import { S } from "fluent-json-schema";
import * as jwt from 'jsonwebtoken';

export default function (instance: FastifyInstance, options: any, next: any) {
    instance.get(
      '/:type/:id',
      {
        schema: {
          tags: ['Guest Service'],
          summary: 'Unsecured - Generate a signed Id Token Hint for Azure AD B2C. ',
          description: 'Generate a signed Id Token Hint for Azure AD B2C with either email id or phone number. This route is not secured and does not require authentication token.',
          params: S.object()
            .prop('type', S.enum(['email', 'phone']))
            .prop('id', S.string().description('email id or phone')),
        },
      },
      async function (req: any, rep: any) {
        const { type, id } = req.params;
        // For valid email id/phone number we need to create an id Hint.
        // ID Hint will expire in 30 days making essentially the link expire in 30 days.
        const currentEpochTime = Math.floor(Date.now() / 1000);
        const thirtyDaysInSeconds = 30 * 24 * 60 * 60;
        const expirationEpochTime = currentEpochTime + thirtyDaysInSeconds;
        let idHint = {
          "aud": "aadB2c",
          "iss": "node-services",
          "exp": expirationEpochTime,
          "iat": currentEpochTime,
          "relicEmail": '',
          "relicPhone": '',
        };
  
        if ( type !== 'phone' && type != 'email' ) {
          //Check that type is either email or phone
          throw req.server.httpErrors.badRequest('Only email or phone allowed as hint types.');
        } else if (type === 'email') {
          // Check that id string has an email format if type is email
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          if (!emailRegex.test(id)) {
            throw req.server.httpErrors.badRequest('Invalid email format.');
          }
          idHint.relicEmail = id;
          delete idHint.relicPhone;
        } else if (type === 'phone') {
          // Check that id string has a US or India phone format
          const phoneRegex = /^\+\d{1,2}\d{10}$/;
          if (!phoneRegex.test(id)) {
            throw req.server.httpErrors.badRequest('Invalid phone number format.');
          }
          idHint.relicPhone = id;
          delete idHint.relicEmail;
        }
  
        //This secret is hardcoded since it will be same across environments. It is also configured in AD B2C. 
        const secret = "pzgJFgjP4KueO4MoOxIPzutD8mIQznnPVybv7FcIQ70=";
        const signedIdHint = jwt.sign(idHint, secret);
        return signedIdHint;
      }
    );    

  next();
}