import { useEffect } from 'react';
import { toast } from 'react-toastify';
import { useForm } from '@refinedev/react-hook-form';
import { useCreate, useTranslate, useCustomMutation } from '@refinedev/core';

import LoadingButton from '@mui/lab/LoadingButton';
import {
  Stack,
  Alert,
  Button,
  Dialog,
  Divider,
  DialogTitle,
  DialogActions,
} from '@mui/material';

import { useParams } from 'src/routes/hooks';

import { TrainingModulesDropdown } from 'src/components/dropdown/trainingModulesDropdown';

const TrainingModulesAddToTrainingAddDialogue = ({
  open,
  onClose,
}: {
  open: boolean;
  onClose: VoidFunction;
}) => {
  const { id } = useParams();

  const {
    handleSubmit,
    reset,
    control,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm({
    defaultValues: {
      selectedModules: [],
    },
  });

  const {
    refineCore: { query },
  } = useForm();

  const trainingsData = query?.data?.data;

  const trainingModules = trainingsData?.modules;

  useEffect(() => {
    const selectTrainingModulesIds = trainingModules?.map(
      (module: any) => module.id,
    );
    setValue('selectedModules', selectTrainingModulesIds);
  }, [setValue, trainingModules]);

  const { mutate: insertModules } = useCreate({
    mutationOptions: {
      onSuccess: () => {
        onClose();
        reset();
        toast.success('Training modules updated successfully.');
      },
    },
  });

  const { mutate: deleteModules } = useCustomMutation();

  const translate = useTranslate();

  const onSubmit = handleSubmit(async data => {
    try {
      // Get the IDs of the previously selected training modules
      const previousModulesIds: string[] = trainingModules?.map(
        (module: any) => module.id,
      );

      // Get the IDs of the currently selected training modules from the form data
      const selectedModulesIds: string[] = data?.selectedModules ?? [];

      // Determine which modules have been newly added
      const insertedIds = selectedModulesIds.filter(
        id => !previousModulesIds.includes(id),
      );

      // Determine which modules have been removed
      const deletedIds = previousModulesIds.filter(
        id => !selectedModulesIds.includes(id),
      );

      // Check if both insertions and deletions are requested simultaneously
      if (insertedIds.length > 0 && deletedIds.length > 0) {
        // Error: Both insert and delete are not allowed in the same request
        toast.error(
          'Error: Please perform additions and deletions as separate actions; both cannot be done simultaneously.',
        );
      } else if (insertedIds.length > 0) {
        // Handle the insertion of new modules
        const insertedModules = insertedIds.map(id => ({ id }));
        insertModules({
          resource: `trainings/${id}/modules/`,

          values: {
            modules: insertedModules,
          },
        });
      } else if (deletedIds.length > 0) {
        // Handle the deletion of existing modules
        const deletedModules = deletedIds.map(id => ({ id }));
        deleteModules(
          {
            url: `trainings/${id}/modules/`,
            method: 'delete',
            values: {
              modules: deletedModules,
            },
          },
          {
            onSuccess: () => {
              onClose();
              reset();
              toast.success('Training modules updated successfully.');
            },
          },
        );
      } else {
        // No changes detected
        toast.error('Error: No changes detected.');
      }
    } catch (error) {
      // Log any errors that occur during the process
      console.error(error);
      toast.error('Error: An error occurred while updating training modules.');
    }
  });

  return (
    <Dialog
      open={open}
      fullWidth
      maxWidth="sm"
      onClose={onClose}
      aria-labelledby="alert-dialog-title"
      aria-describedby="alert-dialog-description"
    >
      <Stack component="form" onSubmit={onSubmit} autoComplete="off">
        <DialogTitle id="alert-dialog-title">
          {translate('trainings.titles.update-modules')}
        </DialogTitle>

        <Divider />

        <Stack spacing={3} sx={{ p: 3 }}>
          <Alert severity="info">
            {translate('trainings.messages.add-modules-info')}
          </Alert>
          <TrainingModulesDropdown
            control={control}
            name="selectedModules"
            label={translate('trainings-modules.label')}
            error={!!(errors as any)?.selectedModules}
            helperText={(errors as any)?.selectedModules?.message}
            value={trainingModules ?? null}
          />
        </Stack>
        <Divider />

        <DialogActions>
          <Button
            onClick={() => {
              onClose();
              reset();
            }}
          >
            {translate('buttons.cancel')}
          </Button>

          <LoadingButton
            type="submit"
            variant="contained"
            loading={isSubmitting}
          >
            {translate('buttons.save')}
          </LoadingButton>
        </DialogActions>
      </Stack>
    </Dialog>
  );
};

export default TrainingModulesAddToTrainingAddDialogue;
