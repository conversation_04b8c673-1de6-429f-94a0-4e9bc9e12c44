import React from 'react';

import Typography from '@mui/material/Typography';
import {
  GridToolbarExport,
  GridToolbarContainer,
  GridToolbarColumnsButton,
} from '@mui/x-data-grid-pro';

import Iconify from 'src/components/iconify';

// ----------------------------------------------------------------------

export type DataGridToolbarProps = {
  title: string;
  actions?: React.ReactNode;
  setViewButtonEl?: React.Dispatch<
    React.SetStateAction<HTMLButtonElement | null>
  >;
  statusFilter?: JSX.Element;
  exportButton?: boolean;
};

export function DataGridToolbar({
  title,
  actions,
  setViewButtonEl,
  statusFilter,
  exportButton = true,
}: DataGridToolbarProps) {
  return (
    <GridToolbarContainer sx={{ alignItems: 'center' }}>
      <Typography variant="h6" sx={{ flexGrow: 1 }}>
        {title}
      </Typography>
      {statusFilter && statusFilter}
      {setViewButtonEl && <GridToolbarColumnsButton ref={setViewButtonEl} />}
      {exportButton && <GridToolbarExport />}
      {actions && actions}
    </GridToolbarContainer>
  );
}
