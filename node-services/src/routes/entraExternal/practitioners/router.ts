import { FastifyInstance, FastifyPluginOptions, FastifyReply, FastifyRequest } from 'fastify';
import { RelicPractitioner, RelicIdParam } from 'relic-ui';

import { relicRequire } from '../../../utils/common-js';
const { RelicIdParamSchema, RelicPractitionerSchema } = relicRequire('relic-ui/schema');

interface PractitionerIdParams {
  Params: RelicIdParam;
}

export default async function (fastify: FastifyInstance, opts: FastifyPluginOptions) {
  fastify.post(
    '/',
    {
      schema: {
        ...opts.schema,
        body: RelicPractitionerSchema,
        summary: `Create a new Practitioner. Works only with provider as "entra-external".`,
        tags: ['Practitioner Service'],
        description: `Create a new Practitioner. Works only with provider as "entra-external".`,
      },
    },
    async function (req: FastifyRequest, rep: FastifyReply): Promise<RelicPractitioner> {
      const input: RelicPractitioner = req.body as RelicPractitioner;
      const relicPractitioner: RelicPractitioner = await fastify.createPractitioner(input);
      if (fastify.identityService.createUser) {
        await fastify.identityService.createUser(relicPractitioner);
      }
      return relicPractitioner;
    },
  );

  fastify.get(
    '/:id',
    {
      schema: {
        ...opts.schema,
        params: RelicIdParamSchema,
        summary: `Get a specific practitioner by Id. Id can be EHR Id, Relic Care Id, email or mobile number.`,
        tags: ['Practitioner Service'],
        description: `Fetches a specific practitioner from the database using the provided ID.`,
      },
    },
    async function getPractitioner(req: FastifyRequest<PractitionerIdParams>, rep: FastifyReply) {
      const practitionerId = req.params.id;
      const practitioner = await fastify.getPractitioner(practitionerId);
      if (!practitioner) {
        throw fastify.httpErrors.notFound(`Practitioner not found, id: ${practitionerId}`);
      }
      return practitioner;
    },
  );

  fastify.patch(
    '/:id',
    {
      schema: {
        ...opts.schema,
        body: RelicPractitionerSchema,
        summary: `Update an existing Practitioner`,
        tags: ['Practitioner Service'],
        description: `Updates an existing Practitioner in Relic Care Mongo DB.`,
      },
    },
    async function updatePractitioner(req: FastifyRequest<PractitionerIdParams>, rep: FastifyReply) {
      const { id } = req.params;
      const existingPractitioner = await fastify.getPractitioner(id);
      if (!existingPractitioner) {
        throw fastify.httpErrors.notFound(`Practitioner not found, id: ${id}`);
      }
      let { name, email, mobilePhone, organizationId, enabled, provider } = req.body as RelicPractitioner;
      if (mobilePhone) {
        mobilePhone = fastify.formatPhoneNumber(mobilePhone);
      }
      const relicPractitioner: RelicPractitioner = {
        ...existingPractitioner,
        email: email || existingPractitioner.email,
        mobilePhone: mobilePhone || existingPractitioner.mobilePhone,
        enabled: enabled,
        organizationId: organizationId,
        name: name,
        provider: provider,
      };
      const practitioner = await fastify.updatePractitioner(relicPractitioner);
      if (fastify.identityService.updateUser) {
        await fastify.identityService.updateUser(practitioner);
      }
      return practitioner;
    },
  );
}
