import { Box, Typography } from '@mui/material';
import { MuiTelInput } from 'mui-tel-input';

interface PhoneInputViewProps {
  value?: string;
  label?: string;
}

/**
 * A read-only component for displaying phone numbers using MuiTelInput in disabled state
 * but with styling optimized for view-only display.
 */
const PhoneInputView = ({
  value = '',
  label = 'Phone Number',
}: PhoneInputViewProps) => {
  return (
    <Box sx={{ width: '100%', mb: 0.5 }}>
      {/* Label */}
      {label && (
        <Typography
          component="span"
          variant="subtitle2"
          sx={{
            fontSize: 12,
            color: 'text.secondary',
            display: 'block',
            mb: 0.5,
          }}
        >
          {label}
        </Typography>
      )}

      {/* Phone Number Display - show dash if no value */}
      {!value ? (
        <Typography component="span" variant="body2">
          -
        </Typography>
      ) : (
        <MuiTelInput
          value={value}
          defaultCountry="US"
          onlyCountries={['US']}
          forceCallingCode
          disableDropdown
          disabled
          fullWidth
          sx={theme => ({
            pointerEvents: 'none',
            paddingLeft: 0,
            '& .MuiInputBase-input.Mui-disabled': {
              WebkitTextFillColor: theme.palette.text.primary,
              color: theme.palette.text.primary,
              opacity: 1,
              m: 0,
              p: 0,
            },
            '& .MuiOutlinedInput-notchedOutline': {
              border: 'none',
            },
            '& .MuiTelInput-Flag': {
              ml: 0,
            },
            '&.MuiOutlinedInput-root': {
              paddingLeft: 0,
            },
          })}
        />
      )}
    </Box>
  );
};

export default PhoneInputView;
