import * as React from 'react';
import { useState, useEffect } from 'react';
import {
  Autocomplete,
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  InputAdornment,
  TextField,
  Typography,
} from '@mui/material';
import { MuiTelInput } from 'mui-tel-input';
import availableLanguages from '../../data/languages.json';
import { RelicCommunicationLanguage } from '../../types';
import PhoneIcon from '@mui/icons-material/Phone';
import MicIcon from '@mui/icons-material/Mic';
import { useThreadContext } from '../RelicThreadProvider';

interface CallDialogProps {
  isDialogOpen: boolean;
  handleDialogClose: () => void;
  doVoiceCall: () => void;
  doPhoneCall: () => void;
  phoneNumberError: string | null;
  setPhoneNumberError: React.Dispatch<React.SetStateAction<string | null>>;
  languageError: string | null;
  setLanguageError: React.Dispatch<React.SetStateAction<string | null>>;
}

const CallDialog: React.FC<CallDialogProps> = ({
  isDialogOpen,
  handleDialogClose,
  doVoiceCall,
  doPhoneCall,
  phoneNumberError,
  setPhoneNumberError,
  languageError,
  setLanguageError,
}) => {
  const { thread, updateThread } = useThreadContext(); // Use the updated updateThread function
  const [callType, setCallType] = useState<'voice' | 'phone'>('voice');
  const [selectedLanguageLabel, setSelectedLanguageLabel] = useState('Voice Language');

  useEffect(() => {
    setSelectedLanguageLabel(callType === 'voice' ? 'Voice Language' : 'Phone Language');
  }, [callType]);

  const handlePhoneNumberChange = (newValue: string) => {
    // MuiTelInput provides the number with country code, we just need to clean it up
    // and ensure it's a valid US number with +1 prefix
    const isValid = /^\+1[2-9]\d{9}$/.test(newValue.replace(/\s+/g, ''));

    if (isValid) {
      updateThread(newValue.replace(/\s+/g, ''), undefined);
      setPhoneNumberError(null);
    } else if (newValue.startsWith('+1')) {
      updateThread(newValue.replace(/\s+/g, ''), undefined);
      if (newValue.replace(/\s+/g, '').length > 12) {
        setPhoneNumberError('Invalid phone number. Please enter a 10-digit US phone number.');
      }
    } else {
      setPhoneNumberError('Please enter a valid US phone number with the country code.');
    }
  };

  const handleLanguageChange = (
    event: React.SyntheticEvent<Element, Event>,
    changedLanguage: RelicCommunicationLanguage | null,
  ) => {
    if (changedLanguage) {
      // Directly update the language using updateThread
      updateThread(undefined, changedLanguage);
      setLanguageError(null);
    } else {
      setLanguageError('Please select a language.');
    }
  };

  // No need for formatPhoneNumberForDisplay with MuiTelInput

  const validateAndStartCall = () => {
    const { threadSubject } = thread || {};
    if (callType === 'phone') {
      if (!threadSubject?.targetPhoneNumber) {
        setPhoneNumberError('Please enter a phone number.');
        return;
      }

      const phoneNumberWithoutSpaces = threadSubject.targetPhoneNumber.replace(/\s+/g, '');
      if (!/^\+1[2-9]\d{9}$/.test(phoneNumberWithoutSpaces)) {
        setPhoneNumberError('Please enter a valid 10-digit US phone number.');
        return;
      }
    }
    if (!threadSubject?.patientLanguage) {
      setLanguageError('Please select a language.');
      return;
    }
    callType === 'voice' ? doVoiceCall() : doPhoneCall();
  };

  return (
    <Dialog fullWidth maxWidth="xs" open={isDialogOpen} onClose={handleDialogClose}>
      <DialogTitle>Voice Conversation</DialogTitle>
      <DialogContent>
        <Box sx={{ display: 'flex', flexDirection: 'column', mb: 2, mt: 1, gap: 2 }}>
          <Autocomplete
            id="language-autocomplete"
            options={availableLanguages}
            getOptionLabel={(option: RelicCommunicationLanguage) => option.display}
            isOptionEqualToValue={(option, value) => option.code === value.code}
            value={thread?.threadSubject?.patientLanguage || null}
            onChange={handleLanguageChange}
            renderInput={(params) => (
              <TextField
                {...params}
                label={selectedLanguageLabel}
                variant="outlined"
                fullWidth
                error={!!languageError}
                helperText={languageError || 'Select the language for Voice Conversation'}
              />
            )}
          />

          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              gap: '20px',
              justifyContent: 'flex-start',
              padding: '16px',
              borderRadius: '8px',
              border: `2px solid ${callType === 'voice' ? '#007bff' : '#e0e0e0'}`,
              backgroundColor: callType === 'voice' ? '#f0f8ff' : '#f5f5f5',
              cursor: 'pointer',
              '&:hover': {
                borderColor: '#007bff',
              },
            }}
            onClick={() => setCallType('voice')}
          >
            <Box>
              <MicIcon fontSize="large" />
            </Box>
            <Box>
              <Typography variant="body1">Voice Call</Typography>
              <Typography variant="caption">Voice Conversation using Browser</Typography>
            </Box>
          </Box>
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              gap: '20px',
              justifyContent: 'flex-start',
              padding: '16px',
              borderRadius: '8px',
              border: `2px solid ${callType === 'phone' ? '#007bff' : '#e0e0e0'}`,
              backgroundColor: callType === 'phone' ? '#f0f8ff' : '#f5f5f5',
              cursor: 'pointer',
              '&:hover': {
                borderColor: '#007bff',
              },
            }}
            onClick={() => setCallType('phone')}
          >
            <Box>
              <PhoneIcon fontSize="large" />
            </Box>
            <Box>
              <Typography variant="body1">Phone Call</Typography>
              <Typography variant="caption">Voice Conversation using Phone</Typography>
            </Box>
          </Box>

          {callType === 'phone' && (
            <Box sx={{ mt: 2 }}>
              <MuiTelInput
                autoFocus
                id="editablePhoneNumber"
                label="Phone Number"
                fullWidth
                value={thread?.threadSubject?.targetPhoneNumber || ''}
                onChange={handlePhoneNumberChange}
                error={!!phoneNumberError}
                helperText={phoneNumberError || 'Enter the phone number you wish to call.'}
                defaultCountry="US"
                forceCallingCode
                disableFormatting={false}
                focusOnSelectCountry
              />
            </Box>
          )}
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleDialogClose}>Cancel</Button>
        <Button variant="contained" onClick={validateAndStartCall}>
          {callType === 'voice' ? 'Start Voice Call' : 'Start Phone Call'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default CallDialog;
