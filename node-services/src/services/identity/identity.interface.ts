import { IUser, RelicPractitioner } from 'relic-ui';
import { SignPayloadType } from '@fastify/jwt';
import { User } from '@microsoft/microsoft-graph-types';

export interface IdentityServiceInterface {
  whoAmI(decodedJwtToken: SignPayloadType): Promise<IUser>;

  getUser(practitioner: RelicPractitioner): Promise<User>;
  createUser?(practitioner: RelicPractitioner): Promise<User>;
  updateUser?(practitioner: RelicPractitioner): Promise<User>;
  deleteUser?(practitioner: RelicPractitioner): Promise<void>;
}
