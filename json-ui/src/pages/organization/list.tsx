import React, { useEffect } from 'react';
import { UrlField, useDataGrid } from '@refinedev/mui';
import {
  useList,
  useOnError,
  useTranslate,
  IResourceComponentsProps,
} from '@refinedev/core';

import { Box } from '@mui/material';
import Card from '@mui/material/Card';
import { GridColDef, DataGridPro, GridCellParams } from '@mui/x-data-grid-pro';

import { useRouter } from 'src/routes/hooks';

import { useSearch } from 'src/hooks/use-search';

import Label from 'src/components/label';
import { RowActionsPopover } from 'src/components/list';
import { NewButton } from 'src/components/refine-customs/new-btn';
import { DataGridToolbar } from 'src/components/list/data-grid-toolbar';

import facilityType from '../../data/facilityType.json';

export const OrganizationList: React.FC<IResourceComponentsProps> = () => {
  const translate = useTranslate();
  const router = useRouter();

  const { dataGridProps, setFilters } = useDataGrid({
    sorters: {
      initial: [
        {
          field: 'name',
          order: 'asc',
        },
      ],
    },
    queryOptions: {
      cacheTime: 0,
      staleTime: 0,
    },
  });

  const [viewButtonEl, setViewButtonEl] =
    React.useState<HTMLButtonElement | null>(null);
  const { mutate: onError } = useOnError();

  const { globalSearch } = useSearch();
  const handleOnCellClick = React.useCallback(
    (params: GridCellParams) => {
      if (params.field !== 'actions') {
        router.push(
          `/organizations/edit/${params.id}?name=${encodeURIComponent(params.row.name)}`,
        );
      }
    },
    [router],
  );

  const columns = React.useMemo<GridColDef[]>(
    () => [
      {
        field: 'name',
        flex: 1,
        headerName: translate('organizations.fields.name'),
        minWidth: 200,
      },
      {
        field: 'website',
        flex: 1,
        headerName: translate('organizations.fields.website'),
        minWidth: 150,
        renderCell: function render({ value }) {
          return <UrlField value={value} />;
        },
      },
      {
        field: 'type',
        flex: 1,
        headerName: translate('organizations.fields.type'),
        minWidth: 300,
        renderCell: function render({ value }) {
          return (
            <label>
              {facilityType.find(item => item.code === value)?.display}
            </label>
          );
        },
      },
      {
        field: 'active',
        headerName: translate('organizations.fields.active'),
        minWidth: 100,
        renderCell: function render({ value }) {
          return (
            <Label variant="soft" color={value ? 'success' : 'error'}>
              {translate(value ? 'status.active' : 'status.inactive')}
            </Label>
          );
        },
      },
      {
        field: 'actions',
        headerName: translate('table.actions'),
        sortable: false,
        renderCell: function render({ row }) {
          return (
            <>
              <RowActionsPopover
                rowId={row.id}
                showButtonProps={{
                  onClick: () =>
                    router.push(
                      `/organizations/edit/${row.id}?name=${row.name}`,
                    ),
                }}
              />
            </>
          );
        },
        align: 'center',
        headerAlign: 'center',
        minWidth: 80,
      },
    ],
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [translate, handleOnCellClick],
  );

  const Toolbar = React.useCallback(
    () => (
      <DataGridToolbar
        setViewButtonEl={setViewButtonEl}
        title={translate('organizations.organizations')}
        actions={
          <NewButton
            href={'/organizations/create'}
            label={translate('buttons.create')}
          />
        }
      />
    ),
    [translate],
  );

  useEffect(() => {
    try {
      if (globalSearch.length > 0) {
        setFilters([
          {
            field: 'search',
            value: globalSearch.length > 0 ? globalSearch : '',
            operator: 'contains',
          },
        ]);
      } else {
        setFilters([]);
      }
    } catch (error) {
      onError(error);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [globalSearch]);

  return (
    <Card>
      <Box sx={{ height: 'calc(100vh - 96px)', width: '100%' }}>
        <DataGridPro
          {...dataGridProps}
          pagination
          sx={{
            '& .MuiDataGrid-cell': {
              display: 'flex',
              alignSelf: 'center', // Center aligns the items vertically
              height: '100%', // Ensure the cell takes full height for vertical centering,
              border: 'none',
            },
          }}
          disableColumnMenu
          columns={columns}
          localeText={{ toolbarColumns: 'View' }}
          onCellClick={handleOnCellClick}
          slots={{
            toolbar: Toolbar,
          }}
          slotProps={{
            panel: {
              anchorEl: viewButtonEl,
            },
            toolbar: {
              onClick: (e: React.MouseEvent) => {
                e.stopPropagation();
                setViewButtonEl(null);
              },
            },
          }}
        />
      </Box>
    </Card>
  );
};
