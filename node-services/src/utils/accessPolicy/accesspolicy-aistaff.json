{"resourceType": "AccessPolicy", "name": "AIStaff", "resource": [{"resource": "patient", "alias": [{"target": "facility-portal", "resource": "patients"}], "canRead": true, "canUpdate": true, "canDelete": true, "canCreate": true, "canList": true}, {"resource": "documents", "canRead": true, "canUpdate": true, "canDelete": true, "canCreate": true, "canList": true}, {"resource": "agents", "canRead": true, "canUpdate": true, "canDelete": true, "canCreate": true, "canList": true}, {"resource": "staff", "canRead": true, "canUpdate": true, "canDelete": true, "canCreate": true, "canList": true}, {"resource": "companies", "canRead": true, "canUpdate": true, "canDelete": true, "canCreate": true, "canList": true}, {"resource": "facilities", "canRead": true, "canUpdate": true, "canDelete": true, "canCreate": true, "canList": true}, {"resource": "organizations", "canRead": true, "canUpdate": true, "canDelete": true, "canCreate": true, "canList": true}, {"resource": "conditions", "canRead": true, "canUpdate": true, "canDelete": true, "canCreate": true, "canList": true}, {"resource": "allergyintolerances", "canRead": true, "canUpdate": true, "canDelete": true, "canCreate": true, "canList": true}, {"resource": "locations", "canRead": true, "canUpdate": true, "canDelete": true, "canCreate": true, "canList": true}, {"resource": "my-facility", "canAccess": [{"name": "my-facility", "allowed": true}, {"name": "patients", "allowed": true}, {"name": "documents", "allowed": true}]}, {"resource": "live-data", "canAccess": [{"name": "live-data", "allowed": true}, {"name": "sandbox", "allowed": true}, {"name": "companies", "allowed": true}, {"name": "staff", "allowed": true}, {"name": "facilities", "allowed": true}]}, {"resource": "json-ui", "canAccess": [{"name": "json-ui", "allowed": true}, {"name": "conditions", "allowed": true}, {"name": "allergyintolerances", "allowed": true}, {"name": "locations", "allowed": true}]}]}