import * as React from 'react';
import { RelicAgent } from 'relic-ui';
import { FieldValues } from 'react-hook-form';
import { useForm } from '@refinedev/react-hook-form';
import { EmailField, useDataGrid } from '@refinedev/mui';
import { useParams, useLocation, useSearchParams } from 'react-router-dom';
import {
  IUser,
  IUserIdentity,
  RelicOrganization,
  RelicCommunicationLanguage,
} from 'relic-ui';
import {
  HttpError,
  useTranslate,
  useGetIdentity,
  IResourceComponentsProps,
} from '@refinedev/core';

import Box from '@mui/material/Box';
import Stack from '@mui/material/Stack';
import Drawer from '@mui/material/Drawer';
import Button from '@mui/material/Button';
import { TextField } from '@mui/material';
import IconButton from '@mui/material/IconButton';
import { GridColDef } from '@mui/x-data-grid-pro';
import CircularProgress from '@mui/material/CircularProgress';

import { useRouter } from 'src/routes/hooks';

import { usePopover } from 'src/hooks/use-popover';
import { useResponsive } from 'src/hooks/use-responsive';

import Label from 'src/components/label';
import Iconify from 'src/components/iconify';
import { ListAgents } from 'src/components/_agent';
import PhoneInput from 'src/components/phone-input';
import { RowActionsPopover } from 'src/components/list';
import { ListLocations } from 'src/components/_location';
import { Edit } from 'src/components/refine-customs/edit';
import { ListFacilities } from 'src/components/_facility';
import ListThreads from 'src/components/_threads/list/threads-list';
import { OrgTypeDropdown } from 'src/components/dropdown/orgTypeDropdown';
import { LanguageDropdown } from 'src/components/dropdown/languageDropdown';

import facilityType from '../../data/facilityType.json';
import {
  TabList,
  OrganizationTabs,
  OrganizationToolbar,
  OrganizationToolbarPopover,
  OrganizationDetailsPopover,
  OrganizationDetailsViewOnly,
} from '../../components/_organization/edit';

const BLANKTABS: { value: string; label: string }[] = [];

const DRAWER_WIDTH = {
  lg: 380,
  md: 320,
  sm: 280,
  xs: '100%',
};

export const OrganizationEdit: React.FC<IResourceComponentsProps> = () => {
  const translate = useTranslate();

  const router = useRouter();

  const popoverDetails = usePopover();

  const popoverAdd = usePopover();

  const upMd = useResponsive('up', 'md');

  const { id: organizationId } = useParams<{ id?: string }>();

  const [orgType, setOrgType] = React.useState('medplum');

  const [tabs, setTabs] = React.useState(BLANKTABS);

  const [searchParams, setSearchParams] = useSearchParams();

  const location = useLocation();

  const queryParams = new URLSearchParams(location.search);

  const initialTab = queryParams.get('tab') || 'agent';

  const [currentTab, setCurrentTab] = React.useState(initialTab);

  const [openLocation, setOpenLocation] = React.useState(false);

  const [openAgent, setOpenAgent] = React.useState(false);

  const [openThread, setOpenThread] = React.useState(false);

  const [showEdit, setShowEdit] = React.useState(false);

  const [showOrganization, setShowOrganization] = React.useState(false);

  const { data: currentUser } = useGetIdentity<IUser>();

  const currentIdentity: IUserIdentity =
    currentUser?.userIdentity as IUserIdentity;

  const currentRole: string = currentIdentity?.role?.name
    ? currentIdentity?.role?.name
    : 'member';

  const {
    watch,
    handleSubmit,
    saveButtonProps,
    refineCore: { queryResult, onFinish, formLoading },
    register,
    control,
    formState: { errors },
  } = useForm<FieldValues, HttpError>({
    refineCoreProps: {
      redirect: 'edit',
    },
  });

  let thisOrganization = null;
  let isOrgLoaded = false;

  if (queryResult && 'data' in queryResult && 'isSuccess' in queryResult) {
    ({ data: thisOrganization, isSuccess: isOrgLoaded } = queryResult);
  }
  const currentOrganization = thisOrganization?.data as RelicOrganization;

  const values = watch();

  const handleBack = () => {
    router.push('/organizations');
  };

  // Type for the new parameters to update
  const updateQueryParams = React.useCallback(
    (newParams: Record<string, string>): void => {
      const updatedParams = new URLSearchParams(searchParams);

      // Merge existing and new parameters
      Object.entries(newParams).forEach(([key, value]) =>
        updatedParams.set(key, encodeURIComponent(value)),
      );
      setSearchParams(updatedParams);
    },
    [searchParams, setSearchParams],
  );

  const handleChangeTab = React.useCallback(
    (event: React.SyntheticEvent, newValue: string) => {
      updateQueryParams({ tab: newValue });
      setCurrentTab(newValue);
    },
    [updateQueryParams],
  );

  React.useEffect(() => {
    if (upMd) {
      setShowOrganization(true);
    } else {
      setShowOrganization(false);
    }
  }, [upMd]);

  React.useEffect(() => {
    if (
      currentOrganization &&
      currentOrganization.pointClickCare &&
      currentOrganization.pointClickCare?.id
    ) {
      setOrgType('pcc');
      setTabs([
        { value: 'agent', label: translate('agents.agents') },
        { value: 'threads', label: translate('threads.threads') },
        { value: 'facilities', label: translate('facilities.facilities') },
      ]);
    } else {
      setOrgType('medplum');
      setTabs([
        { value: 'agent', label: translate('agents.agents') },
        { value: 'threads', label: translate('threads.threads') },
        { value: 'locations', label: translate('locations.locations') },
      ]);
    }
    setCurrentTab(initialTab);
  }, [currentOrganization, initialTab, translate]);

  const { dataGridProps: agentGridProps, setFilters: setAgentGridFilters } =
    useDataGrid<RelicAgent>({
      resource: 'agents',
      filters: {
        mode: 'server', // Assuming server-side filtering
        permanent: organizationId
          ? [{ field: 'organizationId', operator: 'eq', value: organizationId }]
          : [],
      },
      queryOptions: {
        enabled: isOrgLoaded && currentTab === 'agent',
        cacheTime: 0,
        staleTime: 0,
      },
      syncWithLocation: false,
    });

  const agentColumns = React.useMemo<GridColDef[]>(() => [], []);

  const {
    dataGridProps: threadGridProps,
    filters,
    setFilters: setThreadGridFilters,
  } = useDataGrid({
    resource: 'communication/chat/threads',
    sorters: {
      initial: [
        {
          field: 'updateDate',
          order: 'desc',
        },
      ],
    },
    filters: {
      mode: 'server', // Assuming server-side filtering
      permanent: organizationId
        ? [{ field: 'organizationId', operator: 'eq', value: organizationId }]
        : [],
    },
    queryOptions: {
      enabled: isOrgLoaded && currentTab === 'threads',
    },
    syncWithLocation: false,
  });

  const threadColumns = React.useMemo<GridColDef[]>(() => [], []);

  const { dataGridProps: staffGridProps } = useDataGrid<HttpError>({
    resource: 'staff',
    permanentFilter: [
      {
        field: 'organizationId',
        operator: 'eq',
        value: currentOrganization?.id,
      },
    ],
    initialPageSize: 25,
    queryOptions: {
      enabled: isOrgLoaded && currentOrganization !== undefined && currentTab === 'staff',
    },
  });

  const staffColumns = React.useMemo<GridColDef[]>(
    () => [
      {
        field: 'givenName',
        flex: 1,
        headerName: translate('staff.fields.givenName'),
        minWidth: 100,
      },
      {
        field: 'familyName',
        flex: 1,
        headerName: translate('staff.fields.familyName'),
        minWidth: 100,
      },
      {
        field: 'email',
        flex: 1,
        headerName: translate('staff.fields.email'),
        minWidth: 200,
        renderCell: function render({ value }) {
          return <EmailField value={value} />;
        },
      },
      {
        field: 'name',
        flex: 1,
        headerName: translate('Facility'),
        minWidth: 200,
        renderCell: function render({ value }) {
          console.log('values2', value);
          return currentOrganization ? currentOrganization.name : 'Loading...';
        },
      },

      {
        field: 'status',
        flex: 1,
        headerName: translate('staff.fields.status'),
        minWidth: 100,
        renderCell: ({ row }) => (
          <Label
            variant="soft"
            color={
              (row.status === 'notInvited' && 'error') ||
              (row.status === 'invited' && 'warning') ||
              'success'
            }
          >
            {row.status === 'active' && translate('status.active')}
            {row.status === 'invited' && translate('status.invited')}
            {row.status === 'notInvited' && translate('status.notInvited')}
          </Label>
        ),
      },
      {
        field: 'actions',
        hideable: false,
        headerName: translate('table.actions'),
        sortable: false,
        renderCell: function render({ row }) {
          return (
            <>
              <RowActionsPopover
                rowId={row.id}
                deleteButtonProps={{
                  confirmTitle: translate('staff.titles.delete'),
                  confirmContent: `Are you sure you want to delete ${row.givenName} ${row.familyName}?`,
                  resource: 'staff',
                  recordItemId: row.id,
                }}
              />
            </>
          );
        },
        align: 'center',
        headerAlign: 'center',
        minWidth: 10,
      },
    ],
    [currentOrganization, translate],
  );

  const {
    dataGridProps: facilityGridProps,
    setFilters: setListFacilityFilters,
  } = useDataGrid<HttpError>({
    resource: 'facilities',
    meta: {
      headers: {
        'x-provider': 'pcc',
      }
    },
    sorters: {
      initial: [
        {
          field: 'facilityName',
          order: 'asc',
        },
      ],
    },
    permanentFilter: [
      {
        field: 'relicOrganizationId',
        operator: 'eq',
        value: currentOrganization?.id,
      },
    ],
    queryOptions: {
      enabled: isOrgLoaded && orgType === 'pcc' && currentTab === 'facilities',
    },
  });

  const facilityColumns = React.useMemo<GridColDef[]>(() => [], []);

  // Location
  const {
    dataGridProps: locationGridProps,
    setFilters: setListLocationFilters,
  } = useDataGrid({
    resource: 'locations',
    sorters: {
      initial: [
        {
          field: 'name',
          order: 'asc',
        },
      ],
    },
    permanentFilter: [
      {
        field: 'organizationId',
        operator: 'eq',
        value: currentOrganization?.id,
      },
    ],
    initialPageSize: 25,
    queryOptions: {
      enabled: isOrgLoaded && orgType === 'medplum' && currentTab === 'locations',
    },
  });

  const locationColumns = React.useMemo<GridColDef[]>(() => [], []);

  const renderEditForm = (
    <Stack
      spacing={3}
      component="form"
      autoComplete="off"
      sx={{
        flexDirection: 'column',
        display: showEdit ? 'flex' : 'none',
      }}
    >
      {/* Name Field */}
      <TextField
        {...register('name', {
          required: 'This field is required',
        })}
        error={!!(errors as any)?.name}
        helperText={(errors as any)?.name?.message}
        fullWidth
        InputLabelProps={{ shrink: true }}
        type="text"
        label={translate('organizations.fields.name')}
        name="name"
        required
        disabled
      />
      {/* Type field */}
      <OrgTypeDropdown
        control={control}
        name="type"
        label={translate('organizations.fields.type')}
        value={currentOrganization?.type || null}
        error={!!(errors as any)?.type}
        helperText={(errors as any)?.type?.message}
      />

      {/* Website Field */}
      <TextField
        {...register('website', {
          required: 'This field is required',
        })}
        error={!!(errors as any)?.website}
        helperText={(errors as any)?.website?.message}
        fullWidth
        InputLabelProps={{ shrink: true }}
        type="text"
        label={translate('organizations.fields.website')}
        name="website"
        required
      />

      {/* Default language */}
      <LanguageDropdown
        control={control}
        name="fhirStore.defaultLanguage"
        label={translate('organizations.fields.language')}
        value={
          (values?.fhirStore?.defaultLanguage as RelicCommunicationLanguage) ??
          (currentOrganization?.fhirStore
            ?.defaultLanguage as RelicCommunicationLanguage)
        }
        error={!!(errors as any)?.fhirStore?.defaultLanguage}
        helperText={(errors as any)?.fhirStore?.defaultLanguage?.message}
      />

      {/* Phone field */}
      <PhoneInput
        control={control}
        label={translate('organizations.fields.phone')}
        name="phone"
        value={currentOrganization?.phone}
      />

      {/* Fax field */}
      <PhoneInput
        control={control}
        label={translate('organizations.fields.fax')}
        name="fax"
        value={currentOrganization?.fax}
      />

      {/* PCC Facility field */}
      <TextField
        {...register('pointClickCare.id')}
        error={!!(errors as any)?.pointClickCare?.id}
        helperText={(errors as any)?.pointClickCare?.id.message}
        InputLabelProps={{ shrink: true }}
        type="text"
        label={translate('organizations.fields.pointClickCareId')}
        placeholder={translate('organizations.fields.pointClickCareId')}
        name="pointClickCare.id"
        disabled={currentRole === 'admin' ? false : true}
        fullWidth
      />

      {/* Template patient summary field */}
      {/* <TextField
        {...register('template.patientSummary', {
          required: 'This field is required',
        })}
        error={!!(errors as any)?.template?.patientSummary}
        helperText={(errors as any)?.template?.patientSummary?.message}
        multiline
        minRows={4}
        maxRows={8}
        fullWidth
        InputLabelProps={{ shrink: true }}
        type="text"
        label={translate('organizations.fields.patientSummary')}
        name="template.patientSummary"
        required
      /> */}

      {/* Template welcome sms field */}
      {/* <TextField
        {...register('template.welcomeSms', {
          required: 'This field is required',
        })}
        error={!!(errors as any)?.template?.welcomeSms}
        helperText={(errors as any)?.template?.welcomeSms?.message}
        multiline
        minRows={4}
        maxRows={8}
        fullWidth
        InputLabelProps={{ shrink: true }}
        type="text"
        label={translate('organizations.fields.welcomeSms')}
        name="template.welcomeSms"
        required
      /> */}

      {/* Template welcome sms field */}
      {/* <TextField
        {...register('template.welcomeEmail', {
          required: 'This field is required',
        })}
        error={!!(errors as any)?.template?.welcomeEmail}
        helperText={(errors as any)?.template?.welcomeEmail?.message}
        multiline
        minRows={4}
        maxRows={8}
        fullWidth
        InputLabelProps={{ shrink: true }}
        type="text"
        label={translate('organizations.fields.welcomeEmail')}
        name="template.welcomeEmail"
        required
      /> */}
    </Stack>
  );

  const updateCurrentTab = (newState: string) => {
    setCurrentTab(newState);
  };

  /**
   * Pre submission handler for create form
   * @param {FieldValues} values
   * @returns void
   */
  const onEditOrgSubmit = async (values: FieldValues) => {
    const submittedValues = {
      ...currentOrganization,
      ...values,
    }
    // submittedValues.type = values.type.code;
    await onFinish({ ...submittedValues });
    setShowEdit(false);
  };

  return (
    <>
      <OrganizationToolbarPopover
        openLocation={openLocation}
        openAgent={openAgent}
        openThread={openThread}
        popover={popoverAdd}
        onOpenLocation={() => {
          popoverAdd.onClose();
          setOpenLocation(true);
        }}
        onOpenAgent={() => {
          popoverAdd.onClose();
          setOpenAgent(true);
        }}
        onOpenThread={() => {
          popoverAdd.onClose();
          setOpenThread(true);
        }}
        onCloseAgent={() => setOpenAgent(false)}
        onCloseLocation={() => setOpenLocation(false)}
        onCloseThread={() => setOpenThread(false)}
        updateCurrentTab={updateCurrentTab}
        agentList={agentGridProps.rows as RelicAgent[]}
        currentOrganization={currentOrganization}
      />

      <OrganizationDetailsPopover
        popover={popoverDetails}
        onOpenEdit={() => {
          setShowEdit(true);
          popoverDetails.onClose();
        }}
      />

      <OrganizationToolbar
        onBack={handleBack}
        showOrganization={showOrganization}
        onOpenAdd={popoverAdd.onOpen}
        organizationName={currentOrganization?.name}
        onToggleOrganization={() => setShowOrganization(!showOrganization)}
      />

      <Stack
        sx={{
          height: 'calc(100vh - 96px)',
          ...(showOrganization && {
            width: {
              sm: `calc(100% - ${DRAWER_WIDTH.sm}px)`,
              md: `calc(100% - ${DRAWER_WIDTH.md}px)`,
              lg: `calc(100% - ${DRAWER_WIDTH.lg}px)`,
            },
          }),
        }}
      >
        {queryResult?.isLoading && (
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              minHeight: '50vh',
            }}
          >
            <CircularProgress color="inherit" />
          </Box>
        )}
        {!queryResult?.isLoading && (
          <OrganizationTabs
            tabOptions={tabs}
            currentTab={currentTab}
            onChangeTab={handleChangeTab}
          />
        )}
        {!queryResult?.isLoading &&
          orgType === 'pcc' &&
          currentTab === 'facilities' && (
            <ListFacilities
              {...facilityGridProps}
              currentOrganizationsData={currentOrganization}
              columns={facilityColumns}
              setFilters={setListFacilityFilters}
            />
          )}
        {!queryResult?.isLoading &&
          orgType === 'medplum' &&
          currentTab === 'locations' && (
            <ListLocations
              {...locationGridProps}
              currentOrganizationsData={currentOrganization}
              columns={locationColumns}
              setFilters={setListLocationFilters}
            />
          )}
        {!queryResult?.isLoading && currentTab === 'agent' && (
          <ListAgents
            {...agentGridProps}
            setFilters={setAgentGridFilters}
            currentOrganization={currentOrganization}
            columns={agentColumns}
          />
        )}

        {!queryResult?.isLoading && currentTab === 'threads' && (
          <ListThreads
            {...threadGridProps}
            filters={filters}
            setFilters={setThreadGridFilters}
            currentOrganization={currentOrganization}
            columns={threadColumns}
          />
        )}

        {!queryResult?.isLoading && currentTab === 'staff' && (
          <TabList {...staffGridProps} columns={staffColumns} />
        )}
      </Stack>

      <Drawer
        anchor="right"
        variant="persistent"
        open={showOrganization}
        PaperProps={{
          sx: {
            zIndex: 1,
            top: 8 * 18.5,
            boxSizing: 'border-box',
            width: {
              xs: DRAWER_WIDTH.xs,
              sm: DRAWER_WIDTH.sm,
              md: DRAWER_WIDTH.md,
              lg: DRAWER_WIDTH.lg,
            },
          },
        }}
        sx={{
          flexShrink: 0,
          width: {
            xs: DRAWER_WIDTH.xs,
            sm: DRAWER_WIDTH.sm,
            md: DRAWER_WIDTH.md,
            lg: DRAWER_WIDTH.lg,
          },
        }}
      >
        <Edit
          saveButtonProps={{
            ...saveButtonProps,
            onClick: handleSubmit(onEditOrgSubmit),
          }}
          //breadcrumb={null}
          // goBack={null}
          wrapperProps={{
            sx: {
              flexGrow: 1,
              borderRadius: 0,
              boxShadow: 'none',
              overflowY: 'auto',
              mb: `${8 * 18.5}px`,
            },
          }}
          headerButtonProps={{
            sx: { display: 'none' },
          }}
          headerProps={{
            title: 'Org Details',
            action: (
              <>
                {showEdit ? (
                  <Button
                    variant="outlined"
                    startIcon={<Iconify icon={'eva:close-fill'} />}
                    onClick={() => setShowEdit(false)}
                  >
                    Cancel
                  </Button>
                ) : (
                  <IconButton onClick={popoverDetails.onOpen}>
                    <Iconify icon="eva:more-vertical-fill" />
                  </IconButton>
                )}
              </>
            ),
          }}
          footerButtonProps={{
            ...(!showEdit && {
              sx: { display: 'none' },
            }),
          }}
          deleteButtonProps={{
            confirmTitle: translate('patients.titles.delete'),
            confirmContent: translate('content.confirm.delete'),
          }}
        >
          <OrganizationDetailsViewOnly
            currentOrganization={currentOrganization}
            showEdit={showEdit}
          />
          {isOrgLoaded && renderEditForm}
        </Edit>
      </Drawer>
    </>
  );
};
