import { Button, CircularProgress } from '@mui/material';

const AuthButton = ({
  id,
  href,
  onClick,
  src,
  alt,
  isLoading,
  style,
}: {
  id: string;
  href: string;
  onClick?: () => void;
  src: string;
  alt: string;
  isLoading?: boolean;
  style?: React.CSSProperties;
}) => (
  <Button
    id={id}
    href={href} // Provide a default value for href
    onClick={onClick}
    target="_self"
    rel="noopener noreferrer"
    disabled={isLoading}
    style={{
      display: 'flex',
      justifyContent: 'center',
      padding: '0px',
      ...style,
    }}
  >
    <img
      src={src}
      alt={alt}
      style={{
        width: '95%',
        opacity: isLoading ? 0.6 : 1,
      }}
    />
    {isLoading && (
      <div
        style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          color: 'white', // Ensure spinner is visible
        }}
      >
        <CircularProgress size={24} color="secondary" />
      </div>
    )}
  </Button>
);

export default AuthButton;
