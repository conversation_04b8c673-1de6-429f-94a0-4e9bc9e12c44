// Generated by https://quicktype.io
//
// To change quicktype's target language, run command:
//
//   "Set quicktype target language"

export interface CopilotClient {
  companyId: string;
  createdAt: string;
  customFields: CustomFields;
  email: string;
  familyName: string;
  firstLoginDate: null;
  givenName: string;
  id: string;
  inviteUrl: string;
  lastActiveDate: null;
  lastLoginDate: null;
  object: string;
  status: string;
}

export interface CustomFields {
  guid?: string;
  phoneNumber?: string;
  title?: string;
  medplumPassword?: string;
}
