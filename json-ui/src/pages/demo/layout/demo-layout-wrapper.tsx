import React from 'react';

import { Box } from '@mui/material';

import WebsiteHeader from 'src/components/header/websiteHeader';

const DemoLayoutWrapper = ({ children }: { children: React.ReactNode }) => {
  return (
    <>
      <WebsiteHeader />
      <Box
        component="div"
        display="flex"
        justifyContent="center"
        alignItems="center"
        sx={{
          minHeight: 'calc(100vh - 70px)', // Ensures minimum height is full viewport minus header height
          position: 'relative',
          overflow: 'hidden',
          '&::before': {
            width: 1,
            height: 1,
            zIndex: -1,
            content: "''",
            opacity: 0.24,
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundSize: 'cover',
            backgroundRepeat: 'no-repeat',
            backgroundPosition: 'center center',
            backgroundImage: 'url(/images/overlay_4.jpg)',
          },
        }}
      >
        {children}
      </Box>
    </>
  );
};

export default DemoLayoutWrapper;
