import React from 'react';
import { Controller } from 'react-hook-form';

import { TextField, Autocomplete } from '@mui/material';

interface InvitationDropdownProps {
  control: any;
  name: string;
  label: string;
  value: string;
  error: boolean;
  helperText: string;
  onChange?: (value: string) => void;
  disabled?: boolean;
}

type InvitationType = {
  label: string;
  value: string;
};

const invitationType: InvitationType[] = [
  { label: 'Cellphone', value: 'cellphone' },
  { label: 'Email', value: 'email' },
  { label: 'Both', value: 'both' },
  { label: 'None', value: 'none' },
];

const InvitationDropdown: React.FC<InvitationDropdownProps> = ({
  control,
  name,
  label,
  value,
  error,
  helperText,
  onChange,
  disabled,
}) => {
  return (
    <Controller
      control={control}
      name={name}
      defaultValue={value}
      rules={{ required: 'This field is required' }}
      render={({ field }) => {
        const { onChange: fieldOnChange, value: fieldValue } = field;

        const selectedOption =
          invitationType.find(option => option.value === fieldValue) || null;

        return (
          <Autocomplete
            options={invitationType}
            disabled={disabled}
            value={selectedOption}
            getOptionLabel={(option: InvitationType) => option.label || ''}
            isOptionEqualToValue={(option, value) =>
              option.value === value?.value
            }
            onChange={(_, newValue) => {
              const newValueString = newValue ? newValue.value : '';
              fieldOnChange(newValueString);
              if (onChange) {
                onChange(newValueString);
              }
            }}
            renderInput={params => (
              <TextField
                {...params}
                disabled={disabled}
                label={label}
                variant="outlined"
                error={error}
                helperText={helperText}
                required
              />
            )}
          />
        );
      }}
    />
  );
};

export default InvitationDropdown;
