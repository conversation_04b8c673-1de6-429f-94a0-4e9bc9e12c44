import { MedplumClient } from '@medplum/core'
import { requestContext } from '@fastify/request-context'
import { RelicOrganization, RelicPatient } from 'relic-ui'
import { FastifyInstance, FastifyPluginAsync, FastifyPluginOptions } from 'fastify'
import { Collection } from 'mongodb'

declare module 'fastify' {
    interface FastifyInstance {
        fhirClient: () => MedplumClient | undefined;
        relicPatients: () => Collection<RelicPatient>; // Replace 'any' with the actual type if known
    }
}

const fhirPlugin: FastifyPluginAsync = async (fastify: FastifyInstance, options: FastifyPluginOptions) => {

    fastify.decorate('fhirClient', function (): MedplumClient | undefined {
        const decodedJwtToken: any = requestContext.get('decodedJwtToken' as never)
        const fhirClient = new MedplumClient()
        if (!decodedJwtToken) {
            fastify.log.warn('No decoded JWT token found in request context. FHIR client cannot be created.')
            return undefined
        }
        if (decodedJwtToken.payload?.iss?.includes('api.medplum.com')) {
            // If the issuer is Medplum, we can create a MedplumClient using user's access token
            const accessToken = requestContext.get('accessToken' as never) as string
            //Practitioner Based Access - We have an access token
            if (accessToken) {
                fhirClient.setAccessToken(accessToken)
                return fhirClient
            }
        } else {
            //Organization based access - No access token.
            const organization = requestContext.get('organization')
            const clientId = organization.fhirStore?.clientApplication.id
            const clientSecret = fastify.getCredentials(clientId)
            if (clientId && clientSecret) {
                fhirClient.setBasicAuth((organization as RelicOrganization).fhirStore.clientApplication.id, clientSecret.secret)
                return fhirClient
            }
        }
        return undefined
    });

    // Make relicPatients available at the app/global level
    fastify.decorate('relicPatients', function(): Collection<RelicPatient> {
        return fastify.mongo.reliccare.db.collection<RelicPatient>('patients');
    });
}

export default fhirPlugin
