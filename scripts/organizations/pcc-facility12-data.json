{"id": "7760ffcd-64d2-43df-9548-055b257dfaed", "resourceType": "Organization", "type": "prov", "active": true, "name": "FACILITY_12", "website": "localhost:3000", "location": [{"id": "112f4240-3f61-42e1-996e-0a550cdb94dd", "resourceType": "Location", "status": "active", "name": "FACILITY_12", "phone": "(*************", "fax": "(*************"}], "copilotCompany": {"id": "50114cf4-f3c9-45a7-b9a4-57fdb9e0a9e4", "name": "FACILITY_12", "iconImageUrl": "https://sunriseseniorliving.com/assets/logo.png", "fallbackColor": "blue", "leadInternalUserId": "ffcd8392-64d2-43df-9548-055b257dfaee"}, "fhirStore": {"clientApplication": {"id": "1940c3ef-8b6f-4ac0-a66c-d6aca3bff786", "resourceType": "ClientApplication", "compartment": "Project/ae9023d0-0710-4398-bec4-74cba82d7718"}, "defaultLanguage": {"system": "http://hl7.org/fhir/ValueSet/languages", "code": "en-US", "display": "English - US"}, "provider": "medplum"}, "defaultChatEndpoint": {"url": "https://yusuke-test-3.unitedstates.communication.azure.com/", "provider": "Azure Communication Service"}, "template": {"patientSummary": "My name is {{name}}. I am a {{age}} years old {{sex}} suffering from {{conditions}} admitted at {{facilityName}}. {{goal}}. I expect {{expectation}}.", "welcomeSms": "Hello {{name}}!\nWelcome to RelicCare!\nTo access your account, please visit {{verifyUrl}}.\nThank you for choosing RelicCare.", "welcomeEmail": "<!DOCTYPE html>\n<html>\n    <head>\n        <meta charset=\"utf-8\">\n        <meta http-equiv=\"X-UA-Compatible\" content=\"IE-edge\">\n        <title>Welcome to RelicCare</title>\n    </head>\n\n    <body>\n        <p>Dear {{name}},</p>\n        <p>Welcome to RelicCare! We are thrilled to have you join our community.</p>\n        <p>To access your account, please visit: <a href=\"{{verifyUrl}}\">Verify my Account</a>.</p>\n        <p>Thank you for choosing RelicCare. We look forward to providing you with a seamless and enjoyable experience.</p>\n        <p>Best regards,<br/>\n            RelicCare\n        </p>\n    </body>\n</html>", "agentSummary": "Your name is {{name}}."}, "endpoints": [{"service": ["chat", "voip"], "endpoint": "https://yusuke-test-3.unitedstates.communication.azure.com/", "provider": "Azure Communication Service", "defaultAgents": [{"id": "8:acs:8ca4fbc2-e63c-4283-a5b7-feb83074370e_0000001f-6604-a98e-bc66-563a0d00aa4c", "type": "Patient Agent", "default": true}, {"id": "8:acs:8ca4fbc2-e63c-4283-a5b7-feb83074370e_0000001b-57e2-4794-4ff7-343a0d000d7a", "type": "Staff Agent", "default": true}]}, {"service": ["patient-portal"], "endpoint": "https://contoso.myrelic.care", "provider": "Relic Care"}]}