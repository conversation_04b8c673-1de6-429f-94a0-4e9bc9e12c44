import { RelicSearchQuery } from "@/types/request";
import { Training } from "relic-ui";
import { FastifyReply, FastifyRequest } from "fastify";

async function createTraining(req: FastifyRequest, rep: FastifyReply) {
    const trainingRequest: Training = req.body as Training;
    return await (req.server as any).createTraining(trainingRequest);
};

async function searchTrainings(req: FastifyRequest, rep: FastifyReply) {
    const { total, data } = await (req.server as any).searchTrainings(req.query);
    rep.header('x-total-count', total);
    return data;
};

async function getTrainingDetails(req: FastifyRequest, rep: FastifyReply) {
    (req.query as RelicSearchQuery).id = req.params['id'];
    return await (req.server as any).getTrainingDetails(req.query);
};

async function updateTrainingDetails(req: FastifyRequest, rep: FastifyReply) {
    throw new Error('not supported');
};

async function deleteTraining(req: FastifyRequest, rep: FastifyReply) {
    (req.query as RelicSearchQuery).id = req.params['id'];
    return await (req.server as any).deleteTraining(req.query);
};

async function addModulesToTraining(req: FastifyRequest, rep: FastifyReply) {
    const id: string = req.params['id'];
    const { modules } = req.body as any;
    return await (req.server as any).addModulesToTraining(id, modules);
};

async function getModulesAssignedToTraining(req: FastifyRequest, rep: FastifyReply) {
    const training: Training = await (req.server as any).getTrainingDetails(req.params['id']);
    return training.modules;
};

async function removeModulesFromTraining(req: FastifyRequest, rep: FastifyReply) {
    const id: string = req.params['id'];
    const { modules } = req.body as any;
    return await (req.server as any).removeModulesFromTraining(id, modules);
};

const trainingsHandler = {
    createTraining,
    searchTrainings,
    getTrainingDetails,
    updateTrainingDetails,
    deleteTraining,
    addModulesToTraining,
    getModulesAssignedToTraining,
    removeModulesFromTraining
}
export default trainingsHandler;