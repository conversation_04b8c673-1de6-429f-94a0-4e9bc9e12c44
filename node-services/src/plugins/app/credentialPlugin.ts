import { FastifyInstance, FastifyPluginAsync, FastifyPluginOptions } from 'fastify';
import * as crypto from 'crypto';
import config from 'config';
import { RelicClientSecret } from 'relic-ui';

declare module 'fastify' {
  export interface FastifyInstance {
    getCredentials(key: string): RelicClientSecret | null;
  }
}

/**
 * credentialPlugin Plugin for Fastify.
 * This plugin provides a decorator:
 * 
 * @param {FastifyInstance} encapsulatedInstance - The Fastify instance.
 * @param {Object} options - Plugin options.
 */
const credentialPlugin: FastifyPluginAsync = async (encapsulatedInstance: FastifyInstance, options: FastifyPluginOptions) => {


    encapsulatedInstance.decorate("getCredentials", function (key: string): RelicClientSecret | null {
        const trimmedKey = key.trim();
        return config.get(trimmedKey) ?? config.get(hashKey(trimmedKey)) ?? null
    })
}

/**
 * Generates a SHA-256 hash of the given key.
 * 
 * This function takes a string as input and returns its SHA-256 hash value in hexadecimal format.
 * 
 * @param {string} key - The input string to be hashed.
 * @returns {string} The hexadecimal representation of the SHA-256 hash of the input key.
 */
function hashKey(key: string): string {
    const hash = crypto.createHash('sha256').update(key);
    return hash.digest('hex');
}


export default credentialPlugin;
