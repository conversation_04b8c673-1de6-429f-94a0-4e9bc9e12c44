import { useMemo } from 'react';
import { TrainingModule } from 'relic-ui';
import { DateField } from '@refinedev/mui';
import { useTranslate } from '@refinedev/core';
import { RelicPatient, RelicOrganization, RelicPractitioner } from 'relic-ui';

import { Box, Stack, Typography } from '@mui/material';

const TrainingModulesSidebar = ({
  trainingModulesDetails,
  showEdit,
  organizationAutocompleteProps,
  practitionerAutocompleteProps,
}: {
  trainingModulesDetails: TrainingModule;
  showEdit: boolean;
  organizationAutocompleteProps: any;
  practitionerAutocompleteProps: any;
}) => {
  const translate = useTranslate();

  const currentOrganization: RelicOrganization = useMemo(() => {
    return organizationAutocompleteProps?.options?.find(
      (org: RelicOrganization) =>
        org.id === trainingModulesDetails?.organizationId,
    );
  }, [organizationAutocompleteProps, trainingModulesDetails]);

  const currentCreatedBy: RelicPractitioner = useMemo(() => {
    return practitionerAutocompleteProps?.options?.find(
      (practitioner: RelicPractitioner) =>
        practitioner.id === trainingModulesDetails?.createdBy?.id,
    );
  }, [practitionerAutocompleteProps, trainingModulesDetails]);

  return (
    <>
      <Stack
        spacing={2}
        component={'form'}
        sx={{
          flexDirection: 'column',
          display: showEdit ? 'none' : 'flex',
        }}
      >
        <Box>
          <Typography
            component={'span'}
            variant="subtitle2"
            sx={{
              fontSize: 12,
              color: 'text.secondary',
              display: 'block',
              mb: 0.5,
            }}
          >
            {translate('trainings-modules.fields.organizationId')}
          </Typography>

          <Typography component={'span'} variant="body2">
            {currentOrganization?.name || '-'}
          </Typography>
        </Box>
        <Box>
          <Typography
            component={'span'}
            variant="subtitle2"
            sx={{
              fontSize: 12,
              color: 'text.secondary',
              display: 'block',
              mb: 0.5,
            }}
          >
            {translate('trainings-modules.fields.name')}
          </Typography>

          <Typography component={'span'} variant="body2">
            {trainingModulesDetails?.name || '-'}
          </Typography>
        </Box>
        <Box>
          <Typography
            component={'span'}
            variant="subtitle2"
            sx={{
              fontSize: 12,
              color: 'text.secondary',
              display: 'block',
              mb: 0.5,
            }}
          >
            {translate('trainings-modules.fields.description')}
          </Typography>

          <Typography component={'span'} variant="body2">
            {trainingModulesDetails?.description || '-'}
          </Typography>
        </Box>
        <Box>
          <Typography
            component={'span'}
            variant="subtitle2"
            sx={{
              fontSize: 12,
              color: 'text.secondary',
              display: 'block',
              mb: 0.5,
            }}
          >
            {translate('trainings-modules.fields.role')}
          </Typography>

          <Typography component={'span'} variant="body2">
            {trainingModulesDetails?.role?.['display-role'] || '-'}
          </Typography>
        </Box>
        <Box>
          <Typography
            component={'span'}
            variant="subtitle2"
            sx={{
              fontSize: 12,
              color: 'text.secondary',
              display: 'block',
              mb: 0.5,
            }}
          >
            {translate('trainings-modules.fields.storage')}
          </Typography>

          <Typography component={'span'} variant="body2">
            {trainingModulesDetails?.storage?.containerName || '-'}
          </Typography>
        </Box>
        <Box>
          <Typography
            component={'span'}
            variant="subtitle2"
            sx={{
              fontSize: 12,
              color: 'text.secondary',
              display: 'block',
              mb: 0.5,
            }}
          >
            {translate('trainings-modules.fields.created_by')}
          </Typography>

          <Typography component={'span'} variant="body2">
            {currentCreatedBy?.name || '-'}
          </Typography>
        </Box>
        <Box>
          <Typography
            component={'span'}
            variant="subtitle2"
            sx={{
              fontSize: 12,
              color: 'text.secondary',
              display: 'block',
              mb: 0.5,
            }}
          >
            {translate('trainings-modules.fields.created_on')}
          </Typography>

          <Typography component={'span'} variant="body2">
            {trainingModulesDetails?.createDate ? (
              <DateField value={trainingModulesDetails?.createDate} />
            ) : (
              '-'
            )}
          </Typography>
        </Box>
      </Stack>
    </>
  );
};

export default TrainingModulesSidebar;
