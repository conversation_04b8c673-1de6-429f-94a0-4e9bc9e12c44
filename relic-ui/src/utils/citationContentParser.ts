import { Citation } from '../types';

export function wrapCitationsInSentences(content: string, citations: Citation[]) {
  // Replace newlines with <br/> for line breaks in the content
  content = content.replace(/\n/g, '<br/>');

  // Create a map of citation IDs to their content for easy lookup
  const citationMap: { [key: string]: string } = citations.reduce((map: { [key: string]: string }, citation) => {
    map[citation.id] = citation.title;
    return map;
  }, {});

  // Split content by <br/> tags to manage each segment separately
  const segments = content.split(/(<br\s*\/?>)/g);

  // Set to track which citations have already been processed in each sentence
  const sentenceContains = new Set();

  const updatedSegments = segments.map((segment) => {
    // If segment is a <br/> tag, leave it as-is
    if (segment.trim() === '<br/>') {
      return segment;
    }

    // Enhanced regex to capture sentences with more punctuation
    const sentenceRegex = /[^.!?;:]*[.!?;:]+|[^.!?;:]+$/g;
    const sentences = segment.match(sentenceRegex) || [];

    const updatedSentences = sentences.map((sentence) => {
      // Match all [doc*] patterns in the sentence
      const matches = sentence.match(/\[doc\d+\]/g);
      if (matches) {
        // Track processed citations within the current sentence to prevent duplicates
        const processedCitations = new Set();

        matches.forEach((match) => {
          const citationId = match.slice(1, -1); // Remove the brackets around 'docX'
          if (!processedCitations.has(citationId) && citationMap[citationId]) {
            // Regex to handle citation removal with or without space before the citation tag
            const citationRegex = new RegExp(`(\\s?\\[${citationId}\\])`, 'g');
            sentence = sentence.replace(citationRegex, (match) => (match.startsWith(' ') ? '' : ' '));

            // Track the citation to prevent redundant processing
            if (!sentenceContains.has(citationId)) {
              processedCitations.add(citationId);
              sentenceContains.add(citationId);
            }
          }
        });

        // Wrap sentences containing citations in a tooltip span
        if (processedCitations.size > 0) {
          return `<span data-ids="${Array.from(processedCitations).join(',')}" data-type="tooltip">${sentence.trim()}</span>`;
        }
        return sentence.trim();
      }
      return sentence.trim();
    });

    // Join sentences back into a single segment
    return updatedSentences.join(' ');
  });

  // Join segments with <br/> tags correctly positioned
  let updatedContent = updatedSegments.join('');

  // Append a reference span at the end of the content for any global references
  updatedContent = `${updatedContent} <span data-type='reference'></span>`;
  return updatedContent;
}

export function truncateText(text: string, maxLength = 200) {
  if (text.length > maxLength) {
    const truncated = text.slice(0, maxLength + 1);
    const lastSpaceIndex = truncated.lastIndexOf(' ');
    return truncated.slice(0, lastSpaceIndex) + '…';
  }
  return text;
}

