import { useTranslate } from '@refinedev/core';
import { RelicPatient, RelicOrganization } from 'relic-ui';

import Popover from '@mui/material/Popover';
import MenuItem from '@mui/material/MenuItem';

import { ReturnType as PopoverProps } from 'src/hooks/use-popover';

import Iconify from 'src/components/iconify';
import CreateThread from 'src/components/_threads/create/threads-create';
import { CreateDocument as NewDocumentDialog } from 'src/components/_document';
import NewAllergieDialog from 'src/components/_patient/edit/patient-new-allergie-dialog';
import NewConditionDialog, {
  AddNewConditionProps,
} from 'src/components/_patient/edit/patient-new-condition-dialog';
// ----------------------------------------------------------------------

type AddPopoverProps = {
  popover: PopoverProps;
  openDocument: boolean;
  onOpenDocument: VoidFunction;
  onCloseDocument: VoidFunction;
  openCondition: boolean;
  onOpenCondition: VoidFunction;
  onCloseCondition: VoidFunction;
  addConditionProps?: AddNewConditionProps;
  openAllergie: boolean;
  onOpenAllergie: VoidFunction;
  onCloseAllergie: VoidFunction;
  openThread: boolean;
  onOpenThread: VoidFunction;
  onCloseThread: VoidFunction;
  currentPatient?: RelicPatient;
  currentOrganization?: RelicOrganization;
};

export default function PatientAddPopover({
  popover,
  openDocument,
  onOpenDocument,
  onCloseDocument,
  openCondition,
  onOpenCondition,
  onCloseCondition,
  addConditionProps,
  openAllergie,
  onOpenAllergie,
  onCloseAllergie,
  openThread,
  onOpenThread,
  onCloseThread,
  currentPatient,
  currentOrganization,
}: AddPopoverProps) {
  const translate = useTranslate();

  return (
    <>
      <Popover
        open={!!popover.open}
        anchorEl={popover.open}
        onClose={popover.onClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        slotProps={{
          paper: {
            sx: {
              mt: 0.5,
              minWidth: 160,
            },
          },
        }}
      >
        <MenuItem onClick={onOpenThread}>
          <Iconify width={18} icon={'eva:plus-fill'} sx={{ mr: 1 }} />
          {translate('threads.titles.create')}
        </MenuItem>

        <MenuItem onClick={onOpenDocument}>
          <Iconify width={18} icon={'eva:plus-fill'} sx={{ mr: 1 }} />
          {translate('documents.titles.create')}
        </MenuItem>

        {/* <MenuItem onClick={onOpenCondition}>
          <Iconify width={18} icon={'eva:plus-fill'} sx={{ mr: 1 }} />
          {translate('conditions.titles.create')}
        </MenuItem>

        <MenuItem onClick={onOpenAllergie}>
          <Iconify width={18} icon={'eva:plus-fill'} sx={{ mr: 1 }} />
          {translate('allergyintolerances.titles.create')}
        </MenuItem> */}
      </Popover>

      {openCondition && (
        <NewConditionDialog
          open={openCondition}
          onClose={onCloseCondition}
          {...addConditionProps}
        />
      )}

      {openAllergie && (
        <NewAllergieDialog open={openAllergie} onClose={onCloseAllergie} />
      )}

      {openDocument && (
        <NewDocumentDialog open={openDocument} onClose={onCloseDocument} />
      )}

      {openThread && (
        <CreateThread
          open={openThread}
          onClose={onCloseThread}
          currentPatient={currentPatient}
          currentOrganization={currentOrganization}
        />
      )}
    </>
  );
}
