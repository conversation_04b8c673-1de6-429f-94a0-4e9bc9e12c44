export function formatToYYYYMMDD(d: Date) {
  return `${d.getFullYear()}-${(d.getMonth() + 1).toString().padStart(2, '0')}-${d
    .getDate()
    .toString()
    .padStart(2, '0')}`;
}

/**
 * Calculate the age based on the birth date.
 * @param {string} birthDateString - The birth date in string format (e.g., 'YYYY-MM-DD').
 * @param {Date} [currentDate=new Date()] - The current date to calculate age from. Defaults to today's date.
 * @returns {string} - The age in years, formatted as a string (e.g., '25 years old').
 */
export function calculateAge(birthDateString?: string, currentDate: Date = new Date()): string {
    if (!birthDateString) {
        return '';
    }

    const birthDate = new Date(birthDateString);
    let age = currentDate.getFullYear() - birthDate.getFullYear();
    const monthDifference = currentDate.getMonth() - birthDate.getMonth();
    if (monthDifference < 0 || (monthDifference === 0 && currentDate.getDate() < birthDate.getDate())) {
        age--;
    }
    return `${age} years old`;
}

//simpler function to format date 
export const formatDate = (dateString: string | undefined): string => {
    return dateString ? new Date(dateString).toLocaleDateString() : '';
};