import * as React from 'react';
import { useTranslate } from '@refinedev/core';
import { RelicPatient, RelicCommunicationLanguage } from 'relic-ui';
import {
  Control,
  FieldErrors,
  FieldValues,
  UseFormRegister,
  UseFormSetValue,
} from 'react-hook-form';

import Stack from '@mui/material/Stack';
import Input from '@mui/material/Input';
import MenuItem from '@mui/material/MenuItem';
import TextField from '@mui/material/TextField';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';

import PhoneInput from 'src/components/phone-input';
import { LanguageDropdown } from 'src/components/dropdown/languageDropdown';

interface PatientEditFormProps {
  showEdit: boolean;
  register: UseFormRegister<FieldValues>;
  errors: FieldErrors<FieldValues>;
  setValue: UseFormSetValue<FieldValues>;
  values: FieldValues;
  control: Control<FieldValues, object>;
  formLoading: boolean;
  currentPatient: RelicPatient;
  currentIdentityProvider: string;
}

const PatientEditForm: React.FC<PatientEditFormProps> = ({
  showEdit,
  register,
  errors,
  setValue,
  values,
  control,
  formLoading,
  currentPatient,
  currentIdentityProvider,
}) => {
  const translate = useTranslate();
  return (
    <Stack
      spacing={3}
      component="form"
      autoComplete="off"
      sx={{
        flexDirection: 'column',
        display: showEdit ? 'flex' : 'none',
      }}
    >
      <TextField
        {...register('name', {
          required: 'This field is required',
        })}
        fullWidth
        type="text"
        name="name"
        InputLabelProps={{ shrink: true }}
        label={translate('patients.fields.name')}
        error={!!(errors as any)?.name}
        helperText={(errors as any)?.name?.message}
        disabled
      />

      <DatePicker
        {...register('birthDate', {
          //required: 'This field is required',
        })}
        format="dd/MM/yyyy"
        label={translate('patients.fields.birthDate')}
        onChange={newValue => setValue('birthDate', newValue)}
        value={values.birthDate ? new Date(values.birthDate) : null}
        disabled
        slotProps={{
          textField: {
            fullWidth: true,
            error: !!errors?.birthDate,
            helperText: (errors as any)?.birthDate?.message,
            InputLabelProps: { shrink: true },
          },
        }}
      />

      <TextField
        {...register('gender', {
          //required: 'This field is required',
        })}
        select
        fullWidth
        name="gender"
        value={values.gender || ''}
        InputLabelProps={{ shrink: true }}
        label={translate('patients.fields.gender')}
        error={!!errors?.gender}
        helperText={(errors as any)?.gender?.message}
        disabled
        SelectProps={{
          displayEmpty: true,
          sx: {
            ...(!values.gender && {
              color: 'text.disabled',
            }),
          },
        }}
      >
        <MenuItem value="" sx={{ color: 'text.disabled', display: 'none' }}>
          {translate('selects.empty')}
        </MenuItem>

        {[
          { value: 'Male', label: 'selects.gender.male' },
          { value: 'Female', label: 'selects.gender.female' },
          { value: 'Other', label: 'selects.gender.other' },
          { value: 'Unknown', label: 'selects.gender.incognito' },
        ].map(option => (
          <MenuItem key={option.value} value={option.value}>
            {translate(option.label)}
          </MenuItem>
        ))}
      </TextField>

      <LanguageDropdown
        control={control}
        name="primaryLanguage"
        label={translate('patients.fields.primaryLanguage')}
        value={
          formLoading
            ? (undefined as unknown as RelicCommunicationLanguage)
            : (currentPatient?.primaryLanguage as RelicCommunicationLanguage)
        }
        error={!!(errors as any)?.primaryLanguage}
        helperText={(errors as any)?.primaryLanguage?.message}
      />

      <Input
        sx={{ display: 'none' }}
        type="hidden"
        {...register('maritalStatus')}
        value={currentPatient?.maritalStatus}
      />

      <TextField
        {...register('email', {
          ...(currentIdentityProvider !== 'pcc' && {
            required: 'This field is required',
          }),
          pattern: {
            value: /\S+@\S+\.\S+/,
            message: 'Entered value does not match email format',
          },
        })}
        fullWidth
        type="email"
        name="email"
        InputLabelProps={{ shrink: true }}
        label={translate('patients.fields.email')}
        error={!!(errors as any)?.email}
        helperText={(errors as any)?.email?.message}
      />

      <PhoneInput
        control={control}
        name="mobilePhone"
        label={translate('patients.fields.mobilePhone')}
        value={currentPatient?.mobilePhone}
      />

      <PhoneInput
        control={control}
        name="homePhone"
        label={translate('patients.fields.homePhone')}
        value={currentPatient?.homePhone}
      />

      <Input
        sx={{ display: 'none' }}
        type="hidden"
        {...register('link')}
        value={currentPatient?.link}
      />

      <Input
        sx={{ display: 'none' }}
        type="hidden"
        {...register('organizationId')}
        value={currentPatient?.organizationId}
      />

      <Input
        sx={{ display: 'none' }}
        type="hidden"
        {...register('active')}
        value={currentPatient?.active}
      />
    </Stack>
  );
};

export default PatientEditForm;
