<?xml version="1.0" encoding="utf-8" ?>
<TrustFrameworkPolicy 
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
  xmlns:xsd="http://www.w3.org/2001/XMLSchema" 
  xmlns="http://schemas.microsoft.com/online/cpim/schemas/2013/06" 
  PolicySchemaVersion="0.3.0.0" 
  TenantId="patientauth.onmicrosoft.com" 
  PolicyId="B2C_1A_TrustFrameworkExtensionsRelic" 
  PublicPolicyUri="http://patientauth.onmicrosoft.com/B2C_1A_TrustFrameworkExtensionsRelic">
  
  <BasePolicy>
    <TenantId>patientauth.onmicrosoft.com</TenantId>
    <PolicyId>B2C_1A_TrustFrameworkLocalizationRelic</PolicyId>
  </BasePolicy>
  
</TrustFrameworkPolicy>