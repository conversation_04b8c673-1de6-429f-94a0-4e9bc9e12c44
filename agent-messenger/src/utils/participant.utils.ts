import type { RelicChatParticipant } from 'relic-ui';
import { getIdentifierRawId } from '@azure/communication-common';
import { isEqual } from 'lodash';
import type { AcsChatMessageReceivedInThreadEventData } from '@azure/eventgrid';

export function getParticipant(participants: RelicChatParticipant[], acsId: string): RelicChatParticipant | undefined {
  return participants.find((participant) => isEqual(getIdentifierRawId(participant.id), acsId));
}

export function getHumanPatient(participants: RelicChatParticipant[]): RelicChatParticipant | undefined {
  return participants.find((participant) => participant.resourceType === 'Patient');
}

export function getHumanPractitioner(participants: RelicChatParticipant[]): RelicChatParticipant | undefined {
  return participants.find((participant) => participant.resourceType === 'Practitioner' && !participant.type);
}

export function getThreadAssistant(participants: RelicChatParticipant[]): RelicChatParticipant | undefined {
  return participants.find(
    (participant) =>
      participant.resourceType === 'Practitioner' &&
      (participant.type === 'Patient Agent' || participant.type === 'Staff Agent'),
  );
}

export function getAiAssistant(participants: RelicChatParticipant[]): RelicChatParticipant | undefined {
  return participants.find(
    (participant) => participant.resourceType === 'Practitioner' && participant.type === 'System Agent',
  );
}

export function getMessageSenderParticipant(
  participants: RelicChatParticipant[],
  message: AcsChatMessageReceivedInThreadEventData,
): RelicChatParticipant | undefined {
  const senderId = message.senderCommunicationIdentifier.communicationUser.id;
  return participants.find((participant) => {
    const participantId = getIdentifierRawId(participant.id);
    return isEqual(participantId, senderId);
  });
}
