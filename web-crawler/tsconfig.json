{"compilerOptions": {"module": "ESNext", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "sourceMap": true, "baseUrl": "./", "incremental": false, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "strict": true, "rootDir": "./src", "moduleResolution": "node", "esModuleInterop": true, "target": "ES2022", "outDir": "./dist", "noUnusedLocals": false, "skipLibCheck": true, "lib": ["DOM"]}, "include": ["src"], "exclude": ["node_modules", "dist"]}