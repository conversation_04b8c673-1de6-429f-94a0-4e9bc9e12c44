/* eslint-disable react-hooks/exhaustive-deps */
import { toast } from 'react-toastify';
import React, { useMemo, useEffect } from 'react';
import { IUser, RelicAgent, RelicOrganization } from 'relic-ui';
import { List, ExportButton } from '@refinedev/mui';
import {
  useMany,
  useDelete,
  useExport,
  CrudFilter,
  useOnError,
  useTranslate,
  useInvalidate,
  useGetIdentity,
} from '@refinedev/core';

import {
  GridColDef,
  DataGridPro,
  gridClasses,
  GridCellParams,
  DataGridProProps,
} from '@mui/x-data-grid-pro';
import {
  Box,
  Modal,
  Button,
  Dialog,
  DialogTitle,
  DialogActions,
  DialogContent,
  useMediaQuery,
  DialogContentText,
  Theme,
} from '@mui/material';
import { Lock } from '@mui/icons-material';

import { useRouter } from 'src/routes/hooks';

import { useSearch } from 'src/hooks/use-search';

import Label from 'src/components/label';
import { NewButton } from 'src/components/refine-customs/new-btn';

import { RowActionsPopover } from '../../list';
import { DataGridToolbar } from '../../list/data-grid-toolbar';
type SetFilterBehavior = 'merge' | 'replace';

interface AgentListProps extends DataGridProProps {
  currentOrganization?: RelicOrganization;
  setFilters: ((filters: CrudFilter[], behavior?: SetFilterBehavior) => void) &
    ((setter: (prevFilters: CrudFilter[]) => CrudFilter[]) => void);
}

export default function ListAgents({
  currentOrganization,
  setFilters,
  ...dataGridProps
}: AgentListProps) {
  const translate = useTranslate();

  const { globalSearch } = useSearch();

  const { mutate: onError } = useOnError();

  const { data: currentUser } = useGetIdentity<IUser>();

  const isSmallScreen = useMediaQuery('(max-width:600px)');

  const router = useRouter();

  const { triggerExport, isLoading: exportLoading } = useExport();

  const [showEditAlert, setShowEditAlert] = React.useState(false);

  const currentUserRole: string = React.useMemo(() => {
    const userRole: string = currentUser?.userIdentity?.role
      ? (currentUser?.userIdentity?.role.name as string)
      : '';
    return userRole;
  }, [currentUser]);

  const currentUserOrgId: string = React.useMemo(() => {
    const organizationId = currentUser?.userIdentity?.portalIdentity?.organizationId as string;
    return organizationId;
  }, [currentUser]);

  const enabledOrgId: string = React.useMemo(() => {
    let enabledOrgId = currentUserOrgId;
    if (currentOrganization && currentOrganization.id) {
        enabledOrgId = currentOrganization.id;
    }
    return enabledOrgId;
  }, [currentUserOrgId, currentOrganization]);

  useEffect(() => {
    try {
      if (globalSearch.length > 0) {
        setFilters([
          {
            field: 'search',
            value: globalSearch.length > 0 ? globalSearch : '',
            operator: 'contains',
          },
        ]);
      } else {
        setFilters([]);
      }
    } catch (error) {
      onError(error);
    }
  }, [globalSearch]);

  const [viewButtonEl, setViewButtonEl] =
    React.useState<HTMLButtonElement | null>(null);

  const Toolbar = React.useCallback(
    () => (
      <DataGridToolbar
        title={translate('agents.agents')}
        exportButton={false}
        setViewButtonEl={setViewButtonEl}
        actions={
          <>
            <ExportButton onClick={triggerExport} loading={exportLoading} />
            <NewButton
              href={'/agents/create'}
              label={translate('buttons.create')}
            />
          </>
        }
      />
    ),
    [translate],
  );

  const { data: relatedOrgs, isLoading: organizationIsLoading } = useMany({
    resource: 'organizations',
    ids: dataGridProps?.rows?.map((item: any) => item?.organizationId) ?? [],
    queryOptions: {
      enabled: !!dataGridProps?.rows,
    },
  });

  const handleOnCellClick = React.useCallback((params: any) => {
    if (params.field !== 'actions') {
      if (params.row.env === 'default' && currentUserRole !== 'admin') {
        setShowEditAlert(true);
        return;
      } else {
        router.push(`/agents/edit/${params.id}?name=${params?.row?.name}`);
      }
    }
  }, []);

  const { mutate: onDelete } = useDelete();

  const handleMarkInactiveClick = React.useCallback((relicAgent: RelicAgent) => {
      const organizationName = relatedOrgs?.data?.find(
        (item: any) => item?.id === relicAgent?.organizationId,
      )?.name;
      if (relicAgent.type === 'System Agent') {
        toast.warn('Cannot deactivate - selected agent is a System Agent.');
        return;
      }
      if (relicAgent.organizationId !== currentUserOrgId && currentUserRole !== 'admin') {
        setShowEditAlert(true);
        return;
      }
      onDelete({
        resource: 'agents',
        id: relicAgent.id,
        successNotification: (data, values, resource) => ({
          message: translate('notifications.markInactiveSuccess', {
            resource,
          }),
          description: 'Successful',
          type: 'success',
        }),
        errorNotification: error => ({
          message: translate('notifications.markInactiveError', {
            resource: 'condition',
            statusCode: error?.statusCode,
          }),
          description: error?.message,
          type: 'error',
        }),
      });
    },
    [onDelete, translate],
  );

  const handleEditCLick = React.useCallback((relicAgent: RelicAgent) => {
    if (currentUserOrgId !== relicAgent.organizationId && currentUserRole !== 'admin') {
      setShowEditAlert(true);
      return;
    } else {
      router.push(`/agents/edit/${relicAgent.id}?name=${relicAgent?.name}`);
    }
  }, []);

  // Helper function to determine the row color based on organization
  const getAgentRowColor = React.useCallback(
    (row: RelicAgent, theme: Theme) => row.organizationId === enabledOrgId ? 'inherit' : theme.palette.grey[400],
    [enabledOrgId]
  );

  // Helper function to render actions cell
  const renderActionsCell = React.useCallback(
    ({ row }: { row: RelicAgent }) => {
      if (row.organizationId !== enabledOrgId) {
        return (
          <Box
            display="flex"
            justifyContent="center"
            alignItems="center"
            width="100%"
          >
            <Lock
              fontSize="small"
              sx={{ color: theme => theme.palette.grey[400] }}
            />
          </Box>
        );
      }
      return (
        <RowActionsPopover
          rowId={row.id}
          editButtonProps={{
            onClick: () => handleEditCLick(row),
          }}
          markInactiveButtonProps={{
            onConfirm: () => handleMarkInactiveClick(row),
          }}
        />
      );
    },
    [enabledOrgId, handleEditCLick, handleMarkInactiveClick]
  );

  const columns = React.useMemo<GridColDef<RelicAgent>[]>(() => {
    return [
      {
        field: 'name',
        flex: 1,
        headerName: translate('agents.fields.name'),
        minWidth: 200,
        renderCell: function render({ value, row }) {
          return (
            <Box
              component={'span'}
              sx={{
                color: theme => getAgentRowColor(row, theme),
              }}
            >
              {value}
            </Box>
          );
        },
      },
      {
        field: 'role',
        flex: 1,
        headerName: translate('agents.fields.role'),
        minWidth: 200,
        renderCell: function render({ value, row }) {
          return (
            <Box
              component={'span'}
              sx={{
                color: theme => getAgentRowColor(row, theme),
              }}
            >
              {value?.['display-role'] || value?.['practitioner-role']}
            </Box>
          );
        },
      },
      {
        field: 'organizationId',
        flex: 1,
        headerName: translate('agents.fields.organizationId'),
        minWidth: 300,
        sortable: false,
        renderCell: function render({ value, row }) {
          return organizationIsLoading ? (
            <>Loading...</>
          ) : (
            <Box
              component={'span'}
              sx={{
                color: theme => getAgentRowColor(row, theme),
              }}
            >
              {relatedOrgs?.data?.find((item: any) => item.id === value)?.name}
            </Box>
          );
        },
      },
      {
        field: 'type',
        flex: 1,
        headerName: translate('agents.fields.type'),
        minWidth: 200,
        renderCell: function render({ value, row }) {
          return (
            <Box
              component={'span'}
              sx={{
                color: theme => getAgentRowColor(row, theme),
              }}
            >
              {value}
              {row.env === 'default' && ' - Default'}
              {row.env === 'custom' && ' - Custom'}
            </Box>
          );
        },
      },
      {
        field: 'active',
        headerName: translate('agents.fields.active'),
        minWidth: 100,
        sortable: false,
        renderCell: function render({ value }) {
          return (
            <Label variant="soft" color={value ? 'success' : 'error'}>
              {translate(value ? 'status.active' : 'status.inactive')}
            </Label>
          );
        },
      },
      {
        field: 'actions',
        headerName: translate('table.actions'),
        sortable: false,
        renderCell: renderActionsCell,
        align: 'center',
        headerAlign: 'center',
        minWidth: 80,
      },
    ];
  }, [translate, relatedOrgs?.data, getAgentRowColor, renderActionsCell]);

  const displayColumns = useMemo(() => {
    let filteredColumns = columns;

    if (isSmallScreen) {
      filteredColumns = columns.filter(
        col =>
          col.field === 'name' ||
          col.field === 'active' ||
          col.field === 'actions',
      );
    }
    if (currentOrganization && currentOrganization.id) {
      filteredColumns = filteredColumns.filter(
        col => col.field !== 'organizationId',
      );
    }

    return filteredColumns;
  }, [columns, currentOrganization, isSmallScreen]);

  function renderGrid() {
    return (
      <>
        <DataGridPro
          {...dataGridProps}
          pagination
          sx={{
            '& .MuiDataGrid-cell': {
              display: 'flex',
              alignSelf: 'center',
              height: '100%',
              border: 'none',
            },
          }}
          disableColumnMenu
          columns={displayColumns}
          localeText={{ toolbarColumns: 'View' }}
          onCellClick={handleOnCellClick}
          slots={
            currentOrganization && currentOrganization.id
              ? undefined
              : {
                  toolbar: Toolbar,
                }
          }
          slotProps={{
            panel: {
              anchorEl: viewButtonEl,
            },
            toolbar: {
              onClick: (e: React.MouseEvent) => {
                e.stopPropagation();
                setViewButtonEl(null);
              },
            },
          }}
        />
      </>
    );
  }

  function renderGridTransHeader() {
    return (
      <List
        headerProps={{
          sx: {
            display: 'none',
          },
        }}
        contentProps={{
          sx: {
            p: '0 !important',
          },
        }}
        breadcrumb={null}
        wrapperProps={{
          sx: {
            boxShadow: 'none',
            background: 'transparent',
            [`& .${gridClasses.root}`]: {
              [`& .${gridClasses.cell}`]: {
                py: 1,
              },
              [`& .${gridClasses.columnHeader}`]: {
                bgcolor: 'transparent',
              },
              [`& .${gridClasses.columnHeaders}`]: {
                bgcolor: 'transparent',
                borderBottomStyle: 'dashed',
              },
            },
          },
        }}
      >
        {renderGrid()}
      </List>
    );
  }

  return (
    <>
      {currentOrganization && currentOrganization.id
        ? renderGridTransHeader()
        : renderGrid()}
      <Dialog
        open={showEditAlert}
        onClose={() => setShowEditAlert(false)}
        aria-labelledby="edit-alert-dialog-title"
        aria-describedby="edit-alert-dialog-description"
      >
        <DialogTitle id="alert-dialog-title">
          {translate('agents.edit.alert.title')}
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="alert-dialog-description">
            {translate('agents.edit.alert.agentEditAlert')}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowEditAlert(false)}>Okay</Button>
        </DialogActions>
      </Dialog>
    </>
  );
}
