import { BaseRecord, useTranslate } from '@refinedev/core';
import { Control, Controller, FieldErrors, FieldValues } from 'react-hook-form';

import { TextField } from '@mui/material';

import { Edit } from 'src/components/refine-customs/edit';

interface Props {
  control: Control<FieldValues, object>;
  errors: FieldErrors<FieldValues>;
  agentData: BaseRecord | undefined;
  saveButtonProps: any;
}

const AgentPromptTab = ({
  control,
  errors,
  agentData,
  saveButtonProps,
}: Props) => {
  const translate = useTranslate();
  return (
    <Edit
      deleteButtonProps={{
        hidden: true,
      }}
      refreshButtonProps={{
        hidden: true,
      }}
      headerButtonProps={{
        hidden: true,
      }}
      headerProps={{
        hidden: true,
      }}
      title=""
      contentProps={{
        sx: {
          flex: '1 1 auto',
          overflowY: 'auto',
          padding: '5px 0px 0px 0px',
        },
      }}
      saveButtonProps={saveButtonProps}
    >
      <Controller
        name="azureAssistantSetup.systemPrompt"
        control={control}
        defaultValue={agentData?.azureAssistantSetup?.systemPrompt}
        render={({ field }) => (
          <TextField
            {...field}
            fullWidth
            type="text"
            multiline
            name="azureAssistantSetup.systemPrompt"
            InputLabelProps={{ shrink: true }}
            InputProps={{
              sx: {
                height: '100%', // Make the TextField fill the available height
              },
            }}
            label={translate('agents.fields.systemPrompt')}
            error={!!(errors as any)?.azureAssistantSetup?.systemPrompt}
            helperText={
              (errors as any)?.azureAssistantSetup?.systemPrompt?.message
            }
          />
        )}
      />
    </Edit>
  );
};

export default AgentPromptTab;
