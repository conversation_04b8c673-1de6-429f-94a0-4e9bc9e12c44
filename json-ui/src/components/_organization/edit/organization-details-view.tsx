import { useTranslate } from '@refinedev/core';
import { RelicOrganization } from 'relic-ui';

import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';

import { PhoneInputView } from 'src/components/phone-input';
import facilityType from 'src/data/facilityType.json';

type OrganizationEditDetailsViewProps = {
  showEdit: boolean;
  currentOrganization: RelicOrganization;
};

export default function OrganizationDetailsViewOnly({
  showEdit,
  currentOrganization,
}: OrganizationEditDetailsViewProps) {
  const translate = useTranslate();
  return (
    <Stack
      spacing={2}
      component="form"
      sx={{
        flexDirection: 'column',
        display: showEdit ? 'none' : 'flex',
      }}
    >
      {/* Org Name */}
      <div>
        <Typography
          component={'span'}
          variant="subtitle2"
          sx={{
            fontSize: 12,
            color: 'text.secondary',
            display: 'block',
            mb: 0.5,
          }}
        >
          {translate('organizations.fields.name')}
        </Typography>

        <Typography component={'span'} variant="body2">
          {currentOrganization?.name || '-'}
        </Typography>
      </div>

      {/* Org Type */}
      <div>
        <Typography
          component={'span'}
          variant="subtitle2"
          sx={{
            fontSize: 12,
            color: 'text.secondary',
            display: 'block',
            mb: 0.5,
          }}
        >
          {translate('organizations.fields.type')}
        </Typography>

        <Typography component={'span'} variant="body2">
          {
            facilityType.find(item => item.code === currentOrganization?.type)
              ?.display
          }
        </Typography>
      </div>

      {/* Website */}
      <div>
        <Typography
          component={'span'}
          variant="subtitle2"
          sx={{
            fontSize: 12,
            color: 'text.secondary',
            display: 'block',
            mb: 0.5,
          }}
        >
          {translate('organizations.fields.website')}
        </Typography>

        <Typography component={'span'} variant="body2">
          {currentOrganization?.website || '-'}
        </Typography>
      </div>

      {/* Default Language */}
      <div>
        <Typography
          component={'span'}
          variant="subtitle2"
          sx={{
            fontSize: 12,
            color: 'text.secondary',
            display: 'block',
            mb: 0.5,
          }}
        >
          {translate('organizations.fields.language')}
        </Typography>

        <Typography component={'span'} variant="body2">
          {currentOrganization?.fhirStore?.defaultLanguage?.display || '-'}
        </Typography>
      </div>

      {/* Phone */}
      <div>
        <PhoneInputView
          value={currentOrganization?.phone}
          label={translate('organizations.fields.phone')}
        />
      </div>

      {/* Fax */}
      <div>
        <PhoneInputView
          value={currentOrganization?.fax}
          label={translate('organizations.fields.fax')}
        />
      </div>

      {/* PCC Id */}
      <div>
        <Typography
          component={'span'}
          variant="subtitle2"
          sx={{
            fontSize: 12,
            color: 'text.secondary',
            display: 'block',
            mb: 0.5,
          }}
        >
          {translate('organizations.fields.pointClickCareId')}
        </Typography>

        <Typography component={'span'} variant="body2">
          {currentOrganization?.pointClickCare?.id || '-'}
        </Typography>
      </div>

      {/* Location */}
      {/* <div>
        <Typography
          component={'span'}
          variant="subtitle2"
          sx={{ fontSize: 12, color: 'text.secondary', display: 'block', mb: 0.5 }}
        >
          {translate("organizations.fields.location")}
        </Typography>

        {currentOrganization?.location?.length ? (
          <Stack direction={'row'} spacing={1} flexWrap={'wrap'} sx={{ mt: 0.5 }}>
            {currentOrganization?.location.map((location: Location) => (
              <Chip key={location.name} label={location.name} />
            ))}
          </Stack>
        ) : (
          '-'
        )}
      </div> */}

      {/* Patient Summary Template */}
      {/* <div>
        <Typography
          component={'span'}
          variant="subtitle2"
          sx={{ fontSize: 12, color: 'text.secondary', display: 'block', mb: 0.5 }}
        >
          {translate("organizations.fields.patientSummary")}
        </Typography>

        <Typography component={'span'} variant="body2">
          {currentOrganization?.template?.patientSummary || '-'}
        </Typography>
      </div> */}

      {/* Welcome Sms Template */}
      {/* <div>
        <Typography
          component={'span'}
          variant="subtitle2"
          sx={{ fontSize: 12, color: 'text.secondary', display: 'block', mb: 0.5 }}
        >
          {translate("organizations.fields.welcomeSms")}
        </Typography>

        <Typography component={'span'} variant="body2">
          {currentOrganization?.template?.welcomeSms || '-'}
        </Typography>
      </div> */}

      {/* Welcome Email Template */}
      {/* <div>
        <Typography
          component={'span'}
          variant="subtitle2"
          sx={{ fontSize: 12, color: 'text.secondary', display: 'block', mb: 0.5 }}
        >
          {translate("organizations.fields.welcomeEmail")}
        </Typography>

        <Typography
            component={'span'}
            variant="body2"
            dangerouslySetInnerHTML={{
              __html: `${currentOrganization?.template?.welcomeEmail || '-'}`,
            }}
          />
      </div> */}
    </Stack>
  );
}
