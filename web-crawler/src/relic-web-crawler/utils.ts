import path from 'path';
import { EXCLUDED_URL_REGEX, SUPPORTED_FILE_EXTS_REGEX } from './constants.js';
import { CrawlRequest, RelicCrawlerOptions } from 'relic-ui'
import { PlaywrightCrawlerOptions } from 'crawlee';


/**
 * Clean the URL
 * 
 * if URL is for a file link, delete query parameters
 * 
 * else return original url
 * @param {string} url 
 * @returns {string}
 */
export function cleanUrl(url: string): string {
    const parsedUrl = new URL(url);
    if (isFileLink(url)) {
        return path.join(parsedUrl.origin, parsedUrl.pathname);
    }
    return url;
}


/**
 * Determine if the URL refers to a file
 * @param {string} url 
 * @returns {boolean}
 */
export function isFileLink(url: string): boolean {
    const parsedUrl = new URL(url);
    return SUPPORTED_FILE_EXTS_REGEX.test(parsedUrl.pathname);
}

/**
 * Determine if the URL is supported
 * @param {string} url 
 * @returns {boolean}
 */
export function isUrlSupported(url: string): boolean {
    const parsedUrl = new URL(url);
    return !EXCLUDED_URL_REGEX.test(parsedUrl.pathname);
}

/**
 * Format string for storage filename
 * 
 * 1. Keep alphanumeric characters, period(.) and dash(-)
 * 
 * 2. Replace whitespace with dash(-)
 * 
 * 3. length limit of 500
 * 
 * @param {string} input 
 * @returns {string}
 */
export function formatString(input: string): string {
    const MAX_LENGTH = 500;
    const validCharactersRegex = /[^A-Za-z0-9_.-]/g;
    input = input.replace(/\s+/g, '_');
    input = input.replace(validCharactersRegex, '');
    return input.substring(0, MAX_LENGTH);
}

/**
 * Validate actor input
 * 
 * @param {CrawlerRunRequest} input 
 * @throws {Error}
 */
export function validateInput(input: CrawlRequest): void {

}

/**
 * Converts a `RelicCrawlerOptions` object to `PlaywrightCrawlerOptions`.
 * This is a function and cannot be imported through relic-ui as relic-ui is a front end library and designed only as a type library for server side usage.
 *
 * @param config - The configuration object for the crawler.
 * @returns The converted `PlaywrightCrawlerOptions` object.
 */
export function toPlaywrightCrawlerOptions(config: RelicCrawlerOptions): PlaywrightCrawlerOptions {
    const { removeElementCssSelector, maxUrlsToCrawl, relevanceParams, useReadableContent, maxDepth, crawlEmbeddedLinks, ...playwrightOptions } = config;
    return playwrightOptions as PlaywrightCrawlerOptions;
}

/**
 * Generate title for a page
 * This helps in differentiating between pages with the same title but different query parameters
 * 
 * @param title - Title of the page
 * @param url - URL of the page
 * @returns Title for the page
 */
export function addSearchParamsToTitle(title: string, url: string): string {
    try {
        if (title == url) {
            return title;
        }
        const urlObj = new URL(url);
        const searchParams = urlObj.searchParams;
        if (searchParams.size == 0) {
            return title;
        }

        const formattedParams = Array.from(searchParams.entries())
            .map(([key, value]) => `${key}:${value}`)
            .join('|');


        // Format the output
        return `${title} - ${formattedParams}`;
    } catch (error) {
        return title;
    }
}

/**
 * Create a documentId accepted by azure storage
 * @param names 
 */
export function createDocumentId(...names: string[]): string {
    return `${formatString(names.join('-'))}-${Date.now()}.json`;
}