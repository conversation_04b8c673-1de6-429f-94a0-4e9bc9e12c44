import { useTranslate } from '@refinedev/core';

import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Divider from '@mui/material/Divider';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';

import Iconify from 'src/components/iconify';

// ----------------------------------------------------------------------

type AccountToolbarProps = {
  userName?: string;
  onBack: VoidFunction;
  showDetails: boolean;
  onToggleDetails: VoidFunction;
  onOpenAdd: (event: React.MouseEvent<HTMLElement, MouseEvent>) => void;
};

export default function AccountToolbar({
  onBack,
  onOpenAdd,
  userName,
  showDetails,
  onToggleDetails,
}: AccountToolbarProps) {
  const translate = useTranslate();

  return (
    <Stack
      direction={'row'}
      alignItems={'center'}
      sx={{
        pb: 3,
        zIndex: 9,
        position: 'relative',
        pt: { xs: 2, lg: 0 },
        background: 'background.paper',
      }}
    >
      <IconButton onClick={onBack}>
        <Iconify icon="eva:arrow-ios-back-fill" />
      </IconButton>

      <Typography variant="h5" sx={{ flexGrow: 1 }}>
        {userName}
      </Typography>

      <Button
        variant="contained"
        startIcon={<Iconify icon={'eva:plus-fill'} />}
        onClick={onOpenAdd}
      >
        {translate('buttons.create')}
      </Button>

      <IconButton
        onClick={onToggleDetails}
        color={showDetails ? 'inherit' : 'default'}
        sx={{ ml: 1 }}
      >
        <Iconify
          icon={
            !showDetails
              ? 'tabler:layout-sidebar-right-collapse'
              : 'tabler:layout-sidebar-right-collapse-filled'
          }
        />
      </IconButton>

      <Divider
        sx={{
          bottom: 0,
          position: 'absolute',
          left: { xs: -8, md: -16, lg: -24 },
          right: { xs: -8, md: -16, lg: -24 },
        }}
      />
    </Stack>
  );
}
