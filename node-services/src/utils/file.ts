import fs from 'fs';
import crypto from 'crypto';
import path from 'path';

/**
 * Suffix file name. ex: filename.txt -> filenamesuffix.txt
 * @param {string} filename
 * @param {string} suffix
 * @param {boolean} randomize - if true, randomize the local filename
 * @returns {string} filename with suffix appended
 */
function suffixFilename(filename: string, suffix: string, randomize: boolean = false): string {
  const ext = (path.parse(filename)).ext;
  const dir = (path.parse(filename)).dir + '/';;
  const targetFileName: string = randomize ? crypto.randomUUID() : dir + (path.parse(filename)).name; 
  return targetFileName + suffix + ext;
}

/**
 * Deletes a local file
 * @param {string} filePath - File to be deleted
 * @returns {Promise<void>} Not sure if this promise resolves to void or undefined. Need to check.
 */
async function deleteLocalFile(filePath: string): Promise<void> {
  try {
    await fs.promises.access(filePath, fs.constants.F_OK);
    await fs.promises.unlink(filePath);
  } catch (error) {
    //Do nothing if file does not exist.
  }
}

export { suffixFilename, deleteLocalFile };
