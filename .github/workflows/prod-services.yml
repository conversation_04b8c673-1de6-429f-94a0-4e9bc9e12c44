name: Build & Deploy Prod Services

# Trigger the workflow manually by updating the branch name in the trigger & jobs
on:
  push:
    branches:
      - 'random-scripts'
      - 'cb1-441-scripts'

jobs:
  prod-services:
    if: github.ref == 'refs/heads/cb1-441-scripts'
    runs-on: ubuntu-latest
    steps:
    # checkout the repo
    - name: 'Checkout from Github'
      uses: actions/checkout@main
      
    - name: 'Login via Azure CLI'
      uses: azure/login@v2
      with:
        creds: '{"clientId":"${{ secrets.AZURE_CLIENT_ID }}","clientSecret":"${{ secrets.AZURE_CLIENT_SECRET }}","subscriptionId":"${{ secrets.AZURE_DEV_SUBSCRIPTION_ID }}","tenantId":"${{ secrets.AZURE_TENANT_ID }}"}'

    - name: Get current time
      uses: josStorer/get-current-time@v2
      id: current-time
      with:
        format: YYYYMMDD-HHmm
        timezone: "America/Los_Angeles"

    - name: 'Build and push image'
      uses: docker/login-action@v3
      with:
        registry: ${{ secrets.REGISTRY_LOGIN_SERVER }}
        username: ${{ secrets.AZURE_CLIENT_ID }}
        password: ${{ secrets.AZURE_CLIENT_SECRET }}
    - run: |
        cd node-services
        docker build -t ${{ secrets.REGISTRY_LOGIN_SERVER }}/prod-services:${{ steps.current-time.outputs.formattedTime }} -f app/node-services .
        docker push ${{ secrets.REGISTRY_LOGIN_SERVER }}/prod-services:${{ steps.current-time.outputs.formattedTime }}

    - name: 'Update Container App to use latest image'
      uses: Azure/cli@v1.0.7
      with:
        inlineScript: |
          az account set --subscription 7dbbdb01-e1ea-46ea-83ff-c7b38975411d
          az containerapp update -n prod-services -g relic-prod-group --image aicounsellor.azurecr.io/prod-services:${{ steps.current-time.outputs.formattedTime }}

  prod-messenger:
    if: github.ref == 'refs/heads/cb1-441-scripts'
    runs-on: ubuntu-latest
    steps:
    - name: 'Checkout from Github'
      uses: actions/checkout@main

    - name: 'Login via Azure CLI'
      uses: azure/login@v2
      with:
        creds: '{"clientId":"${{ secrets.AZURE_CLIENT_ID }}","clientSecret":"${{ secrets.AZURE_CLIENT_SECRET }}","subscriptionId":"${{ secrets.AZURE_DEV_SUBSCRIPTION_ID }}","tenantId":"${{ secrets.AZURE_TENANT_ID }}"}'

    - name: Get current time
      uses: josStorer/get-current-time@v2
      id: current-time
      with:
        format: YYYYMMDD-HHmm
        timezone: "America/Los_Angeles"
  
    - name: 'Build and push image'
      uses: docker/login-action@v3
      with:
        registry: ${{ secrets.REGISTRY_LOGIN_SERVER }}
        username: ${{ secrets.AZURE_CLIENT_ID }}
        password: ${{ secrets.AZURE_CLIENT_SECRET }}
    - run: |
        docker build -t ${{ secrets.REGISTRY_LOGIN_SERVER }}/prod-messenger:${{ steps.current-time.outputs.formattedTime }} -f app/agent-messenger .
        docker push ${{ secrets.REGISTRY_LOGIN_SERVER }}/prod-messenger:${{ steps.current-time.outputs.formattedTime }}

    - name: 'Update Container App to use latest image'
      uses: Azure/cli@v1.0.7
      with:
        inlineScript: |
          az account set --subscription 7dbbdb01-e1ea-46ea-83ff-c7b38975411d
          az containerapp update -n prod-messenger -g relic-prod-group --image aicounsellor.azurecr.io/prod-messenger:${{ steps.current-time.outputs.formattedTime }}
