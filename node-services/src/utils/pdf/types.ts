import { BlobServiceClient } from '@azure/storage-blob';

export type TranslateDocumentOptions = {
    workingDirectory: string; // main folder directory for 1 translate doc request, <main_downloads>/<organization_id>/<user_id>/
    sourceDocumentId: string;
    sourceLang: string;
    targetLang: string;
    sourceFilepath: string;
    numHeaderLines: number;
    numFooterLines: number;
    sourceJsonTextblocksFileDir?: string;
    translatedJsonTextblocksFileDir?: string;
    filenameWithoutExt?: string;
    azureServiceClient: BlobServiceClient;
    translationType: 'mono' | 'bilingual';
}

export type TranslateDocumentResult = TranslateDocumentOptions & {
    translatedFilepath: string;
    sourceTextBlocksUpdated: boolean;
}
