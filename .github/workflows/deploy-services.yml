name: Build & Deploy Dev Services

on:
  push:
    branches:
      - 'main'

env:
  # Azure Configuration
  AZURE_TENANT_ID: ${{ secrets.AZURE_TENANT_ID }}
  AZURE_CLIENT_ID: ${{ secrets.AZURE_CLIENT_ID }}
  AZURE_CLIENT_SECRET: ${{ secrets.AZURE_CLIENT_SECRET }}
  AZURE_SUBSCRIPTION_ID: ${{ secrets.AZURE_SUBSCRIPTION_ID }}
  REGISTRY_LOGIN_SERVER: ${{ secrets.REGISTRY_LOGIN_SERVER }}
  
  # Azure Resource Configuration
  AZURE_RESOURCE_GROUP: ai-counsellor
  AZURE_REGISTRY_NAME: aicounsellor
  AZURE_REGISTRY_URL: aicounsellor.azurecr.io

  # Apify Configuration
  APIFY_BUILD_TOKEN: ${{ secrets.APIFY_BUILD_TOKEN }}
  APIFY_ACTOR_ID: M4nchkJlaBsRI9jpF
  APIFY_API_BASE_URL: https://api.apify.com/v2/acts

jobs:
  apify-actor:
    if: github.ref == 'refs/heads/some-random-branch-that-does-not-exist'
    runs-on: ubuntu-latest
    steps:
      # checkout the repo
      - name: 'Checkout from Github'
        uses: actions/checkout@main

      - name: 'Deploy Actor to Apify'
        uses: distributhor/workflow-webhook@v3
        with:
          webhook_url: ${{ env.APIFY_API_BASE_URL }}/${{ env.APIFY_ACTOR_ID }}/builds?token=${{ env.APIFY_BUILD_TOKEN }}&version=0.0&tag=latest&waitForFinish=60
          webhook_secret: ${{ env.APIFY_BUILD_TOKEN }}

  node-services:
    if: github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    steps:
    # checkout the repo
    - name: 'Checkout from Github'
      uses: actions/checkout@main
      
    - name: 'Login via Azure CLI'
      uses: azure/login@v2
      with:
        creds: '{"clientId":"${{ env.AZURE_CLIENT_ID }}","clientSecret":"${{ env.AZURE_CLIENT_SECRET }}","subscriptionId":"${{ env.AZURE_SUBSCRIPTION_ID }}","tenantId":"${{ env.AZURE_TENANT_ID }}"}'

    # - name: 'Delete node-services repository'
    #   uses: azure/login@v2
    #   with:
    #     creds: '{"clientId":"${{ env.AZURE_CLIENT_ID }}","clientSecret":"${{ env.AZURE_CLIENT_SECRET }}","subscriptionId":"${{ env.AZURE_SUBSCRIPTION_ID }}","tenantId":"${{ env.AZURE_TENANT_ID }}"}'
    # - run: |
    #       az acr repository delete --name ${{ env.AZURE_REGISTRY_NAME }} --resource-group ${{ env.AZURE_RESOURCE_GROUP }} --repository node-services --yes

    
    - name: 'Build and push image'
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY_LOGIN_SERVER }}
        username: ${{ env.AZURE_CLIENT_ID }}
        password: ${{ env.AZURE_CLIENT_SECRET }}
    - run: |
        docker build -t ${{ env.REGISTRY_LOGIN_SERVER }}/node-services:$GITHUB_SHA -f app/node-services .
        docker push ${{ env.REGISTRY_LOGIN_SERVER }}/node-services:$GITHUB_SHA

    - name: 'Update Container App to use latest image'
      uses: Azure/cli@v1.0.7
      with:
        inlineScript: |
          az containerapp update -n node-services -g ${{ env.AZURE_RESOURCE_GROUP }} --image ${{ env.AZURE_REGISTRY_URL }}/node-services:$GITHUB_SHA

  agent-messenger:
    if: github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    steps:
      - name: 'Checkout from Github'
        uses: actions/checkout@main

      - name: 'Login via Azure CLI'
        uses: azure/login@v2
        with:
          creds: '{"clientId":"${{ env.AZURE_CLIENT_ID }}","clientSecret":"${{ env.AZURE_CLIENT_SECRET }}","subscriptionId":"${{ env.AZURE_SUBSCRIPTION_ID }}","tenantId":"${{ env.AZURE_TENANT_ID }}"}'

      # - name: 'Delete agent-messenger repository'
      #   uses: azure/login@v2
      #   with:
      #     creds: '{"clientId":"${{ env.AZURE_CLIENT_ID }}","clientSecret":"${{ env.AZURE_CLIENT_SECRET }}","subscriptionId":"${{ env.AZURE_SUBSCRIPTION_ID }}","tenantId":"${{ env.AZURE_TENANT_ID }}"}'
      # - run: |
      #     az acr repository delete --name ${{ env.AZURE_REGISTRY_NAME }} --resource-group ${{ env.AZURE_RESOURCE_GROUP }} --repository agent-messenger --yes
      
      - name: 'Build and push image'
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY_LOGIN_SERVER }}
          username: ${{ env.AZURE_CLIENT_ID }}
          password: ${{ env.AZURE_CLIENT_SECRET }}

      - run: |
          docker build -t ${{ env.REGISTRY_LOGIN_SERVER }}/agent-messenger:$GITHUB_SHA -f app/agent-messenger .
          docker push ${{ env.REGISTRY_LOGIN_SERVER }}/agent-messenger:$GITHUB_SHA

      - name: 'Update Container App to use latest image'
        uses: Azure/cli@v1.0.7
        with:
          inlineScript: |
            az containerapp update -n agent-messenger -g ${{ env.AZURE_RESOURCE_GROUP }} --image ${{ env.AZURE_REGISTRY_URL }}/agent-messenger:$GITHUB_SHA
            
