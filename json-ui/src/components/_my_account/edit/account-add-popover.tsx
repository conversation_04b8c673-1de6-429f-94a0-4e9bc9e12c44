import { useTranslate } from '@refinedev/core';
import { IUser, RelicPatient, RelicOrganization } from 'relic-ui';

import Popover from '@mui/material/Popover';
import MenuItem from '@mui/material/MenuItem';

import { ReturnType as PopoverProps } from 'src/hooks/use-popover';

import Iconify from 'src/components/iconify';
import { CreateDocument } from 'src/components/_document';
import CreateThread from 'src/components/_threads/create/threads-create';

// ----------------------------------------------------------------------

type AddPopoverProps = {
  popover: PopoverProps;
  openDocument: boolean;
  onOpenDocument: VoidFunction;
  onCloseDocument: VoidFunction;
  openThread: boolean;
  onOpenThread: VoidFunction;
  onCloseThread: VoidFunction;
  currentUser?: IUser;
  currentOrganization?: RelicOrganization;
  currentPatient?: RelicPatient;
};

export default function AccountAddPopover({
  popover,
  openDocument,
  onOpenDocument,
  onCloseDocument,
  openThread,
  onOpenThread,
  onCloseThread,
  currentUser,
  currentOrganization,
  currentPatient,
}: AddPopoverProps) {
  const translate = useTranslate();

  return (
    <>
      <Popover
        open={!!popover.open}
        anchorEl={popover.open}
        onClose={popover.onClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        slotProps={{
          paper: {
            sx: {
              mt: 0.5,
              minWidth: 160,
            },
          },
        }}
      >
        <MenuItem onClick={onOpenThread}>
          <Iconify width={18} icon={'eva:plus-fill'} sx={{ mr: 1 }} />
          {translate('threads.titles.create')}
        </MenuItem>

        <MenuItem onClick={onOpenDocument}>
          <Iconify width={18} icon={'eva:plus-fill'} sx={{ mr: 1 }} />
          {translate('documents.titles.create')}
        </MenuItem>
      </Popover>

      {openDocument && (
        <CreateDocument open={openDocument} onClose={onCloseDocument} />
      )}

      {openThread && (
        <CreateThread
          open={openThread}
          onClose={onCloseThread}
          currentOrganization={currentOrganization}
          currentPatient={currentPatient}
        />
      )}
    </>
  );
}
