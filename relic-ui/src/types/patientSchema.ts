import S from 'fluent-json-schema';

const phoneSchema = S.object()
  .prop('mobilePhone', S.string())
  .prop('homePhone', S.string())
  .anyOf([
    S.object().prop('mobilePhone', S.string().minLength(10).required()),
    S.object().prop('homePhone', S.string().minLength(10).required()),
  ]);
const linkSchema = S.array().items(
  S.object()
    .prop('resourceType', S.string().required())
    .prop(
      'relationship',
      S.object()
        .prop('system', S.string().required())
        .prop('code', S.string().required())
        .prop('display', S.string().required()),
    )
    .prop('use', S.string().required())
    .prop('name', S.string().required())
    .prop('email', S.string().format('email').required())
    .prop(
      'gender',
      S.string().enum(['Male', 'Female', 'Other', 'Unknown', 'male', 'female', 'other', 'unknown']).required(),
    )
    .prop('birthDate', S.string().format('date').required())
    .extend(phoneSchema),
);

const communicationLanguageSchema = S.array().items(
  S.object()
    .prop('system', S.string().required())
    .prop('code', S.string().required())
    .prop('display', S.string().required())
    .prop('preferred', S.boolean()),
);

const conditionSchema = S.array().items(
  S.object()
    .prop('clinicalStatus', S.string().required())
    .prop('verificationStatus', S.string().required())
    .prop('onsetDateTime', S.string().format('date'))
    .prop('abatementDateTime', S.string().format('date'))
    .prop('note', S.string()),
);

const locationSchema = S.object().prop(
  'Location',
  S.object().prop('reference', S.string().required()).prop('display', S.string().required()),
);

const patientSchema = S.object()
  .prop('name', S.string().required())
  .prop('email', S.string().format('email').required())
  .prop(
    'gender',
    S.string().required().enum(['Male', 'Female', 'Other', 'Unknown', 'male', 'female', 'other', 'unknown']),
  )
  .prop('birthDate', S.string().format('date').required())
  .prop(
    'maritalStatus',
    S.string()
      .enum([
        'Annulled',
        'Divorced',
        'Interlocutory',
        'Legally Separated',
        'Married',
        'Polygamous',
        'Never Married',
        'Domestic partner',
        'Unmarried',
        'Widowed',
        'Unknown',
      ])
      .required(),
  )
  .prop('active', S.boolean().default(true))
  .prop('link', linkSchema)
  .prop('Location', locationSchema)

  .prop('communicationLanguage', communicationLanguageSchema);

export { phoneSchema, linkSchema, communicationLanguageSchema, conditionSchema, locationSchema, patientSchema };
