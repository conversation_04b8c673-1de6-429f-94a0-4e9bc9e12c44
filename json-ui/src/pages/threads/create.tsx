import React from 'react';
import { useDataGrid } from '@refinedev/mui';
import { IUser, RelicAgent, IUserIdentity } from 'relic-ui';
import { CrudFilter, useGetIdentity } from '@refinedev/core';

import { useRouter } from 'src/routes/hooks';

import CreateThread from 'src/components/_threads/create/threads-create';

export const ThreadsCreate = () => {
  const [open, setOpen] = React.useState(true);
  const router = useRouter();

  const { data: currentUser } = useGetIdentity<IUser>();
  const currentIdentity: IUserIdentity =
    currentUser?.userIdentity as IUserIdentity;

  const organizationId = currentIdentity?.portalIdentity?.organizationId;

  const currentRole: string = currentIdentity?.role?.name
    ? currentIdentity?.role?.name
    : 'member';

  const initialFilters: CrudFilter[] = React.useMemo(() => {
    const filter: CrudFilter = {
      field: 'organizationId',
      value: organizationId,
      operator: 'eq',
    };
    return currentRole === 'admin' ? [] : [filter];
  }, [organizationId, currentRole]);

  const { dataGridProps: listAgentProps } = useDataGrid<RelicAgent>({
    resource: 'agents',
    sorters: {
      initial: [
        {
          field: 'name',
          order: 'asc',
        },
      ],
    },
    filters: {
      permanent: initialFilters,
    },
  });

  const handleCLose = () => {
    setOpen(false);
    router.push('/threads');
  };

  return <CreateThread open={open} onClose={handleCLose} />;
};
