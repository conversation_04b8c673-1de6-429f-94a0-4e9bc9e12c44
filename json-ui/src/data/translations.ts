// translations.ts
export const fields: any = {
    en: {
      allergies: "Allergies",
      dob: "Date of Birth",
      physician: "Physician",
      facility: "Facility",
      resident: "Resident",
      admissionDate: "Admission Date",
      location: "Location",
      page: "Page",
      of: "of",
      focus: "Focus",
      goal: "Goal",
      interventions: "Interventions",
      position: "Position",
      frequencyResolved: "Frequency/Resolved",
      dateInitiated: "Date Initiated",
      revisionDate: "Revision Date",
      targetDate: "Target Date",
      notAvailable: "N/A",
      noFocusItems: "No focus items available",
    },
    hi: {
      allergies: "एलर्जी",
      dob: "जन्म तिथि",
      physician: "चिकित्सक",
      facility: "सुविधा",
      resident: "निवासी",
      admissionDate: "प्रवेश तिथि",
      location: "स्थान",
      page: "पृष्ठ",
      of: "का",
      focus: "केंद्र",
      goal: "लक्ष्य",
      interventions: "हस्तक्षेप",
      position: "स्थिति",
      frequencyResolved: "आवृत्ति/संपन्न",
      dateInitiated: "प्रारंभ तिथि",
      revisionDate: "समीक्षा तिथि",
      targetDate: "लक्ष्य तिथि",
      notAvailable: "उपलब्ध नहीं",
      noFocusItems: "कोई केंद्रित आइटम उपलब्ध नहीं",
    },
    ar: {
      allergies: "الحساسية",
      dob: "تاريخ الميلاد",
      physician: "الطبيب",
      facility: "المنشأة",
      resident: "المقيم",
      admissionDate: "تاريخ القبول",
      location: "الموقع",
      page: "صفحة",
      of: "من",
      focus: "التركيز",
      goal: "الهدف",
      interventions: "التدخلات",
      position: "الموقع",
      frequencyResolved: "التكرار/تم الحل",
      dateInitiated: "تاريخ البدء",
      revisionDate: "تاريخ المراجعة",
      targetDate: "التاريخ المستهدف",
      notAvailable: "غير متاح",
      noFocusItems: "لا توجد عناصر متاحة",
    },
    ko: {
      allergies: "알레르기",
      dob: "생년월일",
      physician: "의사",
      facility: "시설",
      resident: "거주자",
      admissionDate: "입원일",
      location: "위치",
      page: "페이지",
      of: "의",
      focus: "초점",
      goal: "목표",
      interventions: "개입",
      position: "위치",
      frequencyResolved: "빈도/해결됨",
      dateInitiated: "시작 날짜",
      revisionDate: "수정 날짜",
      targetDate: "목표 날짜",
      notAvailable: "없음",
      noFocusItems: "사용 가능한 항목 없음",
    },
    ja: {
      allergies: "アレルギー",
      dob: "生年月日",
      physician: "医師",
      facility: "施設",
      resident: "居住者",
      admissionDate: "入院日",
      location: "場所",
      page: "ページ",
      of: "の",
      focus: "焦点",
      goal: "目標",
      interventions: "介入",
      position: "位置",
      frequencyResolved: "頻度/解決済み",
      dateInitiated: "開始日",
      revisionDate: "改訂日",
      targetDate: "目標日",
      notAvailable: "利用不可",
      noFocusItems: "利用可能な項目がありません",
    },
    zh: {
      allergies: "过敏",
      dob: "出生日期",
      physician: "医生",
      facility: "设施",
      resident: "居民",
      admissionDate: "入院日期",
      location: "位置",
      page: "页",
      of: "的",
      focus: "焦点",
      goal: "目标",
      interventions: "干预",
      position: "位置",
      frequencyResolved: "频率/已解决",
      dateInitiated: "开始日期",
      revisionDate: "修订日期",
      targetDate: "目标日期",
      notAvailable: "不可用",
      noFocusItems: "没有可用的项目",
    },
    es: {
      allergies: "Alergias",
      dob: "Fecha de Nacimiento",
      physician: "Médico",
      facility: "Instalación",
      resident: "Residente",
      admissionDate: "Fecha de Admisión",
      location: "Ubicación",
      page: "Página",
      of: "de",
      focus: "Enfoque",
      goal: "Objetivo",
      interventions: "Intervenciones",
      position: "Posición",
      frequencyResolved: "Frecuencia/Resuelto",
      dateInitiated: "Fecha de Inicio",
      revisionDate: "Fecha de Revisión",
      targetDate: "Fecha Objetivo",
      notAvailable: "N/D",
      noFocusItems: "No hay elementos de enfoque disponibles",
    },
    th: {
      allergies: "อาการแพ้",
      dob: "วันเกิด",
      physician: "แพทย์",
      facility: "สถานที่",
      resident: "ผู้อยู่อาศัย",
      admissionDate: "วันที่เข้ารับ",
      location: "สถานที่ตั้ง",
      page: "หน้า",
      of: "ของ",
      focus: "จุดสนใจ",
      goal: "เป้าหมาย",
      interventions: "การแทรกแซง",
      position: "ตำแหน่ง",
      frequencyResolved: "ความถี่/แก้ไขแล้ว",
      dateInitiated: "วันที่เริ่มต้น",
      revisionDate: "วันที่แก้ไข",
      targetDate: "วันที่เป้าหมาย",
      notAvailable: "ไม่มีข้อมูล",
      noFocusItems: "ไม่มีรายการที่ใช้ได้",
    },
    my: {
      allergies: "ဓာတ်မတည့်မှု",
      dob: "မွေးနေ့",
      physician: "ဆရာဝန်",
      facility: "အဆောက်အအုံ",
      resident: "နေထိုင်သူ",
      admissionDate: "လက်ခံသည့်ရက်",
      location: "တည်နေရာ",
      page: "စာမျက်နှာ",
      of: "၏",
      focus: "အာရုံစိုက်ချက်",
      goal: "ရည်မှန်းချက်",
      interventions: "လက်လှမ်းဖြတ်စရာ",
      position: "တည်နေရာ",
      frequencyResolved: "ကြိမ်နှုန်း/ဖြေရှင်းပြီး",
      dateInitiated: "စတင်ရက်",
      revisionDate: "ပြန်လည်စစ်ဆေးမှုရက်",
      targetDate: "ရည်မှန်းရက်",
      notAvailable: "မရနိုင်ပါ",
      noFocusItems: "မည်သည့်အရာမျှရနိုင်ပါ",
    },
    fr: {
        allergies: "Allergies",
        dob: "Date de Naissance",
        physician: "Médecin",
        facility: "Établissement",
        resident: "Résident",
        admissionDate: "Date d'Admission",
        location: "Emplacement",
        page: "Page",
        of: "de",
        focus: "Focus",
        goal: "Objectif",
        interventions: "Interventions",
        position: "Position",
        frequencyResolved: "Fréquence/Résolu",
        dateInitiated: "Date de Début",
        revisionDate: "Date de Révision",
        targetDate: "Date Cible",
        notAvailable: "N/A",
        noFocusItems: "Aucun élément disponible",
      },
      nl: {
        allergies: "Allergieën",
        dob: "Geboortedatum",
        physician: "Arts",
        facility: "Faciliteit",
        resident: "Bewoner",
        admissionDate: "Opnamedatum",
        location: "Locatie",
        page: "Pagina",
        of: "van",
        focus: "Focus",
        goal: "Doel",
        interventions: "Interventies",
        position: "Positie",
        frequencyResolved: "Frequentie/Opgelost",
        dateInitiated: "Startdatum",
        revisionDate: "Herzieningsdatum",
        targetDate: "Streefdatum",
        notAvailable: "N/B",
        noFocusItems: "Geen beschikbare items",
      },
      hy: {
        allergies: "Ալերգիաներ",
        dob: "Ծննդյան ամսաթիվ",
        physician: "Բժիշկ",
        facility: "Հաստատություն",
        resident: "Բնակիչ",
        admissionDate: "Մուտքի ամսաթիվ",
        location: "Վայր",
        page: "Էջ",
        of: "ի",
        focus: "Կենտրոն",
        goal: "Նպատակ",
        interventions: "Միջամտություններ",
        position: "Դիրք",
        frequencyResolved: "Հաճախականություն/Լուծված",
        dateInitiated: "Սկսման ամսաթիվ",
        revisionDate: "Վերանայման ամսաթիվ",
        targetDate: "Թիրախային ամսաթիվ",
        notAvailable: "Անհասանելի է",
        noFocusItems: "Առկա չեն կենտրոնացված միավորներ",
      },
      bg: {
        allergies: "Алергии",
        dob: "Дата на раждане",
        physician: "Лекар",
        facility: "Заведение",
        resident: "Жител",
        admissionDate: "Дата на приемане",
        location: "Местоположение",
        page: "Страница",
        of: "от",
        focus: "Фокус",
        goal: "Цел",
        interventions: "Интервенции",
        position: "Позиция",
        frequencyResolved: "Честота/Решено",
        dateInitiated: "Начална дата",
        revisionDate: "Дата на преразглеждане",
        targetDate: "Целева дата",
        notAvailable: "Няма данни",
        noFocusItems: "Няма налични елементи за фокусиране",
      },
      yue: {
        allergies: "過敏",
        dob: "出生日期",
        physician: "醫生",
        facility: "設施",
        resident: "住客",
        admissionDate: "入院日期",
        location: "位置",
        page: "頁",
        of: "的",
        focus: "重點",
        goal: "目標",
        interventions: "介入",
        position: "位置",
        frequencyResolved: "頻率/已解決",
        dateInitiated: "開始日期",
        revisionDate: "修訂日期",
        targetDate: "目標日期",
        notAvailable: "無法提供",
        noFocusItems: "沒有可用的重點項目",
      },
      en_US: {
        allergies: "Allergies",
        dob: "Date of Birth",
        physician: "Physician",
        facility: "Facility",
        resident: "Resident",
        admissionDate: "Admission Date",
        location: "Location",
        page: "Page",
        of: "of",
        focus: "Focus",
        goal: "Goal",
        interventions: "Interventions",
        position: "Position",
        frequencyResolved: "Frequency/Resolved",
        dateInitiated: "Date Initiated",
        revisionDate: "Revision Date",
        targetDate: "Target Date",
        notAvailable: "N/A",
        noFocusItems: "No focus items available",
      },
      fil: {
        allergies: "Mga Alerhiya",
        dob: "Araw ng Kapanganakan",
        physician: "Doktor",
        facility: "Pasilidad",
        resident: "Residente",
        admissionDate: "Petsa ng Pagpasok",
        location: "Lokasyon",
        page: "Pahina",
        of: "ng",
        focus: "Pokusan",
        goal: "Layunin",
        interventions: "Mga Interbensyon",
        position: "Posisyon",
        frequencyResolved: "Dalasan/Naresolba",
        dateInitiated: "Petsa ng Pagsisimula",
        revisionDate: "Petsa ng Rebisyon",
        targetDate: "Petsa ng Target",
        notAvailable: "Hindi Magagamit",
        noFocusItems: "Walang magagamit na pokus na item",
      },
      it: {
        allergies: "Allergie",
        dob: "Data di Nascita",
        physician: "Medico",
        facility: "Struttura",
        resident: "Residente",
        admissionDate: "Data di Ammissione",
        location: "Posizione",
        page: "Pagina",
        of: "di",
        focus: "Focus",
        goal: "Obiettivo",
        interventions: "Interventi",
        position: "Posizione",
        frequencyResolved: "Frequenza/Risolto",
        dateInitiated: "Data di Inizio",
        revisionDate: "Data di Revisione",
        targetDate: "Data Target",
        notAvailable: "N/D",
        noFocusItems: "Nessun elemento disponibile",
      },
      fa: {
        allergies: "آلرژی‌ها",
        dob: "تاریخ تولد",
        physician: "پزشک",
        facility: "مرکز",
        resident: "ساکن",
        admissionDate: "تاریخ پذیرش",
        location: "مکان",
        page: "صفحه",
        of: "از",
        focus: "تمرکز",
        goal: "هدف",
        interventions: "مداخلات",
        position: "موقعیت",
        frequencyResolved: "فرکانس/حل‌شده",
        dateInitiated: "تاریخ شروع",
        revisionDate: "تاریخ بازبینی",
        targetDate: "تاریخ هدف",
        notAvailable: "در دسترس نیست",
        noFocusItems: "مورد متمرکزی موجود نیست",
      },
      ru: {
        allergies: "Аллергии",
        dob: "Дата рождения",
        physician: "Врач",
        facility: "Учреждение",
        resident: "Житель",
        admissionDate: "Дата поступления",
        location: "Местоположение",
        page: "Страница",
        of: "из",
        focus: "Фокус",
        goal: "Цель",
        interventions: "Вмешательства",
        position: "Позиция",
        frequencyResolved: "Частота/Решено",
        dateInitiated: "Дата начала",
        revisionDate: "Дата пересмотра",
        targetDate: "Целевая дата",
        notAvailable: "Недоступно",
        noFocusItems: "Нет доступных элементов",
      },
      vi: {
        allergies: "Dị ứng",
        dob: "Ngày sinh",
        physician: "Bác sĩ",
        facility: "Cơ sở",
        resident: "Cư dân",
        admissionDate: "Ngày nhập viện",
        location: "Vị trí",
        page: "Trang",
        of: "của",
        focus: "Trọng tâm",
        goal: "Mục tiêu",
        interventions: "Can thiệp",
        position: "Vị trí",
        frequencyResolved: "Tần suất/Đã giải quyết",
        dateInitiated: "Ngày bắt đầu",
        revisionDate: "Ngày xem xét lại",
        targetDate: "Ngày mục tiêu",
        notAvailable: "Không có sẵn",
        noFocusItems: "Không có mục nào có sẵn",
      }                                                      
  };
  