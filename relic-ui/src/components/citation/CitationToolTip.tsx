import React from 'react';
import Tooltip, { TooltipProps, tooltipClasses } from '@mui/material/Tooltip';
import { Box, Divider, Stack, styled, Typography } from '@mui/material';
import FindInPageIcon from '@mui/icons-material/FindInPage';
import CitationDetailsModal from './CitationDetailsModal';
import { Theme } from '@mui/material/styles';
import { Citation } from '../../types';
import { CitationStyle } from '../../types/communicationPanel';
import CitationViewer from './CitationViewer';
import { truncateText } from '../../utils/citationContentParser';

interface CitationToolTipProps {
  citations: Citation[];
  citationIds: string[];
  children: React.ReactElement;
  noReference?: boolean;
  styles: CitationStyle;
  chunk?: boolean; // Optional prop, defaulting to false
}

interface TooltipStyles {
  backgroundColor?: string;
  textColor?: string;
  borderColor?: string;
  hoverBackgroundColor?: string;
  titleColor?: string;
  linkColor?: string;
}

interface CitationToolTipStyles {
  tooltip?: TooltipStyles;
  link?: TooltipStyles;
}

const HtmlTooltip = styled(({ className, ...props }: TooltipProps & { styles: CitationToolTipStyles }) => (
  <Tooltip {...props} classes={{ popper: className }} />
))(({ theme, styles }: { theme: any; styles: CitationToolTipStyles }) => ({
  [`& .${tooltipClasses.tooltip}`]: {
    backgroundColor: styles?.tooltip?.backgroundColor ?? theme.palette.grey[100],
    color: styles?.tooltip?.textColor ?? theme.palette.common.white,
    maxWidth: 700,
    fontSize: theme.typography.pxToRem(10),
    border: `1px solid ${styles?.tooltip?.borderColor}`,
    boxShadow: theme.shadows[5],
    padding: '5px',
  },
}));

const CitationToolTip: React.FC<CitationToolTipProps> = ({
  citations,
  children,
  citationIds,
  noReference,
  styles,
  chunk = false, // Default chunk to false
}) => {
  const [open, setOpen] = React.useState(false);
  const [openViewer, setOpenViewer] = React.useState(false);

  const singleCitation = citationIds.length === 1 ? citations.find((c) => c.id === citationIds[0]) : null;
  const citationIndex = citations.findIndex((c) => c.id === citationIds[0]) + 1;

  const handleOpen = () => {
    if (singleCitation) {
      setOpenViewer(true); // Open CitationViewer if there’s only one citation
    } else {
      setOpen(true); // Otherwise, open the tooltip modal
    }
  };

  return (
    <>
      <CitationDetailsModal
        citations={citations}
        showCitationModel={open}
        setShowCitationModel={setOpen}
        citationIds={citationIds}
        children={children}
        chunk={chunk}
      />
      {singleCitation && (
        <CitationViewer
          open={openViewer}
          onClose={() => setOpenViewer(false)}
          citationContent={singleCitation.content} // Pass the content from the citation
          citationUrl={singleCitation.url} // Pass the content or URL from the citation
        />
      )}
      <HtmlTooltip
        title={
          citationIds.length > 1 ? (
            <Box>
              {citationIds.map((id: string, index: number) => {
                const citation = citations.find((c: Citation) => c.id === id);
                return citation ? (
                  <React.Fragment key={`tooltip-${id}-${index}`}>
                    <Stack
                      direction="row"
                      spacing={2}
                      sx={{
                        padding: '5px',
                        '&:hover': {
                          backgroundColor: styles?.tooltip?.hoverBackgroundColor,
                          color: (theme: Theme) => theme.palette.common.black,
                          borderRadius: '5px',
                          cursor: 'pointer',
                        },
                      }}
                      onClick={() => setOpen(true)}
                    >
                      <Box width="20px">
                        <FindInPageIcon fontSize="large" color="primary" />
                      </Box>
                      <Box>
                        <Typography
                          sx={{
                            fontWeight: 'bold',
                            color: (theme: Theme) => styles?.tooltip?.titleColor ?? theme.palette.common.black,
                            fontSize: '14px',
                          }}
                        >
                          {citation.title}
                        </Typography>
                        <Typography
                          sx={{
                            color: (theme: Theme) => styles?.tooltip?.linkColor ?? theme.palette.info.main,
                            fontSize: '10px',
                          }}
                        >
                          {truncateText(citation.content)}
                        </Typography>
                      </Box>
                    </Stack>
                    {citationIds.length > index + 1 && <Divider sx={{ marginTop: '5px', marginBottom: '5px' }} />}
                  </React.Fragment>
                ) : null;
              })}
            </Box>
          ) : null
        }
        styles={styles}
      >
        {noReference ? (
          children
        ) : (
          <Box
            component="span"
            onClick={handleOpen}
            sx={{
              textDecoration: chunk ? 'none' : 'underline',
              textDecorationStyle: 'dotted',
              textDecorationColor: '#B0B0B0',
              color: styles?.link?.textColor,
              fontWeight: 400,
              cursor: 'pointer',
              textUnderlineOffset: '5px',
              '&:hover': {
                backgroundColor: styles?.link?.hoverBackgroundColor,
                color: '#000000',
              },
            }}
          >
            {children}
            {!chunk && <sup>[{citationIndex}]</sup>}
          </Box>
        )}
      </HtmlTooltip>
    </>
  );
};

export default CitationToolTip;
