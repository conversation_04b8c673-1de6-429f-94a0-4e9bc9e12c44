import React from 'react';

import { AuthProps } from 'src/types';
import { B2CForgotPasswordPage } from './forgotPassword';
import { B2CLoginPage } from './login';
import { B2CRegisterPage } from './register';
import { B2CUpdatePasswordPage } from './updatePassword';

/**
 * **refine** has a default auth page form served on the `/login` route when the `authProvider` configuration is provided.
 * @see {@link https://refine.dev/docs/api-reference/mui/components/mui-auth-page/} for more details.
 */
export const AuthPage: React.FC<AuthProps> = props => {
  const { type } = props;
  const renderView = () => {
    switch (type) {
      case 'register':
        return <B2CRegisterPage {...props} />;
      case 'forgotPassword':
        return <B2CForgotPasswordPage {...props} />;
      case 'updatePassword':
        return <B2CUpdatePasswordPage {...props} />;
      default:
        return <B2CLoginPage {...props} />;
    }
  };

  return <>{renderView()}</>;
};
