import { FastifyInstance, FastifyPluginAsync, FastifyPluginOptions, FastifyReply, FastifyRequest } from 'fastify';
import { AccessPolicy } from 'relic-ui';

const accessPolicyRoutes: FastifyPluginAsync = async (fastify: FastifyInstance, options: FastifyPluginOptions) => {
  fastify.get(
    '/',
    {
      schema: {
        ...options.schema,
        description: "Returns access policy for current user based on request.header['x-access-token'].",
        tags: ['Access & Identity Service'],
        summary: 'Retrieve access policy configuration',
      },
    },
    async function (req: FastifyRequest, rep: FastifyReply): Promise<AccessPolicy> {
      return await fastify.getMyAccessPolicy();
    },
  );
};

export default accessPolicyRoutes;
