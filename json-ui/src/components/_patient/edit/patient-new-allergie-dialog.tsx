import { useTranslate } from '@refinedev/core';
import { useForm } from '@refinedev/react-hook-form';

import Stack from '@mui/material/Stack';
import Dialog from '@mui/material/Dialog';
import Button from '@mui/material/Button';
import Divider from '@mui/material/Divider';
import TextField from '@mui/material/TextField';
import LoadingButton from '@mui/lab/LoadingButton';
import DialogTitle from '@mui/material/DialogTitle';
import DialogActions from '@mui/material/DialogActions';

// ----------------------------------------------------------------------

type Props = {
  open: boolean;
  onClose: VoidFunction;
};

export default function PatientNewAllergieDialog({ open, onClose }: Props) {
  const translate = useTranslate();

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
  } = useForm({
    defaultValues: {
      code: '',
      note: '',
      status: '',
    },
  });

  const onSubmit = handleSubmit(async (data) => {
    try {
      await new Promise((resolve) => setTimeout(resolve, 1000));
      reset();
      onClose();
      console.info('DATA', data);
    } catch (error) {
      console.error(error);
    }
  });

  return (
    <>
      <Dialog
        fullWidth
        maxWidth="xs"
        open={open}
        onClose={onClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <Stack component="form" onSubmit={onSubmit} autoComplete="off">
          <DialogTitle id="alert-dialog-title">
            {translate('allergyintolerances.titles.create')}
          </DialogTitle>

          <Divider />

          <Stack spacing={3} sx={{ p: 3 }}>
            <TextField
              {...register('code', {
                required: 'This field is required',
              })}
              error={!!(errors as any)?.code}
              helperText={(errors as any)?.code?.message}
              fullWidth
              type="text"
              label={translate('conditions.fields.code')}
              name="code"
            />

            <TextField
              {...register('note', {
                required: 'This field is required',
              })}
              error={!!(errors as any)?.note}
              helperText={(errors as any)?.note?.message}
              fullWidth
              type="text"
              label={translate('conditions.fields.note')}
              name="note"
            />

            <TextField
              {...register('status', {
                required: 'This field is required',
              })}
              error={!!(errors as any)?.status}
              helperText={(errors as any)?.status?.message}
              fullWidth
              multiline
              minRows={4}
              maxRows={8}
              type="text"
              label={translate('conditions.fields.clinicalStatus')}
              name="status"
            />
          </Stack>

          <Divider />

          <DialogActions>
            <Button
              onClick={() => {
                onClose();
                reset();
              }}
            >
              {translate('buttons.cancel')}
            </Button>

            <LoadingButton type="submit" variant="contained" loading={isSubmitting}>
              {translate('buttons.create')}
            </LoadingButton>
          </DialogActions>
        </Stack>
      </Dialog>
    </>
  );
}
