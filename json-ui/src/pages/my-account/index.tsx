import {
  <PERSON><PERSON><PERSON><PERSON>er,
  HttpError,
  useGetIdentity,
  useOne,
  useTranslate,
  useUpdate,
} from '@refinedev/core';
import { useDataGrid } from '@refinedev/mui';
import { useForm } from '@refinedev/react-hook-form';
import React, { useState } from 'react';
import { FieldValues } from 'react-hook-form';
import { toast } from 'react-toastify';
import {
  IUser,
  IUserIdentity,
  RelicChatParticipant,
  RelicDocument,
  RelicOrganization,
} from 'relic-ui';

import Box from '@mui/material/Box';
import CircularProgress from '@mui/material/CircularProgress';
import Stack from '@mui/material/Stack';
import TextField from '@mui/material/TextField';
import { GridColDef } from '@mui/x-data-grid-pro';

import { usePopover } from 'src/hooks/use-popover';

import { CreateDocument, ListDocuments } from 'src/components/_document';
import {
  AccountAddPopover,
  AccountDetailsPopover,
  AccountDrawer,
  AccountTabs,
  AccountToolbar,
} from 'src/components/_my_account/edit';
import ListThreads from 'src/components/_threads/list/threads-list';
import { LoginProviderDropdown } from 'src/components/dropdown/loginProviderDropdown';
import { OrganizationDropdown } from 'src/components/dropdown/organizationDropdown';
import PhoneInput from 'src/components/phone-input';

// ----------------------------------------------------------------------

type IListFilterVariables = {
  q?: string;
  PatientId?: string;
};

// eslint-disable-next-line react-refresh/only-export-components
export const DRAWER_WIDTH = {
  lg: 380,
  md: 320,
  sm: 280,
  xs: '100%',
};

const TAB_OPTIONS = [
  { value: 'document', label: 'my-account.tabs.documents' },
  { value: 'conversation', label: 'my-account.tabs.conversations' },
];

const MyAccountComponent = () => {
  const translate = useTranslate();
  const { data: currentUser } = useGetIdentity<IUser>();
  const currentIdentity: IUserIdentity =
    currentUser?.userIdentity as IUserIdentity;

  const [currentTab, setCurrentTab] = useState('document');
  const [showDrawer, setShowDrawer] = useState(true);
  const [showEdit, setShowEdit] = useState(false);
  const [openDocument, setOpenDocument] = useState(false);
  const [openThread, setOpenThread] = useState(false);

  const popoverDetails = usePopover();
  const popoverAdd = usePopover();

  const organizationId =
    currentUser?.userIdentity?.portalIdentity.organizationId;

  const { data: organization } = useOne<RelicOrganization>({
    resource: 'organizations',
    id: organizationId || '',
    queryOptions: {
      enabled: !!organizationId,
    },
  });

  const userOrganization = organization?.data as RelicOrganization;

  const {
    register,
    control,
    formState: { errors },
    handleSubmit,
  } = useForm({
    defaultValues: {
      name: currentUser?.userIdentity?.portalIdentity.name || '',
      email: currentUser?.userIdentity?.portalIdentity.email || '',
      phone: currentUser?.userIdentity?.portalIdentity.mobilePhone || '',
      organizationId:
        currentUser?.userIdentity?.portalIdentity.organizationId || null,
      provider: currentUser?.userIdentity?.provider || '',
    },
  });

  const {
    dataGridProps: listDocumentProps,
    setFilters: setListDocumentFilters,
    tableQuery: listDocumentsQuery,
  } = useDataGrid<RelicDocument, HttpError, IListFilterVariables>({
    resource: 'documents',
    filters: {
      mode: 'server', // Assuming server-side filtering
    },
    queryOptions: {
      // enabled: currentTab === 'documents',
      cacheTime: 0,
      staleTime: 0,
    },
    syncWithLocation: false,
  });

  const refetchDocuments = React.useCallback(() => {
    listDocumentsQuery.refetch();
  }, [listDocumentsQuery]);

  const documentColumns = React.useMemo<GridColDef[]>(() => [], []);

  const threadsInitialFilters: CrudFilter[] = React.useMemo(() => {
    const me: RelicChatParticipant = {
      id: {
        communicationUserId:
          currentIdentity?.communicationIdentities[0]?.userId,
      },
      resourceId: currentIdentity?.id,
      resourceType:
        currentIdentity?.resourceType === 'Patient'
          ? 'Patient'
          : 'Practitioner',
    };
    const myThreadsFilter: CrudFilter = {
      field: 'participant',
      value: encodeURIComponent(JSON.stringify(me)),
      operator: 'eq',
    };
    return [myThreadsFilter];
  }, [currentIdentity]);

  const {
    dataGridProps: listThreadsProps,
    filters: listThreadsFilters,
    setFilters: setListThreadsFilters,
  } = useDataGrid({
    resource: 'communication/chat/threads',
    sorters: {
      initial: [
        {
          field: 'updateDate',
          order: 'desc',
        },
      ],
    },
    filters: {
      mode: 'server',
      permanent: threadsInitialFilters,
    },
  });

  const threadsColumns = React.useMemo<GridColDef[]>(() => [], []);

  const { mutate, isLoading } = useUpdate();

  const onSubmit = async (data: FieldValues) => {
    if (
      !currentUser ||
      !currentUser.userIdentity ||
      !currentUser.userIdentity.id
    ) {
      toast.error('User not found');
      return;
    }
    mutate(
      {
        resource: 'practitioners',
        id: currentUser.userIdentity.id,
        values: {
          ...data,
          enabled: true,
          userPrincipalName: `${currentUser.userIdentity.id}@${
            currentUser.userIdentity.provider
          }`,
        },
      },
      {
        onSuccess: () => {
          setShowEdit(false);
          toast.success(
            translate('notifications.editSuccess', {
              resource: translate('my-account.title'),
            }),
          );
        },
        onError: error => {
          toast.error(
            translate('notifications.editError', {
              resource: translate('my-account.title'),
              statusCode: error?.statusCode || 500,
            }),
          );
          console.error(error);
        },
      },
    );
  };

  const handleChangeTab = (event: React.SyntheticEvent, newValue: string) => {
    setCurrentTab(newValue);
  };

  if (!currentUser) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
        <CircularProgress />
      </Box>
    );
  }

  const renderEditForm = (
    <Stack
      spacing={3}
      component="form"
      autoComplete="off"
      sx={{
        flexDirection: 'column',
        display: showEdit ? 'flex' : 'none',
      }}
    >
      <TextField
        {...register('name', {
          required: translate('my-account.errors.required'),
        })}
        error={!!errors?.name}
        helperText={(errors as any)?.name?.message}
        fullWidth
        InputLabelProps={{ shrink: true }}
        type="text"
        label={translate('my-account.fields.name')}
        name="name"
      />
      <TextField
        {...register('email', {
          required: translate('my-account.errors.required'),
          pattern: {
            value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
            message: translate('pages.login.errors.validEmail'),
          },
        })}
        error={!!errors?.email}
        helperText={(errors as any)?.email?.message}
        fullWidth
        InputLabelProps={{ shrink: true }}
        type="email"
        label={translate('my-account.fields.email')}
        name="email"
      />
      <PhoneInput
        control={control}
        name="phone"
        label={translate('my-account.fields.phone')}
        required
      />
      <OrganizationDropdown
        control={control}
        name="organizationId"
        label={translate('my-account.fields.organization')}
        value={currentUser?.userIdentity?.portalIdentity.organizationId || null}
        error={!!errors?.organizationId}
        helperText={(errors as any)?.organizationId?.message}
        disabled
      />
      <LoginProviderDropdown
        control={control}
        name="provider"
        label={translate('my-account.fields.provider')}
        error={!!(errors as any)?.provider}
        helperText={(errors as any)?.provider?.message}
        value={currentUser?.userIdentity?.provider || ''}
        disabled={true}
      />
    </Stack>
  );

  return (
    <>
      <AccountToolbar
        onBack={() => window.history.back()}
        userName={currentUser?.userIdentity?.portalIdentity.name}
        showDetails={showDrawer}
        onToggleDetails={() => setShowDrawer(!showDrawer)}
        onOpenAdd={popoverAdd.onOpen}
      />

      <Stack
        sx={{
          ...(showDrawer && {
            width: {
              sm: `calc(100% - ${DRAWER_WIDTH.sm}px)`,
              md: `calc(100% - ${DRAWER_WIDTH.md}px)`,
              lg: `calc(100% - ${DRAWER_WIDTH.lg}px)`,
            },
          }),
        }}
      >
        <AccountTabs
          currentTab={currentTab}
          onChangeTab={handleChangeTab}
          tabOptions={TAB_OPTIONS}
        />

        {currentTab === 'conversation' && (
          <ListThreads
            {...listThreadsProps}
            setFilters={setListThreadsFilters}
            filters={listThreadsFilters}
            title={translate('threads.titles.mine')}
            columns={threadsColumns}
            currentIdentity={currentIdentity}
          />
        )}
        {currentTab === 'document' && (
          <ListDocuments
            {...listDocumentProps}
            loading={listDocumentsQuery.isFetching}
            refetchDocuments={refetchDocuments}
            columns={documentColumns}
            setFilters={setListDocumentFilters}
            currentIdentity={currentIdentity}
            pageSizeOptions={[10]}
            pagination
          />
        )}
      </Stack>

      <AccountDrawer
        showDrawer={showDrawer}
        setShowEdit={setShowEdit}
        onFinish={onSubmit}
        saveButtonProps={{
          disabled: isLoading,
          onClick: handleSubmit(onSubmit),
        }}
        handleSubmit={handleSubmit}
        showEdit={showEdit}
        currentUser={currentUser}
        isLoading={isLoading}
        popoverDetails={popoverDetails}
        renderEditForm={renderEditForm}
      />

      <AccountDetailsPopover
        popover={popoverDetails}
        onOpenEdit={() => setShowEdit(true)}
      />

      <AccountAddPopover
        popover={popoverAdd}
        openDocument={openDocument}
        onOpenDocument={() => setOpenDocument(true)}
        onCloseDocument={() => setOpenDocument(false)}
        openThread={openThread}
        onOpenThread={() => setOpenThread(true)}
        onCloseThread={() => setOpenThread(false)}
        currentUser={currentUser}
        currentOrganization={userOrganization}
        currentPatient={undefined}
      />

      {openDocument && (
        <CreateDocument
          open={openDocument}
          onClose={() => setOpenDocument(false)}
        />
      )}
    </>
  );
};

export default MyAccountComponent;
