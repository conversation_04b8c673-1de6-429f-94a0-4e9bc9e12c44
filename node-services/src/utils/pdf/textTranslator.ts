import * as fs from 'fs';
import axios, { AxiosRequestConfig } from 'axios';
import * as path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { TranslateDocumentOptions } from './types';
import config from 'config';

interface TranslatedLines {
    detected_source_language: string;
    text: string;
}

export type TextBlockLine = {
    text: string;
    seq: string;
    paraLines: string;
} 
interface TextBlockJson {
    page: {
        lines: TextBlockLine[];
    };
}


const MAX_TRANSLATION_CHARACTER_COUNT_PER_BATCH = 50000;
const MAX_TRANSLATION_ARRAY_COUNT_PER_BATCH = 1000;
const ignoredFields : any = config.get("REPORTS.CAREPLAN.IGNORE_FIELDS");
//Language codes supported by Deepl
const deeplSupportedSourceLangCodes: string[] = [
    'bg', 'cs', 'da', 'de', 'en', 'el', 'es', 'et', 'fi', 'fr',
    'hu', 'id', 'it', 'ja', 'ko', 'lt', 'lv', 'nb', 'nl', 'pl',
    'pt', 'ro', 'ru', 'sk', 'sl', 'sv', 'tr', 'uk', 'zh'
];

const deeplSupportedTargetLangCodes: string[] = [
    'bg', 'cs', 'da', 'de', 'el', 'en-gb', 'en-us', 'es', 'et', 'fi', 'fr',
    'hu', 'id', 'it', 'ja', 'ko', 'lt', 'lv', 'nb', 'nl', 'pl',
    'pt-br', 'pt-pt', 'ro', 'ru', 'sk', 'sl', 'sv', 'tr', 'uk', 'zh'
];

const collator = new Intl.Collator(undefined, {
    numeric: true,
    sensitivity: 'base',
});

function azureConfig(
    endpoint: string,
    key: string,
    location: string,
    url: string,
    method: string,
    params: URLSearchParams,
    inputText: string
): AxiosRequestConfig {
    return {
        baseURL: endpoint,
        url: url,
        method: method,
        headers: {
            'Ocp-Apim-Subscription-Key': key,
            'Ocp-Apim-Subscription-Region': location,
            'Content-type': 'application/json',
            'X-ClientTraceId': uuidv4().toString(),
        },
        params: params,
        data: inputText,
        responseType: 'json',
    };
}

async function translateWithAzure(textArray: string[], sourceLang: string, targetLang: string): Promise<any> {
    let result: string;
    const key: string = config.get("AZURE_TRANSLATION.API_KEY");
    const endpoint: string = config.get("AZURE_TRANSLATION.ENDPOINT");
    const url = '/translate';
    const method = 'post';

    // Add your location, also known as region. The default is global.
    // This is required if using an Azure AI multi-service resource.
    const location: string = "eastus";
    const params: URLSearchParams = new URLSearchParams();
    params.append("api-version", "3.0");
    params.append("from", sourceLang);
    params.append("to", targetLang);
    const body = textArray?.map(d => { return { text: d } });

    const azureConfigObj = azureConfig(endpoint, key, location, url, method, params, body as any);

    try {
        const response = await axios(azureConfigObj);
        result = (response.data || [])?.map(d => { return { text: d.translations?.[0].text ?? '' } });
    } catch (error) {
        console.error(error);
        throw error;
    }

    return result;
}

async function translateWithDeepl(textArray: string[], sourceLang: string, targetLang: string): Promise<TranslatedLines[]> {
    const deeplUrl: string = new URL('/v2/translate', config.get("DEEPL.API_URL")).toString();
    const apiEndpoint: string = deeplUrl;
    const RELIC_DEEPL_API_KEY: string = config.get("DEEPL.API_KEY");

    try {
        const body = {
            text: textArray,
            source_lang: sourceLang,
            target_lang: targetLang,
        };

        const response = await axios.post(apiEndpoint, body, {
            headers: {
                Authorization: `DeepL-Auth-Key ${RELIC_DEEPL_API_KEY}`,
                "Content-Type": "application/json",
            },
        });
        return response.data.translations;
    } catch (error) {
        console.error(error);
        throw error;
    }
};

function isLanguageSupportedByDeepL(sourceLanguage: string, targetLanguage: string): boolean {
    return deeplSupportedSourceLangCodes.includes(sourceLanguage) && deeplSupportedTargetLangCodes.includes(targetLanguage);
}

function isDateString(value: string): boolean {
    const isoDateRegex = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}.\d{3}Z$/;
    return isoDateRegex.test(value);
}

function isNumber(value: string): boolean {
    return !isNaN(Number(value));
}

function sanitizeText(text: string, paraLines: number) {
    let endsWithUnderscore = text?.endsWith('_');
    if (endsWithUnderscore && paraLines == 1) {
        return text.replace(/[.:_-]/g, '').trim();
    } else {
        return text.trim();
    }
}

export const translateTextBlocks = async (options: TranslateDocumentOptions): Promise<string[]> => {
    const inputDir: string = options.sourceJsonTextblocksFileDir;
    const outputDir: string = options.translatedJsonTextblocksFileDir;
    const sourceLang: string = options.sourceLang;
    const targetLang: string = options.targetLang.toLowerCase();

    try {
        try {
            await fs.promises.access(outputDir, fs.constants.F_OK);
            console.debug(`'${outputDir}' directory exists.`);
        } catch (err) {
            console.debug(`'${outputDir}' directory does not exist. Creating the directory...`);
            try {
                await fs.promises.mkdir(outputDir, { recursive: true });
                console.debug(`'${outputDir}' directory created successfully.`);
            } catch (err) {
                console.error('Error creating directory:', err);
                throw err;
            }
        }

        // Read the files in the input directory
        const files = fs.readdirSync(inputDir).sort(collator.compare);
        const textBlockFiles = files;
        let translatedTextBlocksFiles: string[] = [];
        let translateService = null;

        if (isLanguageSupportedByDeepL(sourceLang, targetLang)) {
            translateService = translateWithDeepl;
        } else {
            translateService = translateWithAzure;
        }

        for (let j = 0; j < textBlockFiles.length; j++) {
            const file = textBlockFiles[j];
            const inputPath = path.join(inputDir, file);
            const outputFile = `${path.parse(file).name}-translated.json`;
            const jsonFilePath = path.join(outputDir, outputFile);

            // Read the text block JSON file
            const textBlocks: TextBlockJson = JSON.parse(fs.readFileSync(inputPath).toString());
            let currentBatch: string[] = [];
            let startIndex = 0;
            let currentCharacterCount = 0;

            for (let i = 0; i < textBlocks.page.lines.length; i++) {
                const sanitizedText = sanitizeText(textBlocks.page.lines[i].text, Number(textBlocks.page.lines[i].paraLines));
                const characterCount = sanitizedText.length;

                if (currentCharacterCount + characterCount > MAX_TRANSLATION_CHARACTER_COUNT_PER_BATCH || currentBatch.length >= MAX_TRANSLATION_ARRAY_COUNT_PER_BATCH) {
                    // If exceeded, translate the current batch and reset variables for a new batch
                    const translatedText: any[] = await translateService(currentBatch, sourceLang, targetLang);
                    for (let k = 0; k < translatedText.length; k++) {
                        const translation = translatedText[k].text;
                        const searchString = currentBatch[k];
                        const originalText = textBlocks.page.lines[startIndex + k].text;
                        let newText = (options.translationType == 'mono') ? originalText.replace(searchString, translation) : translation;
                        if (newText == originalText) {
                            newText = translation;
                        }
                        textBlocks.page.lines[startIndex + k].text = newText;
                    }

                    startIndex += currentBatch.length;
                    currentBatch = [];
                    currentCharacterCount = 0;
                }

                currentBatch.push(sanitizedText);
                currentCharacterCount += characterCount;
            }

            if (currentBatch.length > 0) {
                const translatedText: any[] = await translateService(currentBatch, sourceLang, targetLang);
                for (let i = 0; i < translatedText.length; i++) {
                    const translation = translatedText[i].text;
                    const searchString = currentBatch[i];
                    const originalText = textBlocks.page.lines[startIndex + i].text;
                    let newText = (options.translationType == 'mono') ? originalText.replace(searchString, translation) : translation;
                    if (newText == originalText) {
                        newText = translation;
                    }
                    textBlocks.page.lines[startIndex + i].text = newText;
                }
                startIndex += currentBatch.length;
            }

            // Save the translated text blocks to a new JSON file
            fs.writeFileSync(jsonFilePath, JSON.stringify(textBlocks, null, 2));
            translatedTextBlocksFiles.push(jsonFilePath);
            console.debug('Translated text blocks saved to file: ', jsonFilePath);
        }

        return translatedTextBlocksFiles;
    } catch (error) {
        console.error('Error translating text blocks: ', error);
        throw error;
    }
};

function replaceTagsWithTranslatedText(obj: any, translationMapping: { tag: string, translated_text: string }[]): any {
    for (const key in obj) {
        if (typeof obj[key] === 'object' && obj[key] !== null) {
            replaceTagsWithTranslatedText(obj[key], translationMapping);
        } else if (typeof obj[key] === 'string' && obj[key].startsWith('$')) {
            const translation = translationMapping.find(mapping => mapping.tag === obj[key]);
            if (translation) {
                obj[key] = translation.translated_text;
            }
        }
    }
    return obj;
}

function extractTextAndAddTags(obj: any, textArray: string[], tagMapping: { [tag: string]: string }, tagCounter: { count: number }): any {
    if (Array.isArray(obj)) {
        for (let i = 0; i < obj.length; i++) {
            obj[i] = extractTextAndAddTags(obj[i], textArray, tagMapping, tagCounter);
        }
    } else if (typeof obj === 'object' && obj !== null) {
        for (const key in obj) {
            if (typeof obj[key] === 'object' && obj[key] !== null) {
                obj[key] = extractTextAndAddTags(obj[key], textArray, tagMapping, tagCounter);
            } else if (typeof obj[key] === 'string' && !isDateString(obj[key]) && !isNumber(obj[key]) && !ignoredFields.includes(key)) {
                const value = obj[key];
                const tag = `$${tagCounter.count}`;
                textArray.push(value);
                tagMapping[tag] = value;
                obj[key] = tag;  // Replace with tag
                tagCounter.count++;
            }
        }
    }
    return obj;
}

async function extractTextForTranslationWithTags(carePlanJson: any): Promise<{ taggedJson: any, textArray: string[], tagMapping: { [tag: string]: string } }> {
    const textArray: string[] = [];
    const tagMapping: { [tag: string]: string } = {};
    const tagCounter = { count: 1 };
    try {
        const taggedJson = extractTextAndAddTags(carePlanJson, textArray, tagMapping, tagCounter);
        return { taggedJson, textArray, tagMapping };
    } catch (error) {
        console.error('Error processing care plan JSON:', error);
        throw error;
    }
}

async function translateCarePlanDocument(carePlanJson: Map<string, any>, sourceLanguage: string, targetLanguage: string): Promise<Map<string, any>> {
    try {
        let translateService = null;
        let currentBatch: any[] = [];
        let currentCharacterCount = 0;
        let translatedTextArray: any[] = [];
        if (isLanguageSupportedByDeepL(sourceLanguage, targetLanguage)) {
            translateService = translateWithDeepl;
        } else {
            translateService = translateWithAzure;
        }

        const { taggedJson, textArray } = await extractTextForTranslationWithTags(carePlanJson);

        for (let i = 0; i < textArray.length; i++) {
            const sanitizedText = textArray[i].trim();
            const characterCount = sanitizedText.length;

            if (currentCharacterCount + characterCount > MAX_TRANSLATION_CHARACTER_COUNT_PER_BATCH || currentBatch.length >= MAX_TRANSLATION_ARRAY_COUNT_PER_BATCH) {
                // Translate the current batch and add the results to the translatedTextArray array
                const translatedBatch = await translateService(currentBatch, sourceLanguage, targetLanguage);
                translatedTextArray.push(...translatedBatch);

                // Reset for a new batch
                currentBatch = [];
                currentCharacterCount = 0;
            }

            // Add the current text to the batch
            currentBatch.push(sanitizedText);
            currentCharacterCount += characterCount;
        }

        // Translate any remaining text in the last batch
        if (currentBatch.length > 0) {
            const translatedBatch = await translateService(currentBatch, sourceLanguage, targetLanguage);
            translatedTextArray.push(...translatedBatch);
        }

        const translationMapping = translatedTextArray.map((translation, index) => ({
            tag: `$${index + 1}`,
            translated_text: translation?.text || ''
        }));

        const translatedCarePlanJson = replaceTagsWithTranslatedText(taggedJson, translationMapping);

        return translatedCarePlanJson;
    } catch (error) {

        console.error('Error translating text blocks: ', error);
        throw error;
    }
}

export async function generateTranslatedCarePlanJson(carePlanDocument: Map<string, any>, sourceLanguage: string, targetLanguage: string): Promise<Map<string, any>> {
    if (sourceLanguage == targetLanguage) {
        return carePlanDocument;
    } else {
        return await translateCarePlanDocument(carePlanDocument, sourceLanguage, targetLanguage);
    }
}