import { CreateResponse, HttpError, CustomResponse } from '@refinedev/core';
import dataProvider from '@refinedev/simple-rest';
import axios, { AxiosRequestConfig, InternalAxiosRequestConfig } from 'axios';
import createAuthRefreshInterceptor from 'axios-auth-refresh';

import { getNodeServicesApi } from '../utils/app-config';
import {
  convertToNodeServicesRequest,
  getActiveLogin,
  silentAuthRefresh,
} from './utils';

const nodeServicesUrl = `${getNodeServicesApi()}/api`;
type MethodTypesWithBody = 'post' | 'put' | 'patch';

const nodeServiceAxiosInstance = axios.create({
  baseURL: nodeServicesUrl, // Changed from 'url' to 'baseURL' for correct axios config
});

const refreshRequest = async (failedRequest?: any) => {
  try {
    const activeLogin = getActiveLogin();
    await silentAuthRefresh(activeLogin);
    const refreshedLogin = getActiveLogin();
    if (refreshedLogin && refreshedLogin.accessToken && failedRequest) {
      failedRequest.response.config.headers['x-access-token'] =
        refreshedLogin.accessToken;
    }
  } catch (error) {
    console.log('Error refreshing token:', error);
    Promise.reject(`Error refreshing token: ${error.message}`);
  }
};

nodeServiceAxiosInstance.interceptors.request.use(
  async (request: InternalAxiosRequestConfig) => {
    const axiosRequest = request as AxiosRequestConfig;
    const nodeServicesRequest = (await convertToNodeServicesRequest(
      axiosRequest,
    )) as InternalAxiosRequestConfig;
    return nodeServicesRequest;
  },
);

// Refer https://github.com/Flyrell/axios-auth-refresh?tab=readme-ov-file#available-options for more options
createAuthRefreshInterceptor(nodeServiceAxiosInstance, refreshRequest, {
  statusCodes: [401],
  onRetry: (request: AxiosRequestConfig) => {
    console.log('Retrying request', request);
    return request;
  },
  pauseInstanceWhileRefreshing: true,
});

nodeServiceAxiosInstance.interceptors.response.use(
  response => response,
  error => {
    const customError: HttpError = {
      ...error,
      message:
        error?.response?.data?.message ||
        error?.response?.message ||
        error?.message ||
        'An unknown error occurred',
      statusCode: error?.status || error?.response?.status,
    };

    return Promise.reject(customError);
  },
);

const nodeServicesRestProvider = {
  ...dataProvider(nodeServicesUrl, nodeServiceAxiosInstance),
  create: async <TData>(createParams: any): Promise<CreateResponse<TData>> => {
    if (createParams.resource === 'documents') {
      const variables: any = createParams.variables;
      if (variables && variables.action && variables.action === 'translate') {
        const url = `${nodeServicesUrl}/documents/${variables.sourceDocumentId}/translate`;
        const { data } = await nodeServiceAxiosInstance.post(url, {
          targetLanguage: variables.targetLanguage,
          translationType: variables.translationType,
        });
        return { data };
      }
    }
    //usual Rest request for catch all cases
    const url = `${nodeServicesUrl}/${createParams.resource}`;
    const { headers, method } = createParams.meta ?? {};
    const requestMethod = (method as MethodTypesWithBody) ?? 'post';
    const { data } = await nodeServiceAxiosInstance[requestMethod](
      url,
      createParams.variables,
      {
        headers,
      },
    );
    return {
      data,
    };
  },
  custom: async <TData>({
    url,
    method,
    payload,
    query,
    headers,
  }: any): Promise<CustomResponse<TData>> => {
    let requestUrl = `${nodeServicesUrl}/${url}`;

    // Handle query parameters if provided
    if (query) {
      requestUrl = `${requestUrl}?${new URLSearchParams(query).toString()}`;
    }

    // Handle method-specific logic
    if (method === 'get') {
      const { data } = await nodeServiceAxiosInstance.get(requestUrl, {
        headers,
      });
      return { data };
    }

    const requestMethod = (method as MethodTypesWithBody) ?? 'post';
    const { data } = await nodeServiceAxiosInstance[requestMethod](
      requestUrl,
      payload,
      {
        headers,
      },
    );

    return { data };
  },
  getApiUrl: () => {
    return nodeServicesUrl;
  },
};

export const relicDataProvider = {
  ...nodeServicesRestProvider,
};
