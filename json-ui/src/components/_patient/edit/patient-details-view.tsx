import { useTranslate } from '@refinedev/core';
import { DateField, EmailField } from '@refinedev/mui';
import { RelicPatient } from 'relic-ui';

import Box from '@mui/material/Box';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';

import { PhoneInputView } from 'src/components/phone-input';
import { calculateAge } from 'src/utils/date';

// ----------------------------------------------------------------------

type PatientEditDetailsViewProps = {
  showEdit: boolean;
  currentPatient: RelicPatient;
};

export default function PatientDetailsViewOnly({
  showEdit,
  currentPatient,
}: PatientEditDetailsViewProps) {
  const translate = useTranslate();

  /* This is calculating the age of the patient based on their birth date. */
  const age: string = calculateAge(currentPatient?.birthDate);

  return (
    <Stack
      spacing={2}
      component="form"
      sx={{
        flexDirection: 'column',
        display: showEdit ? 'none' : 'flex',
      }}
    >
      <div>
        <Typography
          component={'span'}
          variant="subtitle2"
          sx={{
            fontSize: 12,
            color: 'text.secondary',
            display: 'block',
            mb: 0.5,
          }}
        >
          {translate('patients.fields.name')}
        </Typography>

        <Typography component={'span'} variant="body2">
          {currentPatient?.name || '-'}
        </Typography>
      </div>

      <div>
        <Typography
          component={'span'}
          variant="subtitle2"
          sx={{
            fontSize: 12,
            color: 'text.secondary',
            display: 'block',
            mb: 0.5,
          }}
        >
          {translate('patients.fields.birthDate')}
        </Typography>
        <Stack
          direction={'row'}
          alignItems={'center'}
          spacing={0.5}
          sx={{ typography: 'body2' }}
        >
          <DateField value={`${currentPatient?.birthDate} ` || '-'} />
          <Box component={'span'} sx={{ color: 'text.disabled' }}>
            ({age})
          </Box>
        </Stack>
      </div>

      <div>
        <Typography
          component={'span'}
          variant="subtitle2"
          sx={{
            fontSize: 12,
            color: 'text.secondary',
            display: 'block',
            mb: 0.5,
          }}
        >
          {translate('patients.fields.gender')}
        </Typography>

        <Typography component={'span'} variant="body2">
          {currentPatient?.gender || '-'}
        </Typography>
      </div>

      <div>
        <Typography
          component={'span'}
          variant="subtitle2"
          sx={{
            fontSize: 12,
            color: 'text.secondary',
            display: 'block',
            mb: 0.5,
          }}
        >
          {translate('patients.fields.primaryLanguage')}
        </Typography>

        <Typography component={'span'} variant="body2">
          {currentPatient?.primaryLanguage?.display || '-'}
        </Typography>
      </div>

      {/* <div>
        <Typography
          component={'span'}
          variant="subtitle2"
          sx={{ fontSize: 12, color: 'text.secondary', display: 'block', mb: 0.5 }}
        >
          {translate('patients.fields.maritalStatus')}
        </Typography>

        <Typography component={'span'} variant="body2">
          {currentPatient?.maritalStatus || '-'}
        </Typography>
      </div> */}

      <div>
        <Typography
          component={'span'}
          variant="subtitle2"
          sx={{
            fontSize: 12,
            color: 'text.secondary',
            display: 'block',
            mb: 0.5,
          }}
        >
          {translate('patients.fields.email')}
        </Typography>

        <EmailField value={currentPatient?.email || '-'} />
      </div>

      <div>
        <PhoneInputView
          value={currentPatient?.mobilePhone}
          label={translate('patients.fields.mobilePhone')}
        />
      </div>
      <div>
        <PhoneInputView
          value={currentPatient?.homePhone}
          label={translate('patients.fields.homePhone')}
        />
      </div>

      {/* <div>
        <Typography
          component={'span'}
          variant="subtitle2"
          sx={{ fontSize: 12, color: 'text.secondary', display: 'block', mb: 0.5 }}
        >
          {translate('patients.fields.link')}
        </Typography>

        {currentPatient?.link?.length ? (
          <Stack direction={'row'} spacing={1} flexWrap={'wrap'} sx={{ mt: 0.5 }}>
            {currentPatient?.link.map((item) => (
              <Chip key={item.name} label={item.name} />
            ))}
          </Stack>
        ) : (
          '-'
        )}
      </div>

      <div>
        <Typography
          component={'span'}
          variant="subtitle2"
          sx={{ fontSize: 12, color: 'text.secondary', display: 'block', mb: 0.5 }}
        >
          {translate('patients.fields.organizationId')}
        </Typography>

        <Typography component={'span'} variant="body2">
          {currentPatient?.managingOrganization?.display || '-'}
        </Typography>
      </div>

      <div>
        <Typography
          component={'span'}
          variant="subtitle2"
          sx={{ fontSize: 12, color: 'text.secondary', display: 'block', mb: 0.5 }}
        >
          {translate('patients.fields.active')}
        </Typography>

        <Label color={currentPatient?.active ? 'success' : 'error'} sx={{ mt: 0.5 }}>
          {JSON.stringify(currentPatient?.active)}
        </Label>
      </div> */}
    </Stack>
  );
}
