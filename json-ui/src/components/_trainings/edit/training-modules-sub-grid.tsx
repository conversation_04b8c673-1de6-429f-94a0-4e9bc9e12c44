import React, { useMemo } from 'react';
import { useMany, useTranslate } from '@refinedev/core';

import { GridColDef, DataGridPro, GridRowsProp } from '@mui/x-data-grid-pro';

import { useRouter } from 'src/routes/hooks';

const TrainingModuleSubGrid = ({ rows }: { rows: GridRowsProp }) => {
  const translate = useTranslate();

  const { data: relatedOrgs, isLoading: organizationIsLoading } = useMany({
    resource: 'organizations',
    ids: rows?.map((item: any) => item?.organizationId) ?? [],
    queryOptions: {
      enabled: !!rows,
    },
  });

  const columns = React.useMemo<GridColDef[]>(
    () => [
      {
        field: 'name',
        headerName: translate('trainings-modules.fields.name'),
        minWidth: 400,
      },
      {
        field: 'storage',
        headerName: translate('trainings-modules.fields.storage'),
        minWidth: 500,
        renderCell: function render({ value }) {
          return <>{value?.containerName}</>;
        },
      },
      {
        field: 'organizationId',
        flex: 1,
        headerName: translate('trainings-modules.fields.organizationId'),
        minWidth: 400,
        renderCell: function render({ value }) {
          return organizationIsLoading ? (
            <>Loading...</>
          ) : (
            relatedOrgs?.data?.find((item: any) => item.id === value)?.name
          );
        },
      },
    ],
    [organizationIsLoading, relatedOrgs?.data, translate],
  );

  const displayColumns = useMemo(() => {
    const filteredColumns = columns;

    return filteredColumns;
  }, [columns]);
  const router = useRouter();

  const handleOnCellClick = React.useCallback(
    (params: any) => {
      if (params.field !== 'actions') {
        router.push(
          `/trainings/modules/edit/${params.id}?name=${params?.row?.name}`,
        );
      }
    },
    [router],
  );

  return (
    <DataGridPro
      rows={rows ?? []}
      getRowId={row => row?.id}
      disableColumnMenu
      columns={displayColumns}
      pagination
      sx={{
        '& .MuiDataGrid-cell': {
          display: 'flex',
          alignSelf: 'center',
          height: '100%',
          border: 'none',
        },
      }}
      onCellClick={handleOnCellClick}
      localeText={{ toolbarColumns: 'View' }}
    />
  );
};

export default TrainingModuleSubGrid;
