'use strict';
import { ObfuscationMap } from './relicPatient';

export type CleanMap = {
  name: string;
  name_1: string;
  name_2: string;
  name_3: string | null;
  email: string;
};

export type Field = { field: string; type?: string };

export type PluginOptions = {
  obfuscationFields?: Field[];
  // other properties of options
};

export type ObfuscationDocument = {
  id: string;
  resourceType: string;
  cleanMap: CleanMap;
};

export type PatientObfuscationResponse = {
  _id: string;
  resourceType: string;
  id: string;
  obfuscationMap: ObfuscationMap;
};
