import React from 'react';
import { IUser, IUserIdentity } from 'relic-ui';
import { useGetIdentity } from '@refinedev/core';

import { Box } from '@mui/material';

import { useRouter } from 'src/routes/hooks';

import CreateTrainingModules from 'src/components/_trainings-modules/create/trainings-modules-create';

export const TrainingsModulesCreate = () => {
  const [open, setOpen] = React.useState(true);

  const router = useRouter();

  const { data: currentUser } = useGetIdentity<IUser>();
  const currentIdentity: IUserIdentity =
    currentUser?.userIdentity as IUserIdentity;

  const organizationId = currentIdentity?.portalIdentity?.organizationId;

  const handleCLose = () => {
    setOpen(false);
    router.push('/trainings/modules');
  };
  return (
    <Box>
      <CreateTrainingModules
        open={open}
        onClose={handleCLose}
        organizationId={organizationId}
      />
    </Box>
  );
};
