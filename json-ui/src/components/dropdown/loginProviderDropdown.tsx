import React from 'react';
import { Control, Controller } from 'react-hook-form';

import TextField from '@mui/material/TextField';
import Autocomplete from '@mui/material/Autocomplete';

interface LoginProviderDropdownProps {
  control: Control<any>;
  name: string;
  label: string;
  value: string | null;
  error: boolean;
  helperText: string;
  onChange?: (value: string | null) => void;
  disabled?: boolean;
}

const loginProviderOptions = [
  {
    code: 'msgraph',
    display: 'Microsoft Azure B2C',
  },
  {
    code: 'entra',
    display: 'Microsoft Entra',
  },
  {
    code: 'medplum',
    display: 'Medplum',
  },
  {
    code: 'pcc',
    display: 'PointClickCare',
  },
];

export const LoginProviderDropdown: React.FC<LoginProviderDropdownProps> = ({
  control,
  name,
  label,
  value,
  error,
  helperText,
  onChange,
  disabled,
}) => {
  return (
    <Controller
      control={control}
      name={name}
      defaultValue={value}
      rules={{ required: 'This field is required' }}
      render={({ field }) => {
        const { onChange: fieldOnChange, value: fieldValue } = field;
        return (
          <Autocomplete
            options={loginProviderOptions}
            disabled={disabled}
            {...field}
            getOptionLabel={option => option.display || ''}
            isOptionEqualToValue={(option, value) => {
              return value && typeof value === 'object' && 'code' in value
                ? option.code === value.code
                : option.code === value;
            }}
            onChange={(_, newValue) => {
              fieldOnChange(newValue && newValue.code ? newValue.code : null);
              if (onChange) {
                onChange(newValue ? newValue.code : null);
              }
            }}
            renderInput={params => (
              <TextField
                {...params}
                margin="normal"
                disabled={disabled}
                label={label}
                variant="outlined"
                error={error}
                helperText={helperText}
                required
              />
            )}
            value={
              loginProviderOptions.find(type => type.code === fieldValue) ||
              null
            }
          />
        );
      }}
    />
  );
};
