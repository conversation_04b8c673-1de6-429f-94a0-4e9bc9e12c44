FROM node:22-slim AS base

ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
RUN corepack enable
RUN corepack prepare pnpm@10.0.0 --activate

WORKDIR /services
COPY ./relic-ui ./relic-ui
COPY ./node-services ./node-services
COPY ./agent-messenger ./agent-messenger
COPY ./json-ui ./json-ui
COPY ./.npmrc ./.npmrc
COPY ./package.json ./package.json
COPY ./pnpm-lock.yaml ./pnpm-lock.yaml
COPY ./pnpm-workspace.yaml ./pnpm-workspace.yaml
COPY ./turbo.json ./turbo.json

RUN npm install -g nodemon
RUN apt-get update && apt-get install -y fontconfig fonts-noto procps
RUN --mount=type=cache,id=pnpm,target=/pnpm/store pnpm --filter node-services... --filter relic-ui... install --frozen-lockfile
RUN pnpm --filter relic-ui... run build
RUN pnpm --filter node-services... run build

WORKDIR /services/node-services
EXPOSE 3000

ENV ADDRESS=0.0.0.0 PORT=3000

CMD [ "node", "-r", "newrelic", "dist/index.js" ]