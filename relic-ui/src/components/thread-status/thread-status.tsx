import { useEffect, useState } from 'react';
import Alert from '@mui/material/Alert';
import IconButton from '@mui/material/IconButton';
import CloseIcon from '@mui/icons-material/Close';
import { AlertProps } from '@mui/material/Alert';
import { useThreadContext } from '../RelicThreadProvider';
import { ThreadState } from '../../types';

export default function ThreadStatus() {
  const { topic, threadError, threadState } = useThreadContext();
  const [alertProps, setAlertProps] = useState<AlertProps>();
  const [alertVisible, setAlertVisible] = useState<boolean>(true);

  useEffect(() => {
    if (threadError) {
      setAlertProps({ severity: 'error', variant: 'outlined', children: threadError });
      setAlertVisible(true);
      return;
    }
    if (topic) {
      switch (threadState) {
        case ThreadState.Idle:
          setAlertVisible(false); // No alert needed
          break;
        case ThreadState.Connecting:
          setAlertProps({
            severity: 'info',
            variant: 'outlined',
            children: topic?.threadMessage,
          });
          if (topic?.threadMessage) setAlertVisible(true);
          else setAlertVisible(false);
          break;
        case ThreadState.Connected:
          setAlertProps({
            severity: 'success',
            variant: 'outlined',
            children: topic?.threadMessage,
          });
          if (topic?.threadMessage) setAlertVisible(true);
          else setAlertVisible(false);
          break;
        case ThreadState.ReadOnly:
          setAlertProps({
            severity: 'info',
            variant: 'outlined',
            children: `This conversation is read-only for you as you are not a part of this conversation.`,
          });
          setAlertVisible(true);
          break;
        default:
          setAlertVisible(false);
          break;
      }
    }
  }, [threadState, topic, threadError]);

  const handleClose = () => {
    setAlertVisible(false);
  };

  return (
    alertVisible &&
    alertProps && (
      <Alert
        {...alertProps}
        sx={{
          margin: '4px 4px 0px 4px',
          padding: '0',
          '& .MuiAlert-icon': {
            padding: '0',
            margin: '8px 0px 8px 12px',
            '& > svg': {
              width: '16px',
              height: '16px',
            },
          },
          '& .MuiAlert-message': {
            padding: '0',
            margin: '6px',
            fontSize: '12px',
          },
          '& .MuiAlert-action': {
            margin: 'auto 0 auto auto',
            padding: '0',
            '& > button': {
              padding: '3px',
            },
            '& > button > svg': {
              width: '20px',
              height: '20px',
            },
          },
        }}
        action={
          <IconButton aria-label="close" color="inherit" size="small" onClick={handleClose}>
            <CloseIcon />
          </IconButton>
        }
      />
    )
  );
}
