import fastifyStatic, { FastifyStaticOptions } from '@fastify/static'
import { FastifyInstance } from 'fastify'
import fs from 'node:fs'
import path from 'node:path'


export const autoConfig = (fastify: FastifyInstance): FastifyStaticOptions => {
  const dirPath = path.join(__dirname, '../../public')
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath)
  }

  return {
    root: dirPath,
    prefix: '/public/', // optional: default '/'
  }
}

/**
 * This plugins allows to serve static files as fast as possible.
 *
 * @see {@link https://github.com/fastify/fastify-static}
 */
export default fastifyStatic
