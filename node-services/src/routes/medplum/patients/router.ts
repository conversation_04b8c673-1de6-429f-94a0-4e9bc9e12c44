'use strict';

import { S } from 'fluent-json-schema';
import { patientSchema, conditionSchema } from '../../../types/patientSchema';
import { FastifyInstance, FastifyPluginOptions, FastifyReply, FastifyRequest } from 'fastify';
import { RelicPatient } from 'relic-ui';

const createPatientSchema = patientSchema.prop('conditions', conditionSchema);

export default async function patientRoutes(fastify: FastifyInstance, opts: FastifyPluginOptions) {
  fastify.get(
    '/',
    {
      schema: {
        ...opts.schema,
        query: S.object()
          .prop(
            '_sort',
            S.string()
              .default('name')
              .description(
                'Sample value: name, birthDate, gender, email, link, organizationId, active,\nWhere to get: it is the value of patient table column name',
              ),
          )
          .prop('_order', S.string().enum(['asc', 'desc']).default('asc'))
          .prop('_start', S.number().default(0))
          .prop('_end', S.number().default(25))
          .prop('active', S.boolean().default(true))
          .prop('_search', S.string().maxLength(255).description('Search by name, email, phone'))
          .prop('id', S.string().description('Search by patient id')),
        description: 'Get all patients for an Organization / Facility',
        summary: 'Get all patients for an Organization / Facility',
      },
    },
    async function fetchMultiple(req: FastifyRequest, rep: FastifyReply) {
      const { total, data } = await req.server.ehrService.searchPatients(req.query);
      rep.header('x-total-count', total);
      return data;
    },
  );

  fastify.post(
    '/',
    {
      schema: {
        ...opts.schema,
        description: 'Create Patient API. This API will create a FHIR patient, encounter, and condition resource.',
        summary: 'Create a new Patient admitted in an Organization / Facility',
        body: createPatientSchema,
      },
    },
    async function (req: FastifyRequest, rep: FastifyReply) {
      return await fastify.createPatient(req) as RelicPatient;
    },
  );
}
