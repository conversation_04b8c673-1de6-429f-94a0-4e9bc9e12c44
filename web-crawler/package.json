{"name": "web-crawler", "version": "0.0.1", "description": "RelicCare Default Web Crawler", "private": true, "packageManager": "pnpm@10.10.0", "type": "module", "engines": {"node": ">=18.0.0"}, "dependencies": {"@apify/log": "^2.5.13", "@azure/communication-chat": "^1.5.4", "@azure/storage-blob": "^12.17.0", "@crawlee/playwright": "3.13.3", "@medplum/core": "^4.0.0", "@medplum/fhirtypes": "^4.0.0", "@mozilla/readability": "^0.6.0", "apify": "^3.2.6", "axios": "^1.7.8", "bullmq": "^5.12.3", "crawlee": "3.13.3", "dotenv": "^16.4.5", "express": "^4.21.1", "fluent-json-schema": "^5.0.0", "jsdom": "^24.0.0", "officeparser": "^5.0.0", "playwright": "*", "playwright-extra": "^4.3.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "tsx": "^4.6.2", "typescript": "^5.6.3", "uuid": "^11.0.3"}, "devDependencies": {"@apify/tsconfig": "^0.1.0", "@types/express": "^4.17.21", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "eslint": "^9.25.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "prettier": "^3.2.5", "relic-ui": "workspace:relic-ui", "ts-loader": "^9.5.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.3.3", "wait-on": "^8.0.1"}, "scripts": {"start": "node dist/main.js", "dev": "wait-on ../relic-ui/dist/index.d.ts && tsx src/index.ts", "build": "tsc", "check-types": "tsc --noEmit", "lint": "eslint ./src --ext .ts", "lint:fix": "eslint ./src --ext .ts --fix", "test": "echo \"Error: oops, the actor has no tests yet, sad!\" && exit 1", "postinstall": "npx crawlee install-playwright-browsers & npx playwright install"}, "author": "It's not you it's me", "license": "ISC"}