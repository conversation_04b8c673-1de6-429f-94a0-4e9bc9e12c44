import { toast } from 'react-toastify';
import { useState, useEffect } from 'react';
import { Control, FieldValues } from 'react-hook-form';
import { useStepsForm } from '@refinedev/react-hook-form';
import {
  RefineButtonTestIds,
  RefineButtonClassNames,
} from '@refinedev/ui-types';
import {
  IUser,
  RelicPatient,
  availableLanguages,
  RelicCommunicationLanguage,
} from 'relic-ui';
import {
  useList,
  useLogin,
  useApiUrl,
  useCreate,
  useCustom,
  useTranslate,
  useGetIdentity,
  useIsAuthenticated,
} from '@refinedev/core';

import { Telegram } from '@mui/icons-material';
import LoadingButton from '@mui/lab/LoadingButton';
import {
  Box,
  Step,
  Stepper,
  TextField,
  StepButton,
  useMediaQuery,
} from '@mui/material';

import { useRouter, useSearchParams } from 'src/routes/hooks';

import { getIdTokenHint } from 'src/providers/utils';

import TextMask from 'src/components/text-mask';
import { Create } from 'src/components/refine-customs/create';
import { StateDropdown } from 'src/components/dropdown/stateDropdown';
import { LanguageDropdown } from 'src/components/dropdown/languageDropdown';
import { ExperienceViaDropdown } from 'src/components/dropdown/experienceVia';
import { FacilityTypeDropdown } from 'src/components/dropdown/facilityTypeDropdown';
import { ScenarioTypeDropdown } from 'src/components/dropdown/demoScenarioDropdown';

import DemoLayoutWrapper from '../layout';
import PhoneInput from 'src/components/phone-input';

const defaultStepTitles = ['Contact', 'Facility Details', 'Demo Scenario'];
const defaultLanguage = availableLanguages.find(
  (lang: RelicCommunicationLanguage) => lang.code === 'en-US',
) as RelicCommunicationLanguage;

interface RegistrationProps {
  isSecured: boolean;
}

export const Registration: React.FC<RegistrationProps> = ({ isSecured }) => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const queryScenario = searchParams.get('scenario');
  const demoType = searchParams.get('type');
  const isCompliance = queryScenario?.toLowerCase().includes('compliance');

  const { data: currentUser } = useGetIdentity<IUser>();
  const t = useTranslate();
  const isSmallScreen = useMediaQuery('(max-width:600px)');
  const apiUrl = useApiUrl('demo_provider');
  const { mutate: login } = useLogin();
  const { isLoading, data: session } = useIsAuthenticated();

  const [stepTitles, setStepTitles] = useState(defaultStepTitles);

  const loggedInUser = {
    name: currentUser?.name || '',
    email: currentUser?.userIdentity?.email || '',
    mobilePhone:
      currentUser?.userIdentity?.profile?.telecom?.find(
        t => t.system === 'phone',
      )?.value || '',
  };

  const {
    saveButtonProps,
    refineCore: { formLoading },
    register,
    handleSubmit,
    control,
    watch,
    formState: { errors },
    setValue,
    steps: { currentStep, gotoStep },
  } = useStepsForm({
    refineCoreProps: {
      resource: 'demo/patients',
      dataProviderName: 'demo_provider',
      queryOptions: {
        enabled: false,
      },
    },
    mode: 'onBlur',
    defaultValues: {
      name: '',
      email: '',
      mobilePhone: '',
      facility: '',
      facility_type: '',
      state: null,
      scenario: isCompliance
        ? 'regulatory_compliance_qa'
        : 'patient_satisfaction_survey',
      persona: '60876e0e-922f-444c-acc3-035acbce2143',
      primaryLanguage: defaultLanguage,
      experience_via: isCompliance ? 'browser' : 'phone',
    },
  });

  const values = watch();

  const { data: demoPatientsData } = useList<RelicPatient>({
    resource: 'demo/organization/patients',
    dataProviderName: 'demo_provider',
  });

  const {
    data: patientData,
    isFetching: patientFetching,
    refetch: refetchPatient,
  } = useCustom<RelicPatient>({
    url: `${apiUrl}/demo/patients`,
    method: 'get',
    queryOptions: {
      enabled: false,
    },
    config: {
      query: {
        email: values?.email || loggedInUser.email,
        mobilePhone: values.mobilePhone || loggedInUser?.mobilePhone,
      },
    },
  });

  const { mutate: createThread, isLoading: createThreadLoading } = useCreate({
    resource: 'demo/patients/chat/threads',
    dataProviderName: 'demo_provider',
    mutationOptions: {
      onSuccess: async data => {
        const threadId = data.data.threadId as string;
        await b2cLogin(threadId);
      },
    },
  });

  const { mutate: createPatient, isLoading: createPatientLoading } = useCreate({
    resource: 'demo/patients',
    dataProviderName: 'demo_provider',
    mutationOptions: {
      onSuccess: async data => {
        createSurveyThread(data.data as RelicPatient);
      },
    },
  });

  const createComplianceThread = async (relicPatient: RelicPatient) => {
    const patientId = relicPatient.id as string;
    const organizationId = relicPatient.organizationId as string;
    const { email, mobilePhone } = values;
    const payload = {
      participants: [
        {
          resourceId: patientId,
          resourceType: 'Patient',
        },
      ],
      id: patientId,
      type: 'Default_Practitioner',
      inviteVia: 'none',
      receiverEmail: email,
      receiverPhone: mobilePhone,
      topic: '',
      subject: {
        organizationId: organizationId,
        type: 'Default',
        targetPhoneNumber: mobilePhone,
        patientLanguage: {
          system: 'http://hl7.org/fhir/ValueSet/languages',
          code: 'en-US',
          display: 'English (United States)',
        },
      },
    };
    createThread({
      values: payload,
    });
  };

  const createSurveyThread = async (relicPatient: RelicPatient) => {
    const patientId = relicPatient.id as string;
    const organizationId = relicPatient.organizationId as string;
    const { email, mobilePhone, primaryLanguage } = values;
    const payload = {
      participants: [
        {
          resourceId: patientId,
          resourceType: 'Patient',
        },
      ],
      id: patientId,
      type: 'Default_Practitioner',
      receiverEmail: email,
      receiverPhone: mobilePhone,
      topic: '',
      subject: {
        organizationId: organizationId,
        type: 'Default',
        targetPhoneNumber: mobilePhone,
        patientLanguage: primaryLanguage,
      },
    };
    createThread({
      values: payload,
    });
  };

  const onSubmit = async (values: FieldValues) => {
    try {
      const {
        name,
        email,
        mobilePhone,
        primaryLanguage,
        facility,
        facility_type,
        state,
        persona,
        experience_via,
        scenario,
      } = values;

      if (!patientData?.data) {
        const updatedData = {
          sourcePatientId: persona,
          name,
          email,
          mobilePhone,
          facilityName: facility,
          facilityType: facility_type && facility_type.display,
          experience_via,
          scenario,
          state: state && state.code,
          primaryLanguage: [{ ...primaryLanguage, preferred: true }],
        };
        createPatient({
          values: updatedData,
        });
      } else {
        if (scenario.toLowerCase().includes('compliance')) {
          createComplianceThread(patientData.data as RelicPatient);
        } else {
          createSurveyThread(patientData.data as RelicPatient);
        }
      }
    } catch (error) {
      console.error('Submission error:', error);
    }
  };

  const b2cLogin = async (threadId: string) => {
    const redirectTo = isSecured
      ? `/check`
      : searchParams.get('to')
        ? `${window.location.origin}/demo${searchParams.get('to')}`
        : `${window.location.origin}/demo/check`;

    if (threadId) {
      localStorage.setItem('threadId', threadId);
    }

    if (isSecured && session?.authenticated) {
      router.push(redirectTo);
    } else {
      const phone = values.mobilePhone.replace(/[()-]/g, '');
      const token = await getIdTokenHint('phone', phone);
      login({
        id_token_hint: token as string,
        redirectTo: redirectTo,
      });
    }
  };

  useEffect(() => {
    if (demoPatientsData?.data?.length) {
      setValue('persona', demoPatientsData.data[0].id);
    }
  }, [demoPatientsData, setValue]);

  useEffect(() => {
    if (!queryScenario) {
      router.push('/demo-home');
    }
  }, [router, queryScenario]);

  useEffect(() => {
    if (currentUser?.accessToken) {
      setValue('name', loggedInUser.name as string);
      setValue('email', loggedInUser.email as string);
      setValue('mobilePhone', loggedInUser.mobilePhone as string);
      if (currentStep === 0) {
        handleNext();
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [demoType, currentUser]);

  const renderFormByStep = (step: number) => {
    switch (step) {
      case 0:
        return (
          <>
            <TextField
              {...register('name', {
                required: 'This field is required',
              })}
              error={!!(errors as any)?.name}
              helperText={(errors as any)?.name?.message}
              margin="normal"
              fullWidth
              type="text"
              label={t('demo.fields.name')}
              name="name"
              value={values.name}
            />
            <TextField
              {...register('email', {
                required: 'This field is required',
                pattern: {
                  value: /\S+@\S+\.\S+/,
                  message: 'Entered value does not match email format',
                },
              })}
              error={!!(errors as any)?.email}
              helperText={(errors as any)?.email?.message}
              margin="normal"
              fullWidth
              type="email"
              label={t('demo.fields.email')}
              name="email"
              value={values.email}
            />

            <PhoneInput
              control={control as unknown as Control<FieldValues, object>}
              label={t('demo.fields.mobilePhone')}
              name="mobilePhone"
              value={values.mobilePhone}
            />
          </>
        );
      case 1:
        return (
          <>
            <FacilityTypeDropdown
              control={control}
              name="facility_type"
              label={t('demo.fields.facility_type')}
              error={!!(errors as any)?.facility_type}
              helperText={(errors as any)?.facility_type?.message}
              value={values.facility_type}
            />
            <TextField
              {...register('facility')}
              error={!!(errors as any)?.facility}
              helperText={(errors as any)?.facility?.message}
              margin="normal"
              fullWidth
              type="text"
              value={values.facility}
              label={t('demo.fields.facility')}
              name="facility"
            />
            <StateDropdown
              control={control}
              name="state"
              label={t('demo.fields.state')}
              error={!!(errors as any)?.state}
              helperText={(errors as any)?.state?.message}
              value={values.state}
            />
          </>
        );
      case 2:
        return (
          <>
            <ScenarioTypeDropdown
              control={control}
              name="scenario"
              label={t('demo.fields.scenario')}
              error={!!(errors as any)?.scenario}
              helperText={(errors as any)?.scenario?.message}
              value={values.scenario}
              disabled={!!queryScenario}
            />
            {!isCompliance && (
              <LanguageDropdown
                control={control}
                name="primaryLanguage"
                label={t('demo.fields.primaryLanguage')}
                error={!!(errors as any)?.primaryLanguage}
                helperText={(errors as any)?.primaryLanguage?.message}
                value={values.primaryLanguage}
                disabled={!!isCompliance}
              />
            )}
            <ExperienceViaDropdown
              control={control}
              name="experience_via"
              label={t('demo.fields.experience_via')}
              error={!!(errors as any)?.experience_via}
              helperText={(errors as any)?.experience_via?.message}
              value={values.experience_via}
              disabled={!!isCompliance}
            />
          </>
        );
    }
  };

  const handleNext = async () => {
    const updatedTitles = [...stepTitles];
    let step = currentStep;

    if (step === 0) {
      const { data: patientExistRes } = await refetchPatient();
      step = patientExistRes?.data?.id ? 2 : 1;
      updatedTitles[0] = values.name || loggedInUser.name;
      if (!isSecured && patientExistRes?.data && step === 2) {
        toast.info(
          `We found your record, you will receive OTP at your phone number ending with ${patientExistRes.data.mobilePhone?.slice(-4)}`,
        );
      }
    } else if (step === 1) {
      updatedTitles[0] = values.name || loggedInUser.name;
      updatedTitles[1] = values.facility || 'Facility Details';
      step = 2;
    } else if (step === 2) {
      step = 3;
    }

    setStepTitles(updatedTitles);
    gotoStep(step);
  };

  const handlePrev = () => {
    const updatedTitles = [...stepTitles];
    const step = currentStep - 1;

    if (currentStep === 1) {
      updatedTitles[0] = defaultStepTitles[0];
    } else if (currentStep === 2) {
      updatedTitles[1] = defaultStepTitles[1];
    }

    setStepTitles(updatedTitles);
    gotoStep(step);
  };

  const FooterButtons = (
    <>
      {currentStep > 0 && (
        <LoadingButton
          onClick={handlePrev}
          variant="contained"
          color="primary"
          loading={patientFetching}
        >
          Previous
        </LoadingButton>
      )}
      {currentStep < stepTitles.length - 1 ? (
        <LoadingButton
          onClick={handleNext}
          variant="contained"
          color="primary"
          loading={patientFetching}
        >
          Next
        </LoadingButton>
      ) : (
        <LoadingButton
          startIcon={<Telegram />}
          sx={{ minWidth: 0 }}
          variant="contained"
          data-testid={RefineButtonTestIds.SaveButton}
          className={RefineButtonClassNames.SaveButton}
          loading={createPatientLoading || createThreadLoading}
          onClick={handleSubmit(onSubmit)}
        >
          Go To Demo
        </LoadingButton>
      )}
    </>
  );

  const StepView = (
    <Stepper nonLinear activeStep={currentStep} orientation={'horizontal'}>
      {stepTitles.map((label, index) => (
        <Step key={label}>
          <StepButton disabled>{!isSmallScreen && label}</StepButton>
        </Step>
      ))}
    </Stepper>
  );

  const RegistrationForm = (
    <Create
      title="Relic AI Demo"
      wrapperProps={{
        sx: {
          borderRadius: 2,
          display: 'flex',
          width: { xs: '100%', sm: '70%' },
          maxWidth: '600px',
          boxShadow: 'none',
          flexDirection: 'column',
          margin: 'auto',
          padding: { xs: 2, sm: 4 },
        },
      }}
      isLoading={formLoading}
      saveButtonProps={saveButtonProps}
      footerButtons={FooterButtons}
    >
      <Box
        component="form"
        sx={{
          display: 'flex',
          flexDirection: 'column',
        }}
        autoComplete="off"
      >
        {StepView}
        {renderFormByStep(currentStep)}
      </Box>
    </Create>
  );

  return (
    <>
      {isSecured ? (
        <Box
          sx={{
            display: 'flex',
            width: '100%',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          {RegistrationForm}
        </Box>
      ) : (
        <DemoLayoutWrapper>
          <Box
            sx={{
              display: 'flex',
              width: '100%',
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            {RegistrationForm}
          </Box>
        </DemoLayoutWrapper>
      )}
    </>
  );
};
