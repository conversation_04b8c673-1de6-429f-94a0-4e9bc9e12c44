import React from 'react';
import { useAutocomplete } from '@refinedev/mui';
import { Control, Controller } from 'react-hook-form';

import TextField from '@mui/material/TextField';
import Autocomplete from '@mui/material/Autocomplete';

interface TrainingModulesDropdownProps {
  control: Control<any>;
  name: string;
  label: string;
  value: any[] | null;
  error: boolean;
  helperText: string;
  onChange?: (value: any[] | null) => void;
  disabled?: boolean;
}

export const TrainingModulesDropdown: React.FC<
  TrainingModulesDropdownProps
> = ({
  control,
  name,
  label,
  value,
  error,
  helperText,
  onChange,
  disabled,
}) => {
  const { autocompleteProps: trainingModulesAutocompleteProps } =
    useAutocomplete({
      resource: 'trainings/modules',
    });

  return (
    <Controller
      control={control}
      name={name}
      defaultValue={value}
      rules={{ required: 'This field is required' }}
      render={({ field }) => {
        const { onChange: fieldOnChange, value: fieldValue } = field;
        return (
          <Autocomplete
            multiple
            loading={trainingModulesAutocompleteProps.loading}
            options={trainingModulesAutocompleteProps.options}
            onInputChange={trainingModulesAutocompleteProps.onInputChange}
            disabled={disabled}
            autoHighlight
            getOptionLabel={(option: any) =>
              trainingModulesAutocompleteProps.options?.find(
                org => org.id === option.id,
              )?.name ?? ''
            }
            isOptionEqualToValue={(option, value) => {
              return (
                value === undefined ||
                option?.id?.toString() === (value?.id ?? value).toString()
              );
            }}
            onChange={(_, newValues) => {
              const selectedIds = newValues.map(item => item.id);
              fieldOnChange(selectedIds);
              if (onChange) {
                onChange(newValues);
              }
            }}
            renderInput={params => (
              <TextField
                {...params}
                disabled={disabled}
                label={label}
                variant="outlined"
                error={error}
                helperText={helperText}
              />
            )}
            value={trainingModulesAutocompleteProps.options?.filter(option =>
              fieldValue?.includes(option.id),
            )}
          />
        );
      }}
    />
  );
};
