import { useRef, useCallback } from 'react';

/**
 * A custom hook that creates a debounced version of a callback function.
 * The debounced function will delay invoking the callback until after `delay` milliseconds
 * have elapsed since the last time it was called.
 *
 * @template T - The type of the callback function
 * @param callback - The function to debounce
 * @param delay - The number of milliseconds to delay the callback execution
 * @returns A debounced version of the callback function that maintains the original type
 *
 * @example
 * ```typescript
 * const debouncedSearch = useDebounce((query: string) => {
 *   // Search operation
 * }, 500);
 * ```
 */
export function useDebounce<T extends (...args: any[]) => void>(
  callback: T,
  delay: number,
): T {
  const timeoutRef = useRef<NodeJS.Timeout>();

  const debouncedCallback = useCallback(
    (...args: Parameters<T>) => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      timeoutRef.current = setTimeout(() => {
        callback(...args);
      }, delay);
    },
    [callback, delay],
  ) as T;

  return debouncedCallback;
}
