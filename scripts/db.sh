
#!/bin/bash
az login

#MONGODB_URI="***********************************************************************************************************************************************************************************************************************************************"
SUBSCRIPTION="56b648be-c67e-4ae0-a13d-1e471401974f"
RESOURCE_GROUP="ai-counsellor"
LOCALTION="westus"

ACCOUNT_NAME="relic-prod-account"
DATABASE="relic-db"
ORGANIZATIONS="organizations"
AGENTS="agents"
PRACTITIONERS="practitioners"
DOCUMENTS="documents"
PATIENTS="patients"
THREADS="threads"
#JSON_FILE="system-agent.json"
#DEPLOYMENT_NAME=""




#Check if the cosmosdb account exists
if az cosmosdb show --name $ACCOUNT_NAME --resource-group $RESOURCE_GROUP --output none 2>/dev/null; then
    echo "cosmosdb account $ACCOUNT_NAME already exists."
else
    az cosmosdb create --name $ACCOUNT_NAME \
        --resource-group $RESOURCE_GROUP \
        --kind MongoDB \
        --locations regionName=$LOCALTION failoverPriority=0 \
        --default-consistency-level Session \
        --max-interval 5 \
        --max-staleness-prefix 100 \
        --enable-automatic-failover false \
        --enable-multiple-write-locations false \
        --enable-analytical-storage false \
        --analytical-storage-schema-type FullFidelity \
        --disable-key-based-metadata-write-access false \
        --enable-partition-merge false \
        --backup-interval 240 \
        --backup-retention 8 \
        --network-acl-bypass None \
        --subscription $SUBSCRIPTION

    echo "cosmosdb account $ACCOUNT_NAME Created successfully."

fi

#Check if the database exists
if az cosmosdb mongodb database show --account-name $ACCOUNT_NAME --name $DATABASE --resource-group $RESOURCE_GROUP --output none 2>/dev/null; then
    echo "Database $DATABASE already exists."
else
    # Create the database if it doesn't exist
    az cosmosdb mongodb database create --account-name $ACCOUNT_NAME --name $DATABASE --resource-group $RESOURCE_GROUP --throughput 400 #--max-throughput 1000
    echo "**************************** Database $DATABASE created successfully ****************************"
fi


# Check if the collection organizations exists
if az cosmosdb mongodb collection show --account-name $ACCOUNT_NAME --database-name $DATABASE --name $ORGANIZATIONS --resource-group $RESOURCE_GROUP --output none 2>/dev/null; then
    echo "Collection $ORGANIZATIONs already exists."
else
    # Create the collection relic-db if it doesn't exist. Apply indexes.json wherever applicable.
    az cosmosdb mongodb collection create --account-name $ACCOUNT_NAME --database-name $DATABASE --name $ORGANIZATIONS --resource-group $RESOURCE_GROUP --idx ./$ORGANIZATIONS/@indexes.json
    echo "**************************** Collection $ORGANIZATIONS created successfully ****************************"
fi

# Check if the collection agents exists
if az cosmosdb mongodb collection show --account-name $ACCOUNT_NAME --database-name $DATABASE --name $AGENTS --resource-group $RESOURCE_GROUP --output none 2>/dev/null; then
    echo "Collection $AGENTS already exists."
else
    # Create the collection relic-db if it doesn't exist. Apply indexes.json wherever applicable.
    az cosmosdb mongodb collection create --account-name $ACCOUNT_NAME --database-name $DATABASE --name $AGENTS --resource-group $RESOURCE_GROUP
    echo "**************************** Collection $AGENTS created successfully ****************************"
fi

# Check if the collection practitioners exists
if az cosmosdb mongodb collection show --account-name $ACCOUNT_NAME --database-name $DATABASE --name $PRACTITIONERS --resource-group $RESOURCE_GROUP --output none 2>/dev/null; then
    echo "Collection $PRACTITIONERS already exists."
else
    # Create the collection relic-db if it doesn't exist. Apply indexes.json wherever applicable.
    az cosmosdb mongodb collection create --account-name $ACCOUNT_NAME --database-name $DATABASE --name $PRACTITIONERS --resource-group $RESOURCE_GROUP
    echo "**************************** Collection $PRACTITIONERS created successfully ****************************"
fi

# Check if the collection documents exists
if az cosmosdb mongodb collection show --account-name $ACCOUNT_NAME --database-name $DATABASE --name $DOCUMENTS --resource-group $RESOURCE_GROUP --output none 2>/dev/null; then
    echo "Collection $DOCUMENTS already exists."
else
    # Create the collection relic-db if it doesn't exist. Apply indexes.json wherever applicable.
    az cosmosdb mongodb collection create --account-name $ACCOUNT_NAME --database-name $DATABASE --name $DOCUMENTS --resource-group $RESOURCE_GROUP --idx ./$DOCUMENTS/@indexes.json
    echo "**************************** Collection $DOCUMENTS created successfully ****************************"
fi

# Check if the collection patients exists
if az cosmosdb mongodb collection show --account-name $ACCOUNT_NAME --database-name $DATABASE --name $PATIENTS --resource-group $RESOURCE_GROUP --output none 2>/dev/null; then
    echo "Collection $PATIENTS already exists."
else
    # Create the collection relic-db if it doesn't exist. Apply indexes.json wherever applicable.
    az cosmosdb mongodb collection create --account-name $ACCOUNT_NAME --database-name $DATABASE --name $PATIENTS --resource-group $RESOURCE_GROUP
    echo "**************************** Collection $PATIENTS created successfully ****************************"
fi

# Check if the collection threads exists
if az cosmosdb mongodb collection show --account-name $ACCOUNT_NAME --database-name $DATABASE --name $THREADS --resource-group $RESOURCE_GROUP --output none 2>/dev/null; then
    echo "Collection $THREADS already exists."
else
    # Create the collection relic-db if it doesn't exist. Apply indexes.json wherever applicable.
    az cosmosdb mongodb collection create --account-name $ACCOUNT_NAME --database-name $DATABASE --name $THREADS --resource-group $RESOURCE_GROUP
    echo "**************************** Collection $THREADS created successfully ****************************"
fi
# Check availability of the following systems with their details before proceeding for Org creation

# COPILOT COMPANY:
    # "id" : "90bac878-9a2e-4d71-ac08-fc1a755f3b5a",
    #"name" : "TEST - Relic Care, Inc",
    #"leadInternalUserId" : "ffcd8392-64d2-43df-9548-055b257dfaee"

echo "COPILOT Company check complete"

# MEDPLUM CLIENT APPLICATION
    #"Org ID" : "********-bda6-4127-b33b-af4ffa82b0d8",
    #"Location ID" : "baea9307-d24a-4d1b-9a5f-10986594d324"
            
echo "**************************** MEDPLUM Client check complete ****************************"

#mongoimport --uri $MONGODB_URI --collection $COLLECTION --db $DATABASE --file ./orgs/$ORG_JSON_FILE --jsonArray
#mongoimport --uri $MONGODB_URI --collection $COLLECTION --db $DATABASE --file ./agents/$JSON_FILE --jsonArray

#echo "**************************** Organization created successfully ****************************"

