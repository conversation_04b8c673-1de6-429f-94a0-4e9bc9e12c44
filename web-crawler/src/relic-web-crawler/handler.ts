import { Log, PlaywrightCrawlingContext, NonRetryableError, Dataset, RequestQueue } from "crawlee";
import { cleanUrl, isFileLink, isUrlSupported, addSearchParamsToTitle, createDocumentId } from "./utils.js";
import { v4 as uuidv4 } from 'uuid';
import { ScraperOutput, ScrapedContent, RelicMessageQueueParams, RelicCrawlerOptions, CrawlRequest, URLLike } from "relic-ui";
import { StorageLocation } from "relic-ui";
import { Page } from "playwright";
import path, { resolve } from "node:path";
import { BlobServiceClient, ContainerClient } from "@azure/storage-blob";
import { existsSync, mkdirSync, readFileSync, rmSync, unlink, writeFileSync } from "node:fs";
import docParser from 'officeparser';
import { Queue } from "bullmq";
import { homedir } from "node:os";
import axios from "axios";
import { RelicMetaData } from "./types.js";
import { Readability } from "@mozilla/readability";
import { isProbablyReaderable } from "@mozilla/readability";
import { JSDOM } from "jsdom";
import { getRedisQueue } from "./queue.js";
export class RelicCrawlHandler {
    private request: CrawlRequest;
    private containerClient: ContainerClient;
    private redisQueue: Queue;
    private relicMessageQueueParams: RelicMessageQueueParams;
    private downloadsFolder: string;
    private successfulScrapes: number = 0;
    private options: RelicCrawlerOptions;
    private baseScraperOutput: ScraperOutput;

    /**
     * Create a new RelicCrawlHandler
     * @returns RelicCrawlHandler
     */
    public static async create(request: CrawlRequest) {
        const handler = new RelicCrawlHandler();
        handler.request = request;
        await handler.initialize();
        return handler;
    }

    /**
     * Initialize the handler and its dependencies
     */
    private async initialize() {
        try {
            const { options, relicMessageQueueParams, crawlStorage } = this.request;
            this.options = options;
            this.relicMessageQueueParams = relicMessageQueueParams;
            this.downloadsFolder = resolve(homedir(), 'downloads', uuidv4());
            mkdirSync(this.downloadsFolder, { recursive: true });
            await this.initializeStorage(crawlStorage);
            this.redisQueue = await getRedisQueue(relicMessageQueueParams);
            this.baseScraperOutput = {
                id: '',
                organizationId: relicMessageQueueParams.organizationId,
                documentId: '',
                sourceDocumentId: '',
                filename: '',
                url: '',
                jsonContentUrl: '',
                translationType: 'mono',
                language: 'EN-US',
                type: 'url',
                header: 0,
                footer: 0,
                status: 'pending',
                createdBy: relicMessageQueueParams.createdBy,
                createDate: new Date(),
                updatedBy: relicMessageQueueParams.createdBy,
                updateDate: new Date(),
                deleted: false,
            };
        } catch (err) {
            throw new Error(`Error initializing crawler: ${err}`);
        }

    }

    /**
     * Cleanup resources
     */
    async cleanup() {
        if (this.downloadsFolder) {
            rmSync(this.downloadsFolder, { recursive: true, force: true });
        }
    }

    /**
     * Check max scrapes
     */
    private async isOkToContinue(ctx: PlaywrightCrawlingContext): Promise<boolean> {
        const { log, request } = ctx;
        log.setOptions({ data: { processId: request.userData.id } });
        if (this.successfulScrapes >= this.options.maxUrlsToCrawl) {
            log.info('Maximum number of successful scrapes reached, stopping crawler.', {
                successfulScrapes: this.successfulScrapes,
                maxUrlsToCrawl: this.options.maxUrlsToCrawl
            });
            ctx.crawler.stop();
            return false;
        }

        return true;
    }

    /**
     * Initialize storage
     * @param crawlStorage 
     */
    private async initializeStorage(crawlStorage: StorageLocation) {
        if (!crawlStorage) {
            return;
        }
        const { accountName, accountKey, containerName } = crawlStorage;
        if (!accountName || !accountKey || !containerName) {
            return;
        }
        const storageConnectionString = `DefaultEndpointsProtocol=https;AccountName=${accountName};AccountKey=${accountKey};EndpointSuffix=core.windows.net`;
        const blobServiceClient = BlobServiceClient.fromConnectionString(storageConnectionString);
        this.containerClient = blobServiceClient.getContainerClient(containerName);
    }

    /**
     * Handle file links
     */
    async handleFile(ctx: PlaywrightCrawlingContext) {
        const { log, request } = ctx;
        const userData = request.userData as RelicMetaData;

        const shouldContinue = await this.isOkToContinue(ctx);
        if (!shouldContinue) {
            return;
        }

        if (!isUrlSupported(request.url)) {
            log.info('URL is unsupported, processing skipped.', { url: request.url });
            return;
        }

        log.info('Processing file', { url: request.url });
        const scrapedContent = await this.scrapeFile({ log, url: request.url });
        const documentId = createDocumentId(userData.id, scrapedContent.title);
        const scraperOutput: ScraperOutput = {
            ...this.baseScraperOutput,
            id: userData.id,
            documentId,
            sourceDocumentId: userData.sourceDocumentId,
            filename: scrapedContent.title,
            url: scrapedContent.url
        };

        const isRelevant = await this.isRelevant({ log, url: scrapedContent.url, summary: scrapedContent.content });
        if (!isRelevant) {
            throw new NonRetryableError('Page is not relevant');
        }

        const blobUrl = await this.uploadScrapedContentToStorage({ log, documentId, scrapedContent });
        scraperOutput.jsonContentUrl = blobUrl || '';
        scraperOutput.status = 'done';
        await this.processScrapeResult({ log, scraperOutput });
        log.info('Successfully processed file', { url: request.url });
    }

    /**
     * Handle page links
     */
    async handlePage(ctx: PlaywrightCrawlingContext) {
        const { log, request, enqueueLinks, page, response } = ctx;
        log.setOptions({ data: { processId: request.userData.id } });
        await page.waitForLoadState('domcontentloaded');

        const shouldContinue = await this.isOkToContinue(ctx);
        if (!shouldContinue) {
            return;
        }

        const canonicalUrl = await page.evaluate(() => {
            const canonical = document.querySelector('link[rel="canonical"]');
            return canonical?.getAttribute('href') || '';
        });

        if (canonicalUrl) {
            log.info('Redirecting to canonical url', { url: canonicalUrl });
            request.url = canonicalUrl;
            await page.goto(canonicalUrl, { waitUntil: 'domcontentloaded' });
        }

        if (!isUrlSupported(request.url)) {
            log.info('URL is unsupported, processing skipped.', { url: request.url });
            return false;
        }


        if (response && response.status() >= 400 && response.status() != 404) {
            log.error('Page returned server error, processing failed', { url: response.url(), status: response.status() });
            throw new NonRetryableError(`Page returned server error: ${response.status()}`);
        }

        if (response && response.status() == 404) {
            log.info('Page not found, waiting for redirect', { url: request.url });
            await this.waitForRedirect({ log, page, url: request.url });
        }

        if (isFileLink(page.url()) || request.userData.isFileLink) {
            log.info('File link, redirecting to file handler', { url: request.url });
            return await this.handleFile(ctx);
        }

        log.info('Processing page', { url: page.url() });
        const userData = request.userData as RelicMetaData;

        const scrapedContent = await this.scrapePage({ log, page });
        const documentId = createDocumentId(userData.id, scrapedContent.title);
        const scraperOutput: ScraperOutput = {
            ...this.baseScraperOutput,
            id: userData.id,
            documentId,
            sourceDocumentId: userData.sourceDocumentId,
            filename: scrapedContent.title,
            url: scrapedContent.url,
        };

        const isRelevant = await this.isRelevant({ log, url: scrapedContent.url, summary: scrapedContent.content });
        if (!isRelevant) {
            throw new NonRetryableError('Page is not relevant');
        }

        const blobUrl = await this.uploadScrapedContentToStorage({ log, documentId, scrapedContent });
        scraperOutput.jsonContentUrl = blobUrl || '';
        scraperOutput.status = 'done';
        await this.processScrapeResult({ log, scraperOutput });
        log.info('Successfully processed page', { url: page.url() });

        await this.addChildrenToRequestQueue({ log, sourceDocumentId: userData.id, children: userData.children });

        if (!this.options.crawlEmbeddedLinks) {
            log.info('Crawling embedded links is disabled, skipping.', { url: page.url() });
            return;
        }

        if (userData.depth + 1 > this.options.maxDepth) {
            log.info('Max depth reached, embedded links will not be enqueued.', { url: page.url() });
            return;
        }

        const childDepth = userData.depth + 1;
        const sourceDocumentId = userData.id;
        await enqueueLinks({
            strategy: 'same-domain',
            userData: request.userData,
            transformRequestFunction: (request) => {
                const userData = request.userData as RelicMetaData;
                userData.id = '';
                userData.depth = childDepth;
                userData.sourceDocumentId = sourceDocumentId;
                request.label = isFileLink(cleanUrl(request.url)) ? 'file' : 'page';
                request.useExtendedUniqueKey = true;
                return request;
            }
        });
        log.info('Successfully enqueued embedded links', { url: page.url() });
    }

    /**
     * Error handler
     */
    async errorHandler(ctx: PlaywrightCrawlingContext, error: Error) {
        const { log } = ctx;
        log.error('Error processing request', { url: ctx.request.url, error });
    }

    /**
     * Failed request handler
     */
    async handleFailure(ctx: PlaywrightCrawlingContext, error: Error) {
        const { log, request } = ctx;
        log.error('Failed to process request after retries', { url: request.url, error });
        const scraperOutput: ScraperOutput = {
            ...this.baseScraperOutput,
            id: request.userData.id,
            documentId: createDocumentId(request.userData.id, request.url),
            sourceDocumentId: request.userData.sourceDocumentId,
            filename: request.url,
            url: request.url,
            status: 'failed',
            errorDetails: error.message,
        }
        await this.processScrapeResult({ log, scraperOutput });
    }

    /**
     * Scrape file
     * If isEstimatorRun is true, do not scrape file contents
     */
    private async scrapeFile({ log, url }: { log: Log, url: string }): Promise<ScrapedContent> {
        //no need to scrape file contents for estimator
        if (this.request.isEstimatorRun) {
            return {
                title: path.basename(url),
                url,
                content: '',
                description: '',
                originalFilepath: url
            };
        }
        log.info('Extracting content from file', { url });
        const filepath = await this.downloadFile({ log, url });
        const scrapedContent: ScrapedContent = {
            title: path.basename(filepath),
            url,
            content: await this.getTextContent(filepath),
            description: '',
            originalFilepath: url
        }
        log.info('File content extracted', { url });
        unlink(filepath, (err) => {
            if (err) {
                log.error('Error deleting file', { url, error: err });
            }
        });
        return scrapedContent;
    }

    /**
     * Scrape page
     */
    private async scrapePage({ log, page }: { log: Log, page: Page }): Promise<ScrapedContent> {
        try {
            await this.removeElements({ page, log });
            if (this.options.useReadableContent) {
                return await this.scrapePageWithReadability({ log, page })
                    .catch(async (error) => {
                        log.error('Error extracting content from page, fallback to playwright', { url: page.url(), error: error.message });
                        return await this.scrapePageWithPlaywright({ log, page });
                    });
            } else {
                return await this.scrapePageWithPlaywright({ log, page });
            }
        } catch (error) {
            log.error('Error scraping page', { url: page.url(), error });
            throw new NonRetryableError(`Error scraping page: ${error}`);
        }
    }

    /**
     * Scrape page with playwright
     */
    private async scrapePageWithPlaywright({ log, page }: { log: Log, page: Page }): Promise<ScrapedContent> {
        log.info('Scraping content from page using playwright', { url: page.url() });
        const scrapedContent: ScrapedContent = {
            title: addSearchParamsToTitle(await page.title() || page.url(), page.url()),
            url: page.url(),
            content: await page.textContent('body'),
            description: '',
            originalFilepath: page.url()
        }
        log.info('Successfully scraped content from page using playwright', { url: page.url() });
        return scrapedContent;
    }

    /**
     * Scrape page with readability js
     */
    private async scrapePageWithReadability({ log, page }: { log: Log, page: Page }): Promise<ScrapedContent> {
        log.info('Scraping content from page using readability js', { url: page.url() });
        const url = page.url();

        const html = await page.evaluate(() => {
            return document.documentElement.outerHTML;
        });
        const dom = new JSDOM(html, { url });
        if (!isProbablyReaderable(dom.window.document)) {
            throw new Error('Not readable by readability js');
        }
        const reader = new Readability(dom.window.document);
        const result = reader.parse();
        if (!result) {
            throw new Error('Readability parsing failed.');
        }
        const scrapedContent: ScrapedContent = {
            url,
            title: addSearchParamsToTitle(result.title || url, url),
            content: result.textContent || '',
            description: result.excerpt || '',
            originalFilepath: url
        };
        await page.evaluate((content) => {
            const parser = new DOMParser();
            const contentOnlyDom = parser.parseFromString(content, 'text/html');
            document.body.replaceWith(contentOnlyDom.body);
        }, result.content);
        log.info('Successfully scraped content from page using readability js', { url: page.url() });
        return scrapedContent;
    }

    /**
     * Get text content from file
     */
    private async getTextContent(filepath: string): Promise<string> {
        if (!existsSync(filepath)) {
            throw new NonRetryableError(`File not found: ${filepath}`);
        }

        try {
            const isTxt: boolean = path.extname(filepath).includes('txt');
            const isJson: boolean = path.extname(filepath).includes('json');
            if (isTxt || isJson) {
                return readFileSync(filepath, 'utf-8');
            }
            return await docParser.parseOfficeAsync(filepath, { putNotesAtLast: true });
        } catch (error) {
            throw new NonRetryableError(`Error parsing file: ${error}`);
        }
    }

    /**
     * Download file to local storage
     */
    private async downloadFile({ log, url }: { log: Log, url: string }): Promise<string> {
        try {
            log.info('Downloading file from url.', { url });
            const response = await axios.get(url, { responseType: 'arraybuffer' });
            const contentType = (response.headers['Content-Type'] || response.headers['content-type'])?.toString();
            const filename = decodeURIComponent((response.headers['content-disposition']?.split('filename=')[1]?.replace(/"/g, '') || path.basename(url)));
            const filepath = path.join(this.downloadsFolder, filename);
            log.info('Successfully downloaded file from url.', { url, httpStatus: response.status, httpStatusText: response.statusText, contentType });
            writeFileSync(filepath, response.data, { mode: 0o770 });
            log.info('Successfully saved file', { filepath });
            return filepath;
        } catch (error) {
            log.error('File download failed', { url, error: (error as Error).message || error });
            throw new NonRetryableError(`File download failed: ${(error as Error).message || error}`);
        }
    }

    /**
     * Upload scraped content to storage
     * If isEstimatorRun is true, do not upload content to storage
     */
    private async uploadScrapedContentToStorage({ log, documentId, scrapedContent }: { log: Log, documentId: string, scrapedContent: ScrapedContent }): Promise<string> {
        if (this.request.isEstimatorRun || this.containerClient == null) {
            return '';
        }
        log.info('Uploading scraped content to storage', { documentId });
        const blobClient = this.containerClient.getBlockBlobClient(documentId);
        const data = JSON.stringify(scrapedContent, null, 2);
        await blobClient.upload(data, data.length, { metadata: { deleted: 'false' }, blobHTTPHeaders: { blobContentType: 'application/json' } });
        log.info('Successfully uploaded scraped content to storage', { documentId });
        return blobClient.url;
    }

    /**
     * Send scraped content output to queue
     * If isEstimatorRun is true, do not send single page result to queue
     */
    private async sendOutputToQueue({ log, scraperOutput }: { log: Log, scraperOutput: ScraperOutput }): Promise<string> {
        if (this.request.isEstimatorRun || this.redisQueue == null) {
            return '';
        }
        log.info('Sending notification', { scraperOutput });
        const jobId = `${scraperOutput.id}-${Date.now()}`;
        const jobName = this.relicMessageQueueParams.jobName;
        await this.redisQueue.add(jobName, scraperOutput, { jobId });
        log.info('Successfully sent notification', { jobId });
        return jobId;
    }

    /**
     * Wait for page to load
     */
    private async waitForRedirect({ log, page, url }: { log: Log, page: Page, url: string }) {
        log.info('Waiting for page to load', { url: url });
        await page.waitForLoadState('domcontentloaded');
        await page.waitForTimeout(30 * 1000);
        if (url != page.url()) {
            log.info('Page redirected', { url: url, pageUrl: page.url() });
            return;
        };
        throw new NonRetryableError('Unable to find redirect');
    }

    /**
     * Remove elements from the page
     */
    private async removeElements({ page, log }: { page: Page, log: Log }): Promise<void> {
        try {
            const cssSelectors = this.options.removeElementCssSelector;
            if (!cssSelectors || cssSelectors.length === 0) {
                return;
            }

            await page.evaluate(() => {
                const body = document.body;
                const keepMainElements = body.querySelectorAll(
                    'main, [role="main"], [class*="main"], [id*="main"]'
                );

                const newBody = document.createElement('body');
                keepMainElements.forEach((el) => {
                    newBody.appendChild(el.cloneNode(true));
                });

                document.body.replaceWith(newBody);
            });
            await page.evaluate((selectors) => {
                selectors?.forEach((selector) => {
                    document.querySelectorAll(selector?.trim()).forEach((el) => el.remove());
                });
            }, cssSelectors);
        } catch (err) {
            log.error('RemoveElementsError:', { err });
        }
    }

    /**
     * Check if the page is relevant
     */
    private async isRelevant({ log, url, summary }: { log: Log, url: string, summary: string }): Promise<boolean> {
        if (!this.options.relevanceParams ||
            !this.options.relevanceParams.relevanceApiEndpoint ||
            !this.options.relevanceParams.subject ||
            !this.options.relevanceParams.intent) {
            return true;
        }
        const { relevanceApiEndpoint, subject, intent } = this.options.relevanceParams;
        log.info('Checking relevance of page', { url, subject, intent });
        const payload = {
            subject,
            intent,
            urls: [
                { url, summary: summary.substring(0, 5000) }
            ]
        };
        return await axios.post(relevanceApiEndpoint, payload, { responseType: 'json' })
            .then(response => {
                log.info('Relevance checker result.', { status: response.status, data: response.data });
                if (response.status !== 200) {
                    return false;
                }
                return response.data?.[0].relevant as boolean;
            })
            .catch(err => {
                log.error('Relevance checker failed.', { err });
                return false;
            })
    }

    /**
     * Add children to request queue when parent page processing is successful
     * 
     * For crawler running with sitemap, children will be added to the request queue
     * @param sourceDocumentId - The parent id
     * @param children - The children to add to the request queue
     */
    private async addChildrenToRequestQueue({ log, sourceDocumentId, children }: { log: Log, sourceDocumentId: string, children: URLLike[] }): Promise<void> {
        if (!children || children.length === 0) {
            return;
        }
        log.info('Adding children to request queue', { sourceDocumentId, numChildren: children.length });
        const requestQueue = await RequestQueue.open();
        const addRequests = async function (requestQueue: RequestQueue, currentUrl: URLLike) {
            const id = currentUrl.id || uuidv4();
            const userData: RelicMetaData = {
                id,
                depth: currentUrl.depth || 0,
                sourceDocumentId: sourceDocumentId,
                children: currentUrl.children || []
            }

            await requestQueue.addRequest({
                url: cleanUrl(currentUrl.url),
                method: 'GET',
                label: isFileLink(currentUrl.url) ? 'file' : 'page',
                userData
            })
        }
        const promises = [];
        children.forEach(child => promises.push(addRequests(requestQueue, child)));
        await Promise.allSettled(promises);
        log.info('Successfully added children to request queue', { sourceDocumentId });
    }

    /**
     * Process scrape result
     */
    private async processScrapeResult({ log, scraperOutput }: { log: Log, scraperOutput: ScraperOutput }): Promise<void> {
        log.info('Scrape complete', { scraperOutput });
        await Promise.all([
            this.sendOutputToQueue({ log, scraperOutput }),
            Dataset.pushData(scraperOutput)
        ]);
        this.successfulScrapes++;
    }
} 