export interface TreeNode {
  id: string;
  url: string;
  sourceDocumentId: string;
  children?: TreeNode[];
}

export interface FlatNode {
  id: string;
  url: string;
  sourceDocumentId: string;
}

/**
 * Recursively flattens a tree of TreeNode objects in post-order (children first, then parent).
 * This function is specifically designed to handle the sitemap structure from Justia.
 *
 * @param nodes - Array of TreeNode roots, a single TreeNode, or a sitemap object
 * @returns Flat list of nodes without the `children` property, children appear before their parents
 */
export function flattenTreeFromChildrenToRoot(
  nodes: TreeNode[] | TreeNode | any,
): FlatNode[] {
  // Initialize result array
  const result: FlatNode[] = [];

  // Handle empty or null input
  if (!nodes) {
    console.warn('flattenTreeFromChildrenToRoot: No nodes provided');
    return result;
  }

  // Special case: If this is a sitemap object with a 'sitemap' property that is an array
  if (nodes.sitemap && Array.isArray(nodes.sitemap)) {
    console.log('Processing sitemap object with sitemap array property');
    return flattenTreeFromChildrenToRoot(nodes.sitemap);
  }

  // Normalize input to array
  const nodeArray = Array.isArray(nodes) ? nodes : [nodes];

  // Process each node
  for (const node of nodeArray) {
    // Skip invalid nodes
    if (!node || typeof node !== 'object') {
      console.warn('flattenTreeFromChildrenToRoot: Invalid node:', node);
      continue;
    }

    // Process children first (if any)
    if (
      node.children &&
      Array.isArray(node.children) &&
      node.children.length > 0
    ) {
      // Recursively process children and add them to result
      const childrenFlat = flattenTreeFromChildrenToRoot(node.children);
      result.push(...childrenFlat);
    }

    // Create a flat node without children
    const flatNode: FlatNode = {
      id: node.id || '',
      url: node.url || '',
      sourceDocumentId: node.sourceDocumentId || '',
    };

    // Add the node itself to result
    result.push(flatNode);
  }

  return result;
}

/**
 * Utility function to validate a tree structure and check for common issues
 * @param nodes - Array of TreeNode roots or a single TreeNode
 * @returns Object with validation results
 */
export function validateTreeStructure(nodes: TreeNode[] | TreeNode): {
  valid: boolean;
  issues: string[];
  stats: {
    totalNodes: number;
    maxDepth: number;
    nodesWithoutUrl: number;
    nodesWithoutId: number;
    duplicateIds: string[];
  };
} {
  const issues: string[] = [];
  const stats = {
    totalNodes: 0,
    maxDepth: 0,
    nodesWithoutUrl: 0,
    nodesWithoutId: 0,
    duplicateIds: [] as string[],
  };

  const seenIds = new Set<string>();

  function traverse(node: TreeNode, depth: number = 0) {
    stats.totalNodes++;
    stats.maxDepth = Math.max(stats.maxDepth, depth);

    // Check for missing required properties
    if (!node.id) {
      stats.nodesWithoutId++;
      issues.push(`Node at depth ${depth} is missing an id`);
    } else {
      // Check for duplicate IDs
      if (seenIds.has(node.id)) {
        stats.duplicateIds.push(node.id);
        issues.push(`Duplicate ID found: ${node.id}`);
      } else {
        seenIds.add(node.id);
      }
    }

    if (!node.url) {
      stats.nodesWithoutUrl++;
      issues.push(`Node ${node.id || 'unknown'} is missing a URL`);
    }

    // Traverse children
    if (node.children && node.children.length > 0) {
      for (const child of node.children) {
        traverse(child, depth + 1);
      }
    }
  }

  // Handle array of nodes or single node
  if (Array.isArray(nodes)) {
    for (const node of nodes) {
      traverse(node);
    }
  } else if (nodes) {
    traverse(nodes);
  }

  return {
    valid: issues.length === 0,
    issues,
    stats,
  };
}

/**
 * Alternative implementation of flattening that preserves the parent-child relationship
 * by adding a parentId field to each flattened node
 * @param nodes - Array of TreeNode roots
 * @returns Flat list of nodes with parentId field added
 */
export interface FlatNodeWithParent extends FlatNode {
  parentId?: string;
  depth: number;
}

export function flattenTreeWithParentInfo(
  nodes: TreeNode[],
): FlatNodeWithParent[] {
  const result: FlatNodeWithParent[] = [];

  function traverse(node: TreeNode, parentId?: string, depth: number = 0) {
    // Create a flattened node with parent info
    const { children, ...nodeWithoutChildren } = node;
    const flatNode: FlatNodeWithParent = {
      ...nodeWithoutChildren,
      parentId,
      depth,
    };

    result.push(flatNode);

    // Traverse all children
    if (children && children.length > 0) {
      for (const child of children) {
        traverse(child, node.id, depth + 1);
      }
    }
  }

  // Handle array of nodes
  if (Array.isArray(nodes)) {
    for (const node of nodes) {
      traverse(node);
    }
  } else if (nodes) {
    // Handle single node case
    traverse(nodes as unknown as TreeNode);
  }

  return result;
}

/**
 * Utility function to print a tree structure in a readable format
 * Useful for debugging tree structures
 * @param nodes - Array of TreeNode roots or a single TreeNode
 * @returns String representation of the tree
 */
export function printTreeStructure(nodes: TreeNode[] | TreeNode): string {
  let result = '';

  function traverse(node: TreeNode, depth: number = 0) {
    const indent = '  '.repeat(depth);
    result += `${indent}- ${node.id} (${node.url.substring(0, 30)}${node.url.length > 30 ? '...' : ''})\n`;

    if (node.children && node.children.length > 0) {
      for (const child of node.children) {
        traverse(child, depth + 1);
      }
    }
  }

  if (Array.isArray(nodes)) {
    for (const node of nodes) {
      traverse(node);
    }
  } else if (nodes) {
    traverse(nodes);
  }

  return result;
}
