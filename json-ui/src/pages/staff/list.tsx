import React, { useEffect } from 'react';
import { IUser, IUserIdentity } from 'relic-ui';
import { EmailField, useDataGrid } from '@refinedev/mui';
import {
  useMany,
  CrudSort,
  CrudFilter,
  useOnError,
  BaseRecord,
  useTranslate,
  useInvalidate,
  useGetIdentity,
  DeleteOneResponse,
  IResourceComponentsProps,
} from '@refinedev/core';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Skeleton from '@mui/material/Skeleton';
import { GridColDef, DataGridPro, GridCellParams } from '@mui/x-data-grid-pro';

import { useRouter } from 'src/routes/hooks';

import { useSearch } from 'src/hooks/use-search';
import { useResponsive } from 'src/hooks/use-responsive';

import Label from 'src/components/label';
import { RowActionsPopover } from 'src/components/list';
import { NewButton } from 'src/components/refine-customs/new-btn';
import { DataGridToolbar } from 'src/components/list/data-grid-toolbar';

// ----------------------------------------------------------------------

export const StaffList: React.FC<IResourceComponentsProps> = () => {
  const translate = useTranslate();

  const router = useRouter();

  const invalidate = useInvalidate();

  const upSm = useResponsive('up', 'sm');

  const [viewButtonEl, setViewButtonEl] =
    React.useState<HTMLButtonElement | null>(null);

  const { data: currentUser } = useGetIdentity<IUser>();

  const { mutate: onError } = useOnError();

  const { globalSearch } = useSearch();

  const currentIdentity: IUserIdentity =
    currentUser?.userIdentity as IUserIdentity;

  const currentRole: string = currentIdentity?.role?.name
    ? currentIdentity?.role?.name
    : 'member';

  const handleOnCellClick = (params: GridCellParams) => {
    if (params.field !== 'actions') {
      router.push(`/staff/edit/${params.id}`);
    }
  };

  const handleDelete = React.useCallback(
    (value: DeleteOneResponse<BaseRecord>) => {
      //Need to invalidate the group list since Practitioner list is a Group Membership List
      invalidate({
        resource: 'group',
        invalidates: ['list', 'many'],
      });
    },
    [invalidate],
  );

  const initialSorters: CrudSort[] = React.useMemo(
    () => [
      {
        field: 'name',
        order: 'asc',
      },
    ],
    [],
  );

  const initialFilters: CrudFilter[] = React.useMemo(() => {
    const filter: CrudFilter = {
      field: 'organizationId',
      operator: 'eq',
      value: currentIdentity?.portalIdentity?.organizationId,
    };
    return currentRole === 'member' ? [filter] : [];
  }, [currentIdentity?.portalIdentity?.organizationId, currentRole]);

  // Filter for Members
  const { dataGridProps, setFilters, setCurrent } = useDataGrid({
    meta: {
      resource: 'practitioners',
    },
    sorters: {
      initial: initialSorters,
    },
    filters: {
      permanent: initialFilters,
    },
    queryOptions: {
      enabled: true,
    },
  });

  // Fetch organization names using useMany
  const { data: organizationsData, isLoading: organizationsLoading } = useMany({
    resource: 'organizations',
    ids: dataGridProps.rows.map(row => row.organizationId),
  });

  // useEffect(() => {
  //   try {
  //     setFilters((prevFilters: CrudFilter[]) => {
  //       // Update search filter while retaining others
  //       const updatedFilters = prevFilters.filter(
  //         (filter: CrudFilter) => (filter as LogicalFilter).field !== 'search',
  //       );
  //       updatedFilters.push({
  //         field: 'search',
  //         value: search.length > 0 ? search : '',
  //         operator: 'contains',
  //       });
  //       return updatedFilters;
  //     });
  //     setCurrent(1); // Reset the page number to 1 when the search changes
  //   } catch (error) {
  //     onError(error);
  //   }
  // }, [search, setFilters, setCurrent, onError]); // Ensure dependencies are stable

  const title = translate('staff.titles.list');

  const columns = React.useMemo<GridColDef[]>(
    () => [
      {
        field: 'name',
        flex: 1,
        headerName: translate('staff.fields.name'),
        align: 'left',
        headerAlign: 'left',
      },
      {
        field: 'email',
        flex: 1,
        headerName: translate('staff.fields.email'),
        align: 'left',
        headerAlign: 'left',
        renderCell: function render({ value }) {
          return <EmailField value={value} />;
        },
      },
      {
        field: 'provider',
        flex: 1,
        headerName: translate('staff.fields.provider'),
        maxWidth: 100,
        align: 'left',
        headerAlign: 'left',
        renderCell: ({ value }) => <Box component="span">{value}</Box>,
      },
      {
        field: 'organizationId',
        flex: 1,
        headerName: translate('staff.fields.organizationId'),
        align: 'left',
        headerAlign: 'left',
        renderCell: function render({ value }) {
          if (organizationsLoading) {
            return (
              <Skeleton
                variant="rectangular"
                sx={{ width: 0.5, height: 16, borderRadius: 0.5 }}
              />
            );
          }
          const organization = organizationsData?.data?.find(
            org => org.id === value,
          );
          return organization ? organization.name : value;
        },
      },
      {
        field: 'enabled',
        flex: 1,
        headerName: translate('staff.fields.status'),
        maxWidth: 75,
        align: 'left',
        headerAlign: 'left',
        renderCell: function render({ value }) {
          return (
            <Label variant="soft" color={value ? 'success' : 'error'}>
              {translate(value ? 'status.enabled' : 'status.disabled')}
            </Label>
          );
        },
      },
      {
        field: 'lastLoginDate',
        flex: 1,
        headerName: translate('staff.fields.inviteAccepted'),
        minWidth: 50,
        renderCell: function render({ value }) {
          if (value) {
            return (
              <Label variant="soft" color={(value && 'success') || 'error'}>
                {value ? 'Yes' : 'No'}
              </Label>
            );
          }
        },
      },
      {
        field: 'actions',
        hideable: false,
        headerName: translate('table.actions'),
        sortable: false,
        renderCell: function render({ row }) {
          return (
            <>
              <RowActionsPopover
                rowId={row.id}
                deleteButtonProps={{
                  confirmTitle: translate('staff.titles.delete'),
                  confirmContent: `Are you sure you want to delete ${row.givenName} ${row.surname}?`,
                  resource: 'staff',
                  recordItemId: row.id,
                  onSuccess: handleDelete,
                }}
              />
            </>
          );
        },
        align: 'center',
        headerAlign: 'center',
        minWidth: 10,
      },
    ],
    [translate, organizationsLoading, organizationsData?.data, handleDelete],
  );

  const Toolbar = React.useCallback(
    () => (
      <DataGridToolbar
        setViewButtonEl={setViewButtonEl}
        title={title}
        actions={
          <NewButton
            href={'/staff/create'}
            label={translate('buttons.create')}
          />
        }
      />
    ),
    [title, translate],
  );

  return (
    <Card>
      <Box sx={{ height: 'calc(100vh - 96px)', width: '100%' }}>
        <DataGridPro
          {...dataGridProps}
          pagination
          sx={{
            '& .MuiDataGrid-cell': {
              display: 'flex',
              alignSelf: 'center', // Center aligns the items vertically
              height: '100%', // Ensure the cell takes full height for vertical centering,
              border: 'none',
            },
          }}
          disableColumnMenu
          columns={columns}
          localeText={{ toolbarColumns: 'View' }}
          onCellClick={handleOnCellClick}
          columnVisibilityModel={{
            email: upSm,
            companyId: upSm,
            status: upSm,
          }}
          slots={{
            toolbar: Toolbar,
          }}
          slotProps={{
            panel: {
              anchorEl: viewButtonEl,
            },
            toolbar: {
              onClick: (e: React.MouseEvent) => {
                e.stopPropagation();
                setViewButtonEl(null);
              },
            },
          }}
        />
      </Box>
    </Card>
  );
};
