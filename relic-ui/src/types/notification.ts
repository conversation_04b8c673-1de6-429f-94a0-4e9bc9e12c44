export type NotificationOptions = {
  recipient: string;
  message?: string;
};

export type EmailNotificationOptions = NotificationOptions & {
  subject: string;
  text?: string;
  html: string;
};

export type SmsNotificationOptions = NotificationOptions & {
  enableDeliveryReport: boolean;
  tag?: string;
};

export type WelcomeUserNotificationOptions = {
  email: string;
  mobileNumber: string;
  template: string;
  subject?: string;
  data: any;
};
