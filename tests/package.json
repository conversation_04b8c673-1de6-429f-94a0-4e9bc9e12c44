{"name": "automation", "version": "1.0.0", "description": "", "main": "index.js", "packageManager": "pnpm@10.10.0", "scripts": {"dev:original": "playwright test", "dev:view": "playwright test src/relic-ai-portal/pcc/organization.spec.ts --headed", "test": "playwright test"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@playwright/test": "^1.44.1", "@types/node": "^20.14.2", "github-actions-ctrf": "^0.0.20", "playwright-ctrf-json-reporter": "^0.0.15"}, "dependencies": {"@medplum/core": "^4.0.0", "@medplum/fhirtypes": "^4.0.0", "fs": "0.0.1-security", "path": "^0.12.7"}}