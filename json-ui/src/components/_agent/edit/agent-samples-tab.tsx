import { BaseRecord, useTranslate } from '@refinedev/core';
import React, { useEffect } from 'react';
import { FieldValues, UseFormSetValue } from 'react-hook-form';

import { Box } from '@mui/material';
import { DataGridPro, GridColDef } from '@mui/x-data-grid-pro';

import { Samples } from 'src/types/agent-types';
import AgentDeleteConfirmDialog from './agent-delete-confirm-dialog';
import AgentsRowActionsPopover from './agents-row-actions-popover';

interface Props {
  agentData: BaseRecord | undefined;
  saveButtonProps: any;
  setValue: UseFormSetValue<FieldValues>;
  setShowAddDialog: React.Dispatch<React.SetStateAction<boolean>>;
  samplesRowData: Samples[];
  setSamplesRowData: React.Dispatch<React.SetStateAction<Samples[]>>;
  samplesSelectedRow: Samples | undefined;
  setSamplesSelectedRow: React.Dispatch<
    React.SetStateAction<Samples | undefined>
  >;
  setFormValue: UseFormSetValue<FieldValues>;
}

const AgentSampleTab = ({
  agentData: agentsData,
  saveButtonProps,
  setValue,
  setShowAddDialog,
  samplesRowData,
  setSamplesRowData,
  samplesSelectedRow,
  setSamplesSelectedRow,
  setFormValue,
}: Props) => {
  const translate = useTranslate();
  const [openDel, setOpenDel] = React.useState(false);

  const handleOpenDel = () => {
    setOpenDel(true);
  };

  const handleCloseDel = () => {
    setOpenDel(false);
  };

  const columns: GridColDef[] = [
    {
      field: 'id',
      headerName: '#',
      flex: 0.5,
      sortable: true,
    },
    {
      field: 'userInput',
      headerName: 'User Input',
      flex: 4,
      sortable: true,
    },
    {
      field: 'chatbotResponse',
      headerName: 'Assistant Response',
      flex: 4,
      sortable: true,
    },
    {
      field: 'actions',
      headerName: translate('table.actions'),
      sortable: false,
      renderCell: function render({ row }) {
        return (
          <>
            <AgentsRowActionsPopover
              onEdit={() => handleEditRow(row)}
              onDelete={() => handleDeleteRow(row)}
            />
          </>
        );
      },
      align: 'center',
      headerAlign: 'center',
      minWidth: 80,
    },
  ];

  useEffect(() => {
    const rows = agentsData?.azureAssistantSetup?.fewShotExamples?.map(
      (template: Samples, index: number) => {
        return {
          id: index + 1,
          chatbotResponse: template.chatbotResponse,
          userInput: template.userInput,
        };
      },
    );
    setSamplesRowData(rows || []);
  }, [agentsData, setSamplesRowData]);

  const handleOnCellClick = React.useCallback((params: any) => {
    if (params.field !== 'actions') {
      handleEditRow(params.row as Samples);
    }
  }, []);

  const handleEditRow = (row: Samples) => {
    setSamplesSelectedRow(undefined);
    setSamplesSelectedRow(row);
    setFormValue('chatbotResponse', row.chatbotResponse);
    setFormValue('userInput', row.userInput);
    setShowAddDialog(true);
  };

  const handleDeleteRow = (row: Samples) => {
    setSamplesSelectedRow(undefined);
    setSamplesSelectedRow(row);
    handleOpenDel();
  };

  const handleOnDeleteConfirm = () => {
    const index = samplesRowData.findIndex(
      r => r.id === samplesSelectedRow?.id,
    );
    const newRows = [...samplesRowData];
    newRows.splice(index, 1);
    setSamplesRowData(newRows);
    setValue(
      'azureAssistantSetup.fewShotExamples',
      newRows.map(({ chatbotResponse, userInput }) => ({
        chatbotResponse,
        userInput,
      })),
    );
    saveButtonProps.onClick();
  };

  return (
    <Box sx={{ height: '100%' }}>
      <DataGridPro
        rows={samplesRowData}
        columns={columns}
        headerFilters={false}
        disableColumnMenu
        getRowHeight={() => 'auto'}
        onCellClick={handleOnCellClick}
        pagination
        isRowSelectable={() => false}
        isCellEditable={() => false}
        sx={{
          '& .MuiDataGrid-columnHeader': {
            background: 'transparent',
            borderBottom: '1px solid rgb(229, 229, 229)',
          },
          '& .MuiDataGrid-cell': {
            borderBottom: '1px solid rgb(242, 242, 242)',
          },
        }}
      />

      <AgentDeleteConfirmDialog
        handleCloseDel={handleCloseDel}
        openDel={openDel}
        handleOnDeleteConfirm={handleOnDeleteConfirm}
      />
    </Box>
  );
};

export default AgentSampleTab;
