/* eslint-disable @typescript-eslint/no-explicit-any */
// import { MessageThreadProps, SendBoxProps } from '@azure/communication-react';

import { ToolbarProps } from './toolbar';

export type CitationStyle = {
  tooltip?: {
    backgroundColor?: string;
    textColor?: string;
    borderColor?: string;
    hoverBackgroundColor?: string;
    titleColor?: string;
    linkColor?: string;
  };
  link?: {
    hoverBackgroundColor?: string;
    textColor?: string;
    titleColor?: string;
    linkColor?: string;
  };
  reference?: {
    textColor?: string;
    backgroundColor?: string;
    borderColor?: string;
  };
};

export interface CommunicationPanelProps {
  pxAboveCommunicationPanel?: number;
  messageThreadProps?: any;
  sendBoxProps?: any;
  toolbarProps?: ToolbarProps;
  messageThreadStyles?: {
    chatMessageContainer?: {
      backgroundColor?: string;
      color?: string;
      fontFamily?: string;
      fontSize?: string;
    };
    myChatMessageContainer?: {
      backgroundColor?: string;
      color?: string;
      fontFamily?: string;
      fontSize?: string;
    };
  };
  sendBoxStyles?: {
    textField?: {
      fontFamily?: string;
      fontSize?: string;
    };
  };
  citationStyles?: CitationStyle;
}
