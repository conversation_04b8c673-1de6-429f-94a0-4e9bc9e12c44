[{"id": "f7744fc2-7e0f-40dc-88a0-1806088560ae", "resourceType": "Practitioner", "organizationId": "6d8a08d3-1b3e-44f7-98c1-f36fee9b5a9a", "version": null, "type": "Staff Agent", "active": true, "role": {"practitioner-role": "Training assistant", "display-role": "Training assistant"}, "perspective": "", "azureAssistantSetup": {"chatParameters": {"deploymentName": "relic-openai-0125", "temperature": 1, "frequencyPenalty": 0, "presencePenalty": 0, "maxResponseLength": 10, "topProbabilities": 0.95}, "systemPrompt": "Determine whether the provided Summary or URL aligns with the given Subject and Intent, and provide a response to indicate relevance.\n\n- If **Summary** is present, assess the relevance of the Summary to the Subject and Intent.\n- If **Summary** is not present, rely on the information in the **URL** to determine relevance.\n- Respond with \"YES\" if the Summary or URL is relevant.\n- Respond with \"NO\" if the Summary or URL is not relevant.\n\n# Steps\n\n1. Evaluate the Subject and Intent provided.\n2. Review the Summary (if present) and assess its relevance to the Subject and Intent.\n3. If no Summary is provided, analyze the URL to determine if it aligns with the Subject and Intent.\n4. Make a final relevance determination.\n5. Clearly state the response as either \"YES\" or \"NO.\"\n\n# Output Format\n\nA single word only: \"YES\" or \"NO.\"", "fewShotExamples": [{"chatbotResponse": "YES", "userInput": "Subject: Diabetes Management\nIntent: Find information on healthy diets for diabetics\nSummary: This article provides a list of foods to help manage blood sugar levels effectively."}, {"chatbotResponse": "YES", "userInput": "Subject: FLU\nIntent: Provide awareness regarding flu.\nURL:https://www.cdc.gov/flu/prevent/prevention.htm"}, {"chatbotResponse": "NO", "userInput": "Subject: Mental Health\nIntent: Learn about managing stress in teenagers\nSummary: This page discusses techniques for managing anxiety in working professionals."}]}, "relicAssistantSetup": {"summarizeTokenThreshold": 1800, "maxOutputWords": 3}, "env": "default", "publicData": {"name": "Training Assistant", "role": "training assistant", "bio": "", "activeSince": "", "chats": "", "messages": "", "avatarUrl": "", "coverUrl": ""}}]