name: Build & Deploy Prod Apps

env:
  VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG }}
  VERCEL_TOKEN: ${{ secrets.VERCEL_TOKEN }}

on:
  push:
    branches:
      - 'random-scripts'
      - 'cb1-441-scripts'

jobs:
  json-ui:
    # Relic Facility Portal deployment at https://facility-portal.vercel.app/
    if: github.ref == 'refs/heads/cb1-441-scripts'
    runs-on: ubuntu-latest
    steps:
      - name: 'Checkout from Github'
        uses: actions/checkout@v4

      - name: 'Install node.js'
        uses: actions/setup-node@v2
        with:
          node-version: '22.0.0'

      - name: 'Install pnpm'
        uses: pnpm/action-setup@v4
        with:
          version: '10.10.0'

      - name: 'Install Vercel CLI'
        run: pnpm install -g vercel@latest
        
      - name: 'Build relic ui'
        run: |
          mv .env.for.production .env.production
          export NODE_OPTIONS='--max-old-space-size=8192'
          export NODE_ENV='production'
          pnpm --filter relic-ui... install --frozen-lockfile
          pnpm run build
        working-directory: relic-ui
            
      - name: 'Install json-ui dependencies'
        run: pnpm --filter json-ui... install --frozen-lockfile
        working-directory: json-ui
            
      - name: 'Build & deploy Facility Portal'
        run: |
          mv .env.for.production .env.production
          export NODE_OPTIONS='--max-old-space-size=8192'
          export NODE_ENV='production'
          export VERCEL_PROJECT_ID=${{ secrets.VERCEL_FACILITY_PORTAL }}
          vercel pull --yes --environment=production --token=$VERCEL_TOKEN
          vercel build --prod --token=$VERCEL_TOKEN
          vercel deploy --prebuilt --prod --token=$VERCEL_TOKEN
        working-directory: json-ui
