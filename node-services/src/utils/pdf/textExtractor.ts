import { PDFNet } from '@pdftron/pdfnet-node';
import fs from 'fs';
import path from 'path';
import _ from 'lodash';
import config from 'config';
import LicenseKey from './licenseKey';
import { TranslateDocumentOptions } from './types';
// interface PageLayout {
//   width: number;
//   height: number;
//   margins: {
//     top: string;
//     bottom: string;
//     left: string;
//     right: string;
//   };
//   contentHeight: string;
//   contentWidth: string;
// }

// interface BoundingBox {
//   x1: number;
//   x2: number;
//   y1: number;
//   y2: number;
// }

// interface LineStyle {
//   getColor: () => Promise<number[]>;
//   getFontName: () => Promise<string>;
//   getFontSize: () => Promise<number>;
//   isSerif: () => Promise<boolean>;
// }

// interface Line {
//   getBBox: () => Promise<BoundingBox>;
//   getStyle: () => Promise<LineStyle>;
//   getCurrentNum: () => Promise<number>;
//   getFirstWord: () => Promise<Word>;
// }

// interface Word {
//   isValid: () => Promise<boolean>;
//   getStringLen: () => Promise<number>;
//   getString: () => Promise<string>;
//   getNextWord: () => Promise<Word>;
//   getStyle: () => Promise<LineStyle>;
// }

interface Page {
  getPageHeight: (index: number) => Promise<number>;
  getPageWidth: (index: number) => Promise<number>;
  getVisibleContentBox: () => Promise<{ height: () => Promise<number>, width: () => Promise<number> }>;
}

// interface GeneratedLine {
//   seq?: number;
//   justify?: string;
//   box?: {
//     x1: string;
//     y1: string;
//     x2: string;
//     y2: string;
//   };
//   height?: string;
//   width?: string;
//   spaceBefore?: string;
//   isParaStart?: boolean;
//   text?: string;
//   fontName?: string;
//   fontSize?: string;
//   color?: string;
// }

interface Document {
  getPageIterator: () => Promise<Iterator>;
}

interface Iterator {
  hasNext: () => Promise<boolean>;
  next: () => Promise<void>;
  current: () => Promise<Page>;
}

export const generateDocumentTextBlocks = async function (options: TranslateDocumentOptions) {
  let result: string;
  async function pdf() {
    result = await extractTextsFromDocument(options);
  }
  return PDFNet.runWithCleanup(pdf, LicenseKey)
    .then(() => {
      return result;
    })
    .catch((error: Error) => {
      console.error('Error:', JSON.stringify(error));
      throw error;
    })
    // .finally(async () => {
    //   await PDFNet.shutdown();
    // })
}


async function extractTextsFromDocument(options: TranslateDocumentOptions): Promise<string> {

  const _extractTexts = async (pdfDocument: Document, options: TranslateDocumentOptions): Promise<string> => {
    const outputFilename = options.filenameWithoutExt ?? path.parse(options.sourceFilepath).name;
    const headerFooterLineCount = { header: options.numHeaderLines, footer: options.numFooterLines };
    const reSequenceLines = (page) => {
      //Sort extracted lines based on 
      // 1) top to bottom  = y1 (largest to smallest) 
      // 2) left to right = x1 (smallest to largest)
      const pointErrorTolerance = 2;
      page.lines.sort((a, b) => {
        if (Math.abs(a.box.y1 - b.box.y1) < pointErrorTolerance) {
          return a.box.x1 - b.box.x1;
        }
        return b.box.y1 - a.box.y1;
      });

      let previousLine: any = {};
      let pageHasHeader = true;
      let pageHasFooter = true;
      const pageLines = page.lines.length;
      let _headerFooterLineCount = JSON.parse(JSON.stringify(headerFooterLineCount));
      //Recalculate spaceBefore based on sorted lines
      for (let i = 0; i < pageLines; i++) {
        if (i > 0) {
          const spaceBefore: number = parseFloat((parseFloat(previousLine.box.y1) - parseFloat(page.lines[i].box.y2)).toFixed(2));
          if (spaceBefore > 0) {
            page.lines[i].spaceBefore = spaceBefore;
            _headerFooterLineCount.header--;
          } else {
            page.lines[i].spaceBefore = 0;
          }
        }
        if (pageHasHeader && _headerFooterLineCount.header && _headerFooterLineCount.header > 0) {
          if (parseFloat(page.lines[i].box.y1) > page.layout.height * 0.9) {
            page.lines[i].runningBlock = 'header';
          } else {
            pageHasHeader = false;
          }
        }
        if (_headerFooterLineCount.footer && i === pageLines - 1) {
          let j = i;
          while (pageHasFooter && _headerFooterLineCount.footer && _headerFooterLineCount.footer > 0) {
            if (parseFloat(page.lines[j].box.y1) < page.layout.height * 0.1) {
              page.lines[j].runningBlock = 'footer';
            } else {
              pageHasFooter = false;
            }
            if (page.lines[j].spaceBefore > 0) {
              _headerFooterLineCount.footer--;
            }
            j--;
          }
        }
        //Delete unnecessary fields
        delete page.lines[i].isParaStart;
        previousLine = _.cloneDeep(page.lines[i]);
      }
      return page;
    };

    const twoDigitHex = (num: number): string => {
      const hexStr = num?.toString(16).toUpperCase();
      return ('0' + hexStr).substr(-2);
    };

    const getJustification = (bBox: any, pageLayout: any): string => {
      const pageWidth = pageLayout.width;
      const pageMarginX = parseFloat(pageLayout.margins.left);
      const pageCenter = pageWidth / 2;
      const lineCenter = (bBox.x1 + bBox.x2) / 2;
      if (Math.round(bBox.x1) === Math.round(pageMarginX)) {
        return 'left';
      }
      if (Math.round(pageCenter) === Math.round(lineCenter)) {
        return 'center';
      }
      return 'none';
    };

    // const addLineStyleAttributes = async (s: any): Promise<string> => {
    //   const rgb = await s.getColor();
    //   const rColorVal = await rgb.get(0);
    //   const gColorVal = await rgb.get(1);
    //   const bColorVal = await rgb.get(2);
    //   const rgbHex =
    //     twoDigitHex(rColorVal) + twoDigitHex(gColorVal) + twoDigitHex(bColorVal);
    //   const fontName = await s.getFontName();
    //   const fontSize = await s.getFontSize();
    //   const serifOutput = (await s.isSerif()) ? ' sans-serif; ' : ' ';
    //   const returnString =
    //     ' style="font-family:' +
    //     fontName +
    //     '; font-size:' +
    //     fontSize +
    //     ';' +
    //     serifOutput +
    //     'color:#' +
    //     rgbHex +
    //     ';"';
    //   return returnString;
    // };

    const addLineStyleJson = async (s: any, lineJson: any): Promise<any> => {
      const rgb = await s.getColor();
      const rColorVal = await rgb.get(0);
      const gColorVal = await rgb.get(1);
      const bColorVal = await rgb.get(2);
      const rgbHex =
        twoDigitHex(rColorVal) + twoDigitHex(gColorVal) + twoDigitHex(bColorVal);
      const fontName = (await s.getFontName())?.toLowerCase()?.trim();
      const fontSize = (await s.getFontSize()).toFixed(1);
      const fontWeight = (await s.getWeight());
      const fontItalic = (await s.isItalic() ?? fontName.includes('italic')) ? 'true' : 'false';
      const fontBold = (fontName.includes('bold')) ? 'true' : 'false';
      const serifOutput = (await s.isSerif()) ? ' sans-serif; ' : ' ';
      lineJson.fontName = `${fontName} ${serifOutput}`.trim();
      lineJson.fontSize = fontSize;
      lineJson.fontWeight = fontWeight;
      lineJson.fontItalic = fontItalic;
      lineJson.color = `#${rgbHex}`;
      lineJson.fontBold = fontBold;
      return lineJson;
    };

    const addPageLayoutJson = async (page: any, pageJson: any): Promise<any> => {
      const pageHeight = await page.getPageHeight(0);
      const pageWidth = await page.getPageWidth(0);
      const printSize = await page.getVisibleContentBox();
      const maxMarginY: number = config.get('PAGELAYOUT.MAX_MARGIN_Y');
      const maxMarginX: number = config.get('PAGELAYOUT.MAX_MARGIN_X');

      pageJson.page.layout = {};
      pageJson.page.layout.height = pageHeight;
      pageJson.page.layout.width = pageWidth;
      const marginY = Math.min(((pageHeight - (await printSize.height())) / 2), maxMarginY);
      const pageMarginY = marginY.toFixed(2);
      const marginX = Math.min(((pageWidth - (await printSize.width())) / 2), maxMarginX);
      const pageMarginX = marginX.toFixed(2);

      pageJson.page.layout.contentHeight = (marginY == maxMarginY) ? (pageHeight - parseFloat(pageMarginY)).toFixed(2) : (await printSize.height()).toFixed(2);
      pageJson.page.layout.contentWidth = (marginX == maxMarginX) ? (pageHeight - parseFloat(pageMarginX)).toFixed(2) : (await printSize.width()).toFixed(2);

      pageJson.page.layout.margins = {
        top: pageMarginY,
        bottom: pageMarginY,
        left: pageMarginX,
        right: pageMarginX,
      };
      return pageJson;
    };

    const mayBeList = (str) => {
      const listRegex1 = /^\s*([•o*]|\w[\.)])\s+.+$/;
      const listRegex2 = /^[•\-–—] |^\d+\. |^[a-zA-Z]\. /;
      return listRegex1.test(str) || listRegex2.test(str);
    };

    const mayBeFormField = (str) => {
      //Only if there are 8 or more underscrores followed by a space and a word, we consider it a form field
      const formFieldRegex = /(_{8,}\s+)[0-9A-Za-z]{1,}/;
      if (formFieldRegex.test(str)) {
        return formFieldRegex.test(str);
      }
      return false;
    };

    const isSamePara = (generatedLine: any, previousLine: any, pageLayout: any): boolean => {
      const pointErrorTolerance = 2;
      if (previousLine && previousLine.seq === undefined) {
        return false;
      }
      const emptySpace = parseFloat((pageLayout.contentWidth - previousLine.width).toFixed(
        2
      ));
      // const lastCharacter = previousLine.text.slice(-1);
      // const lastCharacterIsNotFullStop = lastCharacter !== '.';
      // const lastCharacterIsNotUnderScore = lastCharacter !== '_';
      const spaceBeforeMatch =
        previousLine.spaceBefore < 1
          ? Math.abs(generatedLine.spaceBefore - previousLine.spaceBefore) < pointErrorTolerance
          : true;
      const heightMatch =
        Math.abs(generatedLine.height - previousLine.height) < pointErrorTolerance;
      if (mayBeList(generatedLine.text)) {
        return false;
      }
      //If the empty space is less than 100, the last character is not a full stop, the last character is not an underscore,
      //the height and spacing matches with the previous line, then the line is part of the same paragraph.
      if (
        emptySpace < 100 &&
        heightMatch &&
        spaceBeforeMatch
      ) {
        return true;
      }
      return false;
    };


    function isSameLine(generatedLine: any, previousLine: any, pageLayout: any): boolean {
      //if previous line is a list
      const isPreviousLineList = mayBeList(previousLine.text);
      //and current line height + space before is about zero
      const isInSameLine = Math.abs(parseFloat(generatedLine.height) + parseFloat(generatedLine.spaceBefore)) < 3;
      //and right printed edge is within 100 points of page edge
      const rightPrintedEdge = parseFloat(pageLayout.width) - parseFloat(pageLayout.margins.right);
      const isWrappingText = (rightPrintedEdge - parseFloat(generatedLine.box.x2)) < 100;
      return isPreviousLineList && isInSameLine && isWrappingText;
    }


    const splitLine = (wordsInSplitLine, generatedSplitLine, generatedSplitLines) => {
      const firstWord = wordsInSplitLine[0];
      const lastWord = wordsInSplitLine[wordsInSplitLine.length - 1];
      generatedSplitLine.seq = lastWord.seq;
      generatedSplitLine.box = {
        x1: firstWord.box.x1,
        y1: firstWord.box.y1,
        x2: lastWord.box.x2,
        y2: lastWord.box.y2,
      };
      generatedSplitLine.width = (generatedSplitLine.box.x2 - generatedSplitLine.box.x1).toFixed(2);
      generatedSplitLine.text = wordsInSplitLine.map((word) => word.text).join(' ');
      generatedSplitLines.push(_.cloneDeep(generatedSplitLine));
    }

    const generateSplitLineJson = (line, page) => {
      let generatedSplitLines = [];
      //Clone the line to generate split lines by overwriting seq, box, width and text
      let generatedSplitLine = _.cloneDeep(line);
      let lastSplitWord = 0;
      // Loop through the words in page.currentLine array
      for (let i = 0; i < page.currentLine.length; i++) {
        const word = page.currentLine[i];
        const underscoreRegex = /^_+$/;
        //If we encountered a word containing only underscores, it's time to split the line
        if (underscoreRegex.test(word.text)) {
          const wordsInSplitLine = page.currentLine.slice(lastSplitWord, i + 1);
          splitLine(wordsInSplitLine, generatedSplitLine, generatedSplitLines);
          lastSplitWord = i + 1;
        }
      }
      //If there are words left in the line, split them
      if (page.currentLine.length > lastSplitWord) {
        const wordsInSplitLine = page.currentLine.slice(lastSplitWord);
        splitLine(wordsInSplitLine, generatedSplitLine, generatedSplitLines);
      }
      return generatedSplitLines;
    };

    const generateLineJson = async (line: any, previousLine: any, page: any): Promise<any> => {
      let generatedLine: any = {};
      const bBox = await line.getBBox();
      const lineStyle = await line.getStyle();
      // const lineStyleJson = await addLineStyleAttributes(lineStyle);
      generatedLine.seq = await line.getCurrentNum();
      generatedLine.runningBlock = 'body';
      generatedLine.justify = getJustification(bBox, page.layout);
      generatedLine.paraLines = 1;
      generatedLine.box = {
        x1: bBox.x1.toFixed(2),
        y1: bBox.y1.toFixed(2),
        x2: bBox.x2.toFixed(2),
        y2: bBox.y2.toFixed(2),
      };
      generatedLine.height = (bBox.y2 - bBox.y1).toFixed(2);
      generatedLine.width = (bBox.x2 - bBox.x1).toFixed(2);
      generatedLine.spaceBefore = 0;
      generatedLine.isParaStart = false;
      //using Previous line's y1 and current line's y2, calculate space before
      if (previousLine && previousLine.box && previousLine.box.y1) {
        const spaceBefore = (
          previousLine.box.y1 - generatedLine.box.y2
        ).toFixed(2);
        generatedLine.spaceBefore = spaceBefore;
        //Para Start based on space before
        if (!generatedLine.isParaStart) {
          generatedLine.isParaStart = (generatedLine.spaceBefore > generatedLine.height * 1.1);
        }
      }
      generatedLine = await addLineStyleJson(lineStyle, generatedLine);

      let lineWords = [];
      // For each word in the line...
      let currentFontStyle = null;
      let currentFontBox = null;
      let fontStyles = {};
      let currentFontText = [];

      for (
        let word = await line.getFirstWord();
        await word.isValid();
        word = await word.getNextWord()
      ) {
        const wordLength = await word.getStringLen();
        if (wordLength === 0) {
          continue;
        }
        // Generating wordJson along with generateLineJson
        const generatedWord: any = {};
        const wordbBox = await word.getBBox();
        const wordStyle = await word.getStyle();
        const wordStyleJson = await addLineStyleJson(wordStyle, {});
        const wordText = await word.getString();
        generatedWord.box = {
          x1: wordbBox.x1.toFixed(2),
          y1: wordbBox.y1.toFixed(2),
          x2: wordbBox.x2.toFixed(2),
          y2: wordbBox.y2.toFixed(2),
        };
        generatedWord.seq = generatedLine.seq + ((await word.getCurrentNum()) / 100);
        generatedWord.width = (generatedWord.box.x2 - generatedWord.box.x1).toFixed(2);
        generatedWord.text = wordText;
        page.currentLine.push(generatedWord);

        if (currentFontStyle && !_.isEqual(wordStyleJson, currentFontStyle)) {
          fontStyles[currentFontStyle.fontName] = {
            ...currentFontBox,
            text: currentFontText.join(' '),
            fontSize: currentFontStyle.fontSize,
            fontWeight: currentFontStyle.fontWeight,
            fontItalic: currentFontStyle.fontItalic,
            color: currentFontStyle.color,
            fontBold: currentFontStyle.fontBold,
          };
          currentFontBox = {
            x1: wordbBox.x1.toFixed(2),
            y1: wordbBox.y1.toFixed(2),
            x2: wordbBox.x2.toFixed(2),
            y2: wordbBox.y2.toFixed(2),
          };
          currentFontText = [wordText];
        } else {
          if (!currentFontBox) {
            currentFontBox = {
              x1: wordbBox.x1.toFixed(2),
              y1: wordbBox.y1.toFixed(2),
              x2: wordbBox.x2.toFixed(2),
              y2: wordbBox.y2.toFixed(2),
            };
          } else {
            currentFontBox.x2 = wordbBox.x2.toFixed(2);
            currentFontBox.y2 = wordbBox.y2.toFixed(2);
          }
          currentFontText.push(wordText);
        }

        currentFontStyle = wordStyleJson;
        lineWords.push(wordText);
      }

      if (currentFontStyle) {
        fontStyles[currentFontStyle.fontName] = {
          ...currentFontBox,
          text: currentFontText.join(' '),
          fontSize: currentFontStyle.fontSize,
          fontWeight: currentFontStyle.fontWeight,
          fontItalic: currentFontStyle.fontItalic,
          color: currentFontStyle.color,
          fontBold: currentFontStyle.fontBold,
        };
      }
      generatedLine.text = lineWords.join(' ');
      generatedLine.fontStyles = fontStyles;
      //Para Start based on sequential list
      if (!generatedLine.isParaStart) {
        generatedLine.isParaStart = mayBeList(previousLine.text) && mayBeList(generatedLine.text);
      }
      return generatedLine;
    }

    const isDuplicateTextAndIntersectingRect = async (currentLine, previousLine) => {
      if (currentLine.text?.trim() != previousLine.text?.trim()) {
        return false;
      }
      const box1 = currentLine.box;
      const box2 = previousLine.box;
      const rect1 = await PDFNet.Rect.init(parseFloat(box1.x1), parseFloat(box1.y1), parseFloat(box1.x2), parseFloat(box1.y2));
      const rect2 = await PDFNet.Rect.init(parseFloat(box2.x1), parseFloat(box2.y1), parseFloat(box2.x2), parseFloat(box2.y2));
      return (await rect1.intersectRect(rect1, rect2)) == true;
    }

    const generatePageJson = async (page: any): Promise<any> => {
      const textExtractor = await PDFNet.TextExtractor.create();
      textExtractor.begin(page);

      let pdfLine;
      // let paraHeight = -1;
      let previousLine: any = {};
      let outputJson = await addPageLayoutJson(page, { page: {} });
      outputJson.page.lines = [];

      // For each line on the page...
      for (
        pdfLine = await textExtractor.getFirstLine();
        await pdfLine.isValid();
        pdfLine = await pdfLine.getNextLine()
      ) {
        //Treatment for empty lines
        if ((await pdfLine.getNumWords()) === 0) {
          continue;
        }
        outputJson.page.currentLine = [];
        //Generate line json
        const generatedLine = await generateLineJson(
          pdfLine,
          previousLine,
          outputJson.page
        );

        if (await isDuplicateTextAndIntersectingRect(generatedLine, previousLine)) {
          continue;
        }
        //Combine lines for lists with excess tabspace
        if (isSameLine(generatedLine, previousLine, outputJson.page.layout)) {
          previousLine.text += ' ' + generatedLine.text;
          previousLine.width = (parseFloat(previousLine.width) + parseFloat(generatedLine.width)).toFixed(2);
          previousLine.box.x2 = generatedLine.box.x2;
          previousLine.box.y2 = generatedLine.box.y2;
          previousLine.isParaStart = mayBeList(previousLine.text);
          previousLine.paraLines++;
          continue;
        }
        //Combine lines in one paragraph
        if (
          isSamePara(generatedLine, previousLine, outputJson.page.layout) &&
          previousLine.isParaStart &&
          !generatedLine.isParaStart
        ) {
          previousLine.text += ' ' + generatedLine.text;
          previousLine.width =
            generatedLine.width > previousLine.width
              ? generatedLine.width
              : previousLine.width;
          previousLine.box.y1 = generatedLine.box.y1;
          previousLine.box.x2 = `${Math.max(parseFloat(previousLine.box.x2), parseFloat(generatedLine.box.x2))}`;
          previousLine.paraLines++;
          continue;
        }
        //Split lines if they have form fields
        if (generatedLine.paraLines === 1 && mayBeFormField(generatedLine.text)) {
          const generatedLines = generateSplitLineJson(generatedLine, outputJson.page);
          outputJson.page.lines.push(...generatedLines);
          previousLine = generatedLines[generatedLines.length - 1];
          continue;
        }
        outputJson.page.lines.push(generatedLine);
        previousLine = generatedLine;
        delete outputJson.page.currentLine;
      }
      // Since lines & line segments are not always read top to bottom + left to right, we need to resequence them.
      outputJson.page = reSequenceLines(outputJson.page);
      await textExtractor.destroy();
      return outputJson;
    }

    const itr = await pdfDocument.getPageIterator();
    let page;
    let pageCounter = 0;
    let text_blocks_files: string[] = [];

    for (itr; await itr.hasNext(); itr.next()) {
      page = await itr.current();
      if (page.id === '0') {
        console.debug('Page not found.');
        throw new Error('Page not found');
      }
      pageCounter++;
      let outputJson = await generatePageJson(page);

      const jsonFilePath = path.join(options.sourceJsonTextblocksFileDir, `${path.parse(outputFilename).name}-${pageCounter}.json`);

      try {
        await fs.promises.access(options.sourceJsonTextblocksFileDir, fs.constants.F_OK);
        console.debug(`'${options.sourceJsonTextblocksFileDir}' directory exists.`);
      } catch (err) {
        console.debug(`'${options.sourceJsonTextblocksFileDir}' directory does not exist. Creating the directory...`);
        try {
          await fs.promises.mkdir(options.sourceJsonTextblocksFileDir, { recursive: true });
          console.debug(`'${options.sourceJsonTextblocksFileDir}' directory created successfully.`);
        } catch (err) {
          console.error('Error creating directory:', err);
          throw err;
        }
      }

      try {
        await fs.promises.access(jsonFilePath);
        console.debug(`${jsonFilePath} exists.`);
      } catch (err) {
        console.debug(`${jsonFilePath} does not exist. Creating the file...`);
        try {
          fs.writeFileSync(jsonFilePath, '');
          console.debug(`${jsonFilePath} created successfully.`);
        } catch (err) {
          console.error('Error creating file:', err);
          throw err;
        }
      }

      try {
        fs.writeFileSync(jsonFilePath, JSON.stringify(outputJson, null, 2));
        text_blocks_files.push(jsonFilePath);
      } catch (err) {
        console.error('Error writing JSON file:', err);
        throw err;
      }
    }

    return options.sourceJsonTextblocksFileDir;

  }
  try {
    await PDFNet.startDeallocateStack();
    const pdfDocument = await PDFNet.PDFDoc.createFromFilePath(
      `${options.sourceFilepath}`
    );
    pdfDocument.initSecurityHandler();
    const textBlocksDir = await _extractTexts(pdfDocument, options);
    await pdfDocument.destroy();
    await PDFNet.endDeallocateStack();
    return textBlocksDir;
  } catch (err) {
    console.error(err);
    console.error(err.stack);
    throw err;
  }
}

export const cleanUpTextBlocks = async function (options: TranslateDocumentOptions): Promise<boolean> {
  let isChanged: boolean = false;

  const isDuplicateTextAndIntersectingRect = async (currentLine, previousLine) => {
    if (currentLine.text?.trim() != previousLine.text?.trim()) {
      return false;
    }
    const box1 = currentLine.box;
    const box2 = previousLine.box;
    const rect1 = await PDFNet.Rect.init(parseFloat(box1.x1), parseFloat(box1.y1), parseFloat(box1.x2), parseFloat(box1.y2));
    const rect2 = await PDFNet.Rect.init(parseFloat(box2.x1), parseFloat(box2.y1), parseFloat(box2.x2), parseFloat(box2.y2));
    return (await rect1.intersectRect(rect1, rect2)) == true;
  }

  const removeOverlappingTexts = async function (filepath: string) {
    const textblocks = JSON.parse(fs.readFileSync(filepath, { encoding: 'utf-8' }));
    const lines = Array.from(textblocks?.page?.lines);
    let length = lines.length;
    let previousLine = lines.at(0);
    for (let i = 1; i < length;) {
      if (await isDuplicateTextAndIntersectingRect(lines[i], previousLine)) {
        lines.splice(i, 1);
        length = lines.length;
        isChanged = true;
      } else {
        previousLine = lines[i];
        i++
      }
    }

    textblocks.page.lines = lines;
    fs.writeFileSync(filepath, JSON.stringify(textblocks, null, 2));
  }

  async function cleanUp() {
    const pdfDocument = await PDFNet.PDFDoc.create();
    try {
      const textBlockFiles = fs.readdirSync(options.sourceJsonTextblocksFileDir);
      for (let i = 0; i < textBlockFiles.length; i++) {
        await removeOverlappingTexts(path.join(options.sourceJsonTextblocksFileDir, textBlockFiles[i]));
      }
    } catch (e) {
      throw e;
    } finally {
      await pdfDocument.destroy();
    }

  }
  return PDFNet.runWithCleanup(cleanUp, LicenseKey)
    .then(() => {
      return isChanged;
    })
    .catch((error: Error) => {
      console.error('Error:', JSON.stringify(error));
      throw error;
    })
    // .finally(async () => {
    //   await PDFNet.shutdown();
    // })
}