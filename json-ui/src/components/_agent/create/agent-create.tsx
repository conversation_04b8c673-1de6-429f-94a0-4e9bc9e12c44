import { FieldValues } from 'react-hook-form';
import { useForm } from '@refinedev/react-hook-form';
import { RelicAgent, RelicOrganization } from 'relic-ui';
import { useCreate, useTranslate } from '@refinedev/core';

import Stack from '@mui/material/Stack';
import Dialog from '@mui/material/Dialog';
import TextField from '@mui/material/TextField';

import { Create } from 'src/components/refine-customs/create';
import { AgentRoleDropDown } from 'src/components/dropdown/agentRoleDropdown';
import { OrganizationDropdown } from 'src/components/dropdown/organizationDropdown';

type Props = {
  open: boolean;
  onClose: VoidFunction;
  agentList: RelicAgent[];
  currentOrganization?: RelicOrganization;
};

const CreateAgent = ({
  open,
  onClose,
  agentList,
  currentOrganization,
}: Props) => {
  const translate = useTranslate();
  const { mutate } = useCreate();
  const {
    watch,
    control,
    register,
    handleSubmit,
    setError,
    saveButtonProps,
    reset,
    formState: { errors },
    refineCore: { formLoading, queryResult },
  } = useForm();

  const values = watch();

  const onSubmit = (values: FieldValues) => {
    try {
      const customAgents = agentList?.filter((p: RelicAgent) => {
        return (
          p?.role?.['display-role'] === values.role['display-role'] &&
          p?.organizationId === values.organizationId &&
          p?.env === 'custom'
        );
      }) as RelicAgent[];
      const defaultAgents = agentList?.filter((p: RelicAgent) => {
        return (
          p?.role?.['display-role'] === values.role['display-role'] &&
          p?.organizationId !== values.organizationId &&
          p?.env === 'default'
        );
      }) as RelicAgent[];
      const matchingNameAgents = agentList?.filter((p: RelicAgent) => {
        return p?.name === values.agentName;
      }) as RelicAgent[];
      if (!customAgents && !defaultAgents) {
        //This should never occur. If it does, it means the role is not configured in the system.
        setError('role', {
          type: 'manual',
          message:
            'Selected role is not configured. Contact your administrator.',
        });
        return false;
      }
      if (customAgents && customAgents.length > 0) {
        //Raise error since custom agent already exists.
        setError('role', {
          type: 'manual',
          message: 'Selected role is already configured for this organization.',
        });
        return false;
      }
      if (matchingNameAgents && matchingNameAgents.length > 0) {
        //Raise error since agent with same name already exists.
        setError('agentName', {
          type: 'manual',
          message: 'Agent name is in use. Choose a different name.',
        });
        return false;
      }
      const defaultAgent = defaultAgents && defaultAgents[0];
      const updatedValues = {
        ...defaultAgent,
        name: values.agentName,
        organizationId: values.organizationId,
        env: 'custom',
        publicData: {
          ...defaultAgent?.publicData,
          name: values.agentName,
        },
      };

      mutate({
        resource: 'agents',
        values: {
          ...updatedValues,
        },
      });
      reset();
      onClose();
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <Dialog
      fullWidth
      maxWidth="sm"
      open={open}
      onClose={onClose}
      aria-labelledby="alert-dialog-title"
      aria-describedby="alert-dialog-description"
    >
      <Create
        isLoading={formLoading}
        saveButtonProps={{
          ...saveButtonProps,
          disabled: !values.role || !values.organizationId || !values.agentName,
          onClick: handleSubmit(onSubmit),
        }}
        title={translate('agents.titles.create')}
        onClose={onClose}
      >
        <Stack component="form" spacing={2.5} autoComplete="off">
          <OrganizationDropdown
            control={control}
            name="organizationId"
            label={translate('locations.fields.organizationId')}
            error={!!(errors as any)?.organizationId}
            helperText={(errors as any)?.organizationId?.message}
            value={currentOrganization?.id ?? null}
            disabled={!!currentOrganization}
          />

          <AgentRoleDropDown
            control={control}
            name="role"
            label={translate('agents.fields.role')}
            value={values.role ?? null}
            error={!!(errors as any)?.role}
            helperText={(errors as any)?.role?.message}
          />

          <TextField
            {...register('agentName', {
              required: 'This field is required',
            })}
            error={!!(errors as any)?.agentName}
            helperText={(errors as any)?.agentName?.message}
            margin="normal"
            fullWidth
            InputLabelProps={{ shrink: true }}
            type="text"
            label={translate('agents.fields.name')}
            name="agentName"
          />
        </Stack>
      </Create>
    </Dialog>
  );
};

export default CreateAgent;
