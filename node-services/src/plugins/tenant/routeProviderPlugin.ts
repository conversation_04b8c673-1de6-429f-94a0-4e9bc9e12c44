import { FastifyInstance, FastifyPluginAsync, FastifyPluginOptions, FastifyRegisterOptions } from 'fastify';
import pccProvider from '../../routes/pccProvider';
import guestProvider from '../../routes/guestProvider';
import medplumProvider from '../../routes/medplumProvider';
import entraExternalProvider from '../../routes/entraExternalProvider';
import entraProvider from '../../routes/entraProvider';

import { relicRequire } from '../../utils/common-js';
const { RelicHeaderSchema } = relicRequire('relic-ui/schema');

declare module 'fastify' {}

// Data Provider Options
type ProviderOptions = {
  name: string;
  schema?: Record<string, any>;
};
// Add ProviderOptions to RegisterOptions
type ProviderRegisterOptions = FastifyRegisterOptions<ProviderOptions>;

const routeProviderPlugin: FastifyPluginAsync = async (fastify: FastifyInstance, options: FastifyPluginOptions) => {
  fastify.log.info('Route Provider loading...');

  // Register Guest APIs through guestProvider
  fastify.register(guestProvider, {
    ...options,
    name: 'guestProvider',
    schema: {
      tags: ['Guest Services'],
    },
  } as ProviderRegisterOptions);

  // Register Entra External APIs through entraExternalProvider
  fastify.register(entraExternalProvider, {
    ...options,
    name: 'entraExternalProvider',
    schema: {
      tags: ['Entra-External Services'],
      headers: RelicHeaderSchema,
    },
  } as ProviderRegisterOptions);

  // Register MS Entra APIs through entraProvider
  fastify.register(entraProvider, {
    ...options,
    name: 'entraProvider',
    schema: {
      tags: ['Entra Services'],
      headers: RelicHeaderSchema,
    },
  } as ProviderRegisterOptions);

  // Register pcc APIs through pccProvider
  fastify.register(pccProvider, {
    ...options,
    name: 'pccProvider',
    schema: {
      tags: ['PointClickCare Backend Service'],
      headers: RelicHeaderSchema,
    },
  } as ProviderRegisterOptions);

  // Register medplum APIs through medplumProvider
  fastify.register(medplumProvider, {
    ...options,
    name: 'medplumProvider',
    schema: {
      tags: ['Medplum Backend Service'],
      headers: RelicHeaderSchema,
    },
  } as ProviderRegisterOptions);
};

export default routeProviderPlugin;
