import tap from "tap";
import { fastify } from "../index";
import { MedplumClient, MedplumClientOptions, EmailPasswordLoginRequest, ProfileResource } from '@medplum/core';
import { JSDOM } from 'jsdom';
import config from "config";
import { BlobServiceClient, BlockBlobClient } from "@azure/storage-blob";

const { window } = new JSDOM();
global.window = window as unknown as Window & typeof globalThis;
global.document = window.document;
global.Element = window.Element;

const sessionStorageMock: Storage = {
    store: {} as Record<string, string>,
    getItem: function (key: string) {
        return this.store[key] || null;
    },
    setItem: function (key: string, value: string) {
        this.store[key] = value.toString();
    },
    removeItem: function (key: string) {
        delete this.store[key];
    },
    clear: function () {
        this.store = {};
    },
    get length() {
        return Object.keys(this.store).length;
    },
    key: function (index: number) {
        return Object.keys(this.store)[index] || null;
    },
};

global.sessionStorage = sessionStorageMock;

const practitionerEmail: string = '<EMAIL>';
const practitionerPwd: string = 'Z1sZ9N3F8tcycDDK';
let accessToken: any;

const trainingModuleId: string = '37248c99-fb09-4487-85e6-e55cc45fdaea';
const trainingModuleContainerName: string = 'reliccare-default-training-module';
const trainingContentsApiEndpoint: string = `/api/trainings/modules/${trainingModuleId}/contents`;

const medplumOptions: MedplumClientOptions = {
    verbose: true,
};
const medplum: MedplumClient = new MedplumClient(medplumOptions);
const loginToMedplum = async (
    medplum: MedplumClient,
    email: string,
    password: string
): Promise<ProfileResource> => {
    const loginRequest: EmailPasswordLoginRequest = {
        email,
        password,
    };
    const loginResponse = await medplum.startLogin(loginRequest);
    const userProfile = await medplum.processCode(loginResponse.code as string);
    accessToken = medplum.getActiveLogin()?.accessToken.toString();
    return userProfile;
};

let blobServiceClient: BlobServiceClient | null = null;

async function uploadToAzureBlobStorage(file: Buffer, containerName: string, documentId: string): Promise<string> {
    try {
        const accountName: string = config.get('AZURE.TRAINING_STORAGE_ACCOUNT_NAME') as string;
        const accountKey: string = config.get('AZURE.TRAINING_STORAGE_ACCOUNT_KEY') as string;
        blobServiceClient = BlobServiceClient.fromConnectionString(`DefaultEndpointsProtocol=https;AccountName=${accountName};AccountKey=${accountKey};EndpointSuffix=core.windows.net`);
        const containerClient = blobServiceClient.getContainerClient(containerName);
        const blockBlobClient: BlockBlobClient = containerClient.getBlockBlobClient(documentId);
        await blockBlobClient.uploadData(file, {
            blobHTTPHeaders: { blobContentType: 'application/pdf' },
            blockSize: 4 * 1024 * 1024,
            concurrency: 20,
        });
        return blockBlobClient.url;
    } catch (error) {
        throw new Error('Failed to upload file. Please try again later.');
    }
};

tap.teardown(async () => {
    await fastify.close();
    process.exit(0);
});

tap.before(async () => {
    await loginToMedplum(medplum, practitionerEmail, practitionerPwd);
});

let fileContentId = "";
let fileContentRequestPayload = {
    requestType: 'file',
    type: 'application/pdf',
    filename: `(test) ${Date.now()}.pdf`,
    url: '',
    documentId: `(test) ${Date.now()}.pdf`,
    description: `(test) ${Date.now()}`,
};

let urlContentId = "";
const urlContentRequestPayload = {
    requestType: 'url',
    filename: `(test) MDS 3.0 RAI Manual Archive ${Date.now()}`,
    description: `(test) ${Date.now()}`,
    url: 'https://www.cms.gov/medicare/quality/nursing-home-improvement/archive-manuals'
};

tap.test("add training contents with requestType='file' using POST /api/trainings/modules/:id/contents, should return success", async (t: Tap.Test) => {
    const pdfContent = 'This is a sample PDF file content';
    const fileBuffer = Buffer.from(pdfContent);

    try {
        fileContentRequestPayload.url = await uploadToAzureBlobStorage(fileBuffer, trainingModuleContainerName, fileContentRequestPayload.documentId);
    } catch (e) {
        t.fail(e.message);
    }

    const response = await fastify.inject({
        method: "POST",
        url: trainingContentsApiEndpoint,
        headers: ({
            'x-access-token': accessToken
        }),
        payload: fileContentRequestPayload
    });

    t.equal(response.statusCode, 200, 'response status not 200');
    const responseBody = JSON.parse(response.body);
    fileContentId = responseBody.id;
    t.equal(responseBody.status, 'pending', 'content status not pending');
});

tap.test("get content details for requestType=file using GET /api/trainings/modules/:id/contents/:contentId, should return success", async (t: Tap.Test) => {
    if (fileContentId.length == 0) {
        t.fail('training content with requestType=file not created');
    }
    const response = await fastify.inject({
        method: "GET",
        url: `${trainingContentsApiEndpoint}/${fileContentId}`,
        headers: ({
            'x-access-token': accessToken
        }),
    });

    t.equal(response.statusCode, 200, 'response status not 200');
    const responseBody = JSON.parse(response.body);
    t.equal(responseBody.id, fileContentId, 'id mismatch');
    t.equal(responseBody.documentId, fileContentRequestPayload.documentId, 'documentId mismatch');
    t.equal(responseBody.filename, fileContentRequestPayload.filename, 'filename mismatch');
    t.equal(responseBody.description, fileContentRequestPayload.description, 'description mismatch');
    t.equal(responseBody.type, fileContentRequestPayload.type, 'type mismatch');
    t.equal(responseBody.requestType, fileContentRequestPayload.requestType, 'requestType mismatch');
    t.equal(responseBody.url, fileContentRequestPayload.url, 'url mismatch');
});

tap.test("delete content for requestType=file using DEL /api/trainings/modules/:id/contents/:contentId, should return success", async (t: Tap.Test) => {
    if (fileContentId.length == 0) {
        t.fail('training content with requestType=file not created');
    }
    const response = await fastify.inject({
        method: "DELETE",
        url: `${trainingContentsApiEndpoint}/${fileContentId}`,
        headers: ({
            'x-access-token': accessToken
        }),
    });

    t.equal(response.statusCode, 200, 'response status not 200');
});

tap.test("add training contents with requestType='url' using POST /api/trainings/modules/:id/contents, should return success", async (t: Tap.Test) => {
    const response = await fastify.inject({
        method: "POST",
        url: trainingContentsApiEndpoint,
        headers: ({
            'x-access-token': accessToken
        }),
        payload: urlContentRequestPayload,
    });

    t.equal(response.statusCode, 200, 'response status not 200');
    const responseBody = JSON.parse(response.body);
    urlContentId = responseBody.id;
    t.equal(responseBody.status, 'pending', 'content status not pending');
});

tap.test("get content details for requestType=url using GET /api/trainings/modules/:id/contents/:contentId, should return success", async (t: Tap.Test) => {
    if (urlContentId.length == 0) {
        t.fail('training content with requestType=url not created');
    }
    const response = await fastify.inject({
        method: "GET",
        url: `${trainingContentsApiEndpoint}/${urlContentId}`,
        headers: ({
            'x-access-token': accessToken
        }),
    });

    t.equal(response.statusCode, 200, 'response status not 200');
    const responseBody = JSON.parse(response.body);
    t.equal(responseBody.id, urlContentId, 'id mismatch');
    t.equal(responseBody.filename, urlContentRequestPayload.filename, 'filename mismatch');
    t.equal(responseBody.requestType, urlContentRequestPayload.requestType, 'requestType mismatch');
    t.equal(responseBody.url, urlContentRequestPayload.url, 'url mismatch');
});

tap.test("delete content for requestType=url using DEL /api/trainings/modules/:id/contents/:contentId, should return success", async (t: Tap.Test) => {
    if (urlContentId.length == 0) {
        t.fail('training content with requestType=url not created');
    }
    const response = await fastify.inject({
        method: "DELETE",
        url: `${trainingContentsApiEndpoint}/${urlContentId}`,
        headers: ({
            'x-access-token': accessToken
        }),
    });

    t.equal(response.statusCode, 200, 'response status not 200');
});