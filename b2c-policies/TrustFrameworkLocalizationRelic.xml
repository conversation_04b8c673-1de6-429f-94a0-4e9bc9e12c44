<?xml version="1.0" encoding="utf-8" ?>
<TrustFrameworkPolicy xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns:xsd="http://www.w3.org/2001/XMLSchema"
  xmlns="http://schemas.microsoft.com/online/cpim/schemas/2013/06" PolicySchemaVersion="0.3.0.0" TenantId="patientauth.onmicrosoft.com" PolicyId="B2C_1A_TrustFrameworkLocalizationRelic" PublicPolicyUri="http://patientauth.onmicrosoft.com/B2C_1A_TrustFrameworkLocalizationRelic">

  <BasePolicy>
    <TenantId>patientauth.onmicrosoft.com</TenantId>
    <PolicyId>B2C_1A_TrustFrameworkExtensions</PolicyId>
  </BasePolicy>

  <BuildingBlocks>
    <ContentDefinitions>
      <ContentDefinition Id="api.selfasserted">
        <LoadUri>https://relic-facility-portal.vercel.app/lobby/template</LoadUri>
        <RecoveryUri>~/common/default_page_error.html</RecoveryUri>
        <DataUri>urn:com:microsoft:aad:b2c:elements:contract:selfasserted:2.1.21</DataUri>
        <Metadata>
          <Item Key="DisplayName">Collect information from user page</Item>
        </Metadata>
        <LocalizedResourcesReferences MergeBehavior="Prepend">
          <LocalizedResourcesReference Language="en" LocalizedResourcesReferenceId="api.selfasserted.en" />
          <LocalizedResourcesReference Language="hi" LocalizedResourcesReferenceId="api.selfasserted.hi" />
        </LocalizedResourcesReferences>
      </ContentDefinition>
      <ContentDefinition Id="api.localaccount.emailVerification">
        <LoadUri>https://relic-facility-portal.vercel.app/lobby/template</LoadUri>
        <RecoveryUri>~/common/default_page_error.html</RecoveryUri>
        <DataUri>urn:com:microsoft:aad:b2c:elements:contract:selfasserted:2.1.21</DataUri>
        <Metadata>
          <Item Key="DisplayName">Collect information from user page</Item>
        </Metadata>
        <LocalizedResourcesReferences MergeBehavior="Prepend">
          <LocalizedResourcesReference Language="en" LocalizedResourcesReferenceId="api.localaccount.emailVerification.en" />
        </LocalizedResourcesReferences>
      </ContentDefinition>
      <ContentDefinition Id="api.phonefactor">
        <LoadUri>https://relic-facility-portal.vercel.app/lobby/template</LoadUri>
        <RecoveryUri>~/common/default_page_error.html</RecoveryUri>
        <DataUri>urn:com:microsoft:aad:b2c:elements:contract:multifactor:1.2.10</DataUri>
        <Metadata>
          <Item Key="DisplayName">Multi-factor authentication page</Item>
        </Metadata>
      </ContentDefinition>
      <ContentDefinition Id="api.localaccountpasswordreset">
        <LoadUri>https://relic-facility-portal.vercel.app/lobby/template</LoadUri>
      </ContentDefinition>
    </ContentDefinitions>

    <Localization Enabled="true">
      <SupportedLanguages DefaultLanguage="en" MergeBehavior="Append">
        <SupportedLanguage>hi</SupportedLanguage>
      </SupportedLanguages>

      <LocalizedResources Id="api.selfasserted.en">
        <LocalizedStrings>
          <LocalizedString ElementType="ClaimType" ElementId="countryCode" StringId="DisplayName">Phone Number</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="countryCode" StringId="UserHelpText">Phone Number</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="nationalNumber" StringId="DisplayName">Phone without hyphen or spaces</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="nationalNumber" StringId="UserHelpText">Phone number without hyphen or spaces</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfClaimsPrincipalDoesNotExist">We can't seem to find your account. Please try again.</LocalizedString>
        </LocalizedStrings>
      </LocalizedResources>

      <LocalizedResources Id="api.selfasserted.hi">
        <LocalizedStrings>
          <LocalizedString ElementType="ClaimType" ElementId="countryCode" StringId="DisplayName">फ़ोन नंबर</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="countryCode" StringId="UserHelpText">फ़ोन नंबर</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="nationalNumber" StringId="DisplayName">फ़ोन नंबर दर्ज करें</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="nationalNumber" StringId="UserHelpText">हाइफ़न या रिक्त स्थान के बिना फ़ोन नंबर</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfClaimsPrincipalDoesNotExist">हमें आपका खाता नहीं मिल पा रहा है। कृपया पुन: प्रयास करें।</LocalizedString>
        </LocalizedStrings>
      </LocalizedResources>

      <LocalizedResources Id="api.localaccount.emailVerification.en">
        <LocalizedStrings>
          <LocalizedString ElementType="UxElement" StringId="button_continue">Continue</LocalizedString>
        </LocalizedStrings>
      </LocalizedResources>

      <LocalizedResources Id="api.localaccountpasswordreset.en">
        <LocalizedStrings>
          <LocalizedString ElementType="ClaimType" ElementId="email" StringId="DisplayName">Email Address</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="email" StringId="UserHelpText">Email address that can be used to contact you.</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="email" StringId="PatternHelpText">Please enter a valid email address.</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="newPassword" StringId="DisplayName">New Password</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="newPassword" StringId="UserHelpText">Enter new password</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="newPassword" StringId="PatternHelpText">8-16 characters, containing 3 out of 4 of the following: Lowercase characters, uppercase characters, digits (0-9), and one or more of the following symbols: @ # $ % ^ &amp; * - _ + = [ ] { } | \ : ' , ? / ` ~ " ( ) ; .</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="reenterPassword" StringId="DisplayName">Confirm New Password</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="reenterPassword" StringId="UserHelpText">Confirm new password</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="reenterPassword" StringId="PatternHelpText">8-16 characters, containing 3 out of 4 of the following: Lowercase characters, uppercase characters, digits (0-9), and one or more of the following symbols: @ # $ % ^ &amp; * - _ + = [ ] { } | \ : ' , ? / ` ~ " ( ) ; .</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="error_passwordEntryMismatch">The password entry fields do not match. Please enter the same password in both fields and try again.</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="error_fieldIncorrect">One or more fields are filled out incorrectly. Please check your entries and try again.</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="button_continue">Continue</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="button_cancel">Cancel</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfClaimsPrincipalDoesNotExist">An account could not be found for the provided user ID.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfClaimsTransformationBooleanValueIsNotEqual">Your account has been locked. Contact your support person to unlock it, then try again.</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="required_field">This information is required.</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="ver_but_edit">Change e-mail</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="ver_but_resend">Send new code</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="ver_but_send">Send Code</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="ver_but_verify">Verify code</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="ver_fail_code_expired">That code is expired. Please request a new code.</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="ver_fail_no_retry">You've made too many incorrect attempts. Please try again later.</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="ver_fail_retry">That code is incorrect. Please try again.</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="ver_fail_server">We are having trouble verifying your email address. Please enter a valid email address and try again.</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="ver_fail_throttled">There have been too many requests to verify this email address. Please wait a while, then try again.</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="ver_info_msg">Verification code has been sent to your inbox. Please copy it to the input box below.</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="ver_input">Verification code</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="ver_intro_msg">We have the following email on record for you. We can send a code via email to authenticate you.</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="ver_success_msg">E-mail address verified. You can now continue.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="ServiceThrottled">There are too many requests at this moment. Please wait for some time and try again.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfClaimNotVerified">Click on Send Code to verify your email. {0}</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfClaimsPrincipalAlreadyExists">A user with the specified ID already exists. Please choose a different one.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfIncorrectPattern">Incorrect pattern for: {0}</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfInvalidInput">{0} has invalid input.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfMissingRequiredElement">Missing required element: {0}</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfValidationError">Error in validation by: {0}</LocalizedString>
        </LocalizedStrings>
      </LocalizedResources>

    </Localization>

  </BuildingBlocks>

</TrustFrameworkPolicy>