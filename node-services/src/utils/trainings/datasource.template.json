{"name": "{{datasourceName}}", "type": "azureb<PERSON>b", "connectionString": "{{storageConnectionString}}", "container": {"name": "{{containerName}}", "query": ""}, "dataChangeDetectionPolicy": {"odatatype": "#Microsoft.Azure.Search.HighWaterMarkChangeDetectionPolicy", "highWaterMarkColumnName": "metadata_storage_last_modified"}, "dataDeletionDetectionPolicy": {"odatatype": "#Microsoft.Azure.Search.SoftDeleteColumnDeletionDetectionPolicy", "softDeleteColumnName": "deleted", "softDeleteMarkerValue": "true"}}