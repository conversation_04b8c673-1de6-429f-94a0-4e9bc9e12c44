'use strict';
import { IUserIdentity } from "relic-ui";
import { RelicDocument, DocumentUsers } from "@/types/document";
import { isArray } from "lodash";
import { PccPatientCarePlan } from "@/types/pcc";
import { requestContext } from "@fastify/request-context";
import { FastifyInstance, FastifyPluginOptions } from "fastify";

/**
 * Document Hooks for PointClickCare Care Plan Documents
 * @param {any} fastify - Encapsulated Fastify Instance
 * @param {any} options - Fastify options as needed
 */
export default async function (fastify: FastifyInstance, options: FastifyPluginOptions) {

  /**
 * Pre-handler hook for processing Care Plans if patient's documents are requested.
 * 
 * @param {Object} request - The Fastify request object. Contains the incoming request data.
 * @param {Object} reply - The Fastify reply object. Used to send back the HTTP response.
 * 
 */
  fastify.addHook('preHandler', async (req: any, rep: any) => {
    try {
      const { patientId, _search, _start, _end, _order, _sort } = req.query;
      const pageSize = _end - _start;
      const limit = pageSize;
      const skip = _start;
      const searchOptions: any = {
          limit,
          skip,
          search: _search ?? '',
          sort: _sort,
          order: _order,
      };
      const whoami = requestContext.get('whoami') as IUserIdentity;
      //If PCC practitioner is getting patient documents, process care plans.
      if (whoami?.provider === 'pcc' && req.method === 'GET' && patientId) {
        await fastify.processCarePlans(patientId, req.query, searchOptions);
      }
    } catch (error) {
      throw new Error(`Error while processing Care Plans: ${error.message}`);
    }
  });


  /**
 * Preserialization hook for overriding the response payload for English Care Plans.
 * If the query is for a single Care Plan document then override the response payload.
 *
 * @param {string} 'preSerialization' - Name of the hook
 * @param {any} request - fastify request object
 * @param {any} reply - fastify response object
 * @param {any} payload - response payload
 * @param {Function} done - Callback function to continue with the fastify lifecycle.
 */
  fastify.addHook('preSerialization',async  (req: any, rep: any, payload: any) => {
    //If there are multiple documents, do nothing.
    if (isArray(payload)) {
      return payload;
    }
    //If this is not a care plan document, do nothing.
    const relicDocument = payload as RelicDocument;
    if (!relicDocument || !relicDocument.data || !relicDocument.data.carePlan) {
      return payload;
    }
    if (!relicDocument.access.find((access: DocumentUsers) => access.resourceType === 'CarePlan')) {
      return payload;
    }
    let carePlanDocument: RelicDocument = relicDocument;
    const whoami = requestContext.get('whoami') as IUserIdentity;
    if (whoami?.provider === 'pcc') {
      //Check if this is an english care plan
      const carePlan = relicDocument.data?.carePlan as PccPatientCarePlan;
      if (!carePlan || 'en-us' !== relicDocument.language.toLowerCase()) {
        return payload;
      }
      //For english care plans, refresh from PCC if pdf was deleted.
      if (relicDocument.status === 'pending') {
        carePlanDocument = await fastify.getCarePlanDetails(relicDocument);
      }
    }
    return carePlanDocument;
  });
}


