import * as React from 'react';
import { IUser, IUserIdentity } from 'relic-ui';
import { useAutocomplete } from '@refinedev/mui';
import { useForm } from '@refinedev/react-hook-form';
import { Controller, FieldValues } from 'react-hook-form';
import {
  useTranslate,
  useGetIdentity,
  IResourceComponentsProps,
} from '@refinedev/core';

import Stack from '@mui/material/Stack';
import Switch from '@mui/material/Switch';
import Dialog from '@mui/material/Dialog';
import MenuItem from '@mui/material/MenuItem';
import TextField from '@mui/material/TextField';
import Autocomplete from '@mui/material/Autocomplete';
import FormHelperText from '@mui/material/FormHelperText';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import FormControlLabel from '@mui/material/FormControlLabel';

import { useRouter } from 'src/routes/hooks';

import { formatToYYYYMMDD } from 'src/utils/date';

import PhoneInput from 'src/components/phone-input';
import { Create } from 'src/components/refine-customs/create';

// ----------------------------------------------------------------------

export const PatientCreate: React.FC<IResourceComponentsProps> = () => {
  const router = useRouter();

  const translate = useTranslate();

  const [open, setOpen] = React.useState(true);

  const {
    watch,
    control,
    register,
    setValue,
    saveButtonProps,
    formState: { errors },
    refineCore: { formLoading, onFinish },
    handleSubmit,
  } = useForm();

  const values = watch();

  const { data: currentUser } = useGetIdentity<IUser>();
  const currentIdentity: IUserIdentity =
    currentUser?.userIdentity as IUserIdentity;
  const filters: any =
    currentIdentity.role.name === 'member'
      ? [
          {
            field: 'id',
            operator: 'eq',
            value: currentIdentity?.portalIdentity?.organizationId,
          },
        ]
      : [];

  const { autocompleteProps: organizationAutocompleteProps } = useAutocomplete({
    resource: 'organizations',
    filters: filters,
  });

  const { autocompleteProps: linkAutocompleteProps } = useAutocomplete({
    resource: 'links',
  });

  const handleClose = () => {
    setOpen(false);
    router.push('/patients');
  };

  const onSubmit = (values: FieldValues) => {
    const { birthDate } = values;
    const formattedBirthdate = formatToYYYYMMDD(birthDate);
    onFinish({ ...values, birthDate: formattedBirthdate });
  };

  return (
    <Dialog
      fullWidth
      maxWidth="sm"
      open={open}
      onClose={handleClose}
      aria-labelledby="alert-dialog-title"
      aria-describedby="alert-dialog-description"
    >
      <Create
        isLoading={formLoading}
        saveButtonProps={{
          ...saveButtonProps,
          onClick: handleSubmit(onSubmit),
        }}
        onClose={handleClose}
      >
        <Stack spacing={2.5} component="form" autoComplete="off">
          <TextField
            {...register('name', {
              required: 'This field is required',
            })}
            fullWidth
            type="text"
            name="name"
            InputLabelProps={{ shrink: true }}
            label={translate('patients.fields.name')}
            error={!!(errors as any)?.name}
            helperText={(errors as any)?.name?.message}
          />

          <DatePicker
            {...register('birthDate', {
              required: 'This field is required',
            })}
            format="dd/MM/yyyy"
            label={translate('patients.fields.birthDate')}
            onChange={newValue => setValue('birthDate', newValue)}
            value={values.birthDate ? new Date(values.birthDate) : null}
            slotProps={{
              textField: {
                fullWidth: true,
                error: !!errors?.birthDate,
                helperText: (errors as any)?.birthDate?.message,
                InputLabelProps: { shrink: true },
              },
            }}
          />

          <TextField
            {...register('gender', {
              required: 'This field is required',
            })}
            select
            fullWidth
            name="gender"
            value={values.gender || ''}
            InputLabelProps={{ shrink: true }}
            label={translate('patients.fields.gender')}
            error={!!errors?.gender}
            helperText={(errors as any)?.gender?.message}
            SelectProps={{
              displayEmpty: true,
              sx: {
                ...(!values.gender && {
                  color: 'text.disabled',
                }),
              },
            }}
          >
            <MenuItem value="" sx={{ color: 'text.disabled', display: 'none' }}>
              {translate('selects.empty')}
            </MenuItem>

            {[
              { value: 'Male', label: 'selects.gender.male' },
              { value: 'Female', label: 'selects.gender.female' },
              { value: 'Other', label: 'selects.gender.other' },
              { value: 'Unknown', label: 'selects.gender.incognito' },
            ].map(option => (
              <MenuItem key={option.value} value={option.value}>
                {translate(option.label)}
              </MenuItem>
            ))}
          </TextField>

          <TextField
            {...register('maritalStatus', {
              required: 'This field is required',
            })}
            select
            fullWidth
            name="maritalStatus"
            value={values.maritalStatus || ''}
            InputLabelProps={{ shrink: true }}
            label={translate('patients.fields.maritalStatus')}
            error={!!errors?.maritalStatus}
            helperText={(errors as any)?.maritalStatus?.message}
            SelectProps={{
              displayEmpty: true,
              sx: {
                ...(!values.maritalStatus && {
                  color: 'text.disabled',
                }),
              },
            }}
          >
            <MenuItem value="" sx={{ color: 'text.disabled', display: 'none' }}>
              {translate('selects.empty')}
            </MenuItem>
            {[
              { value: 'Unmarried', label: 'selects.maritalStatus.unmarried' },
              { value: 'Married', label: 'selects.maritalStatus.married' },
              { value: 'Annulled', label: 'selects.maritalStatus.annulled' },
              { value: 'Divorced', label: 'selects.maritalStatus.divorced' },
              {
                value: 'Interlocutory',
                label: 'selects.maritalStatus.interlocutory',
              },
              {
                value: 'Legally Separated',
                label: 'selects.maritalStatus.legally_separated',
              },
              {
                value: 'Polygamous',
                label: 'selects.maritalStatus.polygamous',
              },
              {
                value: 'Never Married',
                label: 'selects.maritalStatus.never_married',
              },
              {
                value: 'Domestic partner',
                label: 'selects.maritalStatus.domestic_partner',
              },
              { value: 'Widowed', label: 'selects.maritalStatus.widowed' },
              { value: 'Unknown', label: 'selects.maritalStatus.unknown' },
            ].map(option => (
              <MenuItem key={option.value} value={option.value}>
                {translate(option.label)}
              </MenuItem>
            ))}
          </TextField>

          <TextField
            {...register('email', {
              required: 'This field is required',
              pattern: {
                value: /\S+@\S+\.\S+/,
                message: 'Entered value does not match email format',
              },
            })}
            fullWidth
            type="email"
            name="email"
            InputLabelProps={{ shrink: true }}
            label={translate('patients.fields.email')}
            error={!!(errors as any)?.email}
            helperText={(errors as any)?.email?.message}
          />

          <PhoneInput
            control={control}
            name="mobilePhone"
            label={translate('patients.fields.mobilePhone')}
          />

          <PhoneInput
            control={control}
            name="homePhone"
            label={translate('patients.fields.homePhone')}
          />

          <Controller
            control={control}
            name="link"
            // eslint-disable-next-line
            defaultValue={[] as any}
            render={({ field }) => (
              <Autocomplete
                {...linkAutocompleteProps}
                {...field}
                multiple
                onChange={(_, value) => {
                  field.onChange(value);
                }}
                getOptionLabel={item =>
                  linkAutocompleteProps?.options?.find(
                    p => p?.id?.toString() === item?.id?.toString(),
                  )?.name ?? ''
                }
                isOptionEqualToValue={(option, value) =>
                  value === undefined ||
                  option?.id?.toString() === value?.id?.toString()
                }
                renderInput={params => (
                  <TextField
                    {...params}
                    label={translate('patients.fields.link')}
                    variant="outlined"
                    error={!!(errors as any)?.link?.id}
                    helperText={(errors as any)?.link?.id?.message}
                  />
                )}
              />
            )}
          />

          <Controller
            control={control}
            name="organizationId"
            rules={{ required: 'This field is required' }}
            // eslint-disable-next-line
            defaultValue={null as any}
            render={({ field }) => (
              <Autocomplete
                {...field}
                autoHighlight
                loading={organizationAutocompleteProps.loading}
                options={organizationAutocompleteProps.options}
                onChange={(_, value) => {
                  field.onChange(value?.id ?? value);
                }}
                getOptionLabel={item =>
                  organizationAutocompleteProps?.options?.find(
                    p => p?.id?.toString() === (item?.id ?? item)?.toString(),
                  )?.name ?? ''
                }
                isOptionEqualToValue={(option, value) =>
                  value === undefined ||
                  option?.id?.toString() === (value?.id ?? value)?.toString()
                }
                renderInput={params => (
                  <TextField
                    {...params}
                    label={translate('patients.fields.organizationId')}
                    placeholder={translate('selects.empty')}
                    error={!!(errors as any)?.organizationId}
                    helperText={(errors as any)?.organizationId?.message}
                    InputLabelProps={{ shrink: true }}
                    required
                  />
                )}
              />
            )}
          />

          <div>
            <FormControlLabel
              label={translate('patients.fields.active')}
              control={
                <Switch
                  {...register('active', {
                    value: true,
                  })}
                  name="active"
                  checked={!!values.active}
                />
              }
            />
            {!!errors.active && (
              <FormHelperText error sx={{ px: 2 }}>
                {(errors as any)?.active?.message}
              </FormHelperText>
            )}
          </div>

          <div>
            <FormControlLabel
              label={translate('patients.fields.sendInvite')}
              control={
                <Switch
                  {...register('sendInvite', {
                    value: true,
                  })}
                  name="sendInvite"
                  checked={!!values.sendInvite}
                />
              }
            />
            {!!errors.sendInvite && (
              <FormHelperText error sx={{ px: 2 }}>
                {(errors as any)?.sendInvite?.message}
              </FormHelperText>
            )}
          </div>
        </Stack>
      </Create>
    </Dialog>
  );
};
