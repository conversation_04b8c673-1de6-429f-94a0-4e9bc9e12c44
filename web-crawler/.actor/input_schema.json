{"title": "Relic Crawler Input", "type": "object", "schemaVersion": 1, "properties": {"isEstimatorRun": {"title": "Is Estimator Run", "type": "boolean", "description": "If true, crawler will only estimate URLs", "default": false}, "crawlStorage": {"title": "Storage Configuration", "type": "object", "editor": "json", "description": "Azure Storage configuration for storing crawled data"}, "relicMessageQueueParams": {"title": "Queue Configuration", "type": "object", "editor": "json", "description": "Redis Queue configuration for crawled data"}, "urls": {"title": "URLs to Crawl", "type": "array", "description": "List of URLs to crawl", "editor": "json"}, "options": {"title": "Additional Options", "type": "object", "editor": "json", "description": "Additional crawler configuration options"}}, "required": ["urls"]}