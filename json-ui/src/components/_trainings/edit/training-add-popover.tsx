import { useTranslate } from '@refinedev/core';

import Popover from '@mui/material/Popover';
import MenuItem from '@mui/material/MenuItem';

import { ReturnType as PopoverProps } from 'src/hooks/use-popover';

import Iconify from 'src/components/iconify';

import TrainingModulesAddToTrainingAddDialogue from './training-modules-add-training-dialogue';

type TrainingAddPopoverProps = {
  popover: PopoverProps;
  onTrainingContentAddClick: () => void;
  showAddTrainingContentDialogue: boolean;
  setShowAddTrainingContentDialogue: React.Dispatch<
    React.SetStateAction<boolean>
  >;
};

export default function TrainingAddPopover({
  popover,
  onTrainingContentAddClick,
  showAddTrainingContentDialogue,
  setShowAddTrainingContentDialogue,
}: TrainingAddPopoverProps) {
  const translate = useTranslate();

  return (
    <>
      <Popover
        open={!!popover.open}
        anchorEl={popover.open}
        onClose={popover.onClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        slotProps={{
          paper: {
            sx: {
              mt: 0.5,
              minWidth: 160,
            },
          },
        }}
      >
        <MenuItem onClick={onTrainingContentAddClick}>
          <Iconify width={18} icon={'eva:plus-fill'} sx={{ mr: 1 }} />
          {translate('trainings-modules.label')}
        </MenuItem>
      </Popover>
      {showAddTrainingContentDialogue && (
        <TrainingModulesAddToTrainingAddDialogue
          open={showAddTrainingContentDialogue}
          onClose={() => setShowAddTrainingContentDialogue(false)}
        />
      )}
    </>
  );
}
