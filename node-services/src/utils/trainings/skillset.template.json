{"name": "{{skillset<PERSON>ame}}", "description": "Default Skillset", "skills": [{"odatatype": "#Microsoft.Skills.Text.SplitSkill", "name": "#1", "description": "split document content to chunks", "context": "/document", "defaultLanguageCode": "en", "textSplitMode": "pages", "maximumPageLength": 3000, "pageOverlapLength": 500, "maximumPagesToTake": 0, "inputs": [{"name": "text", "source": "/document/content"}], "outputs": [{"name": "textItems", "targetName": "pages"}]}, {"odatatype": "#Microsoft.Skills.Text.AzureOpenAIEmbeddingSkill", "name": "#2", "description": "generate vectors", "resourceUrl": "{{openai_endpoint}}", "apiKey": null, "deploymentId": "text-embedding-ada-002", "dimensions": 1536, "modelName": "text-embedding-ada-002", "context": "/document/pages/*", "inputs": [{"name": "text", "source": "/document/pages/*"}], "outputs": [{"name": "embedding", "targetName": "contentVector"}], "authIdentity": null}]}