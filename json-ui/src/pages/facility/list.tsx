import React from 'react';
import { useDataGrid } from '@refinedev/mui';
import { IResourceComponentsProps } from '@refinedev/core';

import { GridColDef } from '@mui/x-data-grid-pro';

import { ListFacilities } from 'src/components/_facility';

export const FacilityList: React.FC<IResourceComponentsProps> = () => {
  const { dataGridProps, setFilters } = useDataGrid({
    sorters: {
      initial: [
        {
          field: 'facilityName',
          order: 'asc',
        },
      ],
    },
  });
  const facilityColumns = React.useMemo<GridColDef[]>(() => [], []);
  return (
    <ListFacilities
      {...dataGridProps}
      columns={facilityColumns}
      setFilters={setFilters}
    />
  );
};
