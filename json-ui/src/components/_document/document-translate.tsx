import { useRef, useState, useEffect } from 'react';
import { useForm } from '@refinedev/react-hook-form';
import { Controller, FieldValues } from 'react-hook-form';
import {
  useOne,
  HttpError,
  useCreate,
  useUpdate,
  useCustom,
  useTranslate,
} from '@refinedev/core';
import {
  RelicPatient,
  RelicDocument,
  availableLanguages,
  RelicCommunicationLanguage,
} from 'relic-ui';

import Dialog from '@mui/material/Dialog';
import DoneIcon from '@mui/icons-material/Done';
import FormControl from '@mui/material/FormControl';
import DeleteIcon from '@mui/icons-material/Delete';
import ReplayIcon from '@mui/icons-material/Replay';
import FileOpenIcon from '@mui/icons-material/FileOpen';
import TranslateIcon from '@mui/icons-material/Translate';
import {
  Box,
  Alert,
  Radio,
  Typography,
  RadioGroup,
  DialogTitle,
  DialogContent,
  FormHelperText,
  CircularProgress,
  FormControlLabel,
} from '@mui/material';

import { CarePlanReport } from 'src/pages/report/pcc/care-plan';

import { Edit } from 'src/components/refine-customs/edit';
import { LanguageDropdown } from 'src/components/dropdown/languageDropdown';

interface TranslateDocumentProps {
  open: boolean;
  onClose: VoidFunction;
  selectedDocument: RelicDocument;
  sourceDocument: RelicDocument;
  leafNodes?: RelicDocument[]; // Add a prop to pass leaf nodes
  patient?: RelicPatient;
}

const TranslateDocument = ({
  open,
  onClose,
  selectedDocument,
  sourceDocument,
  leafNodes = [],
  patient,
}: TranslateDocumentProps) => {
  const translate = useTranslate();
  const { mutate } = useCreate();
  const hasRendered = useRef(false);

  const [isPdfError, setIsPdfError] = useState('');

  const { data: selectedDocumentData, isLoading } = useOne({
    resource: 'documents',
    id: selectedDocument.id,
    errorNotification: (error, values) => {
      setIsPdfError(
        `${error?.statusCode} error while getting document - ${error?.message as string}`,
      );
      return false;
    },
  });

  const [reportLabelsData, setReportLabelsData] = useState<any>(null);
  const [isReportLabelsLoading, setIsReportLabelsLoading] = useState<boolean>(false);

  useEffect(() => {
    if (!selectedDocument?.language) return;
    const fetchLabels = async () => {
      setIsReportLabelsLoading(true);
      try {
        const response = await fetch(
          `${window.location.origin}/locales/reports.${selectedDocument.language.toLowerCase()}.json`
        );
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        setReportLabelsData({ data });
      } catch (error: any) {
        setIsPdfError(
          `${error?.status || ''} error while getting document labels - ${error?.message ?? error.toString()}`
        );
        setReportLabelsData(null);
      } finally {
        setIsReportLabelsLoading(false);
      }
    };
    fetchLabels();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedDocument?.language]);

  const {
    mutate: updateSelectedDocument,
    data: updatedSelectedDocument,
    isError: isupdateSelectedDocumentError,
    isSuccess: isupdateSelectedDocumentSuccess,
    isLoading: isupdateSelectedDocumentLoading,
  } = useUpdate<RelicDocument, HttpError>({
    resource: 'documents',
    id: selectedDocument.id,
  });

  const {
    control,
    handleSubmit,
    formState: { errors, isSubmitting },
    refineCore: { formLoading },
  } = useForm<FieldValues>();

  const [selectedDocumentWithPdf, setSelectedDocumentWithPdf] = useState<
    RelicDocument | undefined
  >(undefined);

  const [isClientPdf, setIsClientPdf] = useState(false);

  //use effect (0) to set selectedDocumentWithPdf & isClientPdf. Triggers when the dialog is launched
  useEffect(() => {
    if (selectedDocument?.url) {
      //By default Use Effect (1) is to generate pdf is suppressed.
      //Trigger for pdf generation use effect 1.
      setSelectedDocumentWithPdf(selectedDocument as RelicDocument);
    }
    const clientPdf: string[] = ['CarePlan']; // Add any other resource types that should be generating client pdf
    if (
      selectedDocument.access?.some(access =>
        clientPdf.includes(access.resourceType),
      )
    ) {
      setIsClientPdf(true);
    }
  }, [selectedDocument]);

  // Use effect (1) to generate selected document pdf if the document needs a client pdf.
  // Triggers once when the dialog is rendered if selected document does not have a pdf report url & the document requires client pdf generation.
  useEffect(() => {
    const setSelectedDocumentReportUrl = async (
      relicPatient: RelicPatient,
      relicDocument: RelicDocument,
    ) => {
      //Todo: Will need a generic report function to generate pdf for different document types.
      const reportLabels = reportLabelsData?.data?.labels?.careplan;
      relicDocument = await CarePlanReport(
        relicPatient,
        relicDocument,
        reportLabels,
      );
      updateSelectedDocument({
        values: {
          url: relicDocument.url,
          filename: relicDocument.filename,
          header: 0,
          footer: 0,
        },
        successNotification: false,
      });
    };
    if (
      !isLoading && //Document data is loaded
      !isReportLabelsLoading && //Report labels are loaded
      reportLabelsData?.data && //Report labels are loaded
      !selectedDocumentWithPdf && //Pdf is not yet generated
      !selectedDocument.url && //Document does not have a url
      !hasRendered.current && //Trigger only once
      isClientPdf //Client pdf generation is needed
    ) {
      hasRendered.current = true;
      setSelectedDocumentReportUrl(
        patient as RelicPatient,
        selectedDocumentData?.data as RelicDocument,
      ).catch(error => {
        console.error(
          'Error generating & uploading PDF report to storage -',
          error,
        );
      });
    }
  }, [
    isClientPdf,
    isLoading,
    isReportLabelsLoading,
    patient,
    reportLabelsData?.data,
    selectedDocument,
    selectedDocumentData?.data,
    selectedDocumentWithPdf,
    updateSelectedDocument,
  ]);

  //Use Effect (2) to set the selectedDocumentWithPdf in state.
  useEffect(() => {
    if (!isupdateSelectedDocumentLoading && isupdateSelectedDocumentSuccess) {
      setSelectedDocumentWithPdf(
        updatedSelectedDocument?.data as RelicDocument,
      );
    }
    if (!isupdateSelectedDocumentLoading && isupdateSelectedDocumentError) {
      console.error(
        'Error updating selected document',
        isupdateSelectedDocumentError,
      );
    }
  }, [
    isupdateSelectedDocumentLoading,
    isupdateSelectedDocumentSuccess,
    isupdateSelectedDocumentError,
    updatedSelectedDocument?.data,
  ]);

  const [selectedLanguage, setSelectedLanguage] = useState<string | null>(
    (selectedDocument?.language?.toLowerCase() !== 'en-us' &&
      selectedDocument.language) ||
      null,
  );

  const [translationType, setTranslationType] = useState(
    selectedDocument?.translationType || 'mono',
  );

  const [isTranslationExisting, setIsTranslationExisting] = useState(false);

  useEffect(() => {
    const existingTranslation = leafNodes.find(
      node =>
        node.language === selectedLanguage &&
        node.translationType === translationType,
    );
    setIsTranslationExisting(!!existingTranslation);
  }, [selectedLanguage, translationType, leafNodes]);

  const onSubmit = async (data: FieldValues) => {
    if (isTranslationExisting) {
      return;
    }
    if (
      selectedDocumentWithPdf &&
      selectedDocumentWithPdf.id !== selectedDocumentWithPdf.sourceDocumentId &&
      (selectedDocumentWithPdf.status === 'done' ||
        selectedDocumentWithPdf.status === 'pending' ||
        selectedDocumentWithPdf.status === 'inprogress')
    ) {
      // Do not call mutate if the translation is already done
      onClose();
      return;
    }

    mutate(
      {
        resource: 'documents',
        values: {
          action: 'translate',
          sourceDocumentId: selectedDocument.sourceDocumentId,
          targetLanguage: data?.code?.code,
          translationType: translationType,
        },
        successNotification: () => {
          if (selectedDocument.status === 'failed') {
            return {
              message: translate(
                `Retrying Translation for file ${selectedDocument.filename}`,
                {
                  resource: 'documents',
                },
              ),
              type: 'success',
            };
          } else {
            return {
              message: translate('Successfully started translation.', {
                resource: 'documents',
              }),
              type: 'success',
            };
          }
        },
        errorNotification: error => ({
          message: translate('notifications.createError', {
            resource: 'documents',
            statusCode: error?.statusCode,
          }),
          description: error?.message || 'Unknown error',
          type: 'error',
        }),
      },
      {
        onSuccess: () => {
          onClose(); // Close the dialog
        },
        onError: error => {
          console.error('Translation failed', error);
        },
      },
    );
  };

  const deleteButtonProps = (row: RelicDocument) => {
    const isLeafNode = row.sourceDocumentId && row.sourceDocumentId !== row.id;
    const leafNodesCount = leafNodes.length - 1;

    const getLanguageDisplay = (code: string) => {
      const language = availableLanguages.find(
        (lang: RelicCommunicationLanguage) => lang.code === code,
      );
      return language ? language.display : code;
    };

    const confirmContent = isLeafNode
      ? `This document is "${getLanguageDisplay(row.language)}" translation of ${
          row.filename
        }. Are you sure you want to delete it?`
      : leafNodesCount && leafNodesCount > 0
        ? `This file has ${leafNodesCount} translated files which will also be deleted as a result. Are you sure you want to delete it?`
        : `Are you sure you want to delete ${row.filename}?`;

    return {
      confirmTitle: translate('documents.titles.delete'),
      confirmContent,
      buttonProps: {
        variant: 'outlined',
        color: 'error',
        startIcon: <DeleteIcon />,
      },
      dialogProps: {
        PaperProps: {
          sx: {
            padding: 2,
            borderRadius: 1,
          },
        },
      },
      // NOTE: Enable the inprogress and pending status to show the delete button to delete the document
      // hidden:
      //   selectedDocument.status === 'pending' ||
      //   selectedDocument.status === 'inprogress'
      //     ? true
      //     : false,
      resource: 'documents',
      recordItemId: selectedDocument.id,
      successNotification: () => ({
        message: translate(`Successfully Deleted ${row.filename}`, {}),
        type: 'success' as const,
      }),
      onSuccess: () => {
        onClose();
      },
    };
  };

  const DocumentLink = ({
    url,
    filename,
  }: {
    url: string;
    filename: string;
  }) => (
    <Box
      component="a"
      href={url}
      target="_blank"
      rel="noopener noreferrer"
      sx={{
        display: 'flex',
        alignItems: 'center',
        border: '1px solid',
        borderColor: 'divider',
        borderRadius: 1,
        p: 1,
        mb: 1,
        textDecoration: 'none',
        color: 'text.primary',
        '&:hover': {
          textDecoration: 'underline',
          cursor: 'pointer',
        },
      }}
    >
      <FileOpenIcon sx={{ mr: 1 }} />
      <Typography variant="body2">{filename}</Typography>
    </Box>
  );

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      {isPdfError && isClientPdf && (
        <>
          <DialogTitle>
            {translate('documents.titles.generating_pdf')}
          </DialogTitle>
          <DialogContent>
            <Typography
              component="div"
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                flexDirection: 'column',
                padding: '20px',
                gap: '20px',
              }}
            >
              <Alert severity="error">
                {`Document generation failed for ${selectedDocument.filename}. ${isPdfError}.`}
              </Alert>
            </Typography>
          </DialogContent>
        </>
      )}
      {!selectedDocumentWithPdf && !isPdfError && isClientPdf && (
        <>
          <DialogTitle>
            {translate('documents.titles.generating_pdf')}
          </DialogTitle>
          <DialogContent>
            <Typography
              component="div"
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                flexDirection: 'column',
                padding: '20px',
                gap: '20px',
              }}
            >
              <CircularProgress size={35} color="info" />
              <Alert severity="info">
                {`Preparing ${selectedDocument.filename} document. Please wait...`}
              </Alert>
            </Typography>
          </DialogContent>
        </>
      )}
      {(selectedDocumentWithPdf || !isClientPdf) && (
        <Edit
          isLoading={formLoading}
          deleteButtonProps={deleteButtonProps(selectedDocument)}
          refreshButtonProps={{
            resource: 'documents',
            recordItemId: selectedDocument.id,
            hidden: true,
          }}
          saveButtonProps={{
            onClick: handleSubmit(onSubmit),
            disabled:
              isSubmitting ||
              isTranslationExisting ||
              selectedDocumentWithPdf?.status === 'pending' ||
              selectedDocumentWithPdf?.status === 'inprogress',
            children:
              selectedDocument.id === selectedDocument.sourceDocumentId
                ? 'Translate'
                : selectedDocument.status === 'failed'
                  ? 'Retry'
                  : 'Done',
            startIcon:
              selectedDocument.status === 'failed' ? (
                <ReplayIcon />
              ) : selectedDocument.id === selectedDocument.sourceDocumentId ? (
                <TranslateIcon />
              ) : (
                <DoneIcon />
              ),
          }}
          onClose={onClose}
          title={`${selectedDocument.filename}`}
        >
          <>
            <Typography
              component={'span'}
              variant="subtitle2"
              sx={{
                fontSize: 12,
                color: 'text.secondary',
                display: 'block',
                mb: 0.5,
              }}
            >
              {translate('documents.fields.source')}
            </Typography>
            {sourceDocument && (
              <DocumentLink
                url={sourceDocument.url}
                filename={sourceDocument.filename}
              />
            )}
          </>

          <FormControl fullWidth margin="normal">
            <LanguageDropdown
              control={control}
              name="code"
              label="Translate To"
              value={
                selectedDocument.id !== selectedDocument.sourceDocumentId
                  ? availableLanguages.find(
                      (lang: RelicCommunicationLanguage) =>
                        lang.code === selectedDocument.language,
                    )
                  : availableLanguages.find(
                      (lang: RelicCommunicationLanguage) =>
                        lang.code === selectedLanguage,
                    )
              }
              error={!!(errors as any)?.code}
              helperText={(errors as any)?.code?.message}
              onChange={value => {
                setSelectedLanguage(value?.code || null);
              }}
              disabled={
                selectedDocument.id !== selectedDocument.sourceDocumentId
              }
            />
          </FormControl>

          <FormControl component="fieldset">
            <Typography
              component={'span'}
              variant="subtitle2"
              sx={{
                fontSize: 12,
                color: 'text.secondary',
                display: 'block',
                mb: 0.5,
              }}
            >
              {translate('documents.fields.translationType')}
            </Typography>
            <Controller
              name="translationType"
              control={control}
              defaultValue="mono"
              render={({ field }) => (
                <RadioGroup
                  row
                  aria-labelledby="translation-type-radio-buttons-group"
                  value={translationType}
                  onChange={e =>
                    setTranslationType(e.target.value as 'mono' | 'bilingual')
                  }
                >
                  <FormControlLabel
                    disabled={
                      selectedDocument.id !==
                        selectedDocument.sourceDocumentId || isClientPdf
                    }
                    value="mono"
                    control={<Radio />}
                    label="Retain Original"
                  />
                  <FormControlLabel
                    disabled={
                      selectedDocument.id !==
                        selectedDocument.sourceDocumentId || isClientPdf
                    }
                    value="bilingual"
                    control={<Radio />}
                    label="Modified"
                  />
                </RadioGroup>
              )}
            />
            <FormHelperText>
              Choose “Retain Original” for the translated document to maintain
              your document format.
            </FormHelperText>
          </FormControl>

          {selectedDocument.sourceDocumentId !== selectedDocument.id &&
            selectedDocumentWithPdf &&
            selectedDocumentWithPdf.status !== 'pending' &&
            selectedDocumentWithPdf.status !== 'failed' &&
            selectedDocumentWithPdf.status !== 'inprogress' && (
              <>
                <Typography
                  component={'span'}
                  variant="subtitle2"
                  sx={{
                    fontSize: 12,
                    color: 'text.secondary',
                    display: 'block',
                    mb: 0.5,
                    mt: 2,
                  }}
                >
                  {translate('documents.fields.target')}
                </Typography>
                <DocumentLink
                  url={selectedDocumentWithPdf.url}
                  filename={`${selectedDocumentWithPdf.filename}`}
                />
              </>
            )}

          {selectedDocumentWithPdf?.status &&
            (selectedDocumentWithPdf.status === 'pending' ||
              selectedDocumentWithPdf.status === 'inprogress') && (
              <Alert sx={{ mt: 1 }} severity="info">
                Document generation for {selectedDocumentWithPdf.filename} is in
                progress. It usually takes no more than a minute. Please check
                back in a bit.
              </Alert>
            )}
          {selectedDocumentWithPdf?.status &&
            selectedDocumentWithPdf.status === 'failed' && (
              <Alert severity="error">
                The translation for {selectedDocumentWithPdf.filename} failed.
                retry. {selectedDocumentWithPdf?.errorDetails}
              </Alert>
            )}
          {isTranslationExisting && (
            <Alert severity="warning">
              Translation with {selectedLanguage} and{' '}
              {translationType === 'mono' ? 'Retain Original' : 'Modified'}{' '}
              formatting already exists.
            </Alert>
          )}
        </Edit>
      )}
    </Dialog>
  );
};

export default TranslateDocument;
