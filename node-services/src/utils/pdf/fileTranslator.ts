import { cleanUpTextBlocks, generateDocumentTextBlocks } from './textExtractor';
import path from 'path';
import { translateTextBlocks } from './textTranslator';
import { printDocument } from './documentPrinter';
import * as fs from 'fs';
import { FontTargetLangMapValue, downloadFonts, getDocumentFontsInfo } from './fonts';
import { TranslateDocumentOptions, TranslateDocumentResult, } from './types';

export type { TranslateDocumentOptions, TranslateDocumentResult };

export async function generateTranslatedDocument(options: TranslateDocumentOptions): Promise<TranslateDocumentResult> {
    try {
        options.filenameWithoutExt = path.parse(options.sourceFilepath).name;
        options.sourceLang = options.sourceLang.toLowerCase();
        options.targetLang = options.targetLang.toLowerCase();
        options.translatedJsonTextblocksFileDir = path.join(options.workingDirectory, "translated_textblocks", options.filenameWithoutExt);
        let sourceTextBlocksUpdated: boolean = false;

        let doGenerateTextblocks = false;
        let translatedPdfPath: string;
        if (options.sourceJsonTextblocksFileDir?.length == 0 || !fs.existsSync(options.sourceJsonTextblocksFileDir)) {
            options.sourceJsonTextblocksFileDir = path.join(options.workingDirectory, "source_textblocks", options.filenameWithoutExt);
            doGenerateTextblocks = true;
        }

        if (doGenerateTextblocks) {
            await generateDocumentTextBlocks(options);
        } else {
            sourceTextBlocksUpdated = await cleanUpTextBlocks(options);
        }

        await translateTextBlocks(options);

        let requiredFonts: Map<string, FontTargetLangMapValue> = null;
        if (options.translationType == 'bilingual') { //for type=mono, we let apryse manage fonts
            requiredFonts = await getDocumentFontsInfo(options);
            await downloadFonts(options, requiredFonts);
        }

        translatedPdfPath = await printDocument(options, requiredFonts);
        return {
            ...options,
            translatedFilepath: translatedPdfPath,
            sourceTextBlocksUpdated: sourceTextBlocksUpdated
        }
    } catch (err) {
        console.error(err);
        console.error(err.stack);
        throw err;
    }
}
