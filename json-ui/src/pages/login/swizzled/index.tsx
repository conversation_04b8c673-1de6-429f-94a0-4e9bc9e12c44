import React from 'react';

import { AuthProps } from 'src/types';
import { AzureB2CLoginPage } from './login';

/**
 * **refine** has a default auth page form served on the `/login` route when the `authProvider` configuration is provided.
 * @see {@link https://refine.dev/docs/api-reference/mui/components/mui-auth-page/} for more details.
 */
export const AuthPage: React.FC<AuthProps> = props => {
  return <AzureB2CLoginPage {...props} />;
};
