/* eslint-disable @typescript-eslint/no-explicit-any */
import Fuse from 'fuse.js';
import { useUserContext } from '../components/RelicUserProvider';
import { useThreadContext } from '../components/RelicThreadProvider';
import { usePropsFor as _usePropsFor, MessageThread, MessageThreadProps, SendBox } from '@azure/communication-react';
import type { SendMessageOptions } from '@azure/communication-chat';
import { extractDisplayNameFromUserIdentity } from '../utils/userUtils';
import { formatTimestampForChatMessage } from '../utils/DateTime';
import { CitationWidgetData, WidgetData } from '../types/widget';
import { AnnouncementWidget, CitationWidget, DefaultMessage, SuggestionWidget } from '../components';

/*
 * Options for MessageThread
 */
const onRenderMessage = (messageProps: any, defaultOnRender: any) => {
  const metadataType = messageProps.message.metadata?.type;
  if (metadataType === 'chat') {
    return <DefaultMessage defaultOnRender={defaultOnRender} messageProps={messageProps} />;
  }
  if (metadataType === 'voice') {
    return <DefaultMessage defaultOnRender={defaultOnRender} messageProps={messageProps} />;
  }
  if (metadataType === 'widget') {
    const widgetData: WidgetData = JSON.parse(messageProps.message.metadata?.widgetData);
    const widget = widgetData?.widget;
    if (widget === 'AnnouncementWidget') {
      return <AnnouncementWidget messageProps={{ message: { ...messageProps?.message, messageType: 'system' } }} />;
    } else if (widget === 'SuggestionWidget') {
      return (
        <SuggestionWidget
          defaultOnRender={defaultOnRender}
          messageProps={messageProps}
          widgetData={widgetData}
          onWidgetClick={
            (suggestion: any, widgetData: any) => {
              console.log('onWidgetClick', suggestion, widgetData);
            } // Send Message
          }
        />
      );
    } else if (widget === 'CitationWidget') {
      return (
        <CitationWidget
          defaultOnRender={defaultOnRender}
          messageProps={messageProps}
          widgetData={widgetData as CitationWidgetData}
        />
      );
    }
  }

  if (metadataType === 'debug') {
    return <></>;
  }

  // Support for legacy messages or unknown metadataType
  return defaultOnRender ? defaultOnRender(messageProps) : <></>;
};

const fuseOptions = {
  keys: ['label', 'altText'],
  threshold: 1.0,
};

/*
 * Wrapper for usePropsFor
 */
type SendBoxPropsDefault = Record<string, unknown> & {
  onSendMessage: (content: string, options?: SendMessageOptions) => Promise<void>;
}; // onSendMessage in SendBoxProps does not have options parameter

/**
 * Helper to determine translation requirements based on sender's resource type and thread language
 */
const getMessageTranslationConfig = (
  resourceType: string | undefined,
  patientLanguage: { code: string; display: string },
) => {
  const isEnglishThread = patientLanguage.code
    .toLowerCase()
    .replace('_', '-')
    .match(/^en(-|$)/);

  if (isEnglishThread) {
    return { needsTranslation: false };
  }

  if (resourceType === 'Practitioner') {
    // Practitioner always writes in English, translate to patient language
    return {
      needsTranslation: true,
      translateTo: patientLanguage.code,
      originalLocale: 'en-US',
    };
  } else {
    // Patient writes in their language, translate to English
    return {
      needsTranslation: true,
      translateTo: 'EN-US',
      originalLocale: patientLanguage.code,
    };
  }
};

export const usePropsFor = (component: any) => {
  /*
   * Case 1. SendBox
   */
  if (component === SendBox) {
    const sendBoxProps = _usePropsFor(component) as unknown as SendBoxPropsDefault;

    const { topic, thread, translate, callConnectionId } = useThreadContext();
    const { myIdentity } = useUserContext();
    const patientLanguage = thread?.threadSubject?.patientLanguage || { code: 'en-US', display: 'English' };

    const onSendMessage = async (content: string) => {
      if (!sendBoxProps) {
        return;
      }

      if (topic?.interactiveWidget) {
        // Handle interactive widget responses
        const requestMessageId = topic.interactiveWidget.requestMessageId;
        const widgetRequest = topic.interactiveWidget.widgetData;

        // find the best matching option in case the button was not clicked and text was typed.
        const fuse = new Fuse(widgetRequest.requestOptions, fuseOptions);
        const result = fuse.search(content);
        const suggestion = result[0];

        const widgetResponse = {
          type: 'WidgetResponse',
          widget: 'SuggestionWidget',
          requestMessageId: requestMessageId,
          action: widgetRequest.action,
          actionParams: widgetRequest.actionParams,
          requestedById: widgetRequest.requestedById, // messageProps.message.senderId,
          requestOptions: widgetRequest.requestOptions,
          response: suggestion,
        };
        const options = {
          senderDisplayName: '', // no display name for user
          metadata: {
            type: 'widget',
            callConnectionId: callConnectionId as string,
            widgetData: JSON.stringify(widgetResponse),
          },
        };
        sendBoxProps.onSendMessage(content, options);
        return;
      }

      const translationConfig = getMessageTranslationConfig(myIdentity?.resourceType, patientLanguage);

      if (!translationConfig.needsTranslation) {
        // No translation needed - send message in original language
        const options = {
          senderDisplayName: myIdentity ? extractDisplayNameFromUserIdentity(myIdentity) : '',
          metadata: {
            type: 'chat',
            callConnectionId: callConnectionId as string,
          },
        };
        sendBoxProps.onSendMessage(content, options);
      } else {
        // Translation needed
        const translateResult = await translate(content, translationConfig.translateTo as string);
        const options = {
          senderDisplayName: myIdentity ? extractDisplayNameFromUserIdentity(myIdentity) : '',
          metadata: {
            type: 'chat',
            callConnectionId: callConnectionId as string,
            messageIntl: JSON.stringify({
              content:
                translationConfig.originalLocale === patientLanguage.code ? content : translateResult.translation,
              locale: translationConfig.originalLocale,
            }),
          },
        };

        const messageToSend =
          translationConfig.originalLocale === patientLanguage.code ? translateResult.translation : content;

        sendBoxProps.onSendMessage(messageToSend ?? 'TRANSLATION_ERROR', options);
      }
    };

    if (sendBoxProps) {
      return { ...sendBoxProps, onSendMessage };
    }
    return sendBoxProps;
  } else if (component === MessageThread) {
    /*
     * Case 2. MessageThread
     */
    const messageThreadProps: MessageThreadProps = _usePropsFor(component, 'chat') as unknown as MessageThreadProps;

    // Translating "CallNotificationWidget" messages to "system" messages for display purposes.
    messageThreadProps.messages = messageThreadProps.messages.map((message: any) => {
      const widgetData =
        message?.metadata && message?.metadata?.widgetData ? JSON.parse(message.metadata.widgetData) : undefined;
      if (widgetData?.widget === 'CallNotificationWidget') {
        return {
          ...message,
          messageType: 'system',
          systemMessageType: 'content',
          iconName: 'Phone',
          content: `${formatTimestampForChatMessage(message.createdOn, new Date())}    ${message.content} `,
        };
      } else {
        return {
          ...message,
        };
      }
    });
    const newMessageThreadProps = {
      ...(messageThreadProps as Record<string, unknown>),
      onRenderMessage,
    };

    return newMessageThreadProps;
  }

  /*
   * Case 3. Other components
   */
  return _usePropsFor(component);
};
