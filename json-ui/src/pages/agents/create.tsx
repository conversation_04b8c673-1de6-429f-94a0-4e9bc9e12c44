import React from 'react';
import { useDataGrid } from '@refinedev/mui';
import { IUser, RelicAgent, IUserIdentity } from 'relic-ui';
import { CrudFilter, useGetIdentity } from '@refinedev/core';

import { useRouter } from 'src/routes/hooks';

import CreateAgent from 'src/components/_agent/create/agent-create';

export const AgentsCreate = () => {
  const [open, setOpen] = React.useState(true);
  const router = useRouter();
  const { data: currentUser } = useGetIdentity<IUser>();
  const currentIdentity: IUserIdentity =
    currentUser?.userIdentity as IUserIdentity;

  const currentRole: string = currentIdentity?.role?.name
    ? currentIdentity?.role?.name
    : 'member';

  const initialFilters: CrudFilter[] = React.useMemo(() => {
    const filter: CrudFilter = {
      field: 'organizationId',
      value: currentIdentity?.portalIdentity?.organizationId,
      operator: 'eq',
    };
    return currentRole === 'admin' ? [] : [filter];
  }, [currentIdentity?.portalIdentity?.organizationId, currentRole]);

  const { dataGridProps: listAgentProps } = useDataGrid<RelicAgent>({
    resource: 'agents',
    sorters: {
      initial: [
        {
          field: 'name',
          order: 'asc',
        },
      ],
    },
    filters: {
      permanent: initialFilters,
    },
  });

  const handleCLose = () => {
    setOpen(false);
    router.push('/agents');
  };

  return (
    <CreateAgent
      open={open}
      onClose={handleCLose}
      agentList={listAgentProps.rows as RelicAgent[]}
    ></CreateAgent>
  );
};
