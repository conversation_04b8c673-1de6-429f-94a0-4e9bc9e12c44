import { Type, Static } from '@sinclair/typebox';
import { CommunicationIdentity } from './communication'; // import CommunicationIdentity
import { IRole } from './portal';
import { IdentityProviderSchema, ListQuerySchema } from './relicBase';

// RelicPractitioner schema and type
export const RelicPractitionerSchema = Type.Object({
  id: Type.String(),
  name: Type.String(),
  resourceType: Type.Literal('Practitioner'),
  enabled: Type.Boolean(),
  organizationId: Type.String(),
  email: Type.Optional(Type.Union([Type.String({ format: 'email' }), Type.Null()])), // Because in MongoDB, it is already initialized as null.
  mobilePhone: Type.Optional(Type.String()),
  provider: IdentityProviderSchema,
  position: Type.Optional(Type.String()),
});

// Use CommunicationIdentity[] for the type
export type RelicPractitioner = Static<typeof RelicPractitionerSchema> & {
  communicationIdentities?: CommunicationIdentity[],
  role: IRole,
};

// PractitionerFilters schema and type
export const PractitionerFiltersSchema = Type.Object({
  organizationId: Type.Optional(Type.String()),
  active: Type.Optional(Type.Boolean()),
  name: Type.Optional(Type.String()),
  email: Type.Optional(Type.String()),
  mobilePhone: Type.Optional(Type.String()),
});
export type PractitionerFilters = Static<typeof PractitionerFiltersSchema> & Static<typeof ListQuerySchema> & {
  id?: {
    $in?: string[];
    $nin?: string[];
  };
};
