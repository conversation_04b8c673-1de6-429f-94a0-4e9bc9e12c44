import React, { useContext } from 'react';
import { EditButtonProps } from '@refinedev/mui';
import { RefineButtonTestIds, RefineButtonClassNames } from '@refinedev/ui-types';
import {
  useCan,
  useLink,
  useResource,
  useTranslate,
  useNavigation,
  useRouterType,
  useRouterContext,
  AccessControlContext,
} from '@refinedev/core';

import Button from '@mui/material/Button';
import EditOutlined from '@mui/icons-material/EditOutlined';

// ----------------------------------------------------------------------

export const EditButton: React.FC<EditButtonProps> = ({
  resource: resourceNameFromProps,
  resourceNameOrRouteName,
  recordItemId,
  hideText = false,
  accessControl,
  svgIconProps,
  meta,
  children,
  onClick,
  ...rest
}) => {
  const accessControlContext = useContext(AccessControlContext);

  const accessControlEnabled =
    accessControl?.enabled ?? accessControlContext.options.buttons.enableAccessControl;

  const hideIfUnauthorized =
    accessControl?.hideIfUnauthorized ?? accessControlContext.options.buttons.hideIfUnauthorized;
  const translate = useTranslate();

  const routerType = useRouterType();
  const Link = useLink();
  const { Link: LegacyLink } = useRouterContext();

  const ActiveLink = routerType === 'legacy' ? LegacyLink : Link;

  const { editUrl: generateEditUrl } = useNavigation();

  const { id, resource } = useResource(resourceNameFromProps ?? resourceNameOrRouteName);

  const { data } = useCan({
    resource: resource?.name,
    action: 'edit',
    params: { id: recordItemId ?? id, resource },
    queryOptions: {
      enabled: accessControlEnabled,
    },
  });

  const disabledTitle = () => {
    if (data?.can) return '';
    else if (data?.reason) return data.reason;
    else return translate('buttons.notAccessTitle', "You don't have permission to access");
  };

  const editUrl =
    resource && (recordItemId ?? id) ? generateEditUrl(resource, recordItemId! ?? id!, meta) : '';

  const { sx, ...restProps } = rest;

  if (accessControlEnabled && hideIfUnauthorized && !data?.can) {
    return null;
  }

  return (
    <ActiveLink
      to={editUrl}
      replace={false}
      onClick={(e: React.MouseEvent<HTMLButtonElement, MouseEvent>) => {
        if (data?.can === false) {
          e.preventDefault();
          return;
        }
        if (onClick) {
          e.preventDefault();
          onClick(e);
        }
      }}
      style={{ textDecoration: 'none' }}
    >
      <Button
        disabled={data?.can === false}
        startIcon={!hideText && <EditOutlined sx={{ selfAlign: 'center' }} {...svgIconProps} />}
        title={disabledTitle()}
        sx={{ minWidth: 0, ...sx }}
        data-testid={RefineButtonTestIds.EditButton}
        className={RefineButtonClassNames.EditButton}
        {...restProps}
      >
        {hideText ? (
          <EditOutlined fontSize="small" {...svgIconProps} />
        ) : (
          children ?? translate('buttons.edit', 'Edit')
        )}
      </Button>
    </ActiveLink>
  );
};
