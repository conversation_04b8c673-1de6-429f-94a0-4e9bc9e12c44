import { S } from 'fluent-json-schema';
import { FastifyInstance, FastifyPluginOptions, FastifyReply, FastifyRequest } from 'fastify';
import { conditionToRelicCondition, relicConditionToCondition } from '../../../../../types/relicPatientConversion';
import { PatientEverything, RelicCondition } from 'relic-ui';

/**
 * Condition Routes
 * Note: Temporary implementation for CB1-345 completion
 * This needs to be reviewed and implemented in separate ticket
 *
 * @param fastify
 * @param options
 * @param next
 */
export default async function conditionRoutes(fastify: FastifyInstance, opts: FastifyPluginOptions) {
  fastify.post(
    '/',
    {
      schema: {
        ...opts.schema,
        params: S.object().prop('id', S.string().description('patient ID')),
        body: S.object()
          .prop('note', S.string())
          .prop('code', S.object().prop('text', S.string().required()))
          .prop('clinicalStatus', S.string().default('Active'))
          .prop('verificationStatus', S.string().default('Confirmed'))
          .prop('onsetDateTime', S.string())
          .prop('abatementDateTime', S.string()),
      },
    },
    async function (req: FastifyRequest, rep: FastifyReply) {
      const { id } = req.params as { id: string };
      const body = req.body as object;
      //Get patient details
      const patientEverything: PatientEverything = await req.server.fetchPatientById(id, rep);
      const condition = relicConditionToCondition(
        { ...body, resourceType: 'Condition' } as RelicCondition,
        patientEverything,
      );
      const service = req.server;
      const updatedCondition = await service.fhirClient().createResource(condition);
      return conditionToRelicCondition(updatedCondition);
    },
  );

  fastify.get(
    '/',
    {
      schema: {
        ...opts.schema,
        description: 'returns patient condition based on patient id',
        summary: 'Get patient condition',
        params: S.object().prop(
          'id',
          S.string().description(
            'Sample value: e2f42379-170b-4393-95bc-1ff02177a671\nWhere to get: id field present in patients collection',
          ),
        ),
      },
    },
    async function (req: FastifyRequest, rep: FastifyReply) {
      const { id } = req.params as { id: string };
      const service = req.server;
      const conditions = await service.fhirClient().searchResources('Condition', { subject: id });
      return conditions.map((condition) => conditionToRelicCondition(condition));
    },
  );

  //TODO: To be fixed. conditionId is missing from req.params. This route will return error
  fastify.delete(
    '/:conditionId',
    {
      schema: {
        ...opts.schema,
        description: 'Delete patient condition based on patient id and condition id',
        summary: 'Delete patient condition',
        params: S.object()
          .prop(
            'id',
            S.string().description(
              'Sample value: e2f42379-170b-4393-95bc-1ff02177a67\nWhere to get: id field present in patients collection ',
            ),
          )
          .prop('conditionId ', S.string().description('Sample value: ?\nWhere to get: ?')),
      },
      onRequest: async function (req: FastifyRequest, rep: FastifyReply) {
        /**
         * NOTE: [HACK] cb1-803
         * [Observation] req.params.conditionId is lost during handler execution
         * [Possible culprit] Fastify-autoload + nested folder + dynamic routing (_id/conditions/:conditionId to (/:id/conditions/:conditionId))
         * conditions is kept inside patients folder since conditions is relevant to a patient only
         * req.params.conditionId is available in onRequest but gets lost before reaching the handler
         * [Workaround] Store the original params in onRequest and restore them in preHandler
         * [Additional Note] For this reason, we can't use 'required()' for req.params.conditionId in the route schema
         */
        const params = { ...(req.params as object) };
        req['originalParams'] = params;
      },
      preHandler: async function (req: FastifyRequest, rep: FastifyReply) {
        req.params = { ...(req.params as object), ...(req['originalParams'] || {}) };
      },
    },
    async function (req: FastifyRequest, rep: FastifyReply) {
      const { conditionId } = req.params as { id: string; conditionId: string };
      if (!conditionId) {
        throw req.server.httpErrors.badRequest('condition ID is required');
      }
      const service = req.server;
      return await service.fhirClient().deleteResource('Condition', conditionId);
    },
  );
}
