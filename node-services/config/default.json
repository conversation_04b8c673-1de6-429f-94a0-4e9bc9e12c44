{"SERVER": {"HOST": "0.0.0.0", "PORT": 3000, "SSL": {"KEY": "../../certs/reliccare.com.********.key", "CERT": "../../certs/reliccare.com.********.crt", "CA": "../../certs/reliccare.com.ca.********.crt"}, "FRONTEND_URL": "https://relic-facility-portal.vercel.app", "AGENTMESSENGER_URL": "https://agent-messenger.calmbay-07fbcdc7.eastus.azurecontainerapps.io", "LOCALHOST_AGENTMESSENGER_URL": "https://63f3-107-139-17-205.ngrok-free.app"}, "AZURE": {"DOCUMENT_STORAGE_ACCOUNT_NAME": "facility", "DOCUMENT_STORAGE_ACCOUNT_KEY": "****************************************************************************************", "TRAINING_STORAGE_ACCOUNT_NAME": "relicstorage", "TRAINING_STORAGE_ACCOUNT_KEY": "****************************************************************************************", "AI_SEARCH_ENDPOINT": "https://relic-ai-search.search.windows.net", "AI_SEARCH_API_KEY": "aQUTqH2T30Uo6OJXIBt1FvESC6jfjzmviOdZ1bjiSuAzSeAZUNpZ", "AI_SEARCH_VERSION": "2024-07-01", "COGNITIVE_SERVICE_ENDPOINT": "https://relic-multi-service.cognitiveservices.azure.com/", "OPENAI_ENDPOINT": "https://relic-openai-north-central.openai.azure.com/", "OPENAI_API_KEY": "********************************", "OPENAI_CHAT_MODEL": "relic-openai-0329", "OPENAI_EMBEDDING_MODEL": "relic-ada", "SUBSCRIPTION_ID": "56b648be-c67e-4ae0-a13d-1e471401974f", "AADB2C": {"SECRET": "pzgJFgjP4KueO4MoOxIPzutD8mIQznnPVybv7FcIQ70=", "CLIENT_ID": "06f2ba7d-cd60-4ab6-8643-5bc673fbec8f", "CLIENT_SECRET": "****************************************", "TENANT_ID": "a4090150-6b4a-4842-b0e5-6f3ea36197f2", "AUTHORITY": "https://login.microsoftonline.com/a4090150-6b4a-4842-b0e5-6f3ea36197f2", "CLIENT_OBJECT_ID": "d59922b2-c0ef-4aa1-b8ef-445cc6f42c4a", "GRAPH_MICROSOFT_URL": "https://graph.microsoft.com", "ISSUER_URL": "patientauth.onmicrosoft.com", "PRACTITIONER_GROUP_ID": "f9cc1d89-a8a1-437d-b507-1a752a8c5244"}, "ENTRA": {"CLIENT_ID": "0e32f196-3cca-448a-b80f-fcfe5f0657ff", "CLIENT_SECRET": "****************************************"}}, "COSMOSDB": {"USER": "relic-dev", "PASSWORD": "5V4fj9SFbSKMxGTlsHLTUv88VfFn2xfgidb4GfFetg9IgFgVVysUENKWCyPr7yP875egNEyRcU1tACDboT5tlA==", "DB_NAME": "relic-care", "HOST": "relic-dev.mongo.cosmos.azure.com", "PORT": "10255"}, "DEEPL": {"API_URL": "https://api.deepl.com/v2/", "API_KEY": "a05f4cb4-4c41-5b0e-15fd-7a75a88c17f7"}, "PCC": {"API_URL": "https://connect.pointclickcare.com", "API_URL_CERT_BASED": "https://connect2.pointclickcare.com", "CLIENT_ID": "iWd1YrOnyWgfV2WSJqkTBXawFFANnhAd", "CLIENT_SECRET": "ADAWV3caVc1ZOoKK", "LOCALHOST_CLIENT_ID": "********************************", "LOCALHOST_CLIENT_SECRET": "LBjSpfHA412j8KBh", "BASEURLS": ["https://connect.pointclickcare.com/api/public/preview1/orgs", "https://connect2.pointclickcare.com/api/public/preview1/orgs"]}, "MEDPLUM": {"API_URL": "https://api.medplum.com", "CLIENT_ID": "0194811a-0960-7768-b865-2b273fdcad8f", "CLIENT_SECRET": "52079bedae5f69273ce0599b6382765d8b1d575090e33323848a186a2ba3c925", "LOCALHOST_CLIENT_ID": "1940c3ef-8b6f-4ac0-a66c-d6aca3bff786", "LOCALHOST_CLIENT_SECRET": "0151de6a95f61082b358ca61618dcc422b47d46701a499811927d86710c560b7"}, "PATIENT": {"DELETE_RETRY_LIMIT": 3, "B2C_TOKEN_JWKS_URI": "https://patientauth.b2clogin.com/patientauth.onmicrosoft.com/b2c_1a_signin_email_relic/discovery/v2.0/keys", "B2C_TOKEN_AUDIENCE": "06f2ba7d-cd60-4ab6-8643-5bc673fbec8f", "B2C_TOKEN_SCOPES": "node-services"}, "ORGANIZATION": {"DELETE_RETRY_LIMIT": 3}, "MAIL": {"SENDGRID": {"FROM": "<EMAIL>", "API_KEY": "*********************************************************************"}, "AZURE": {"FROM": "<EMAIL>", "CONNECTION": "endpoint=https://yusuke-test-3.unitedstates.communication.azure.com/;accesskey=hg/CU1wbjnStIz8MMmF+EuilijvdqRCF4RBiUO0l/DJCvhblrZVkCsmLyvUqRe2WdrCHZ8+M7sOHGgmoU+PNxA=="}}, "RELIC": {"ENV": "development"}, "OBFUSCATION": {"FIELDS": [{"field": "name"}, {"field": "name_1"}, {"field": "name_2"}, {"field": "name_3"}, {"field": "email", "type": "email"}]}, "ACS": {"ENDPOINT": "https://yusuke-test-3.unitedstates.communication.azure.com/", "ACCESS_KEY": "hg/CU1wbjnStIz8MMmF+EuilijvdqRCF4RBiUO0l/DJCvhblrZVkCsmLyvUqRe2WdrCHZ8+M7sOHGgmoU+PNxA==", "PHONENUMBER": "+18552380470", "SMS_MAX_RETRY": 3}, "1940c3ef-8b6f-4ac0-a66c-d6aca3bff786": {"type": "ehr", "provider": "medplum", "secret": "0151de6a95f61082b358ca61618dcc422b47d46701a499811927d86710c560b7"}, "872fe0a8-fda8-49c7-b9ef-0bfadb8d509b": {"type": "ehr", "provider": "medplum", "secret": "929ba3325478e49f360bf86f5ad9c35fe7f0c0e4d5db1c5029711f3eeac96f59"}, "FHIR": {"URL": "http://hl7.org/fhir/"}, "AZURE_TRANSLATION": {"ENDPOINT": "https://api.cognitive.microsofttranslator.com", "API_KEY": "0ee1f02ab62440c6b4dd743646621dbc", "LOCATION": "eastus"}, "RELICCARE": {"ORG_ID": "6d8a08d3-1b3e-44f7-98c1-f36fee9b5a9a"}, "PAGELAYOUT": {"MAX_MARGIN_Y": 50, "MAX_MARGIN_X": 80}, "RELICCARE_AGENTS": {"DEFAULT_PATIENT_AGENT": "Sydney", "DEFAULT_STAFF_AGENT": "Florence", "DEFAULT_AGENTS": ["Sydney", "Florence", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]}, "REDIS": {"PORT": 6380, "HOST": "agent-messenger.redis.cache.windows.net", "PASSWORD": "pqw0sxMMC6BqkNWNIVT8XBY3orTgyK2kyAzCaM2djg8=", "TLS_SERVERNAME": "agent-messenger.redis.cache.windows.net", "MESSAGE_QUEUES": [{"WORKER_TYPE": "DOCUMENT_TRANSLATOR", "QUEUE": "document-translator-queue-development", "CONCURRENCY": 1}, {"WORKER_TYPE": "TRAINING_CONTENT_WRITER", "QUEUE": "training-content-writer-queue-development", "CONCURRENCY": 1}, {"WORKER_TYPE": "TRAINING_CONTENT_ESTIMATOR", "QUEUE": "training-content-estimator-queue-development", "CONCURRENCY": 1}]}, "REPORTS": {"CAREPLAN": {"IGNORE_FIELDS": ["id", "language", "facId", "goalId", "createdDate", "nextReviewDate", "revisionDate", "initiatedDate", "targetDate", "revisionBy", "created<PERSON>y"]}}, "OPENAI": {"ENDPOINT": "https://relic-openai-east.openai.azure.com/", "API_KEY": "********************************", "VERSION": "2024-06-01"}, "BITLY": {"TOKEN": "****************************************", "URL": "https://api-ssl.bitly.com/v4/shorten"}, "APIFY": {"TOKEN": "**********************************************"}, "DOCRAPTOR": {"API_KEY": "3Bmtnzz0LZf5WuQhjK-a"}, "FLOWISE": {"API_URL": "https://dev-flowise.icyocean-18a34833.westus2.azurecontainerapps.io/api/v1/", "API_KEY": "bWzSSEJ5GhU7IWu7mlHtUWvarxRJrDMBnxIAEbG_lLA"}, "RELEVANCE_CHECKER_URL": "https://agent-messenger.calmbay-07fbcdc7.eastus.azurecontainerapps.io/trainings/check-relevance"}