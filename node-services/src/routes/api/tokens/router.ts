import { FastifyInstance } from "fastify";
import { S } from "fluent-json-schema";
import config from "config";

export default function (instance: FastifyInstance, options: any, next: any) {
  instance.get('/azure-sas', {
    schema: {
      ...options.schema,
      tags: ['Token Service'],
      querystring: S.object()
        .prop('url', S.string().description("Azure Storage blob URL. If available, accountName and containerName are ignored."))
        .prop('accountName', S.string().description("Azure Storage Account Name. This is a mandatory field if 'url' is not provided."))
        .prop('containerName', S.string().description("Azure Storage container name. Default is organization id. For trainingContent, use trainingModule > storage > containerName, see trainingModules db collection.")),
      description: 'Get an azure storage SAS Token with Read,Write,Create permissions valid for 10mins',
      summary: 'Get an azure storage SAS Token'
    }
  }, async function (req: any, rep: any) {
    const { url, accountName, containerName } = req.query;
    if (url && url.length > 0) {
      const blobUrl = new URL(url);
      const accountName = blobUrl.hostname.split('.')[0];
      const containerName = blobUrl.pathname.split('/')[1];
      const token: string = await req.server.generateAzureStorageSASToken({ containerName, accountName, permissions: 'r' });
      return { token };
    }
    if (!accountName || !containerName) {
      throw req.server.httpErrors.badRequest('Account & Container names are required if URL is not provided');
    }
    if (accountName !== 'facility' && accountName !== 'relicstorage') {
      throw req.server.httpErrors.badRequest('Invalid account name. Allowed values are facility or relicstorage');
    }
    const token: string = await req.server.generateAzureStorageSASToken({
      accountName: accountName,
      containerName: containerName,
    });
    return { token }
  });

  instance.get('/docraptor', {
    schema: {
      tags: ['Token Service'],
      description: 'Get the DocRaptor API key',
      summary: 'Get DocRaptor API key'
    }
  }, async function (req: any, rep: any) {
    const token = config.get('DOCRAPTOR.API_KEY') as string;
    return { token };
  });

  next();
}