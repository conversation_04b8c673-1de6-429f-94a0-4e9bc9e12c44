import {
  AccessControlContext,
  pickNotDeprecated,
  useCan,
  useDelete,
  useMutationMode,
  useResource,
  useTranslate,
  useWarnAboutChange,
} from '@refinedev/core';
import { DeleteButtonProps as RefineDeleteButtonProps } from '@refinedev/mui';
import {
  RefineButtonClassNames,
  RefineButtonTestIds,
} from '@refinedev/ui-types';
import React, { useContext } from 'react';

import DeleteOutline from '@mui/icons-material/DeleteOutline';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';

// ----------------------------------------------------------------------

type DeleteButtonProps = Partial<RefineDeleteButtonProps> & {
  confirmContent?: string;
  openDel?: boolean;
  onOpenDel?: VoidFunction;
  onCloseDel?: VoidFunction;
};

const DeleteButton: React.FC<DeleteButtonProps> = ({
  resource: resourceNameFromProps,
  resourceNameOrRouteName,
  recordItemId,
  children,
  hideText = false,
  accessControl,
  svgIconProps,
  // More
  onOpenDel,
  ...rest
}) => {
  const accessControlContext = useContext(AccessControlContext);

  const accessControlEnabled =
    accessControl?.enabled ??
    accessControlContext.options.buttons.enableAccessControl;

  const hideIfUnauthorized =
    accessControl?.hideIfUnauthorized ??
    accessControlContext.options.buttons.hideIfUnauthorized;
  const translate = useTranslate();

  const { id, resource } = useResource(
    resourceNameFromProps ?? resourceNameOrRouteName,
  );

  const { isLoading, variables } = useDelete();

  const { data } = useCan({
    resource: resource?.name,
    action: 'delete',
    params: { id: recordItemId ?? id, resource },
    queryOptions: {
      enabled: accessControlEnabled,
    },
  });

  const disabledTitle = () => {
    if (data?.can) return '';
    else if (data?.reason) return data.reason;
    else
      return translate(
        'buttons.notAccessTitle',
        "You don't have permission to access",
      );
  };

  const { sx, ...restProps } = rest;

  if (accessControlEnabled && hideIfUnauthorized && !data?.can) {
    return null;
  }

  return (
    <Button
      color="error"
      onClick={onOpenDel}
      disabled={data?.can === false}
      loading={(recordItemId ?? id) === variables?.id && isLoading}
      startIcon={!hideText && <DeleteOutline {...svgIconProps} />}
      title={disabledTitle()}
      loadingPosition={hideText ? 'center' : 'start'}
      data-testid={RefineButtonTestIds.DeleteButton}
      className={RefineButtonClassNames.DeleteButton}
      sx={{ ...sx }}
      {...restProps}
    >
      {hideText ? (
        <DeleteOutline fontSize="small" {...svgIconProps} />
      ) : (
        (children ?? translate('buttons.delete', 'Delete'))
      )}
    </Button>
  );
};

// ----------------------------------------------------------------------

const DeleteDialog: React.FC<DeleteButtonProps> = ({
  resource: resourceNameFromProps,
  resourceNameOrRouteName,
  recordItemId,
  onSuccess,
  mutationMode: mutationModeProp,
  successNotification,
  errorNotification,
  accessControl,
  meta,
  metaData,
  dataProviderName,
  confirmTitle,
  confirmOkText,
  confirmCancelText,
  invalidates,
  // More
  confirmContent,
  openDel,
  onCloseDel,
}) => {
  const accessControlContext = useContext(AccessControlContext);

  const accessControlEnabled =
    accessControl?.enabled ??
    accessControlContext.options.buttons.enableAccessControl;

  const hideIfUnauthorized =
    accessControl?.hideIfUnauthorized ??
    accessControlContext.options.buttons.hideIfUnauthorized;
  const translate = useTranslate();

  const { id, resource, identifier } = useResource(
    resourceNameFromProps ?? resourceNameOrRouteName,
  );

  const { mutationMode: mutationModeContext } = useMutationMode();

  const mutationMode = mutationModeProp ?? mutationModeContext;

  const { mutate } = useDelete();

  const { data } = useCan({
    resource: resource?.name,
    action: 'delete',
    params: { id: recordItemId ?? id, resource },
    queryOptions: {
      enabled: accessControlEnabled,
    },
  });

  const handleCloseOnConfirm = () => {
    if ((recordItemId ?? id) && identifier) {
      setWarnWhen(false);
      onCloseDel?.();
      mutate(
        {
          id: recordItemId ?? id ?? '',
          resource: identifier,
          mutationMode,
          successNotification,
          errorNotification,
          meta: pickNotDeprecated(meta, metaData),
          metaData: pickNotDeprecated(meta, metaData),
          dataProviderName,
          invalidates,
        },
        {
          onSuccess: value => {
            onSuccess && onSuccess(value);
          },
        },
      );
    }
  };

  const { setWarnWhen } = useWarnAboutChange();

  if (accessControlEnabled && hideIfUnauthorized && !data?.can) {
    return null;
  }

  return (
    <Dialog
      fullWidth
      maxWidth="xs"
      open={!!openDel}
      onClose={onCloseDel}
      aria-labelledby="alert-dialog-title"
      aria-describedby="alert-dialog-description"
    >
      <DialogTitle id="alert-dialog-title">
        {confirmTitle ?? translate('buttons.confirm', 'Are you sure?')}
      </DialogTitle>

      {confirmContent && (
        <DialogContent sx={{ p: 3 }}>{confirmContent}</DialogContent>
      )}

      <DialogActions>
        <Button onClick={onCloseDel} variant="outlined">
          {confirmCancelText ?? translate('buttons.cancel', 'Cancel')}
        </Button>
        <Button variant="contained" onClick={handleCloseOnConfirm} autoFocus>
          {confirmOkText ?? translate('buttons.delete', 'Delete')}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

// ----------------------------------------------------------------------

export type ConfirmDeleteDialogProps = {
  rowId?: string;
  hideText?: boolean;
  deleteButtonProps?: RefineDeleteButtonProps & {
    confirmContent?: string;
  };
};

export function ConfirmDeleteDialog({
  rowId,
  hideText,
  deleteButtonProps,
}: ConfirmDeleteDialogProps) {
  const [openDel, setOpenDel] = React.useState(false);

  const handleOpenDel = () => {
    setOpenDel(true);
  };

  const handleCloseDel = () => {
    setOpenDel(false);
  };

  return (
    <>
      {deleteButtonProps && !deleteButtonProps.hidden ? (
        <>
          <DeleteButton
            hideText={hideText}
            recordItemId={rowId}
            size="small"
            onOpenDel={handleOpenDel}
            sx={{ minWidth: 'auto' }}
          />

          <DeleteDialog
            openDel={openDel}
            onCloseDel={handleCloseDel}
            {...deleteButtonProps}
          />
        </>
      ) : null}
    </>
  );
}
