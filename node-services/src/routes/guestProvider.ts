import path from 'path'
import config from 'config'
import proxy from '@fastify/http-proxy'
import { FastifyHttpProxyOptions } from '@fastify/http-proxy'
import { FastifyInstance, FastifyPluginOptions, FastifyPluginAsync } from 'fastify'
import fastifyAutoload from '@fastify/autoload'

const guestProvider: FastifyPluginAsync = async (fastify: FastifyInstance, opts: FastifyPluginOptions) => {

    fastify.log.info('Guest Routes loading...');
    // PCC OAuth proxy. Documented in src/plugins/external/swagger.ts > transform function
    fastify.register(proxy, {
        upstream: `${config.get('PCC.API_URL')}/auth`,
        prefix: '/api/pcc/auth',
        httpMethods: ['GET', 'POST'],
        replyOptions: {
            rewriteRequestHeaders: (originalReq: any, headers: any) => ({
                Authorization: `Basic ${Buffer.from(config.get('PCC.CLIENT_ID') + ':' + config.get('PCC.CLIENT_SECRET')).toString(
                    'base64'
                )}`,
                'Content-Type': 'application/x-www-form-urlencoded'
            })
        }
    } as FastifyHttpProxyOptions)

    // PCC OAuth proxy for localhost DEVs. Used during PCC login. Documented in src/plugins/external/swagger.ts > transform function
    fastify.register(proxy, {
        upstream: `${config.get('PCC.API_URL')}/auth`,
        prefix: '/local/pcc/auth',
        httpMethods: ['GET', 'POST'],
        config: {
            schema: {
                tags: ['PCC Localhost Auth'],
            },
        },
        replyOptions: {
            rewriteRequestHeaders: (originalReq: any, headers: any) => ({
                Authorization: `Basic ${Buffer.from(
                    config.get('PCC.LOCALHOST_CLIENT_ID') + ':' + config.get('PCC.LOCALHOST_CLIENT_SECRET')
                ).toString('base64')}`,
                'Content-Type': 'application/x-www-form-urlencoded'
            })
        }
    } as FastifyHttpProxyOptions)

    // Medplum OAuth proxy. Documented in src/plugins/external/swagger.ts > transform function
    fastify.register(proxy, {
        upstream: `${config.get('MEDPLUM.API_URL')}/oauth2`,
        prefix: '/api/medplum/auth',
        httpMethods: ['GET', 'POST'],
        replyOptions: {
            rewriteRequestHeaders: (originalReq: any, headers: any) => {
                if (originalReq.url.includes('token')) {
                    headers['Authorization'] = `Basic ${Buffer.from(
                        config.get('MEDPLUM.CLIENT_ID') + ':' + config.get('MEDPLUM.CLIENT_SECRET')
                    ).toString('base64')}`
                    headers['Content-Type'] = 'application/x-www-form-urlencoded'
                }
                return {
                    ...headers
                }
            }
        }
    } as FastifyHttpProxyOptions)

    // Medplum OAuth proxy for localhost DEVs. Used during Medplum login. Documented in src/plugins/external/swagger.ts > transform function
    fastify.register(proxy, {
        upstream: `${config.get('MEDPLUM.API_URL')}/oauth2`,
        prefix: '/local/medplum/auth',
        httpMethods: ['GET', 'POST'],
        replyOptions: {
            rewriteRequestHeaders: (originalReq: any, headers: any) => {
                if (originalReq.url.includes('token')) {
                    headers['Authorization'] = `Basic ${Buffer.from(
                        config.get('MEDPLUM.LOCALHOST_CLIENT_ID') + ':' + config.get('MEDPLUM.LOCALHOST_CLIENT_SECRET')
                    ).toString('base64')}`
                    headers['Content-Type'] = 'application/x-www-form-urlencoded'
                }
                return {
                    ...headers
                }
            }
        }
    } as FastifyHttpProxyOptions)
    
    // Load routes under guest
    fastify.register(fastifyAutoload, {
        dir: path.join(__dirname, 'guest'),
        options: { prefix: '/api' }
    })
    
    fastify.log.info('Guest routes loaded successfully.');

}

export default guestProvider;