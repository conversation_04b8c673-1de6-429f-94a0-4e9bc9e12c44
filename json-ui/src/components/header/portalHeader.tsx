import React from 'react';
import i18n from 'i18next';
import { IUser, IUserIdentity } from 'relic-ui';
import { useGetLocale, useSetLocale, useGetIdentity } from '@refinedev/core';
import { HamburgerMenu, RefineThemedLayoutV2HeaderProps } from '@refinedev/mui';

import Box from '@mui/material/Box';
import Stack from '@mui/material/Stack';
import AppBar from '@mui/material/AppBar';
import Avatar from '@mui/material/Avatar';
import Select from '@mui/material/Select';
import Toolbar from '@mui/material/Toolbar';
import MenuItem from '@mui/material/MenuItem';
import Typography from '@mui/material/Typography';
import FormControl from '@mui/material/FormControl';

import { Search } from '../search';

// ----------------------------------------------------------------------

export const PortalHeader: React.FC<RefineThemedLayoutV2HeaderProps> = ({
  sticky = true,
}) => {
  const { data: currentUser } = useGetIdentity<IUser>();

  const userIdentity = currentUser?.userIdentity as IUserIdentity;

  const changeLanguage = useSetLocale();

  const locale = useGetLocale();

  const currentLocale = locale();

  if (!userIdentity?.portalIdentity?.header) return <></>;
  else if (window.location.pathname.includes('embedded-call')) return <></>;
  else
    return (
      <AppBar
        position={sticky ? 'sticky' : 'relative'}
        sx={{
          bgcolor: 'background.default',
          boxShadow: theme => theme.customShadows.z1,
        }}
      >
        <Toolbar>
          <HamburgerMenu />
          <Stack
            direction="row"
            width="100%"
            justifyContent="flex-end"
            alignItems="center"
            gap="16px"
          >
            {/* <FormControl
              sx={{
                minWidth: 64,
              }}
            >
              <Select
                disableUnderline
                defaultValue={currentLocale}
                inputProps={{ 'aria-label': 'Without label' }}
                variant="standard"
                sx={{
                  '& .lang-select-label': {
                    pr: 1,
                    fontWeight: 'fontWeightMedium',
                    display: {
                      xs: 'none',
                      sm: 'inline-flex',
                    },
                  },
                }}
              >
                {[...(i18n.languages ?? [])].sort().map((lang: string) => (
                  <MenuItem
                    selected={currentLocale === lang}
                    key={lang}
                    defaultValue={lang}
                    onClick={() => {
                      changeLanguage(lang);
                    }}
                    value={lang}
                  >
                    <Stack direction="row" alignItems="center" justifyContent="center">
                      <Avatar
                        sx={{
                          mr: 1,
                          width: 24,
                          height: 24,
                        }}
                        src={`/images/flags/${lang}.svg`}
                      />
                      <Box component="span" className="lang-select-label">
                        {lang === 'en' ? 'English' : 'German'}
                      </Box>
                    </Stack>
                  </MenuItem>
                ))}
              </Select>
            </FormControl> */}
            <Stack
              direction="row"
              gap="16px"
              alignItems="center"
              justifyContent="center"
            >
              <Search />
            </Stack>
            {(currentUser?.avatar || userIdentity?.portalIdentity?.name) && (
              <Stack
                direction="row"
                gap="16px"
                alignItems="center"
                justifyContent="center"
              >
                {userIdentity?.portalIdentity?.name && (
                  <Typography
                    sx={{
                      display: {
                        xs: 'none',
                        sm: 'inline-block',
                      },
                    }}
                    variant="subtitle2"
                  >
                    {userIdentity?.portalIdentity?.name}
                  </Typography>
                )}
                <Avatar
                  src={currentUser?.avatar}
                  alt={userIdentity?.portalIdentity?.name}
                />
              </Stack>
            )}
          </Stack>
        </Toolbar>
      </AppBar>
    );
};
