import React, { useState, useEffect, useCallback } from 'react';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import IconButton from '@mui/material/IconButton';
import CircularProgress from '@mui/material/CircularProgress';
import DialogContent from '@mui/material/DialogContent';
import Box from '@mui/material/Box';
import Alert from '@mui/material/Alert';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import CloseIcon from '@mui/icons-material/Close';
import Paper from '@mui/material/Paper';
import { useUserContext } from '../RelicUserProvider';
interface CitationViewerProps {
  open: boolean;
  onClose: () => void;
  citationUrl: string;
  citationContent: string;
}

const CitationViewer: React.FC<CitationViewerProps> = ({ open, onClose, citationUrl, citationContent }) => {
  const [fileUrl, setFileUrl] = useState<string | null>(null);
  const [fileType, setFileType] = useState<'pdf' | 'docx' | 'doc' | 'txt' | 'url' | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [txtContent, setTxtContent] = useState<string | null>(null);
  const { accessToken, idToken, serviceUri, myIdentity } = useUserContext();
  const [hasOpened, setHasOpened] = useState(false);

  /**
   * Generates a URL with text fragment highlighting parameters.
   *
   * This function takes a URL and text, then creates a URL that, when opened in a
   * supporting browser, will highlight the specified text on the page.
   * It uses the text fragment feature by taking the first three words and last three words
   * of the provided text and adding them as parameters to the URL.
   *
   * @param url - The base URL of the web page.
   * @param text - The text to be highlighted on the web page.
   * @returns A URL with text fragment highlighting parameters appended.
   *
   * @see https://developer.mozilla.org/en-US/docs/Web/URI/Reference/Fragment/Text_fragments
   */
  const browserHightlightedText = useCallback((url: string, text: string) => {
    const words = text.trim().split(' ');
    const firstThreeWords = words.slice(0, 3).join(' ').replace(/\.$/, '');
    const lastThreeWords = words.slice(-3).join(' ').replace(/\.$/, '');
    console.log({ lastThreeWords, firstThreeWords });
    // https://www.example.com/#:~:text=First Word, Last Word of the chunk
    return `${url}#:~:text=${encodeURIComponent(firstThreeWords)},${encodeURIComponent(lastThreeWords)}`;
  }, []);

  useEffect(() => {
    if (open && citationUrl) {
      const validateCitationUrl = async () => {
        setLoading(true);
        setError(null);
        setHasOpened(false); // Reset the flag when a new URL is loaded

        try {
          const isAzureBlobUrl = citationUrl.includes('.blob.core.windows.net');

          if (isAzureBlobUrl) {
            // Determine file type based on file extension
            if (citationUrl.endsWith('.pdf')) {
              setFileType('pdf');
            } else if (citationUrl.endsWith('.docx') || citationUrl.endsWith('.doc')) {
              setFileType('docx');
            } else if (citationUrl.endsWith('.txt')) {
              setFileType('txt');
            } else {
              setFileType(null); // Unsupported file type
            }

            // Fetch SAS token for Azure Blob URL
            const response = await fetch(`${serviceUri}/api/tokens/azure-sas?url=${encodeURIComponent(citationUrl)}`, {
              method: 'GET',
              headers: {
                'Content-Type': 'application/json',
                'x-access-token': accessToken,
                'x-id-token': idToken ?? '',
                'x-organization-id': myIdentity?.portalIdentity?.organizationId || '',
              },
            });

            if (!response.ok) {
              throw new Error('Failed to fetch SAS token');
            }

            const responseData = await response.json();

            const citationSasToken = responseData.data.token;
            const securedUrl = `${citationUrl}?${citationSasToken}`;
            setFileUrl(securedUrl);

            // Fetch content if file is .txt
            if (citationUrl.endsWith('.txt')) {
              const textResponse = await fetch(securedUrl);
              if (!textResponse.ok) {
                throw new Error('Failed to fetch text content');
              }
              const textData = await textResponse.text();
              setTxtContent(textData);
            }
          } else {
            // Non-Azure URLs
            setFileType('url');
            setFileUrl(citationUrl);
          }
        } catch (err) {
          setError('Unable to fetch the document. Please try again.');
        } finally {
          setLoading(false);
        }
      };

      validateCitationUrl();
    }
  }, [open, citationUrl, accessToken, idToken, myIdentity, serviceUri]);

  useEffect(() => {
    if (fileType && fileType !== 'txt' && fileUrl && !hasOpened && !loading) {
      // Open all non-txt file types (pdf, docx, and external URLs) in a new tab and close the modal
      const externalUrl =
        fileType === 'docx' || fileType === 'doc'
          ? `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(fileUrl)}`
          : browserHightlightedText(fileUrl, citationContent);

      console.log(`Opening ${fileType} file in new tab: ${externalUrl}`);

      window.open(externalUrl, '_blank', 'noopener,noreferrer');
      setHasOpened(true); // Set the flag to true to prevent re-opening
      onClose();
    }
  }, [fileType, fileUrl, onClose, hasOpened, loading]);

  // Handle different dialog display states based on conditions
  if (!open) {
    return null; // Don't display anything when not open
  }

  // Small loading dialog when initially loading (except for text files that need full dialog)
  if (loading && fileType !== 'txt') {
    return (
      <Dialog open={open} onClose={onClose} maxWidth="xs" aria-labelledby="citation-loading-dialog">
        <DialogContent>
          <Box display="flex" flexDirection="column" alignItems="center" justifyContent="center" py={3} px={2}>
            <CircularProgress size={40} sx={{ mb: 2 }} />
            <Typography variant="body2" sx={{ color: 'text.secondary' }}>
              Loading your document...
            </Typography>
          </Box>
        </DialogContent>
      </Dialog>
    );
  }

  // Full screen dialog for text files or when there's an error
  if (fileType === 'txt' || error) {
    return (
      <Dialog open={open} onClose={onClose} aria-labelledby="citation-source-viewer">
        <DialogTitle sx={{ position: 'relative', display: 'flex', justifyContent: 'space-between' }}>
          <span>Citation Source</span>
          <IconButton
            edge="end"
            color="inherit"
            onClick={onClose}
            aria-label="close"
            sx={{ position: 'absolute', right: 8, top: 8 }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent>
          <Box sx={{ height: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
            {loading ? (
              <Box display="flex" flexDirection="column" alignItems="center" justifyContent="center">
                <CircularProgress size={50} sx={{ mb: 2 }} />
                <Typography variant="body1" sx={{ color: 'text.secondary' }}>
                  Loading your document, please wait...
                </Typography>
              </Box>
            ) : error ? (
              <Box display="flex" flexDirection="column" alignItems="center" justifyContent="center">
                <Alert severity="error" sx={{ mb: 2 }}>
                  {error}
                </Alert>
                <Button onClick={onClose} variant="contained" color="primary">
                  Close
                </Button>
              </Box>
            ) : (
              <Box
                sx={{
                  height: '100%',
                  width: '100%',
                  overflowY: 'auto',
                  whiteSpace: 'pre-wrap',
                  fontFamily: 'monospace',
                  padding: 2,
                  backgroundColor: '#f9f9f9',
                  borderRadius: 1,
                  boxShadow: 1,
                }}
              >
                {txtContent}
              </Box>
            )}
          </Box>
        </DialogContent>
      </Dialog>
    );
  }

  // For other file types (PDF, DOCX, URL) that are still visible but not loading
  // This is a fallback, though they should normally be closed and opened in a new tab
  return (
    <Dialog open={open} onClose={onClose} maxWidth="xs" aria-labelledby="citation-redirect-dialog">
      <DialogContent>
        <Box display="flex" flexDirection="column" alignItems="center" justifyContent="center" py={3} px={2}>
          <Typography variant="body2" sx={{ mb: 2 }}>
            Opening document in a new tab...
          </Typography>
          <Button onClick={onClose} variant="outlined" size="small">
            Close
          </Button>
        </Box>
      </DialogContent>
    </Dialog>
  );
};

export default CitationViewer;
