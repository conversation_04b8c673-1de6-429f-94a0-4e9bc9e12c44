import { BlobServiceClient, BlockBlobClient } from '@azure/storage-blob';
import axios from 'axios';
import { RelicDocument } from 'relic-ui';

import { getActiveLogin } from 'src/providers/utils';

import { getNodeServicesApi } from './app-config';

let blobServiceClient: BlobServiceClient | null = null;

const fetchToken = async (
  serviceName: string,
  storageOptions: {
    accountName?: string;
    containerName?: string;
  } = {},
): Promise<string> => {
  if (
    serviceName === 'azure-sas' &&
    (!storageOptions.accountName || !storageOptions.containerName)
  ) {
    throw new Error('Invalid storage options');
  }
  const activeLogin = getActiveLogin();

  const queryParams = [
    `accountName=${storageOptions.accountName}`,
    `containerName=${storageOptions.containerName}`,
  ]
    .filter(Boolean)
    .join('&');

  const response = await axios.get(
    `${getNodeServicesApi()}/api/${activeLogin.provider}/tokens/${serviceName}${queryParams ? `?${queryParams}` : ''}`,
    {
      headers: {
        'x-access-token': activeLogin?.accessToken,
        'x-id-token': activeLogin?.idToken ?? '',
        'x-organization-id': activeLogin?.organizationId ?? '',
      },
    },
  );
  return response.data.token;
};

export const uploadToAzureBlobStorage = async (
  file: File,
  relicDocument: RelicDocument,
  uploadOptions: {
    accountName: string;
    containerName: string;
  },
): Promise<RelicDocument> => {
  if (!uploadOptions.accountName || !uploadOptions.containerName) {
    throw new Error('Invalid upload options');
  }
  const sasToken = await fetchToken('azure-sas', uploadOptions);
  if (!sasToken) {
    throw new Error('Failed to get SAS token');
  }
  blobServiceClient = new BlobServiceClient(
    `https://${uploadOptions.accountName}.blob.core.windows.net/?${sasToken}`,
  );

  relicDocument.filename = file.name;
  relicDocument.type = file.type;
  const containerClient = blobServiceClient.getContainerClient(
    uploadOptions.containerName,
  );
  const blockBlobClient: BlockBlobClient = containerClient.getBlockBlobClient(
    relicDocument.documentId,
  );
  await blockBlobClient.uploadData(file, {
    blobHTTPHeaders: {
      blobContentType: file.type || 'application/octet-stream',
    },
    blockSize: 4 * 1024 * 1024,
    concurrency: 20,
  });
  relicDocument.url = blockBlobClient.url;
  return relicDocument;
};

export const generateAndUploadPdf = async (
  relicDocument: RelicDocument,
  reportHtml: string,
  test?: boolean,
): Promise<RelicDocument> => {
  const docraptorKey = await fetchToken('docraptor');
  const config = {
    url: 'https://api.docraptor.com/docs',
    encoding: null, //IMPORTANT! This produces a binary body response instead of text
    headers: {
      'Content-Type': 'application/json',
    },
    json: {
      user_credentials: docraptorKey,
      doc: {
        document_content: reportHtml,
        type: 'pdf',
        test: true, // test: true will not generate the PDF and is free, test: false will generate the PDF and will be deducted from your Docraptor account
      },
    },
  };
  const uploadOptions = {
    accountName: 'facility',
    containerName: relicDocument.organizationId,
  };

  const pdfInBinary = await axios.post(config.url, config.json, {
    headers: config.headers,
    responseType: 'arraybuffer',
  });
  const pdfReport: File = new File([pdfInBinary.data], relicDocument.filename, {
    type: 'application/pdf',
  });
  return await uploadToAzureBlobStorage(
    pdfReport,
    relicDocument,
    uploadOptions,
  );
};
